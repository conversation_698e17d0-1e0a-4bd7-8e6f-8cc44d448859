server {
    listen 80;
    server_name _;
    index index.html;
    root  /usr/share/nginx/html;  #dist上传的路径
    gzip on;
    gzip_static on;

    # 避免访问出现 404 错误
    location / {
        try_files $uri $uri/ @router;
        index  index.html;
    }

    location @router {
        rewrite ^.*$ /index.html last;
    }

    # 接口
    location /api {
        proxy_pass http://id-yc-ent-manage-server:6100;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # OpenAPI接口
    location /openapi {
        proxy_pass http://id-yc-ent-manage-server:6100;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Port $server_port;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

}
