# 基础镜像
FROM harbor.idx.space/ops/nginx:1.24.1
# 设置时区
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
     && echo "Asia/Shanghai" > /etc/timezone
# 项目路径
COPY --chown=nginx:nginx dist /usr/share/nginx/html
COPY --chown=nginx:nginx ./province/nginx.conf /etc/nginx/conf.d/default.conf
# 更改Nginx主目录的权限
RUN chown -R nginx:nginx /var/cache/nginx /var/log/nginx /etc/nginx
RUN touch /var/run/nginx.pid && chown nginx:nginx /var/run/nginx.pid && chmod 644 /var/run/nginx.pid
# 切换用户
USER nginx
# 容器启动时运行Nginx
CMD ["nginx", "-g", "daemon off;"]