## 系统简介
中国烟草工业互联网标识数据管理系统


## 克隆项目
```bash
git clone https://codeup.teambition.com/teleinfo/yc/id-yc-province-web.git
```

## 运行项目
### 环境依赖
node v16+
### 安装依赖
```bash
yarn install
```
### 启动服务
省级节点：npm run dev:province
企业节点：npm run dev:ent
### 前端访问地址
http://localhost:3000

### 构建生产环境 
```bash
npm build
```
## 项目部署
通过Jenkins构建部署，GIT分支 develop分支部署到开发环境 release分支部署到测试环境

## 项目目录结构描述
```bash 
ID-YC-PROVINCE-WEB
    |-- .env.development    //开发环境配置文件
    |-- .env.production     //生产环境配置文件
    |-- .env.staging
    |-- .eslintrc.js        //格式化配置
    |-- .gitignore
    |-- .prettierrc.json
    |-- index.html
    |-- package-lock.json
    |-- package.json        //依赖包
    |-- README.md
    |-- tsconfig.json       //ts配置文件
    |-- vite.config.ts      //vite配置文件
    |-- public
    |-- docker              //docker文件
    |-- src
    |   |-- App.vue
    |   |-- env.d.ts
    |   |-- main.ts                 //主入口文件
    |   |-- mockProdServer.ts
    |   |-- permission.ts           //路由守卫
    |   |-- settings.ts
    |   |-- api                     //接口API
    |   |-- assets                  //静态文件
    |   |-- components              //公共组件
    |   |-- hooks
    |   |-- layout                  //页面菜单栏布局
    |   |-- plugins
    |   |-- router
    |   |   |-- constant.ts
    |   |   |-- dynamicRoutes.ts    //菜单路由
    |   |   |-- index.ts            //公共路由
    |   |   |-- system.ts
    |   |-- store
    |   |-- types                   //ts类型定义
    |   |-- utils                   //通用方法
    |   |   |--constant.ts          //静态变量定义
    |   |   |--request.ts           //axios拦截器
    |   |   |--...
    |   |-- views
    |       |-- login.vue                 //登录页
    |       |-- proxyManager.vue          //托管管理
    |       |-- applicationManage         //应用管理
    |       |-- error
    |       |-- handleManage
    |       |   |-- handle                //对象标识注册
    |       |   |-- handleMaintenance     //标识维护
    |       |   |-- instanceHandle        //实例标识注册
    |       |-- idRes                     //标识解析
    |       |-- logs                      //日志管理
    |       |-- node                      //节点信息
    |       |-- nodeManage                
    |       |   |-- prefixAudit.vue       //前缀审核
    |       |   |-- prefixManage.vue      //前缀管理
    |       |   |-- hostingApply          //托管申请
    |       |   |-- hostingAudit          //托管审核
    |       |   |-- prefixApply           //前缀申请
    |       |-- obj-handle                //对象管理
    |       |-- objectReport              //对象上报
    |       |-- prefix                    
    |       |-- redirect
    |       |-- system
    |           |-- account               //账号管理
    |           |-- auth                  //权限管理
    |           |-- operation             //运维管理
    |           |-- role                  //角色管理
    |-- vite                              //vite相关
        |-- plugins
            |-- auto-import.js
            |-- compression.js
            |-- index.js
            |-- setup-extend.js
            |-- svg-icon.js
```
## 版本