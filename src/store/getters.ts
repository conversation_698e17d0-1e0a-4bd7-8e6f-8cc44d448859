import {
  SYSTEM_TYPE,
  LEVEL_TYPE,
  ROLE_TYPE,
  LOGIN_MODEL,
} from "@/utils/constant";
import { APP_TYPE } from "@/utils/dataPlatform";

const getters = {
  sidebar: (state: any) => state.app.sidebar,
  size: (state: any) => state.app.size,
  device: (state: any) => state.app.device,
  visitedViews: (state: any) => state.tagsView.visitedViews,
  cachedViews: (state: any) => state.tagsView.cachedViews,
  token: (state: any) => state.user.token,
  name: (state: any) => state.user?.userInfo?.username,
  introduction: (state: any) => state.user?.introduction,
  roles: (state: any) => state.user.roles,
  permissions: (state: any) => state.user.permissions,
  permission_routes: (state: any) => state.permission.routes,
  topbarRouters: (state: any) => state.permission.topbarRouters,
  defaultRoutes: (state: any) => state.permission.defaultRoutes,
  sidebarRouters: (state: any) => state.permission.sidebarRouters,
  globalConfig: (state: any) => state.user?.globalConfig,
  levelType: (state: any) => state.user.userInfo?.levelType,
  userInfo: (state: any) => state.user.userInfo,
  userCenterId: (state: any) => state.user?.userCenterId,
  binding: (state: any) => state.user.binding,
  auths: (state: any) => state.user.auths,
  // 护网期间，不能进行账号添加
  enableAddUser: (state: any) => {
    return state.user?.globalConfig?.enableAddUser !== 0;
  },
  // 判断是否是省级系统
  isProvinceSystem: (state: any) =>
    state.user?.globalConfig?.systemType === SYSTEM_TYPE.SYSTEM_PROVINCE,
  // 判断是否是企业系统
  isEntSystem: (state: any) =>
    state.user?.globalConfig?.systemType === SYSTEM_TYPE.SYSTEM_PROXY,
  // 判断是省级账号
  isProvinceUser: (state: any) => {
    return state.user?.userInfo?.levelType === LEVEL_TYPE.PROVINCE;
  },
  // 判断是省级管理员
  isProvinceManager: (state: any) => {
    const isProvince = state.user?.userInfo?.levelType === LEVEL_TYPE.PROVINCE;
    const isAdmin = state.user?.userInfo?.roleInfos?.some(
      (item: any) => item.roleType === ROLE_TYPE.SYSTEM_ADMIN
    );
    return isProvince && isAdmin;
  },
  // 判断是企业账号
  isEntUser: (state: any) => {
    const isEnt = state.user?.userInfo?.levelType === LEVEL_TYPE.PROXY;
    const isApp = state.user?.userInfo?.roleInfos?.some(
      (item: any) => item.roleType === ROLE_TYPE.APP
    );
    return isEnt && !isApp;
  },
  // 判断是应用账号
  isAppUser: (state: any) => {
    const isEnt = state.user?.userInfo?.levelType === LEVEL_TYPE.PROXY;
    const isApp = state.user?.userInfo?.roleInfos?.some(
      (item: any) => item.roleType === ROLE_TYPE.APP
    );
    return isEnt && isApp;
  },

  // 判断是否可以创建中台应用
  canCreateDMM: (state: any) => {
    return !!state.user?.globalConfig?.mpDmm.enable; // true-可以创建中台应用，false-不能创建中台应用
  },

  // 判断是中台应用还是非中台应用
  appType: (state: any) => {
    return state.user?.userInfo?.appType || APP_TYPE.NORMAL; // 1-中台应用，2-非中台应用
  },

  // 判断登录账号是否是中台账号
  isSocialUser: (state: any) => {
    return state.user?.globalConfig?.loginModel !== LOGIN_MODEL.INNER;
  },

  // 判断新增账号类型是内部账号还是业务中台账号
  isAddSocialUser: (state: any) => {
    return state.user?.globalConfig?.addUserType === LOGIN_MODEL.SOCIAL;
  },
};
export default getters;
