import { constantRoutes } from "@/router";

// 匹配views里面所有的.vue文件
const modules = import.meta.glob("./../../views/**/*.vue");

const permission = {
  state: {
    routes: [],
    addRoutes: [],
    defaultRoutes: [],
    topbarRouters: [],
    sidebarRouters: [],
  },
  mutations: {
    SET_ROUTES: (
      state: {
        addRoutes: any;
        routes: (
          | {
              path: string;
              // 匹配views里面所有的.vue文件
              component: any;
              hidden: boolean;
              children: {
                path: string;
                component: () => Promise<typeof import("*.vue")>;
              }[];
            }
          | {
              path: string;
              component: () => Promise<any>;
              hidden: boolean;
              children?: undefined;
            }
          | {
              path: string;
              component: any;
              hidden?: undefined;
              children?: undefined;
            }
        )[];
      },
      routes: ConcatArray<
        | {
            path: string;
            component: any;
            // 匹配views里面所有的.vue文件
            hidden: boolean;
            children: {
              path: string;
              component: () => Promise<typeof import("*.vue")>;
            }[];
          }
        | {
            path: string;
            component: () => Promise<any>;
            hidden: boolean;
            children?: undefined;
          }
        | {
            path: string;
            component: any;
            hidden?: undefined;
            children?: undefined;
          }
      >
    ) => {
      state.addRoutes = routes;
      state.routes = constantRoutes.concat(routes);
    },
    SET_DEFAULT_ROUTES: (
      state: {
        defaultRoutes: (
          | {
              path: string;
              component: any;
              hidden: boolean;
              children: {
                path: string;
                component: () => Promise<typeof import("*.vue")>;
              }[];
            }
          | {
              path: string;
              component: () => Promise<any>;
              hidden: boolean;
              children?: undefined;
            }
          | {
              path: string;
              component: any;
              hidden?: undefined;
              children?: undefined;
            }
        )[];
      },
      routes: ConcatArray<
        | {
            path: string;
            component: any;
            hidden: boolean;
            children: {
              path: string;
              component: () => Promise<typeof import("*.vue")>;
            }[];
          }
        | {
            path: string;
            component: () => Promise<any>;
            hidden: boolean;
            children?: undefined;
          }
        | {
            path: string;
            component: any;
            hidden?: undefined;
            children?: undefined;
          }
      >
    ) => {
      state.defaultRoutes = constantRoutes.concat(routes);
    },
    SET_TOPBAR_ROUTES: (state: { topbarRouters: any }, routes: any) => {
      state.topbarRouters = routes;
    },
    SET_SIDEBAR_ROUTERS: (state: { sidebarRouters: any }, routes: any) => {
      state.sidebarRouters = routes;
    },
  },
  actions: {},
};

export const loadView = (view: string) => {
  let res;
  for (const path in modules) {
    const dir = path.split("views/")[1].split(".vue")[0];
    if (dir === view) {
      res = () => modules[path]();
    }
  }
  return res;
};

export default permission;
