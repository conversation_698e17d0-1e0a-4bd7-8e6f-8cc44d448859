import Cookies from "js-cookie";

const state = {
  sidebar: {
    opened: Cookies.get("sidebarStatus")
      ? !!+Cookies.get("sidebarStatus")
      : true,
    withoutAnimation: false,
    hide: false,
  },
  isIndex: true,
  device: "desktop",
  size: Cookies.get("size") || "default",
};

const mutations = {
  TOGGLE_SIDEBAR: (state: {
    sidebar: { hide: boolean; opened: boolean; withoutAnimation: boolean };
  }) => {
    if (state.sidebar.hide) {
      return false;
    }
    state.sidebar.opened = !state.sidebar.opened;
    state.sidebar.withoutAnimation = false;
    if (state.sidebar.opened) {
      Cookies.set("sidebarStatus", 1);
    } else {
      Cookies.set("sidebarStatus", 0);
    }
  },
  CLOSE_SIDEBAR: (
    state: { sidebar: { opened: boolean; withoutAnimation: boolean } },
    withoutAnimation: boolean
  ) => {
    Cookies.set("sidebarStatus", 0);
    state.sidebar.opened = false;
    state.sidebar.withoutAnimation = withoutAnimation;
  },
  TOGGLE_DEVICE: (state: { device: string }, device: string) => {
    state.device = device;
  },
  SET_SIZE: (state: { size: any }, size: any) => {
    state.size = size;
    Cookies.set("size", size);
  },
  SET_SIDEBAR_HIDE: (
    state: {
      isIndex: boolean;
      sidebar: { hide: boolean; withoutAnimation: boolean };
    },
    data: { isIndex: boolean; status: boolean }
  ) => {
    state.isIndex = data.isIndex;
    state.sidebar.hide = data.status;
    state.sidebar.withoutAnimation = false;
  },
};

const actions = {
  toggleSideBar({ commit }: any) {
    commit("TOGGLE_SIDEBAR");
  },
  closeSideBar({ commit }: any, { withoutAnimation }: any) {
    commit("CLOSE_SIDEBAR", withoutAnimation);
  },
  toggleDevice({ commit }: any, device: string) {
    commit("TOGGLE_DEVICE", device);
  },
  setSize({ commit }: any, size: any) {
    commit("SET_SIZE", size);
  },
  toggleSideBarHide({ commit }: any, { status, route = "" }: any) {
    const isIndex = route === "/index";
    commit("SET_SIDEBAR_HIDE", { status, isIndex });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
