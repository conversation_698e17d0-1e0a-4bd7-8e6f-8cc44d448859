import {
  login,
  loginWithMiddle,
  systemBindExist,
  logout,
  refreshToken,
  getInfo,
  getGlobalConfig,
} from "@/api/login";
import {
  getToken,
  setToken,
  setExpiresIn,
  removeToken,
  deepClone,
} from "@/utils/auth";
import router from "../../router";
import { NAV_ROUTES_MAP, ALL_ROUTES } from "../../router/dynamicRoutes";
import {
  SYSTEM_TYPE,
  LEVEL_TYPE,
  SYSTEM_NAME,
  IS_TENANT,
  LOGIN_MODEL,
} from "@/utils/constant";

let dynamicRoutes: any = [];

// 获取所有权限
function getAuths(data: any) {
  const newArr: any[] = [];
  const fn = (children: any[]) => {
    children.forEach((item: { authCode: any; childNodes: any[] }) => {
      if (item.authCode) {
        newArr.push(item.authCode);
      }
      if (item.childNodes?.length) {
        fn(item.childNodes);
      }
    });
  };
  fn(data);
  return newArr;
}

// 动态添加路由
function addDynamicRoutes(auths: any[]) {
  const addDynamicRoute = (authChild: any[]) => {
    authChild.forEach(
      (auth: { isShow: any; authCode: any; children: any[] }) => {
        auth.isShow = auths.includes(auth.authCode);
        if (auth.children?.length) {
          addDynamicRoute(auth.children);
        }
      }
    );
  };
  addDynamicRoute(dynamicRoutes);

  const fn3 = (data: any[]) => {
    const newArray: any[] = [];
    data.forEach(
      (dataChild: { children: any[]; redirect: any; isShow: any }) => {
        if (dataChild.children?.length) {
          const tmpChildren = fn3(dataChild.children);
          if (tmpChildren?.length) {
            dataChild.redirect = tmpChildren[0].path; // 重定向到第一个子路由
            // 有子节点，父级菜单才展示出来
            dataChild.children = tmpChildren;
            newArray.push(dataChild);
          }
        } else if (dataChild.isShow) {
          newArray.push(dataChild);
        }
      }
    );
    return newArray;
  };
  const newDynamicRoutes = fn3(deepClone(dynamicRoutes));

  newDynamicRoutes.forEach((auth) => {
    router.addRoute({
      ...auth,
    });
  });
  return newDynamicRoutes;
}

const user = {
  state: {
    token: getToken(),
    userCenterId: null,
    binding: null,
    name: "",
    roles: [],
    permissions: [],
    menus: [],
    userInfo: null,
    globalConfig: {
      fullFilled: false,
      login: {
        captchaEnable: false,
        passwordEncrypted: false,
      },
      objectHandle: {
        handleAdmin: "",
      },
      mpDmm: {
        enable: true,
      },
      systemType: SYSTEM_TYPE.SYSTEM_PROVINCE,
      isTenant: IS_TENANT.TENANT_Y,
      enableAddUser: 1, // 是否可以增加账号
      loginModel: LOGIN_MODEL.INNER, // 登录模型
      addUserType: LOGIN_MODEL.INNER, // 新增用户类型
      industryPortalUrl: "",
    },
    auths: [],
  },

  mutations: {
    SET_TOKEN: (state: { token: any }, token: any) => {
      state.token = token;
    },
    SET_USER_CENTER_ID: (state: { userCenterId: any }, userCenterId: any) => {
      state.userCenterId = userCenterId;
    },
    SET_BINDING: (state: { binding: any }, binding: any) => {
      state.binding = binding;
    },
    SET_EXPIRES_IN: (state: { expires_in: any }, time: any) => {
      state.expires_in = time;
    },
    SET_NAME: (state: { name: any }, name: any) => {
      state.name = name;
    },
    SET_ROLES: (state: { roles: any }, roles: any) => {
      state.roles = roles;
    },
    SET_PERMISSIONS: (state: { permissions: any }, permissions: any) => {
      state.permissions = permissions;
    },
    SET_GLOBAL_CONFIG: (
      state: {
        globalConfig: {
          fullFilled: boolean;
          login: any;
          objectHandle: any;
          mpDmm: any;
          systemType: any;
          isTenant: any;
          enableAddUser: number;
          defaultAuthType: number;
          loginModel: number;
          addUserType: number;
          industryPortalUrl: string;
        };
      },
      config: {
        login: any;
        objectHandle: any;
        systemType: any;
        isTenant: any;
        enableAddUser: number;
        defaultAuthType: number;
        mpDmm: any;
        loginModel: number;
        addUserType: number;
        industryPortalUrl: string;
      }
    ) => {
      state.globalConfig.fullFilled = true;
      state.globalConfig.login = config.login;
      state.globalConfig.objectHandle = config.objectHandle;
      state.globalConfig.systemType = config.systemType;
      state.globalConfig.isTenant = config.isTenant;
      state.globalConfig.enableAddUser = config.enableAddUser;
      state.globalConfig.defaultAuthType = config.defaultAuthType;
      state.globalConfig.mpDmm = config.mpDmm;
      if (config.loginModel) {
        state.globalConfig.loginModel = config.loginModel;
      }
      if (config.addUserType) {
        state.globalConfig.addUserType = config.addUserType;
      }
      state.globalConfig.industryPortalUrl = config.industryPortalUrl || "";
    },
    SET_USER_INFO: (state: { userInfo: any }, userInfo: any) => {
      state.userInfo = userInfo;
    },
    SET_AUTHS: (state: { auths: any }, auths: any) => {
      state.auths = auths;
    },
  },

  actions: {
    // 登录
    Login(
      { commit }: any,
      userInfo: {
        username: string;
        password: any;
        captcha: any;
        provinceId: string;
      }
    ) {
      const username = userInfo.username.trim();
      const password = userInfo.password;
      const captcha = userInfo.captcha;
      const provinceId = userInfo.provinceId;
      return new Promise<void>((resolve, reject) => {
        login(username, password, captcha, provinceId)
          .then((res) => {
            setToken(res.token);
            commit("SET_TOKEN", res.token);
            resolve();
          })
          .catch((error: any) => {
            reject(error);
          });
      });
    },
    // 业务中台是否绑定
    SystemBindExist(
      { commit }: any,
      userInfo: { username: string; password: any }
    ) {
      const username = userInfo.username.trim();
      const password = userInfo.password;
      return new Promise<void>((resolve, reject) => {
        systemBindExist({ username, password })
          .then((res) => {
            commit("SET_USER_CENTER_ID", res.userCenterId);
            commit("SET_BINDING", res.binding);
            resolve();
          })
          .catch((error: any) => {
            reject(error);
          });
      });
    },
    // 业务中台登录
    LoginWithMiddle(
      { commit }: any,
      userInfo: {
        username: string;
        password: any;
        captcha: any;
        provinceId: any;
      }
    ) {
      const username = userInfo.username.trim();
      const password = userInfo.password;
      const captcha = userInfo.captcha;
      const provinceId = userInfo.provinceId;
      console.log("123");
      return new Promise<void>((resolve, reject) => {
        loginWithMiddle(username, password, captcha, provinceId)
          .then((res) => {
            setToken(res.token);
            commit("SET_TOKEN", res.token);
            resolve();
          })
          .catch((error: any) => {
            reject(error);
          });
      });
    },

    // 获取用户信息
    GetInfo({ commit, state }: any) {
      return new Promise((resolve, reject) => {
        getInfo()
          .then((response) => {
            console.log("用户信息：", response);
            commit("SET_USER_INFO", response);
            // 根据用户信息获取是企业节点路由还是省级节点路由
            dynamicRoutes = [deepClone(NAV_ROUTES_MAP[response.levelType])]; // 省级节点路由
            dynamicRoutes[0].children = deepClone(ALL_ROUTES);
            // 没有托管前缀的，头部导航需要将名字改成“企业节点”
            if (
              !response.isHosting &&
              response.levelType === LEVEL_TYPE.PROXY
            ) {
              dynamicRoutes[0].title = SYSTEM_NAME.SYSTEM_NAME_PROXY;
            }

            // 企业节点系统，头部导航需要将名字改成“企业节点”
            if (state.globalConfig.systemType === SYSTEM_TYPE.SYSTEM_PROXY) {
              dynamicRoutes[0].title = SYSTEM_NAME.SYSTEM_NAME_PROXY;
            }
            const auths = getAuths(response.authTree || []);
            console.log("所有权限：", auths);
            commit("SET_ROLES", auths);
            commit("SET_AUTHS", auths);
            const newData = addDynamicRoutes(auths);
            console.log("最新动态路由：", newData);
            commit("SET_ROLES", newData);
            resolve((newData.length && newData[0].path) || "/401"); // 没有权限，进入401页面
          })
          .catch((error: any) => {
            reject(error);
          });
      });
    },

    // 刷新token
    RefreshToken({ commit, state }: any) {
      return new Promise<void>((resolve, reject) => {
        refreshToken(state.token)
          .then((res) => {
            setExpiresIn(res.data);
            commit("SET_EXPIRES_IN", res.data);
            resolve();
          })
          .catch((error: any) => {
            reject(error);
          });
      });
    },

    // 退出系统
    LogOut({ commit, state }: any) {
      return new Promise<void>((resolve, reject) => {
        logout(state.token)
          .then(() => {
            commit("SET_TOKEN", "");
            commit("SET_ROLES", []);
            commit("SET_PERMISSIONS", []);
            removeToken();
            resolve();
          })
          .catch((error: any) => {
            reject(error);
          });
      });
    },

    // 前端 登出
    FedLogOut({ commit }: any) {
      return new Promise<void>((resolve) => {
        commit("SET_TOKEN", "");
        removeToken();
        resolve();
      });
    },

    // 获取全局配置
    GetGlobalConfig({ commit }: any) {
      return new Promise((resolve, reject) => {
        getGlobalConfig()
          .then((response) => {
            console.log("全局配置信息：", response);
            // 修改页面title
            if (response.systemType === SYSTEM_TYPE.SYSTEM_PROVINCE) {
              document.title = "省级标识业务管理系统";
            } else if (response.systemType === SYSTEM_TYPE.SYSTEM_PROXY) {
              document.title = "企业标识业务管理系统";
            }
            commit("SET_GLOBAL_CONFIG", response);
            resolve(response);
          })
          .catch((error: any) => {
            reject(error);
          });
      });
    },
  },
};

export default user;
