import defaultSettings from "@/settings";
import { useDynamicTitle } from "@/utils/dynamicTitle";

const { sideTheme, showSettings, topNav, tagsView, fixedHeader, dynamicTitle } =
  defaultSettings;

const storageSetting = localStorage.getItem("layout-setting")
  ? JSON.parse(localStorage.getItem("layout-setting") || "")
  : "";
const state = {
  title: "",
  theme: storageSetting.theme || "#00a57c",
  sideTheme: storageSetting.sideTheme || sideTheme,
  showSettings,
  topNav: storageSetting.topNav === undefined ? topNav : storageSetting.topNav,
  tagsView:
    storageSetting.tagsView === undefined ? tagsView : storageSetting.tagsView,
  fixedHeader:
    storageSetting.fixedHeader === undefined
      ? fixedHeader
      : storageSetting.fixedHeader,
  dynamicTitle:
    storageSetting.dynamicTitle === undefined
      ? dynamicTitle
      : storageSetting.dynamicTitle,
};
const mutations = {
  CHANGE_SETTING: (
    state: { [x: string]: any; hasOwnProperty: (arg0: any) => any },
    { key, value }: any
  ) => {
    if (state.hasOwnProperty(key)) {
      state[key] = value;
    }
  },
};

const actions = {
  // 修改布局设置
  changeSetting({ commit }: any, data: any) {
    commit("CHANGE_SETTING", data);
  },
  // 设置网页标题
  setTitle(title: any) {
    state.title = title;
    useDynamicTitle();
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
