<script setup lang="ts">
import * as echarts from "echarts";
import { provide, onMounted } from "vue";

provide("echarts", echarts);

onMounted(() => {
  document.body.style.setProperty("--el-color-primary", "#00A57C"); // 常规
  document.body.style.setProperty("--el-menu-active-color", "#007457"); // 点击
  document.body.style.setProperty("--el-menu-hover-text-color", "#00D39F");
  document.body.style.setProperty("--el-menu-hover-bg-color", "#00D39F"); // hover
  document.body.style.setProperty("--el-color-primary-dark-2", "#00D39F"); //
  document.body.style.setProperty("--el-color-primary-light-3", "#00D39F");
  document.body.style.setProperty("--el-color-primary-light-5", "#6DE6C7");
  document.body.style.setProperty("--el-color-primary-light-7", "#007457");
  document.body.style.setProperty("--el-color-primary-light-8", "#00D39F");
  document.body.style.setProperty("--el-color-primary-light-9", "#DAFFF6");
  document.body.style.setProperty("--el-text-color-regular", "#272E2C");
  document.body.style.setProperty("--el-text-color-secondary", "#535F5C");
  document.body.style.setProperty("--el-border-color-lighter", "#DFE4E3");
  document.body.style.setProperty("--el-input-placeholder-color", "#7B9790");
  document.body.style.setProperty("--el-input-text-color", "#272E2C");
});
</script>

<template>
  <router-view />
</template>
