<template>
  <el-drawer
    custom-class="handle-detail-drawer"
    v-model="drawerVisible"
    title="标识详情"
    v-loading="loading"
    @closed="handleClose"
  >
    <div class="yc-description-basic">
      <div class="yc-description-title">
        <div class="yc-description-title-tip"></div>
        <div class="yc-description-title-text">基本信息</div>
      </div>
      <el-descriptions :column="1" direction="horizontal">
        <el-descriptions-item label="标识">
          <ellipsisText :value="detail?.handle"></ellipsisText
        ></el-descriptions-item>
        <el-descriptions-item label="标识名称">
          <ellipsisText :value="detail?.name"></ellipsisText
        ></el-descriptions-item>
        <el-descriptions-item label="实体类型">{{
          getEntityTypeName(detail?.entityType) || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="所属应用" v-if="!isProvince">{{
          appDisplay || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="所属应用" v-if="isProvince">{{
          detail?.appName || "-"
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <el-divider />

    <div class="yc-description">
      <div class="yc-description-title">
        <div class="yc-description-title-tip"></div>
        <div class="yc-description-title-text">属性信息</div>
      </div>
      <el-tabs v-model="activeName">
        <el-tab-pane label="基础属性" name="basic">
          <el-table :data="detail?.items" border size="small">
            <el-table-column label="中文名称" property="description">
              <template #default="scope">
                <ellipsisText :value="scope.row.description">{{
                  scope.row.description || "-"
                }}</ellipsisText>
              </template>
            </el-table-column>
            <el-table-column label="英文名称" property="field">
              <template #default="scope">
                <ellipsisText :value="scope.row.field">{{
                  scope.row.description || "-"
                }}</ellipsisText>
              </template>
            </el-table-column>
            <el-table-column label="属性类型" property="length">
              <template #default="scope">
                <div>{{ FIELD_TYPE_NAME_MAP[scope.row.fieldType] }}</div>
              </template>
            </el-table-column>
            <el-table-column label="属性值">
              <template #default="scope">
                <ellipsisText :value="getReferenceDis(scope.row)">{{
                  getReferenceDis(scope.row)
                }}</ellipsisText>
                <!-- <span>{{ getReferenceDis(scope.row) }}</span> -->
              </template>
            </el-table-column>
            <el-table-column label="操作" width="140">
              <template #default="scope">
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click="handleViewReference(scope.row)"
                  >详情</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="扩展属性" name="extend">
          <el-table :data="detail?.extendItems" border>
            <el-table-column label="中文名称" prop="description">
              <template #default="scope">
                <ellipsisText :value="scope.row.description">{{
                  scope.row.description || "-"
                }}</ellipsisText>
              </template></el-table-column
            >
            <el-table-column label="英文名称" prop="field">
              <template #default="scope">
                <ellipsisText :value="scope.row.field">{{
                  scope.row.field || "-"
                }}</ellipsisText>
              </template>
            </el-table-column>
            <el-table-column label="属性类型" prop="fieldType">
              <template #default="scope">
                <div>{{ FIELD_TYPE_NAME_MAP[scope.row.fieldType] }}</div>
              </template>
            </el-table-column>
            <el-table-column label="属性值">
              <template #default="scope">
                <ellipsisText :value="getReferenceDis(scope.row)">{{
                  getReferenceDis(scope.row)
                }}</ellipsisText>
              </template></el-table-column
            >
            <el-table-column label="操作" width="140">
              <template #default="scope">
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click="handleViewReference(scope.row)"
                  >详情</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-drawer>
  <handleItemDrawer
    v-if="handleItemDrawerVisible"
    :data="handleItemData"
    @close="handleItemDrawerVisible = false"
  ></handleItemDrawer>
</template>

<script lang="ts" setup>
import { onMounted, ref, computed, reactive } from "vue";
import { useStore } from "vuex";
import {
  FIELD_TYPE_MAP,
  LEVEL_TYPE,
  FIELD_TYPE_NAME_MAP,
  HANDLE_SOURCE,
} from "@/utils/constant";
import { IHandleDetail, IHandleItem } from "@/types/handle";
import {
  ApiGetHandleDetailNew,
  ApiGetProvinceHandleDetail,
  appList,
} from "@/api/objHandle/manager";
import { ApiGetHandleDetail } from "@/api/objHandle/handleMaintenance";
import { ApiGetAutoHandleDetail } from "@/api/objHandle/autoMaintain";
import handleItemDrawer from "./components/attrDetail.vue";
import ellipsisText from "@/components/ellipsisText/index.vue";

const activeName = ref("basic");
const store = useStore();
const userInfo = computed(() => store.getters.userInfo);
const isProvince = computed(
  () => userInfo.value.levelType === LEVEL_TYPE.PROVINCE
);
const emit = defineEmits(["close"]);

const fieldTypeMap = {
  fixed: 1,
  source: 2,
  handleValue: 3,
  handleWithAttr: 4,
};

function getReferenceDis(data: any) {
  if (data.fieldType === FIELD_TYPE_MAP.fixed) {
    return data.fieldValue;
  }
  if (data.fieldType === FIELD_TYPE_MAP.source) {
    return data.fieldValue;
  }
  if (data.references && data.references.length) {
    return data.references
      .map((reference: any) => reference.referenceHandle)
      .join(",");
  }

  return "-";
}

const props = defineProps({
  id: {
    type: String,
    default: undefined,
  },
  source: {
    type: String,
  },
});

const drawerVisible = ref(true);

const loading = ref(false);

const detail = ref<IHandleDetail>();

const appListResponse = ref<
  {
    id: number;
    appName: string;
  }[]
>([]);

const handleItemDrawerVisible = ref(false);

const handleItemData = ref<IHandleItem>();
const appDisplay = computed(() => {
  if (
    props.source === HANDLE_SOURCE.MAINTAIN ||
    props.source === HANDLE_SOURCE.AUTO
  ) {
    return detail.value?.appName || "-";
  }
  const appItemList = appListResponse.value.filter(
    (item) => item.id === detail.value?.appId
  );
  if (appItemList && appItemList.length > 0) {
    return appItemList[0].appName;
  }
  return "-";
});

function getEntityTypeName(type: number | undefined) {
  if (`${type}` === `1`) return "业务实体";
  if (`${type}` === `2`) return "资源实体";
  return "-";
}

// 获取标识详情接口
function getHandleDetail() {
  const requestParams = {
    handle: props.id,
    type: 1, // 前端写死1，获取所有详情
  };
  const promiseFn = isProvince.value
    ? ApiGetProvinceHandleDetail(requestParams)
    : ApiGetHandleDetailNew(requestParams);
  promiseFn.then((response) => {
    detail.value = response;
  });
}

// 标识维护详情
function getHandleDetailWithMaintain() {
  if (!props.id) return;
  loading.value = true;
  ApiGetHandleDetail({ handle: props.id })
    .then((response) => {
      detail.value = response;
      detail.value.items = response.basicsItems;
    })
    .finally(() => {
      loading.value = false;
    });
}

// 自动维护详情
function getHandleDetailWithAuto() {
  ApiGetAutoHandleDetail({
    handle: props.id,
  }).then((response) => {
    detail.value = response || {};
    detail.value.items = response?.basicsItems || [];
  });
}

// 基础属性详情按钮
function handleViewReference(data: any) {
  handleItemData.value = data;
  handleItemDrawerVisible.value = true;
}

// 获取应用列表接口
function getAppList() {
  appList().then((response: any) => {
    appListResponse.value = Array.isArray(response) ? response : [];
  });
}

function handleClose() {
  emit("close");
}

onMounted(() => {
  getAppList();
  if (props.source === HANDLE_SOURCE.MAINTAIN) {
    getHandleDetailWithMaintain();
    activeName.value = "extend";
    return;
  }
  if (props.source === HANDLE_SOURCE.AUTO) {
    getHandleDetailWithAuto();
    return;
  }
  getHandleDetail();
});
</script>
<style lang="scss" scoped>
.el-divider {
  margin: 8px 0 24px 0;
}
:deep(.el-descriptions__label) {
  width: 72px;
  min-width: 72px;
  display: inline-block;
}
:deep(.el-tabs__nav-wrap::after) {
  content: none;
}
:deep(.el-tabs__active-bar) {
  display: none;
}
:deep(.el-tabs__item) {
  height: auto;
  line-height: normal;
}
:deep(.el-tabs--top .el-tabs__item.is-top:nth-child(2)) {
  border-right: 2px solid #c1c9c7;
}

.yc-description-basic {
  width: 1136px;
}

:deep(.el-descriptions__content) {
  display: inline-block;
  word-wrap: word-break;
  word-break: break-all;
  width: 1040px;
  max-width: 1040px;
  min-width: 1040px;
  display: inline-block;
  vertical-align: middle;
}
</style>
