<template>
  <div class="title-bar">
    <div class="title-bar-container">
      <div class="title-bar-title">
        <h2>{{ data.title }}</h2>
        <a
          v-if="data.subhead || (data.textList && data.textList.length)"
          href="javascript:void(0)"
          title="点击可查看详细帮助"
          @click="showTopTip = !showTopTip"
        ></a>
        <el-button
          v-if="needBack"
          class="back"
          type="text"
          icon="Back"
          size="large"
          @click="router.back()"
          >返回</el-button
        >
      </div>
      <el-collapse-transition>
        <div class="title-bar-content" v-if="showTopTip">
          <div class="transition-box">
            <div class="title-bar-text" v-if="data.subhead">
              <strong>{{ data.subhead }}</strong>
            </div>
            <div
              class="title-bar-text"
              v-for="(item, index) in data.textList"
              :key="index"
            >
              {{ item }}
            </div>
          </div>
        </div>
      </el-collapse-transition>
      <div class="title-bar-line" v-if="!showTopTip && hasSearch"></div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();
defineProps({
  data: {
    type: Object,
    default: () => {
      return {
        title: "",
        subhead: "",
        textList: [],
      };
    },
  },
  needBack: {
    type: Boolean,
    default: false,
  },
  hasSearch: {
    type: Boolean,
    default: true,
  },
});
const showTopTip = ref(false);
</script>
<style lang="scss" scoped>
.title-bar {
  .title-bar-container {
    background: #fff;

    .title-bar-title {
      height: 0.6rem;
      color: #333;
      font-size: 0.14rem;
      font-weight: 700;

      h2 {
        font-size: 0.2rem;
        margin-top: 0.2rem;
        display: inline-block;
        font-weight: 700;
      }

      a {
        display: inline-block;
        vertical-align: middle;
        width: 0.2rem;
        height: 0.2rem;
        background: url(../../assets/images/titleBar/icon-tips.png) no-repeat;
        background-size: 100%;
        margin: -2px 0 0 0.08rem;
      }

      .back {
        float: right;
        font-size: 0.12rem;
        color: #333;
        height: 0.6rem;
        line-height: 0.6rem;
        cursor: pointer;
      }
    }

    .title-bar-content {
      padding-bottom: 0.1rem;
      .transition-box {
        padding: 0.1rem;
        background-color: rgba(0, 111, 209, 0.05);
        border: 1px solid rgba(0, 111, 209, 0.2);
        color: #075090;

        .title-bar-text {
          line-height: 1.5;
          font-size: 0.12rem;
          &.subtitle {
            line-height: 0.3rem;
            font-size: 0.16rem;
          }
        }
      }
    }
    .title-bar-line {
      width: 100%;
      height: 1px;
      background-color: #dedede;
    }
  }
}
</style>
