<template>
  <el-drawer
    custom-class="handle-detail-drawer"
    v-model="drawerVisible"
    title="标识详情"
    v-loading="loading"
    @closed="handleClose"
  >
    <div class="yc-description-basic">
      <div class="yc-description-title">
        <div class="yc-description-title-tip"></div>
        <div class="yc-description-title-text">基本信息</div>
      </div>

      <el-row>
        <el-col :span="8">
          <el-descriptions :column="1" direction="horizontal">
            <el-descriptions-item label="标识类型">
              {{ getHandleType(form.handleType) }}</el-descriptions-item
            >
            <el-descriptions-item label="实体类型">
              {{ getEntityType(form.entityType) }}</el-descriptions-item
            >
            <el-descriptions-item label="实体对象">{{
              form.entityObjName || "-"
            }}</el-descriptions-item>
          </el-descriptions>
        </el-col>
        <el-col :span="8">
          <el-descriptions :column="1" direction="horizontal">
            <el-descriptions-item label="主数据名称">
              {{ form.masterDataName || "-" }}</el-descriptions-item
            >
            <el-descriptions-item label="实体对象类型">
              {{ form.entityObjTypeName || "-" }}</el-descriptions-item
            >
          </el-descriptions>
        </el-col>
      </el-row>
    </div>
    <el-divider />
    <div class="yc-description">
      <div class="yc-description-title">
        <div class="yc-description-title-tip"></div>
        <div class="yc-description-title-text">标识列表</div>
      </div>
      <div class="page-search-body">
        <el-table
          :data="data.tableData"
          v-loading="data.tableLoading"
          border
          size="small"
        >
          <el-table-column label="序号" type="index" width="55" />
          <el-table-column property="name" label="对象标识名称">
            <template #default="scope">
              <ellipsisText :value="scope.row.name"></ellipsisText>
            </template>
          </el-table-column>
          <el-table-column property="handle" label="对象标识">
            <template #default="scope">
              <ellipsisText :value="scope.row.handle"></ellipsisText>
            </template>
          </el-table-column>

          <el-table-column property="appName" label="所属应用">
            <template #default="scope">
              <ellipsisText :value="scope.row.appName"></ellipsisText>
            </template>
          </el-table-column>

          <el-table-column property="updatedTime" label="操作时间" />

          <el-table-column label="操作" :width="isAppRole ? 200 : 100">
            <template #default="scope">
              <el-button
                type="primary"
                text
                @click="showDetailView(scope.row)"
                v-permission="[
                  AUTH_CODE.HANDLE_REGISTER_DETAIL,
                  AUTH_CODE.HANDLE_WATCH_DETAIL,
                ]"
                >详情</el-button
              >

              <el-popconfirm
                v-if="isAppRole"
                :width="200"
                confirm-button-text="确定"
                cancel-button-text="取消"
                title="删除标识会导致标识解析失败，请谨慎删除，是否删除该标识？"
                @confirm="handleDelete(scope.row)"
              >
                <template #reference v-if="isAppRole">
                  <el-button
                    text
                    type="primary"
                    v-permission="AUTH_CODE.HANDLE_REGISTER_DELETE"
                    >删除</el-button
                  >
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          background
          v-model:currentPage="data.page"
          v-model:page-size="data.size"
          :page-sizes="[10, 20, 30, 40]"
          small
          layout="total, sizes, prev, pager, next, jumper"
          :total="data.totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </el-drawer>
  <DetailDrawer
    v-if="detailDrawerVisible"
    :id="data.selectHandleData.id"
    :is-master-data="true"
    @close="detailDrawerVisible = false"
  ></DetailDrawer>
</template>

<script lang="ts" setup>
import { onMounted, ref, computed, reactive } from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
import { AUTH_CODE } from "@/utils/authCode";
import { ROLE_TYPE } from "@/utils/constant";
import { IHandleItem } from "@/types/handle";
import {
  ApiGetMidHandleMainDataPage,
  ApiGetEntityTypeTree,
  ApiGetEntityObjList,
  ApiDeleteMidHandle,
} from "@/api/objHandle/manager";

import DetailDrawer from "@/components/MidHandleDetailDrawer/index.vue";
import ellipsisText from "@/components/ellipsisText/index.vue";

const detailDrawerVisible = ref(false); // 详情
const store = useStore();
const userInfo = computed(() => store.getters.userInfo);

const props = defineProps({
  id: {
    type: Number,
    default: -1,
  },
  source: {
    type: String,
  },
});
const emit = defineEmits(["close"]);

const getHandleType = (id: number | undefined) => {
  if (id === 1) return "主数据标识";
  if (id === 2) return "非主数据标识";
  return "-";
};
const getEntityType = (id: number | undefined) => {
  if (id === 1) return "业务实体";
  if (id === 2) return "资源实体";
  return "-";
};

const drawerVisible = ref(true);

const loading = ref(false);
// 实体对象类型树
const entityObjTypeTree = ref();

const isAppRole = computed(() =>
  userInfo.value.roleInfos.some((item: any) => item.roleType === ROLE_TYPE.APP)
);

interface Data {
  tableData: {
    appName: string;
    createdBy: string;
    createdTime: string;
    entityType: number;
    handle: string;
    id: number;
    name: string;
    type: number;
    reportName: string;
    updatedTime: string;
    uploadState: number;
  }[];
  appList: {
    id: number;
    appName: string;
  }[];
  page: number;
  size: number;
  totalCount: number;
  dialogTitle: string;
  dialogVisible: boolean;
  selectHandleData: any;
  dialogDisabled: boolean;
  deleteLoading: boolean;
  tableLoading: boolean;
  searchLoading: boolean;
  confirmLoading: boolean;
}

const data = reactive<Data>({
  tableData: [],
  appList: [],
  page: 1,
  size: 10,
  totalCount: 1,
  dialogTitle: "",
  dialogVisible: false,
  selectHandleData: null,
  dialogDisabled: false,
  deleteLoading: false,
  tableLoading: false,
  searchLoading: false,
  confirmLoading: false,
});

const form = ref({
  handleType: 0,
  masterDataName: "",
  entityType: 0,
  entityObjType: "",
  entityObj: "",
  entityObjName: "",
  entityObjTypeName: "",
});

// 详情按钮
function showDetailView(item: any) {
  data.selectHandleData = item;
  detailDrawerVisible.value = true;
}

async function getTableData() {
  data.tableLoading = true;
  data.tableData = [];
  const params = {
    id: props.id,
    page: data.page - 1,
    size: data.size,
  };

  await ApiGetMidHandleMainDataPage(params)
    .then((response: any) => {
      const result = response;
      const {
        entityObjectId,
        entityObjectTypeId,
        entityType,
        handleType,
        name,
        objectHandlePageVOS,
      } = response;
      form.value.handleType = handleType;
      form.value.masterDataName = name;
      form.value.entityType = entityType;
      form.value.entityObjType = entityObjectTypeId;
      form.value.entityObj = entityObjectId;
      data.tableData = objectHandlePageVOS?.content || [];
      data.page = objectHandlePageVOS?.pageable.pageNumber
        ? (objectHandlePageVOS?.pageable.pageNumber as any) + 1
        : 1;
      data.totalCount = objectHandlePageVOS?.totalElements || 0;
    })
    .finally(() => {
      data.tableLoading = false;
      data.searchLoading = false;
    });
}

const handleDelete = (data: any) => {
  data.tableLoading = true;
  ApiDeleteMidHandle(data.id)
    .then(() => {
      ElMessage.success("删除成功");
      getTableData();
    })
    .finally(() => {
      data.tableLoading = false;
    });
};
function handleSizeChange(num: number) {
  data.size = num;
  data.page = 1;
  getTableData();
}

function handleCurrentChange(num: number) {
  data.page = num;
  getTableData();
}

const appListResponse = ref<
  {
    id: number;
    appName: string;
  }[]
>([]);

const handleItemDrawerVisible = ref(false);

const handleItemData = ref<IHandleItem>();
// const appDisplay = computed(() => {
//   if (
//     props.source === HANDLE_SOURCE.MAINTAIN ||
//     props.source === HANDLE_SOURCE.AUTO
//   ) {
//     return detail.value?.appName || "-";
//   }
//   const appItemList = appListResponse.value.filter(
//     (item) => item.id === detail.value?.appId
//   );
//   if (appItemList && appItemList.length > 0) {
//     return appItemList[0].appName;
//   }
//   return "-";
// });

function getEntityTypeName(type: number | undefined) {
  if (`${type}` === `1`) return "业务实体";
  if (`${type}` === `2`) return "资源实体";
  return "-";
}

// 获取标识详情接口
// function getHandleDetail() {
//   const requestParams = {
//     handle: props.id,
//     type: 1, // 前端写死1，获取所有详情
//   };
// }

// 基础属性详情按钮
// function handleViewReference(data: any) {
//   handleItemData.value = data;
//   handleItemDrawerVisible.value = true;
// }

function handleClose() {
  emit("close");
}

// 递归查找
function findPathById(node: any, targetId: string): string {
  console.log(node, targetId);
  // 递归遍历树结构并构建路径
  function findInTree(node: any, path: string[] = []): string {
    path.push(node.categoryName); // 将当前节点的 name 添加到路径中
    console.log("findInTree", path);
    if (node.id === targetId) {
      return path.join("/"); // 如果找到目标节点，返回路径
    }

    if (node.children && node.children.length > 0) {
      // 继续遍历子节点
      for (const child of node.children) {
        const result = findInTree(child, path.slice()); // 使用副本，以避免影响其他路径
        if (result) {
          return result; // 如果找到匹配，立即返回结果
        }
      }
    }

    return ""; // 没有找到匹配的节点
  }
  return findInTree(node, []);
}

// 解析获得实体对象类型和实体对象字段
async function getBaseInfo(entityObjTypeId: string, entityObjId: string) {
  ApiGetEntityTypeTree().then(async (res: any) => {
    console.log("tree", res);
    form.value.entityObjTypeName = findPathById(res, entityObjTypeId);
    console.log("form.entityObjTypeName", form.value.entityObjTypeName);
    // entityObjTypeTree.value = res;
    //
    if (entityObjTypeId) {
      await ApiGetEntityObjList({
        categoryId: entityObjTypeId,
      }).then((ObjectRes: any) => {
        const foundItem = ObjectRes.find(
          (item: any) => item.id === entityObjId
        );
        form.value.entityObjName = foundItem?.name ?? "";
      });
    }
  });
}

onMounted(async () => {
  await getTableData();
  await getBaseInfo(form.value.entityObjType, form.value.entityObj);
});
</script>
<style lang="scss" scoped>
:deep(.el-descriptions__cell) {
  display: flex;
}
.el-divider {
  margin: 8px 0 24px 0;
}
:deep(.el-descriptions__label) {
  width: 72px;
  min-width: 72px;
  display: inline-block;
}
:deep(.el-tabs__nav-wrap::after) {
  content: none;
}
:deep(.el-tabs__active-bar) {
  display: none;
}
:deep(.el-tabs__item) {
  height: auto;
  line-height: normal;
}
:deep(.el-tabs--top .el-tabs__item.is-top:nth-child(2)) {
  border-right: 2px solid #c1c9c7;
}

.yc-description-basic {
  width: 1136px;
}

:deep(.el-descriptions__content) {
  word-wrap: word-break;
  word-break: break-all;
  display: inline-block;
  vertical-align: middle;
}
</style>
