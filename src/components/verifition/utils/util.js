export function resetSize(vm) {
  let imgWidth;
  let imgHeight;
  let barWidth;
  let barHeight; // 图片的宽度、高度，移动条的宽度、高度

  const parentWidth = vm.$el.parentNode.offsetWidth || window.offsetWidth;
  const parentHeight = vm.$el.parentNode.offsetHeight || window.offsetHeight;

  if (vm.imgSize.width.indexOf("%") !== -1) {
    imgWidth = `${(parseInt(this.imgSize.width, 10) / 100) * parentWidth}px`;
  } else {
    imgWidth = this.imgSize.width;
  }

  if (vm.imgSize.height.indexOf("%") !== -1) {
    imgHeight = `${(parseInt(this.imgSize.height, 10) / 100) * parentHeight}px`;
  } else {
    imgHeight = this.imgSize.height;
  }

  if (vm.barSize.width.indexOf("%") !== -1) {
    barWidth = `${(parseInt(this.barSize.width, 10) / 100) * parentWidth}px`;
  } else {
    barWidth = this.barSize.width;
  }

  if (vm.barSize.height.indexOf("%") !== -1) {
    barHeight = `${(parseInt(this.barSize.height, 10) / 100) * parentHeight}px`;
  } else {
    barHeight = this.barSize.height;
  }

  return {
    imgWidth,
    imgHeight,
    barWidth,
    barHeight,
  };
}
