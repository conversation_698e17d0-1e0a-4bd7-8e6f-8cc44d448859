import { DIALOG_TYPE_MAP } from "@/utils/constant.ts";

interface IParams {
  type: any;
  oldVal: any;
  newVal: any;
  list: any[];
}

export const isDuplicateValidate = (params: IParams) => {
  const { type, oldVal, newVal, list } = params;
  // 编辑的时候，如果值和编辑之前一致，不做重复校验
  if (type === DIALOG_TYPE_MAP.editor && newVal === oldVal) {
    return false;
  }

  if (list.includes(newVal)) {
    return true;
  }

  return false;
};
