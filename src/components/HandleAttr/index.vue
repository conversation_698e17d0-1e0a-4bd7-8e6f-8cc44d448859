<template>
  <div>
    <div class="addbtn">
      <el-button
        type="primary"
        :disabled="form.handle === '' || form.entPrefix === ''"
        @click="handleAdd"
        >新增{{
          source === HANDLE_SOURCE.MAINTAIN ? "扩展" : "基础"
        }}属性</el-button
      >
    </div>
    <el-table :data="attrData.tableData" size="small" border>
      <!-- <el-table-column label="索引" property="fieldIndex">
        <template #default="scope">
          <span>{{
            scope.row.fieldIndex < 0 ? "-" : scope.row.fieldIndex
          }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="中文名称" property="description">
        <template #default="scope">
          <ellipsisText :value="scope.row.description"></ellipsisText>
        </template>
      </el-table-column>
      <el-table-column label="英文名称" property="field">
        <template #default="scope">
          <ellipsisText :value="scope.row.field"></ellipsisText>
        </template>
      </el-table-column>
      <el-table-column label="属性类型" property="length">
        <template #default="scope">
          <div>{{ FIELD_TYPE_NAME_MAP[scope.row.fieldType] }}</div>
        </template>
      </el-table-column>
      <el-table-column label="属性值">
        <template #default="scope">
          <ellipsisText :value="getReferenceDis(scope.row)"></ellipsisText>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="140">
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            text
            :disabled="isMaintain && !scope.row.editFlag"
            @click="handleEdit(scope.$index, scope.row)"
            >编辑</el-button
          >
          <el-popconfirm
            confirm-button-text="删除"
            cancel-button-text="取消"
            :icon="InfoFilled"
            title="是否删除该基础属性?"
            @confirm="handleDelete(scope.$index)"
          >
            <template #reference>
              <el-button
                size="small"
                type="primary"
                text
                :disabled="isMaintain && !scope.row.editFlag"
                >删除</el-button
              >
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      v-model="attrData.dialogVisible"
      :title="dialogTitle"
      append-to-body
      @close="handleDialogCancel"
      width="1200px"
      align-center
    >
      <DetailForm
        v-if="attrData.dialogVisible"
        ref="attrDetailRef"
        :source="source"
        :dialogType="attrData.dialogType"
      ></DetailForm>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            v-if="attrData.dialogType !== DIALOG_TYPE_MAP.editor"
            :disabled="currentFieldType === FIELD_TYPE_MAP.source"
            type="primary"
            @click="handleDialogConfirm('addNext')"
          >
            新增下一个
          </el-button>
          <el-button @click="attrData.dialogVisible = false">{{
            "取消"
          }}</el-button>
          <el-button type="primary" @click="handleDialogConfirm">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import {
  reactive,
  computed,
  ref,
  PropType,
  watch,
  onMounted,
  provide,
} from "vue";
import { InfoFilled } from "@element-plus/icons-vue";
import DetailForm from "./detailForm/index.vue";
import { deepClone } from "@/utils/auth";
import {
  FIELD_TYPE_MAP,
  DIALOG_TYPE_MAP,
  DIALOG_TYPE_NAME_MAP,
  FIELD_TYPE_NAME_MAP,
  HANDLE_SOURCE,
} from "@/utils/constant";
import ellipsisText from "@/components/ellipsisText/index.vue";

const emit = defineEmits(["del"]);

const attrDetailRef = ref();

// 获取属性编辑弹窗的fieldType
const currentFieldType = computed(() => {
  return attrDetailRef.value?.getFieldType() || FIELD_TYPE_MAP.fixed;
});

// 当前基础属性所有值
const currentTableData = computed(() => {
  return attrData.tableData;
});

const handleSource = computed(() => {
  return props.source;
});

// 当前操作的属性
const currentOperatorAttr = ref();

provide("handleAttrNewTableData", currentTableData);

provide("basicCurrentOperatorAttr", currentOperatorAttr);

provide("handleSource", handleSource);

provide("handleSource", handleSource);

interface DataService {
  id: number;
  dataServiceName: string;
  serviceAddress: string;
  serviceToken: string;
}

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  form: {
    type: Object,
    default: () => ({}),
  },
  source: {
    type: String,
  },
});

const attrData = reactive<any>({
  tableData: [{}],
  referenceIndex: 0,
  dialogVisible: false,
  dialogType: DIALOG_TYPE_MAP.add,
  form: {},
});

// 记录是否是维护
const isMaintain = computed(() => {
  return handleSource.value === HANDLE_SOURCE.MAINTAIN;
});

const currentHandle = computed(() => {
  if (isMaintain.value) return props.form.handle;
  return `${props.form.entPrefix}/${props.form.handle}`;
});

provide("currentHandle", currentHandle);

watch(
  props,
  (val) => {
    attrData.tableData = val.data.tableData ? val.data.tableData : [{}];
  },
  {
    immediate: true,
    deep: true,
  }
);

// 弹窗标题命名
const dialogTitle = computed(() => {
  const prefix =
    attrData.dialogType === DIALOG_TYPE_MAP.addNext
      ? DIALOG_TYPE_NAME_MAP[DIALOG_TYPE_MAP.add]
      : DIALOG_TYPE_NAME_MAP[attrData.dialogType];
  const middle = props.source === HANDLE_SOURCE.MAINTAIN ? "扩展" : "基础";
  // 当新增下一个模式，标题显示新增
  return `${prefix}${middle}属性`;
});

// 增加基础属性
function handleAdd() {
  currentOperatorAttr.value = null;
  attrData.form = deepClone(props.form);
  attrData.dialogType = DIALOG_TYPE_MAP.add;
  attrData.dialogVisible = true;
}

// 编辑基础属性
function handleEdit(index: any, reference: any) {
  currentOperatorAttr.value = JSON.parse(
    JSON.stringify(attrData.tableData[index])
  );
  attrData.dialogType = DIALOG_TYPE_MAP.editor;
  attrData.referenceIndex = index;
  attrData.form = deepClone(props.form);
  attrData.dialogVisible = true;
}

// 删除基础属性
function handleDelete(index: number) {
  // 记录删除属性
  if (attrData.tableData[index].fieldIndex > 0) {
    emit("del", attrData.tableData[index]);
  }
  attrData.tableData.splice(index, 1);
}

// 取消新增/编辑关联属性
function handleDialogCancel() {
  attrData.dialogVisible = false;
}

const addNextFn = () => {
  attrData.dialogType = DIALOG_TYPE_MAP.add;
  currentOperatorAttr.value = null;
  attrDetailRef.value.resetForm();
};

// 保存属性/新增下一个
async function handleDialogConfirm(addNext: string) {
  const validateError = await attrDetailRef.value.validateData();
  if (validateError) return;
  const formData = attrDetailRef.value.getData();
  console.log("formData:", formData);

  if (addNext === "addNext") {
    addNextFn();
  } else {
    attrData.dialogVisible = false;
  }

  // 修改模式，更新列表对应行
  if (attrData.dialogType === DIALOG_TYPE_MAP.editor) {
    const newData = deepClone(formData);
    if (isMaintain.value) {
      // 标识维护编辑，如果英文名称改了，需要删除该数据,再新增一条数据
      const odlData = deepClone(attrData.tableData[attrData.referenceIndex]);
      if (odlData.field !== newData.field) {
        emit("del", odlData);
      }
      newData.fieldIndex = -1;
      attrData.tableData.splice(attrData.referenceIndex, 1); // 先删除
      attrData.tableData[attrData.tableData.length] = newData; // 再新增
      return;
    }
    attrData.tableData[attrData.referenceIndex] = newData;

    return;
  }
  // 设定新增标识索引为负数时间戳，防止重复
  formData.fieldIndex = -Date.parse(new Date().toString());
  if (formData.fieldType === FIELD_TYPE_MAP.source) {
    const { fieldType, fieldList } = formData;
    fieldList.forEach((formDataItem: any) => {
      attrData.tableData.push({ fieldType, ...formDataItem });
    });

    return;
  }
  // 新增、新增下一个模式，列表插入一行;
  attrData.tableData[attrData.tableData.length] = deepClone(formData);
}

const getData = () => {
  return deepClone(attrData.tableData);
};

function getReferenceDis(data: any) {
  if (data.fieldType === FIELD_TYPE_MAP.fixed) {
    return data.fieldValue;
  }
  if (data.fieldType === FIELD_TYPE_MAP.source) {
    return typeof data.fieldValue === "string" && data.fieldValue
      ? data.fieldValue
      : "-";
  }
  if (data.references && data.references.length) {
    return data.references
      .map((reference: any) => reference.referenceHandle)
      .join(",");
  }
  return "-";
}

onMounted(() => {
  console.log(
    "source:",
    props.source,
    HANDLE_SOURCE.MAINTAIN,
    props.source === HANDLE_SOURCE.MAINTAIN
  );
});

defineExpose({
  getData,
});
</script>
<style>
.addbtn {
  text-align: right;
  margin-bottom: 10px;
}
</style>
