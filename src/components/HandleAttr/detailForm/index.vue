<template>
  <el-form
    ref="formRef"
    label-position="left"
    :model="form"
    label-width="100px"
    width="1200"
  >
    <el-form-item label="属性类型" required>
      <el-radio-group v-model="form.fieldType" :disabled="!!isEdit">
        <el-radio
          v-for="item in fieldTypeList"
          :key="item.value"
          :label="item.value"
          >{{ item.name }}</el-radio
        >
      </el-radio-group>
    </el-form-item>
    <el-row v-if="form.fieldType === FIELD_TYPE_MAP.fixed">
      <FixedValue ref="fixedValueRef" :type="dialogType"></FixedValue>
    </el-row>
    <!-- 标识解析数据源 -->
    <el-row v-if="form.fieldType === FIELD_TYPE_MAP.source">
      <HandleResolveSource
        v-if="!isEdit"
        ref="handleResolveSourceRef"
        :type="dialogType"
      ></HandleResolveSource>
      <handleResolveSourceEditor
        ref="handleResolveSourceEditorRef"
        :type="dialogType"
        v-if="isEdit"
      ></handleResolveSourceEditor>
    </el-row>
    <el-row v-if="form.fieldType === FIELD_TYPE_MAP.handleValue">
      <RelateHandle
        ref="relateHandleRef"
        @resolve="resolveHandle"
        :type="dialogType"
      ></RelateHandle>
    </el-row>
    <el-row v-if="form.fieldType === FIELD_TYPE_MAP.handleWithAttr">
      <RelateHandleAttr
        ref="relateHandleAttrRef"
        @resolve="resolveHandle"
        :type="dialogType"
      ></RelateHandleAttr>
    </el-row>
  </el-form>
  <el-dialog
    v-model="data.dialogResolve"
    title="解析结果"
    append-to-body
    width="800"
    @close="data.dialogResolve = false"
  >
    <div class="relate-handle-matrix-graph">
      <matrix-graph ref="matrixGraphRef"></matrix-graph>
    </div>
    <template #footer>
      <el-button type="primary" @click="data.dialogResolve = false">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import {
  reactive,
  watch,
  ref,
  nextTick,
  PropType,
  onMounted,
  inject,
  computed,
} from "vue";
import { ElMessage } from "element-plus";
import { idResolve } from "@/api/idRes/index";
import matrixGraph from "@/views/idRes/components/matrix-graph.vue";
import {
  FIELD_TYPE_MAP,
  FIELD_TYPE_LIST,
  FIELD_TYPE_LIST_M,
  DIALOG_TYPE_MAP,
  HANDLE_SOURCE,
} from "@/utils/constant";

import HandleResolveSource from "../components/handleResolveSource.vue";
import handleResolveSourceEditor from "../components/handleResolveSourceEditor.vue";
import RelateHandle from "../components/relateHandle.vue";
import RelateHandleAttr from "../components/relateHandleAttr.vue";
import FixedValue from "../components/fixedValue.vue";

const basicCurrentOperatorAttr: any = inject("basicCurrentOperatorAttr");

const fixedValueRef = ref();
const handleResolveSourceRef = ref();
const handleResolveSourceEditorRef = ref();
const relateHandleRef = ref();
const relateHandleAttrRef = ref();

const formRef = ref();
const matrixGraphRef = ref();
const loading = ref(false);

const props = defineProps({
  dialogType: {
    type: Number,
  },
  source: {
    type: String,
  },
});

const isEdit = computed(() => props.dialogType === DIALOG_TYPE_MAP.editor);

interface DataSource {
  id: number;
  name: string;
}

const fieldTypeList = computed(() => {
  return props.source === HANDLE_SOURCE.MAINTAIN
    ? FIELD_TYPE_LIST_M
    : FIELD_TYPE_LIST;
});
const data: {
  dialogResolve: boolean;
  dataSource: DataSource[];
} = reactive({
  dialogResolve: false,
  dataSource: [],
});

const form = reactive<any>({
  fieldType: FIELD_TYPE_MAP.fixed,
  data: null,
});

const getFieldType = () => {
  return form.fieldType;
};

function resolveHandle(id: any) {
  if (id) {
    idResolve({ handle: id }).then((response: any) => {
      data.dialogResolve = true;
      nextTick(() => {
        matrixGraphRef.value.refresh(response);
      });
    });
  } else {
    ElMessage({
      message: "请输入标识",
      type: "error",
    });
  }
}

async function validateData() {
  const { fieldType } = form;
  let isError = false;
  // 固定值校验
  if (fieldType === FIELD_TYPE_MAP.fixed) {
    isError = await fixedValueRef.value.formValidate();
  }

  // 标识解析数据源校验
  if (fieldType === FIELD_TYPE_MAP.source) {
    if (isEdit.value) {
      isError = await handleResolveSourceEditorRef.value.formValidate();
    } else {
      isError = await handleResolveSourceRef.value.formValidate();
    }
  }

  // 关联标识校验
  if (fieldType === FIELD_TYPE_MAP.handleValue) {
    isError = await relateHandleRef.value.formValidate();
  }

  // 关联属性校验
  if (fieldType === FIELD_TYPE_MAP.handleWithAttr) {
    isError = await relateHandleAttrRef.value.formValidate();
  }

  return isError;
}

function getData() {
  let tempData: any = {};
  if (form.fieldType === FIELD_TYPE_MAP.fixed) {
    tempData = fixedValueRef.value.getData();
    tempData.fieldType = form.fieldType;
    return tempData;
  }
  if (form.fieldType === FIELD_TYPE_MAP.source) {
    if (!isEdit.value) {
      tempData.fieldList = handleResolveSourceRef.value.getData();
      tempData.fieldType = form.fieldType;
      return tempData;
    }
    tempData = handleResolveSourceEditorRef.value.getData();
    tempData.fieldType = form.fieldType;
    return tempData;
  }
  if (form.fieldType === FIELD_TYPE_MAP.handleValue) {
    tempData = relateHandleRef.value.getData();
    tempData.fieldType = form.fieldType;
    return tempData;
  }
  if (form.fieldType === FIELD_TYPE_MAP.handleWithAttr) {
    tempData = relateHandleAttrRef.value.getData();
    tempData.fieldType = form.fieldType;
    return tempData;
  }
}

const resetForm = () => {
  if (form.fieldType === FIELD_TYPE_MAP.fixed) {
    fixedValueRef.value.resetForm();
  }
  if (form.fieldType === FIELD_TYPE_MAP.handleValue) {
    relateHandleRef.value.resetForm();
  }
  if (form.fieldType === FIELD_TYPE_MAP.handleWithAttr) {
    relateHandleAttrRef.value.resetForm();
  }
};

onMounted(() => {
  form.fieldType = FIELD_TYPE_MAP.handleValue;
  if (props.dialogType === DIALOG_TYPE_MAP.editor) {
    form.fieldType = basicCurrentOperatorAttr.value.fieldType;
  }
});

defineExpose({
  form,
  validateData,
  getData,
  resetForm,
  getFieldType,
});
</script>
<style lang="scss" scoped>
.el-table {
  .is-error {
    height: 70px;
    :deep(.el-form-item__error) {
      top: 50px;
    }
  }
}

.mb10 {
  margin-bottom: 10px;
}
.mb11 {
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 5px;
  color: #929292;
}
.mb12 {
  float: right;
}
.mb13 {
  margin-top: 5px;
  letter-spacing: 0.5px;
  // text-align: center;
  margin: 0 30px;
  > div {
    display: flex;
    align-items: baseline;
    span {
      flex: 1;
      &:first-child {
        text-align: right;
      }
    }
  }
}
.mb14 {
  margin-left: 10px;
}
.mb15 {
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 8px;
  letter-spacing: 0.5px;
  // text-align: center;
  margin: 40px 30px 10px;
  > div {
    display: flex;
    align-items: baseline;
    span {
      flex: 1;
      &:first-child {
        text-align: right;
      }
    }
  }
}
:deep(.el-select-dropdown__empty) {
  padding: 10px 10px !important;
}

:deep(.add-item-btn) {
  width: 100%;
  border: 1px solid #dfe4e3;
  border-top: 0;
  height: 44px;
  border-radius: 0;
  color: #1664ff;
  font-size: 12px;
  line-height: 20px;
  &:hover {
    color: #4086ff !important;
    background-color: transparent !important;
  }
}

.relate-handle-matrix-graph {
  height: 600px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
