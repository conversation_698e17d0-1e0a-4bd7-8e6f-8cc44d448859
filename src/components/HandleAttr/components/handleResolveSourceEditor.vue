<template>
  <el-form
    style="width: 100%"
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="100px"
  >
    <el-form-item label="数据通道" required>
      <el-select
        style="margin-right: 10px"
        v-model="form.dataServiceId"
        disabled
      >
        <el-option
          v-for="item in dataService"
          :key="item.id"
          :value="item.id"
          :label="item.dataServiceName"
        ></el-option>
      </el-select>
      <el-select v-model="form.dataSourceId" required disabled>
        <el-option
          v-for="item in dataChannels"
          :key="item.id"
          :value="item.id"
          :label="item.name"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="英文名称" required>
      <el-input v-model.trim="form.field" disabled clearable />
    </el-form-item>
    <el-form-item label="中文名称" prop="description">
      <el-input
        v-model.trim="form.description"
        clearable
        :validate-event="false"
      />
    </el-form-item>
    <el-form-item label="属性值">
      <HandleTag
        v-if="isTIDEnd(form.field)"
        v-model="form.fieldValue"
      ></HandleTag>
      <span v-else>-</span>
    </el-form-item>
    <el-form-item label="备注" prop="remark">
      <el-input v-model.trim="form.remark" clearable :validate-event="false" />
    </el-form-item>
  </el-form>
</template>
<script setup lang="ts">
import { reactive, ref, inject, computed, onMounted } from "vue";
import { DIALOG_TYPE_MAP } from "@/utils/constant";
import {
  getServiceListApi,
  getDataSourceFieldListApi,
  getDataSourceNewApi,
} from "@/api/objHandle/manager";
import HandleTag from "./handleTag.vue";
import { isDuplicateValidate } from "../common";

const formRef = ref();

const dataService = ref();

const basicAttrTableData: any = inject("handleAttrNewTableData");

const basicCurrentOperatorAttr: any = inject("basicCurrentOperatorAttr");

const handleData: any = inject("handleData");

const dataChannels = ref();

// 判断是否已tid结尾
const isTIDEnd = (data: string) => {
  return data.toLocaleLowerCase().endsWith("_tid");
};

const basicAttrFields = computed(() => {
  return basicAttrTableData.value.map((item: any) => item.field);
});

const basicAttrDescriptions = computed(() => {
  return basicAttrTableData.value.map((item: any) => item.description);
});

const getDataService = async () => {
  return getServiceListApi().then((res: any) => {
    if (res.code) {
      dataService.value = [];
    } else {
      dataService.value = res;
    }
  });
};

const getDataSource = () => {
  const handle = `${handleData.entPrefix}/${handleData.handle}`;
  getDataSourceNewApi({ dataServiceId: form.dataServiceId, handle }).then(
    (res: any) => {
      dataChannels.value = res;
    }
  );
};

const descriptionValidate = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback("请填写中文名称");
  }
  if (value.length > 255) {
    return callback("最大长度为255字符");
  }
  const params = {
    type: props.type,
    oldVal: basicCurrentOperatorAttr.value?.description,
    newVal: value,
    list: basicAttrDescriptions.value,
  };
  if (isDuplicateValidate(params)) {
    return callback("中文名称已存在，请重新填写");
  }

  callback();
};

const rules = reactive({
  description: [{ required: true, validator: descriptionValidate }],
  remark: [
    {
      required: false,
      max: 100,
      message: "最大长度为100字符",
    },
  ],
});

const props = defineProps({
  type: {
    type: Number,
  },
});

const form = reactive<any>({
  field: "",
  description: "",
  remark: "",
  fieldValue: "",
  dataServiceId: null,
  dataServiceName: "",
  dataSourceId: null,
  dataSourceName: "",
  dataChannelType: "",
  editFlag: true, // 标识维护需要该字段，默认是true
});

const formValidate = async () => {
  let isError = false;
  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  return isError;
};

const getData = () => {
  const result = { ...form };
  result.fieldValue =
    form.fieldValue && Array.isArray(form.fieldValue)
      ? JSON.stringify(form.fieldValue)
      : "";
  result.fieldIndex = basicCurrentOperatorAttr.value.fieldIndex;
  return {
    ...result,
  };
};

onMounted(async () => {
  if (basicCurrentOperatorAttr.value) {
    const {
      field,
      description,
      dataServiceId,
      dataSourceId,
      remark,
      fieldValue,
      dataChannelType,
      dataServiceName,
      dataSourceName,
      editFlag,
    } = basicCurrentOperatorAttr.value;
    form.field = field;
    form.dataServiceId = Number(dataServiceId);
    form.dataServiceName = dataServiceName;
    form.dataSourceName = dataSourceName;
    form.dataSourceId = Number(dataSourceId);
    form.description = description;
    form.dataChannelType = dataChannelType;
    form.remark = remark;
    form.editFlag = editFlag;
    form.fieldValue =
      fieldValue && typeof fieldValue === "string"
        ? JSON.parse(fieldValue)
        : "";
  }

  // 获取数据服务
  await getDataService();
  // 获取数据通道
  getDataSource();
});

defineExpose({
  formValidate,
  getData,
});
</script>
