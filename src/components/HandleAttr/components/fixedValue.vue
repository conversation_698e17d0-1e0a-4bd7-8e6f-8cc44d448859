<template>
  <el-form
    style="width: 100%"
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="100px"
  >
    <el-form-item label="英文名称" prop="field">
      <el-input v-model.trim="form.field" :validate-event="false" clearable />
    </el-form-item>
    <el-form-item label="中文名称" prop="description">
      <el-input
        v-model.trim="form.description"
        :validate-event="false"
        clearable
      />
    </el-form-item>
    <el-form-item label="属性值" prop="fieldValue">
      <el-input
        v-model.trim="form.fieldValue"
        :validate-event="false"
        clearable
      />
    </el-form-item>
    <el-form-item label="备注" prop="remark">
      <el-input v-model.trim="form.remark" clearable />
    </el-form-item>
  </el-form>
</template>
<script setup lang="ts">
import { reactive, ref, inject, computed, onMounted } from "vue";
import { DIALOG_TYPE_MAP } from "@/utils/constant";
import { isDuplicateValidate } from "../common";

const formRef = ref();

const basicAttrTableData: any = inject("handleAttrNewTableData");

const basicCurrentOperatorAttr: any = inject("basicCurrentOperatorAttr");

const basicAttrFields = computed(() => {
  return basicAttrTableData.value.map((item: any) => item.field);
});

const basicAttrDescriptions = computed(() => {
  return basicAttrTableData.value.map((item: any) => item.description);
});

const fieldValidate = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback("请填写英文名称");
  }
  if (value.length > 255) {
    return callback("最大长度为255字符");
  }
  const reg = /^[a-zA-Z\d_]+$/;
  if (!reg.test(value)) {
    return callback("英文名称格式错误，请重新填写");
  }
  const params = {
    type: props.type,
    oldVal: basicCurrentOperatorAttr.value?.field,
    newVal: value,
    list: basicAttrFields.value,
  };
  if (isDuplicateValidate(params)) {
    return callback("英文名称已存在，请重新填写");
  }

  callback();
};

const descriptionValidate = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback("请填写中文名称");
  }
  if (value.length > 255) {
    return callback("最大长度为255字符");
  }
  const params = {
    type: props.type,
    oldVal: basicCurrentOperatorAttr.value?.description,
    newVal: value,
    list: basicAttrDescriptions.value,
  };
  if (isDuplicateValidate(params)) {
    return callback("中文名称已存在，请重新填写");
  }

  callback();
};

const rules = reactive({
  field: [{ required: true, validator: fieldValidate }],
  description: [{ required: true, validator: descriptionValidate }],
  remark: [
    {
      required: false,
      max: 100,
      message: "最大长度为100字符",
      trigger: "blur",
    },
  ],
  fieldValue: [
    { required: true, message: "请填写属性值" },
    { max: 255, message: "最大长度为255字符" },
  ],
});

const props = defineProps({
  type: {
    type: Number,
  },
});

const form = reactive<any>({
  field: "",
  description: "",
  remark: "",
  fieldValue: "",
  editFlag: true, // 标识维护需要该字段，默认是true
});

const formValidate = async () => {
  let isError = false;
  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  return isError;
};

const getData = () => {
  if (props.type === DIALOG_TYPE_MAP.add) {
    return { fieldIndex: -1, ...form };
  }
  return {
    fieldIndex: basicCurrentOperatorAttr.value.fieldIndex,
    ...form,
  };
};

const resetForm = () => {
  form.field = "";
  form.description = "";
  form.remark = "";
  form.fieldValue = "";
  formRef.value.resetFields();
};

onMounted(() => {
  if (basicCurrentOperatorAttr.value) {
    form.field = basicCurrentOperatorAttr.value.field;
    form.description = basicCurrentOperatorAttr.value.description;
    form.remark = basicCurrentOperatorAttr.value.remark;
    form.fieldValue = basicCurrentOperatorAttr.value.fieldValue;
    form.editFlag = basicCurrentOperatorAttr.value.editFlag;
  }
});

defineExpose({
  formValidate,
  getData,
  resetForm,
});
</script>
