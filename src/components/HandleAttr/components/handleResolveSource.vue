<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="100px"
    v-loading="loading"
    style="width: 100%"
  >
    <el-form-item label="数据服务" prop="dataService">
      <el-select
        v-model="form.dataService"
        placeholder="请选择数据服务"
        @change="getDataSourceChange"
      >
        <el-option
          v-for="item in dataService"
          :key="item.id"
          :value="item.id"
          :label="item.dataServiceName"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item v-if="form.dataService">
      <div class="data-channels">
        <div class="data-channels-left">
          <div class="data-channels-left-header">可选数据通道</div>
          <div class="data-channels-left-body">
            <el-tree
              ref="treeRef"
              :data="dataChannels"
              :props="defaultProps"
              :load="loadNode"
              node-key="id"
              lazy
              show-checkbox
              @check="handleCheck"
            >
              <template #default="{ node, data }">
                <span class="custom-tree-node">
                  <div style="width: 100px; display: inline-block">
                    <ellipsisText :value="node.label"></ellipsisText>
                  </div>

                  <el-tag v-if="data.dataChannelType === 2 && !data.field"
                    >数组</el-tag
                  >
                </span>
              </template></el-tree
            >
          </div>
        </div>

        <div class="data-channels-right">
          <div class="data-channels-right-header">
            <div class="">已选择项</div>
            <div
              class="delete-wrap"
              :class="{ 'is-disabled': !form.fieldList.length }"
              @click="handleDelAll"
            >
              <el-icon>
                <Delete />
              </el-icon>
            </div>
          </div>
          <div class="data-channels-right-body">
            <div
              class="data-channels-right-blank"
              v-show="!dataChannels.length || !form.fieldList.length"
            >
              <img src="@/assets/blank.svg" />
              <div
                class="blank-text"
                v-if="dataChannels.length && !form.fieldList.length"
              >
                请选择英文名称
              </div>
              <div class="blank-text" v-if="!dataChannels.length">
                标识未配置数据通道
              </div>
              <div class="blank-text" v-if="!dataChannels.length">
                请前往数据服务配置
              </div>
            </div>
            <el-table
              v-show="form.fieldList.length"
              :data="form.fieldList"
              size="small"
              height="394px"
              border
            >
              <el-table-column min-width="150" label="英文名称">
                <template #default="scope">
                  <div>{{ scope.row.field }}</div>
                </template>
              </el-table-column>
              <el-table-column min-width="200">
                <template #header>
                  <div class="table-required">
                    <span class="table-required-icon">*</span
                    ><span>中文名称</span>
                  </div>
                </template>
                <template #default="scope">
                  <el-form-item
                    inline-message
                    :prop="'fieldList.' + scope.$index + '.description'"
                    :rules="[
                      { required: true, validator: descriptionValidate },
                    ]"
                  >
                    <el-input
                      v-model.trim="scope.row.description"
                      placeholder="输入中文名称"
                      clearable
                      :validate-event="false"
                    ></el-input>
                  </el-form-item>
                </template>
              </el-table-column>

              <el-table-column min-width="300" label="属性值">
                <template #default="scope">
                  <HandleTag
                    v-if="isTIDEnd(scope.row.field)"
                    v-model="scope.row.fieldValue"
                  ></HandleTag>
                  <div v-else>-</div>
                </template>
              </el-table-column>
              <el-table-column min-width="250" label="备注">
                <template #default="scope">
                  <el-form-item
                    :prop="'fieldList.' + scope.$index + '.remark'"
                    :rules="[
                      {
                        required: false,
                        max: 100,
                        message: '最大长度为100字符',
                      },
                    ]"
                  >
                    <el-input
                      v-model.trim="scope.row.remark"
                      placeholder="输入备注"
                      clearable
                    ></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column min-width="50" label="操作">
                <template #default="scope">
                  <el-button
                    type="primary"
                    text
                    @click="handleDel(scope.$index)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted, inject, computed, reactive } from "vue";
import type Node from "element-plus/es/components/tree/src/model/node";
import { ElMessage } from "element-plus";
import {
  getServiceListApi,
  getDataSourceFieldListApi,
  getDataSourceNewApi,
} from "@/api/objHandle/manager";
import ellipsisText from "@/components/ellipsisText/index.vue";
import { DIALOG_TYPE_MAP } from "@/utils/constant";
import HandleTag from "./handleTag.vue";
import { isDuplicateValidate } from "../common";

const basicAttrTableData: any = inject("handleAttrNewTableData");

const basicCurrentOperatorAttr: any = inject("basicCurrentOperatorAttr");

const formRef = ref();

const loading = ref(false);

const basicAttrDescriptions = computed(() => {
  return basicAttrTableData.value.map((item: any) => item.description);
});

const basicAttrFields = computed(() => {
  return basicAttrTableData.value.map((item: any) => item.field);
});

const descriptionValidate = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback("请填写中文名称");
  }
  if (value.length > 255) {
    return callback("最大长度为255字符");
  }
  const params = {
    type: props.type,
    oldVal: basicCurrentOperatorAttr.value?.description,
    newVal: value,
    list: basicAttrDescriptions.value,
  };
  if (isDuplicateValidate(params)) {
    return callback("中文名称已存在，请重新填写");
  }

  callback();
};

const fieldValidate = (type: string, value: any) => {
  const { field, description } = basicCurrentOperatorAttr.value || {
    field: "",
    description: "",
  };
  const oldVal = type === "field" ? field : description;
  const list =
    type === "field" ? basicAttrFields.value : basicAttrDescriptions.value;
  const params = {
    type: props.type,
    oldVal,
    newVal: value,
    list,
  };
  if (isDuplicateValidate(params)) {
    return true;
  }
  return false;
};

// 判断是否已tid结尾
const isTIDEnd = (data: string) => {
  return data.toLocaleLowerCase().endsWith("_tid");
};

const dataSourceName = computed(() => {
  if (!form.dataService) return "";
  const matchData = dataService.value.filter(
    (item) => item.id === form.dataService
  );
  if (matchData?.length) {
    return matchData[0].dataServiceName;
  }
  return "";
});

interface DataService {
  id: number;
  dataServiceName: string;
  serviceAddress: string;
  serviceToken: string;
}

interface Tree {
  name: string;
  id: string;
  leaf?: boolean;
  field?: string; // 英文名
  description?: string; // 中文名称
  dataServiceId?: string | number | null; // 数据服务ID
  dataServiceName?: string; // 数据服务Name
  dataSourceType?: 1 | 2; // 2-数组
  dataSourceId?: string | number; // 数据通道ID
  dataSourceName?: string; // 数据通道名称
  remark?: string; //
}

const defaultProps = {
  children: "children",
  label: "name",
  disabled: "disabled",
  isLeaf: "leaf",
};

const handleData: any = inject("handleData");

const form = reactive<{ dataService: number | null | string; fieldList: any }>({
  dataService: null,
  fieldList: [],
});

const rules = {
  dataService: [
    {
      required: true,
      message: "请选择数据服务",
    },
  ],
};

const props = defineProps({
  type: {
    type: Number,
  },
});

const treeRef = ref();

// 数据服务
const dataService = ref<DataService[]>([]);

// 可选通道
const dataChannels = ref([]);

// 存储fieldData
const fieldIdMap = ref<any>({});

// 存储通道与字段的map
const channelFieldMap = computed(() => {
  const newObj = {};
  dataChannels.value.forEach((item: any) => {
    newObj[`${item.id}`] = true;
  });
  return newObj;
});

// 记录之前选中的nodes
const oldCheckedKeys = ref<any>([]);

const getTempId = (id: any, field: any) => {
  return `${id}::${field}`;
};

const loadNode = (node: Node, resolve: (data: Tree[]) => void) => {
  if (node.level === 0) {
    return resolve(dataChannels.value);
  }
  if (node.level >= 1) {
    getDataSourceFieldListApi({
      dataSourceId: node.data.id, // 数据通道ID
      dataServiceId: form.dataService,
    })
      .then((res) => {
        if (Array.isArray(res) && res.length) {
          const result = res.map((resItem) => {
            const tempId = getTempId(node.data.id, resItem.field);
            const tempResult = {
              ...resItem,
              leaf: true,
              name: resItem.field,
              id: tempId,
              description: "",
              dataServiceId: form.dataService,
              dataServiceName: dataSourceName.value,
              dataSourceId: node.data.id,
              dataSourceName: node.data.name,
              dataChannelType: node.data.dataChannelType,
            };
            if (!fieldIdMap.value[tempId]) {
              fieldIdMap.value[tempId] = tempResult;
            }

            return { ...tempResult };
          });
          dataChannels.value.forEach((item: any) => {
            if (item.id === node.data.id) {
              item.disabled = false; // 全选恢复为可选状态
            }
          });
          resolve(result);
        } else {
          resolve([]);
        }
      })
      .catch(() => {
        resolve([]);
      });
  }
};

const handleCheck = (currentNode: any, selectNodes: any) => {
  console.log("当前选中的节点:", currentNode);
  console.log("所有选中的值:", selectNodes);

  const { checkedKeys, checkedNodes } = selectNodes;
  // 判断当前选择的是渠道
  const isChannel = channelFieldMap.value[`${currentNode.id}`];
  if (isChannel) {
    const isSelectAll = checkedKeys.length > oldCheckedKeys.value.length;
    // 全选
    if (isSelectAll) {
      // 找出需要添加的值, 存的是key
      const addFields: any = [];
      // 记录已存在的值
      const existFields: any = [];
      // 找出能添加的值
      const ableFields: any = [];
      checkedKeys.forEach((checkedItem: any) => {
        if (!oldCheckedKeys.value.includes(checkedItem)) {
          addFields.push(checkedItem);
        }
      });
      addFields.forEach((fieldItem: any) => {
        const isExist = isExistFieldFn(fieldItem);
        if (!isExist) {
          const matchFieldItem = checkedNodes.filter(
            (checkItem) => checkItem.id === fieldItem
          );
          if (matchFieldItem[0].field) {
            ableFields.push(matchFieldItem[0]);
          }
        }
        if (isExist) {
          ElMessage.warning("字段已存在，不能重复添加");
          existFields.push(fieldItem);
        }
      });
      if (existFields.length) {
        // 不能全选
        existFields.forEach((existItem) => {
          const checkedIndex = selectNodes.checkedKeys.indexOf(existItem);
          selectNodes.checkedKeys.splice(checkedIndex, 1);
        });
        treeRef.value.setCheckedKeys(selectNodes.checkedKeys);
      }
      if (ableFields.length) {
        form.fieldList = form.fieldList.concat(ableFields);
      }
    }
    // 取消全选
    if (!isSelectAll) {
      // 找到需要删除的值
      const delFields: any = [];
      oldCheckedKeys.value.forEach((checkedItem: any) => {
        if (!checkedKeys.includes(checkedItem)) {
          delFields.push(checkedItem);
        }
      });
      const tempFields: any = [];
      form.fieldList.forEach((fieldItem) => {
        if (!delFields.includes(fieldItem.id)) {
          tempFields.push(fieldItem);
        }
      });
      form.fieldList = tempFields;
    }
  }
  if (!isChannel) {
    // 选中
    const isSelected = selectNodes.checkedKeys.includes(currentNode.id);
    if (!isSelected) {
      // 取消选择
      const index = getIndexInArray(form.fieldList, currentNode.id);
      form.fieldList.splice(index, 1);
    } else {
      // 选中
      const isExistField = isExistFieldFn(currentNode.field);
      if (!isExistField) {
        // 不存在-增加
        form.fieldList.push(currentNode);
      }
      if (isExistField) {
        // 存在-不增加
        ElMessage.warning("字段已存在，不能重复添加");
        const checkedIndex = selectNodes.checkedKeys.indexOf(currentNode.id);
        selectNodes.checkedKeys.splice(checkedIndex, 1);
        treeRef.value.setCheckedKeys(selectNodes.checkedKeys);
      }
    }
  }
  // 记录选中的keys，用于比较当前是全选还是非全选
  oldCheckedKeys.value = treeRef.value.getCheckedKeys();
};

// 判断是否包含field
const isExistFieldFn = (field: string) => {
  let isExist = false;
  form.fieldList.forEach((fieldItem: any) => {
    if (fieldItem.field === field) {
      isExist = true;
    }
  });
  return isExist;
};

// 获取数组中的index
const getIndexInArray = (data: any, id: any) => {
  let index = -1;
  data.forEach((item, itemIndex) => {
    if (id === item.id) {
      index = itemIndex;
    }
  });
  return index;
};

// 获取数据服务
const getDataService = () => {
  loading.value = true;
  return getServiceListApi()
    .then((res: any) => {
      if (res.code) {
        dataService.value = [];
      } else {
        dataService.value = res;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 选择数据服务，数据通道的值
const getDataSourceChange = () => {
  dataChannels.value = [];
  getDataSource();
};

const getDataSource = () => {
  const handle = `${handleData.entPrefix}/${handleData.handle}`;
  getDataSourceNewApi({ dataServiceId: form.dataService, handle }).then(
    (res: any) => {
      if (Array.isArray(res) && res.length > 0) {
        dataChannels.value = res.map((item) => {
          item.disabled = true; // 默认是置灰
          return item;
        });
      } else {
        dataChannels.value = [];
      }
    }
  );
};

const handleDel = (index: number) => {
  const { id } = form.fieldList[index];
  form.fieldList.splice(index, 1);
  const treeCheckedKeys = treeRef.value.getCheckedKeys();
  const checkedIndex = treeCheckedKeys.indexOf(id);
  treeCheckedKeys.splice(checkedIndex, 1);
  // 拿到父级，需要将父级也取消选中
  const dataSourceId = id.split("::")[0];
  let dataSourceIdIndex = -1;
  treeCheckedKeys.forEach((keyItem, index) => {
    if (`${keyItem}` === `${dataSourceId}`) {
      dataSourceIdIndex = index;
    }
  });
  if (dataSourceIdIndex !== -1) {
    treeCheckedKeys.splice(dataSourceIdIndex, 1);
  }

  treeRef.value.setCheckedKeys(treeCheckedKeys);
  oldCheckedKeys.value = treeCheckedKeys;
};

const resetForm = () => {
  form.dataService = "";
  form.fieldList = [];
  formRef.value.resetFields();
};

const getData = () => {
  return form.fieldList.map((fieldItem) => {
    fieldItem.fieldIndex = -1;
    const result = { ...fieldItem };
    result.fieldValue =
      Array.isArray(fieldItem.fieldValue) && fieldItem.fieldValue.length > 0
        ? JSON.stringify(fieldItem.fieldValue)
        : "";
    return {
      ...result,
    };
  });
};

const handleDelAll = () => {
  if (!form.fieldList.length) {
    return;
  }
  form.fieldList = [];
  treeRef.value.setCheckedKeys([]);
  oldCheckedKeys.value = [];
};

const formValidate = async () => {
  let isError = false;
  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  if (!form.fieldList?.length) {
    ElMessage.error("请选择数据通道和对应的英文名称");
    return true;
  }
  const eNameList: Array<string> = [];
  const cNameList: Array<string> = [];

  for (const item of form.fieldList) {
    if (!item.description) {
      ElMessage.error("请填写中文名称");
      return true;
    }
    if (item.description.length > 255) {
      ElMessage.error("中文名称最大长度为255字符" + item.description);
      return true;
    }
    if (fieldValidate("field", item.field)) {
      eNameList.push(item.field);
    }
    if (fieldValidate("description", item.description)) {
      cNameList.push(item.description);
    }
    if (item.remark?.length > 100) {
      ElMessage.error("备注最大长度为100字符");
      return true;
    }
  }
  if (eNameList.length > 0) {
    ElMessage.error(`英文名称【${eNameList.join(",")}】已存在`);
    return true;
  }
  if (cNameList.length > 0) {
    ElMessage.error(`中文名称【${cNameList.join(",")}】已存在`);
    return true;
  }

  return isError;
};

onMounted(() => {
  getDataService();
});

defineExpose({
  getData,
  resetForm,
  formValidate,
});
</script>

<style lang="scss" scoped>
.data-channels {
  height: 436px;
  width: 1160px;
  display: flex;
  flex-direction: row;
  border: 1px solid #dfe4e3;
  border-radius: 2px;
  margin-left: -100px;
}

.data-channels-left {
  width: 200px;
  border-right: 1px solid #dfe4e3;
  display: flex;
  flex-direction: column;
  .data-channels-left-header {
    height: 40px;
    line-height: 40px;
    width: 100%;
    padding: 0 16px;
    background: #eef2f1;
    color: #1d2129;
    font-weight: 500;
    font-size: 12px;
  }
  .data-channels-left-body {
    flex: 1;
    min-height: 0;
  }
}
.data-channels-right {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  .data-channels-right-header {
    background: #eef2f1;
    height: 40px;
    width: 100%;
    padding: 0 16px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
    font-size: 12px;
    color: #1d2129;

    .delete-wrap {
      cursor: pointer;
      width: 20px;
      height: 20px;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      &:hover {
        background: #c7d5d1;
        border-radius: 50%;
      }

      &.is-disabled {
        cursor: not-allowed;
      }
    }
  }
  .data-channels-right-body {
    flex: 1;
    min-height: 0;

    .data-channels-right-blank {
      height: 100%;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      img {
        width: 100px;
      }

      .blank-text {
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        color: #7b9790;
      }
    }
  }
}
.custom-tree-node {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 100px;
}

.el-tree {
  height: 100%;
  overflow: auto;

  .is-current::before {
    width: 0;
  }

  .el-tag {
    background: #e8f4ff;
    color: #0057fe;
    border-color: #e8f4ff;
    margin-left: 2px;
  }
}
</style>
