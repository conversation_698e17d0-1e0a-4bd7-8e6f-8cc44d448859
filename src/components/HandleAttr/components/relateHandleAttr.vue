<template>
  <el-form
    ref="formRef"
    label-position="left"
    :model="form"
    label-width="100px"
    :rules="rules"
    style="width: 100%"
  >
    <el-form-item label="关联标识" prop="handleValue">
      <template #label>
        <div class="handle-label-tips">
          <span class="handle-label-tips-text">关联标识</span>
          <el-tooltip placement="top-start" effect="light">
            <template #content>
              <img
                alt=""
                style="width: 650px; height: 300px"
                fit="contain"
                src="@/assets/images/handle/handleReferanceDemo.jpg"
            /></template>
            <span
              ><el-icon><Warning /></el-icon
            ></span>
          </el-tooltip>
        </div>
      </template>
      <el-radio-group v-model="relateType" size="small" class="relate-type">
        <el-radio-button :label="RelateType.BASELINE">基准</el-radio-button>
        <el-radio-button :label="RelateType.COMPATIBLE">兼容</el-radio-button>
      </el-radio-group>
      <el-table :data="form.references" size="small" border>
        <el-table-column label="关联标识" min-width="400px">
          <template #default="scope">
            <el-row style="flex-wrap: nowrap"
              ><el-form-item
                :prop="'references.' + scope.$index + '.referenceHandle'"
                :rules="[
                  {
                    required: true,
                    validator: referenceHandleValidate,
                  },
                ]"
              >
                <el-input
                  style="width: 200px"
                  :validate-event="false"
                  v-model.trim="scope.row.referenceHandle"
                  @input="
                    scope.row.queryProp = {};
                    scope.row.referenceHandleProp = {};
                  "
                  @blur="() => getHandleInfo(scope.row.referenceHandle)"
                  placeholder="输入关联标识"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item
                style="margin-left: 12px"
                :prop="'references.' + scope.$index + '.referenceHandleProp'"
                :rules="[
                  {
                    required: true,
                    validator: propHandleRulesValidate,
                  },
                ]"
              >
                <el-select
                  style="width: 200px"
                  :loading="loading"
                  :disabled="!scope.row.referenceHandle"
                  :popper-append-to-body="false"
                  v-model="scope.row.referenceHandleProp"
                  placeholder="请选择关联属性"
                  value-key="field"
                  :validate-event="false"
                  filterable
                  @change="handleQueryPropChange"
                >
                  <el-option
                    v-for="item in allFieldList"
                    :key="item.fieldIndex"
                    :value="item"
                    :label="item.field"
                  >
                    <span style="float: left">{{ item.field }}</span>
                    <span style="float: left; margin-left: 5px">{{
                      (item.fieldIndex && item.fieldIndex > 0) || item.remark
                        ? "-"
                        : ""
                    }}</span>
                    <span style="float: left; margin-left: 5px">{{
                      item.fieldIndex && item.fieldIndex > 0
                        ? item.fieldIndex
                        : ""
                    }}</span>
                    <span style="float: left; margin-left: 5px">{{
                      item.remark ? "(" + item.remark + ")" : ""
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item></el-row
            >
          </template>
        </el-table-column>
        <el-table-column label="关联标识属性" min-width="220px">
          <template #default="scope">
            <el-form-item
              :prop="'references.' + scope.$index + '.queryProp'"
              :rules="[
                {
                  required: true,
                  validator: queryPropRulesValidate,
                },
              ]"
            >
              <span style="margin-right: 10px">-</span>
              <el-select
                :validate-event="false"
                :disabled="fixDisabled || !scope.row.referenceHandle"
                :loading="loading"
                :popper-append-to-body="false"
                v-model="scope.row.queryProp"
                placeholder="请选择关联标识属性"
                :no-data-text="
                  scope.row.referenceHandle ? '无数据' : '请输入关联标识属性'
                "
                value-key="field"
                filterable
              >
                <el-option
                  v-for="item in queryFieldList"
                  :key="item.fieldIndex"
                  :value="item"
                  :label="item.field"
                >
                  <span style="float: left">{{ item.field }}</span>
                  <span style="float: left; margin-left: 5px">{{
                    (item.fieldIndex && item.fieldIndex > 0) || item.remark
                      ? "-"
                      : ""
                  }}</span>
                  <span style="float: left; margin-left: 5px">{{
                    item.fieldIndex && item.fieldIndex > 0
                      ? item.fieldIndex
                      : ""
                  }}</span>
                  <span style="float: left; margin-left: 5px">{{
                    item.remark ? "(" + item.remark + ")" : ""
                  }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column
          label="当前标识"
          min-width="200px"
          show-overflow-tooltip
        >
          <template #default>
            <span>{{ currentHandle }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="当前标识属性"
          min-width="180px"
          v-if="relateType === RelateType.COMPATIBLE"
        >
          <template #default="scope">
            <el-form-item
              style="margin-left: 12px"
              :prop="'references.' + scope.$index + '.paramProp'"
              :rules="[
                {
                  required: true,
                  validator: paramPropRulesValidate,
                },
              ]"
              ><el-select
                :validate-event="false"
                :disabled="fixDisabled || !scope.row.referenceHandle"
                v-model="scope.row.paramProp"
                placeholder="请选择标识属性"
                value-key="field"
                filterable
              >
                <el-option
                  v-for="(item, index) in paramFieldList"
                  :key="index"
                  :value="item"
                  :label="item.field"
                >
                  <span style="float: left">{{ item.field }}</span>
                  <span style="float: left; margin-left: 5px">{{
                    (item.fieldIndex && item.fieldIndex > 0) || item.remark
                      ? "-"
                      : ""
                  }}</span>
                  <span style="float: left; margin-left: 5px">{{
                    item.fieldIndex && item.fieldIndex > 0
                      ? item.fieldIndex
                      : ""
                  }}</span>
                  <span style="float: left; margin-left: 5px">{{
                    item.remark ? "(" + item.remark + ")" : ""
                  }}</span>
                </el-option>
              </el-select></el-form-item
            >
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80px">
          <template #default="scope">
            <el-form style="display: inline; margin-left: 12px">
              <el-button
                type="primary"
                text
                @click="resolveHandle(scope.row.referenceHandle)"
                >解析</el-button
              >
            </el-form>
          </template>
        </el-table-column>
      </el-table>
    </el-form-item>
    <el-form-item label="中文名称">
      <ellipsisText :value="form.description"></ellipsisText>
    </el-form-item>
    <el-form-item label="英文名称">
      <ellipsisText :value="form.field"></ellipsisText>
    </el-form-item>
    <el-form-item label="备注" prop="remark">
      <el-input v-model.trim="form.remark" clearable />
    </el-form-item>
  </el-form>
  <el-dialog
    v-model="data.dialogResolve"
    title="解析结果"
    append-to-body
    width="800"
    @close="data.dialogResolve = false"
  >
    <div class="relate-handle-matrix-graph">
      <matrix-graph ref="matrixGraphRef"></matrix-graph>
    </div>
    <template #footer>
      <el-button type="primary" @click="data.dialogResolve = false">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import {
  reactive,
  watch,
  ref,
  nextTick,
  PropType,
  onMounted,
  inject,
  computed,
} from "vue";
import {} from "element-plus";
import { idResolve } from "@/api/idRes/index";
import matrixGraph from "@/views/idRes/components/matrix-graph.vue";
import { ApiGetResolveField } from "@/api/objHandle/manager";
import {
  FIELD_TYPE_MAP,
  FIELD_TYPE_LIST,
  DIALOG_TYPE_MAP,
  HANDLE_SOURCE,
} from "@/utils/constant";
import { isDuplicateValidate } from "../common";
import ellipsisText from "@/components/ellipsisText/index.vue";

const handleData: any = inject("handleData");
const basicAttrTableData: any = inject("handleAttrNewTableData");
const handleExtendAttrTableData: any = inject("handleExtendAttrTableData");
const handleBasicAttrTableData: any = inject("handleBasicAttrTableData");
const currentHandle: any = inject("currentHandle");
const basicCurrentOperatorAttr: any = inject("basicCurrentOperatorAttr");
const allAttrData = computed(() => {
  // 注册的时候，需要扩展属性，维护的时候，需要拿到基础属性
  const tempData =
    handleSource.value === HANDLE_SOURCE.MAINTAIN
      ? [...handleBasicAttrTableData.value]
      : [...handleExtendAttrTableData.value];
  return [...basicAttrTableData.value, ...tempData];
});

const attrReferenceHandle = computed(() => {
  if (!basicAttrTableData.value.length) return [];
  const tempArr: any = [];
  basicAttrTableData.value.forEach((item: any) => {
    const { references } = item;
    if (references?.length) {
      const { referenceHandle } = references[0];
      tempArr.push(referenceHandle);
    }
  });
  return tempArr;
});

const oldReferenceHandle = computed(() => {
  if (!basicCurrentOperatorAttr.value?.references) {
    return "";
  }
  const { references } = basicCurrentOperatorAttr.value;
  if (!references.length) return "";

  return references[0].referenceHandle;
});

const formRef = ref();
const matrixGraphRef = ref();
const loading = ref(false);

const props = defineProps({
  type: {
    type: Number,
  },
});

const RelateType = {
  BASELINE: "BASELINE",
  COMPATIBLE: "COMPATIBLE",
};
const relateType = ref(RelateType.BASELINE);
const allFieldList = ref<PropField[]>([]);
// 定义查询属性下拉框
const queryFieldList = ref<PropField[]>([]);

interface PropField {
  valueKey?: string;
  remark?: string;
  fieldIndex?: number;
  field?: string;
  fieldType?: number;
}

interface FieldTypeList {
  value: number;
  name: string;
}
const data: {
  dialogResolve: boolean;
  fieldTypeList: FieldTypeList[];
} = reactive({
  fieldTypeList: FIELD_TYPE_LIST,
  dialogResolve: false,
});

async function referenceHandleValidate(
  rule: any,
  handleVal: any,
  callback: any
) {
  if (!handleVal) return callback("请选择关联标识");
  if (currentHandle.value === handleVal) {
    // 判断不能关联本身
    return callback("关联标识和当前标识重复");
  }
  const params = {
    type: props.type,
    oldVal: oldReferenceHandle.value,
    newVal: handleVal,
    list: attrReferenceHandle.value,
  };
  if (isDuplicateValidate(params)) {
    return callback("标识已被关联，请重新填写");
  }
  callback();
}

// 标识-属性校验规则生成
async function propHandleRulesValidate(rule: any, val: any, callback: any) {
  if (!val?.field) {
    callback("请选择属性");
    return;
  }
  callback();
}

// 筛选基础属性字段类型为固定值、标识解析数据源的行，获取英文名作为关联属性下拉框内容
const paramFieldList = computed(() => {
  return allAttrData.value
    .filter((item: any) => {
      return (
        item.fieldType === FIELD_TYPE_MAP.source ||
        item.fieldType === FIELD_TYPE_MAP.fixed
      );
    })
    .map((item) => {
      return {
        remark: item.remark,
        fieldIndex: item.fieldIndex,
        field: item.field,
      };
    });
});

const fixDisabled = ref(false);

const form = reactive<Record<string, any>>({
  fieldIndex: -1,
  field: "",
  description: "",
  remark: "",
  fieldValue: "",
  references: [
    {
      referenceHandle: "",
      queryProp: "",
      paramProp: "",
    },
  ],
  editFlag: true, // 标识维护需要该字段，默认是true
});

const handleSource: any = inject("handleSource");

const fieldType = FIELD_TYPE_MAP.handleWithAttr;

const handleValueValidate = (rule: any, value: any, callback: any) => {
  callback();
};

// 校验标识下属性是否都为固定值
async function getHandleInfo(handleVal: any) {
  if (!handleVal) return;
  // 查询
  await getIdResolve(handleVal);
  fixDisabled.value = false;
  if (allFieldList.value.length > 0) {
    // 过滤类型为固定值
    const filterArray = allFieldList.value.filter(
      (item: { fieldType?: number }) => item.fieldType === FIELD_TYPE_MAP.fixed
    );

    // 若输入的关联标识属性都为固定值时,返回true;参数、查询下拉框置灰，可保存，完成关联；
    if (filterArray.length === allFieldList.value.length) {
      fixDisabled.value = true;
      return true;
    }
  }
}

// 根据标识查询属性
function getIdResolve(handleVal: any) {
  if (handleVal) {
    allFieldList.value = [];
    return idResolve({ handle: handleVal })
      .then((response: any) => {
        // 遍历标识解析结果，获取关联属性下拉框数据
        response.values.forEach(
          (
            item: { fieldType: any; remark: any; fieldIndex: any; field: any },
            index: any
          ) => {
            // 只关联固定值和数据源
            if (
              item.fieldType === FIELD_TYPE_MAP.fixed ||
              item.fieldType === FIELD_TYPE_MAP.source
            ) {
              allFieldList.value.push({
                fieldType: item.fieldType,
                remark: item.remark ? item.remark : "",
                fieldIndex: item.fieldIndex,
                field: item.field,
              });
            }
          }
        );
      })
      .finally(() => {
        // 初始化数据源下拉框
        getProperty([FIELD_TYPE_MAP.source]);
      });
  }
}

// 关联标识属性校验规则生成
async function queryPropRulesValidate(rule: any, val: any, callback: any) {
  if (fixDisabled.value && !val?.field) {
    callback();
    return;
  }
  if (!val?.field) {
    callback("请选择关联标识属性");
    return;
  }
  callback();
}

// 关联标识属性校验规则生成
async function paramPropRulesValidate(rule: any, val: any, callback: any) {
  if (fixDisabled.value && !val?.field) {
    callback();
    return;
  }
  if (!val?.field) {
    callback("请选择标识属性");
    return;
  }
  callback();
}

const rules = reactive({
  remark: [
    {
      required: false,
      max: 100,
      message: "最大长度为100字符",
      trigger: "blur",
    },
  ],
  handleValue: {
    required: true,
    validator: handleValueValidate,
    trigger: "blur",
  },
});

// 过滤出指定类型的属性
function getProperty(fieldType: number[]) {
  queryFieldList.value = allFieldList.value.filter((item: any) =>
    fieldType.includes(item.fieldType)
  );
}

function resolveHandle(id: any) {
  if (id) {
    idResolve({ handle: id }).then((response: any) => {
      data.dialogResolve = true;
      nextTick(() => {
        matrixGraphRef.value.refresh(response);
      });
    });
  } else {
    ElMessage({
      message: "请输入标识",
      type: "error",
    });
  }
}

async function validateData() {
  let isError = false;
  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  return isError;
}

function getData() {
  const result = { ...form };
  if (
    relateType.value !== RelateType.COMPATIBLE ||
    !result.references[0].paramProp
  ) {
    result.references[0].paramProp = {};
  }
  if (!result.references[0].queryProp) {
    result.references[0].queryProp = {};
  }

  return { ...result };
}

const handleQueryPropChange = (val) => {
  const { references } = form;
  if (val) {
    ApiGetResolveField({
      handle: references[0].referenceHandle,
      field: val.field,
      fieldType,
    }).then((res: any) => {
      form.field = res.field;
      form.description = res.description;
    });
  }
};

const formValidate = async () => {
  let isError = false;
  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  return isError;
};

const resetForm = () => {
  form.fieldIndex = -1;
  form.field = "";
  form.description = "";
  form.remark = "";
  form.fieldValue = "";
  form.references = [
    {
      referenceHandle: "",
      queryProp: null,
      paramProp: null,
      referenceHandleProp: null,
    },
  ];
  formRef.value.resetFields();
};
onMounted(() => {
  Object.assign(form, basicCurrentOperatorAttr.value);
  if (form.references?.length) {
    const { referenceHandle } = form.references[0];
    getHandleInfo(referenceHandle);
    // 根据paramProp字段来判断是基准还是兼容
    if (form.references[0].paramProp?.field) {
      relateType.value = RelateType.COMPATIBLE;
    }
  }
});

defineExpose({
  form,
  validateData,
  getData,
  formValidate,
  resetForm,
});
</script>
<style lang="scss" scoped>
.el-table {
  .is-error {
    height: 70px;
    :deep(.el-form-item__error) {
      top: 50px;
    }
  }
}

.mb10 {
  margin-bottom: 10px;
}
.mb11 {
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 5px;
  color: #929292;
}
.mb12 {
  float: right;
}
.mb13 {
  margin-top: 5px;
  letter-spacing: 0.5px;
  // text-align: center;
  margin: 0 30px;
  > div {
    display: flex;
    align-items: baseline;
    span {
      flex: 1;
      &:first-child {
        text-align: right;
      }
    }
  }
}
.mb14 {
  margin-left: 10px;
}
.mb15 {
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 8px;
  letter-spacing: 0.5px;
  // text-align: center;
  margin: 40px 30px 10px;
  > div {
    display: flex;
    align-items: baseline;
    span {
      flex: 1;
      &:first-child {
        text-align: right;
      }
    }
  }
}
:deep(.el-select-dropdown__empty) {
  padding: 10px 10px !important;
}

:deep(.add-item-btn) {
  width: 100%;
  border: 1px solid #dfe4e3;
  border-top: 0;
  height: 44px;
  border-radius: 0;
  color: #1664ff;
  font-size: 12px;
  line-height: 20px;
  &:hover {
    color: #4086ff !important;
    background-color: transparent !important;
  }
}

.relate-handle-matrix-graph {
  height: 600px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.el-form-item:nth-child(1)) {
  position: relative;
  flex-direction: column;

  .relate-type {
    position: absolute;
    top: -32px;
    right: 0;
    z-index: 100;
  }
}
.handle-label-tips {
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;

  .el-tooltip__trigger {
    font-size: 12px;
    margin-left: 4px;
    cursor: pointer;
    vertical-align: middle;
  }
}
</style>
