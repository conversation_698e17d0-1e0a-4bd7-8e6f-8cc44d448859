<template>
  <el-dialog
    v-model="dialogVisible"
    title="创建解析语句"
    fullscreen
    destroy-on-close
    align-center
    @close="handleClose"
  >
    <div class="dialog-dataChanelInfo">
      <el-space class="description-title">
        <el-divider direction="vertical" class="description-title-divider" />
        <div class="description-title-text">数据通道信息</div>
      </el-space>
      <el-descriptions :column="1" direction="horizontal">
        <el-descriptions-item label="数据通道名称">
          {{ data?.dataChannelName || "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="所属对象标识">
          {{ data?.objectHandleName || "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="所属数据服务">
          {{ data?.dataServiceName || "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="数据库">
          {{ data?.databaseName || "-" }}
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <el-divider />
    <div class="dialog-parsing-statements">
      <el-space class="description-title">
        <el-divider direction="vertical" class="description-title-divider" />
        <div class="description-title-text">解析语句</div>
      </el-space>
      <ParsingStatements
        ref="parsingStatementsRef"
        :data="data"
      ></ParsingStatements>
    </div>
    <template #footer>
      <span>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import ParsingStatements from "./components/parsing-statements.vue";

const parsingStatementsRef = ref();

const props = defineProps({
  data: Object,
});

const emit = defineEmits(["close", "confirm"]);

const dialogVisible = true;

const handleClose = () => {
  emit("close");
};

const handleConfirm = () => {
  emit("confirm", parsingStatementsRef.value.code);
};
</script>

<style scoped lang="scss">
.description-title {
  margin-bottom: 16px;
  font-weight: 500;

  .description-title-divider {
    width: 4px;
    height: 12px;
    background-color: #00a57c;

    &.el-divider {
      margin: 0 0;
    }
  }
}
.el-divider {
  margin: 8px 0 24px 0;
}
</style>
