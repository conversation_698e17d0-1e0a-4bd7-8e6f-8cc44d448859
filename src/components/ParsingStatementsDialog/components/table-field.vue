<template>
  <div class="table-fields">
    <div
      class="table-fields-header"
      :class="{ 'is-tid': nodeInfo.tableName === tid?.tableName }"
    >
      <div class="table-fields-header-title">
        <ellipsisText :value="nodeInfo.tableName" />
      </div>
      <div class="table-fields-header-btn" @click="handleDelete">
        <el-icon><Close /></el-icon>
      </div>
    </div>
    <div class="table-fields-related">
      <el-button @click="handleOpenDialog"> 关联字段 </el-button>
    </div>
    <div class="table-fields-body">
      <div class="table-field" v-for="item in data" :key="item.column">
        <el-checkbox
          v-model="item.isSelected"
          @change="handleItemChange"
        ></el-checkbox>
        <div class="table-field-text">
          <ellipsisText :value="item.column" />
        </div>
        <div
          v-if="
            nodeInfo.tableName === tid?.tableName && item.column === tid?.column
          "
          class="is-tid"
        >
          TID
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, onMounted, watch } from "vue";
import ellipsisText from "@/components/ellipsisText/index.vue";

import type { ITidMark } from "@/types/tool";

const getGraph: any = inject("getGraph");
const getNode: any = inject("getNode");

const tid: ITidMark = inject("tid") as ITidMark;

const props = defineProps({
  associate: {
    type: Function,
  },
  deleteNode: {
    type: Function,
  },
  selectField: {
    type: Function,
  },
});

const graph = ref();
const node = ref();

const data = ref<any>([]);

const nodeInfo = ref<any>({});

const handleItemChange = () => {
  props &&
    props.selectField &&
    props.selectField({
      table: nodeInfo.value.tableName,
      tableFields: data.value.filter((item: any) => item.isSelected),
    });
  console.log("======数据======:", JSON.parse(JSON.stringify(data.value)));
};

const handleOpenDialog = () => {
  props && props.associate && props.associate(nodeInfo.value);
};

const handleDelete = () => {
  props && props.deleteNode && props.deleteNode(nodeInfo.value);
};
onMounted(() => {
  node.value = getNode();
  graph.value = getGraph();
  nodeInfo.value = node.value.data || {};
  console.log("====当前节点======:", node.value);
  data.value = (nodeInfo.value.tableFields || []).map((item: any) => {
    return { ...item };
  });
});
</script>

<style scoped lang="scss">
.table-fields-header-btn {
  cursor: pointer;
}
.table-fields {
  background-color: #fff;
  font-size: 12px;
  border: 1px solid #d8e7e3;
  cursor: grab;
  display: flex;
  flex-direction: column;
  overflow: auto;
}
.table-fields-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background-color: #d8e7e3;
  padding: 12px 8px;
  height: 40px;
  min-height: 40px;
  color: #4e5969;
  &.is-tid {
    background-color: #00a57c;
    color: #fff;
  }
  .table-fields-header-title {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break: break-all;
  }
}
.table-fields-body {
  flex: 1;
  min-height: 0;
  overflow: auto;
}
.table-fields-related {
  padding: 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  .el-button {
    border: 1px solid #00a57c;
    flex: 1;
    min-width: 0;
    color: #00a57c;
  }
}

.table-field {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  min-height: 36px;
  padding: 1px 12px;
  color: #4e5969;
  border-bottom: 1px solid #e5e8ef;
  white-space: nowrap;
  overflow: hidden;
  font-weight: 400;
  font-size: 12px;
  .table-field-text {
    flex: 1;
    min-width: 0;
    padding: 0 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .is-tid {
    height: 20px;
    line-height: 20px;
    border-radius: 2px;
    background: #e8f4ff;
    padding: 0 8px;
  }
}
</style>
