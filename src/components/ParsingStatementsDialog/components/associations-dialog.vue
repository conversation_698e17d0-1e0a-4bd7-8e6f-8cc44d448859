<template>
  <el-dialog
    v-model="dialogVisible"
    title="关联表字段"
    width="600px"
    @close="handleClose"
    destroy-on-close
    align-center
  >
    <el-form
      class="associations-form"
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="当前表" prop="currentTable">
        <div>{{ formData.currentTable }}</div>
      </el-form-item>
      <el-form-item label="当前表字段" prop="currentTableField">
        <el-select
          v-model="formData.currentTableField"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in currentTableFields"
            :key="item.column"
            :value="item.column"
            :label="item.column"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="关联表" prop="associationTable">
        <el-select
          v-model="formData.associationTable"
          placeholder="请选择"
          clearable
          @change="handleAssociationTableFieldChange"
        >
          <el-option
            v-for="item in associationTables"
            :key="item.tableName"
            :value="item.tableName"
            :label="item.tableName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="关联表字段" prop="associationTableField">
        <el-select
          v-model="formData.associationTableField"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in associationTableFields"
            :key="item.column"
            :value="item.column"
            :label="item.column"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import type { IColumn, IDatabase, IAssociation } from "@/types/tool";
import { ApiDatabaseDetail } from "@/api/tool/index";

const props = defineProps({
  handleId: String,
  table: String,
  modelId: String,
  selectedTables: Array<IDatabase>,
  fields: Array<IColumn>,
});

const formRef = ref();

const emit = defineEmits(["close", "confirm"]);

const dialogVisible = ref(true);

const currentTableFields = ref<IColumn[]>([]);

const associationTables = ref<IDatabase[]>([]);

const associationTableFields = ref<IColumn[]>([]);

const rules = {
  currentTable: [
    {
      required: true,
      message: "请选择当前表",
      trigger: "blur",
    },
  ],
  currentTableField: [
    {
      required: true,
      message: "请选择当前表字段",
      trigger: "blur",
    },
  ],
  associationTable: [
    {
      required: true,
      message: "请选择关联表",
      trigger: "blur",
    },
  ],
  associationTableField: [
    {
      required: true,
      message: "请选择关联表字段",
      trigger: "blur",
    },
  ],
};

const formData = ref<{
  currentTable: string;
  currentTableField: string;
  associationTable: string;
  associationTableField: string;
}>({
  currentTable: "",
  currentTableField: "",
  associationTable: "",
  associationTableField: "",
});

const handleAssociationTableFieldChange = (val: any) => {
  formData.value.associationTableField = "";
  if (!val) {
    associationTableFields.value = [];
    return;
  }
  const modelId = associationTables.value.filter(
    (item) => item.tableName === val
  )[0].modelId;
  ApiDatabaseDetail({ modelId, handleId: props?.handleId || "" }).then(
    (res: any) => {
      associationTableFields.value = res.tableColumnVOS || [];
    }
  );
};

const handleClose = () => {
  emit("close");
};

const handleConfirm = () => {
  formRef.value.validate((valid: any) => {
    if (valid) {
      const associationTableFieldAlias = associationTableFields.value.filter(
        (item) => item.column === formData.value.associationTableField
      )[0]?.columnAlias;
      const currentTableFieldAlias = currentTableFields.value.filter(
        (item) => item.column === formData.value.associationTableField
      )[0]?.columnAlias;
      const tempData: IAssociation = {
        ...formData.value,
        associationTableFieldAlias,
        currentTableFieldAlias,
      };
      emit("confirm", tempData);
    }
  });
};

onMounted(() => {
  formData.value.currentTable = props.table || "";
  currentTableFields.value = props.fields || [];
  try {
    if (props?.selectedTables) {
      associationTables.value = props?.selectedTables?.filter((item) => {
        return item.tableName !== props.table;
      });
    }
  } catch (error) {
    associationTables.value = [];
  }
});
</script>

<style scoped lang="scss">
.associations-form {
  .el-select {
    width: 100%;
  }
}
</style>
