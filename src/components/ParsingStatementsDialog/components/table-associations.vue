<template>
  <div class="table-associations">
    <div class="graph" id="tableGraph" ref="tableGraph"></div>
    <TeleportContainer />
  </div>
</template>

<script setup lang="ts">
import { ref, createVNode, onMounted, nextTick, reactive } from "vue";
import { useTeleport } from "@antv/x6-vue-shape";
import { Graph } from "@antv/x6";
import TableField from "./table-field.vue";
import type { IColumn } from "@/types/tool";

const emit = defineEmits(["associate", "delete", "select"]);

const props = defineProps({
  selectedTableMap: Object,
});

interface Nodes {
  id: any;
  shape: string;
  width: number;
  height: number;
  view: string;
  component: string;
  parentId: any;
  data: any;
  ports: any;
}
interface Edges {
  source: any;
  target: any;
}
interface GraphData {
  nodes: Nodes[];
  edges: Edges[];
}
const graphData = reactive<GraphData>({
  nodes: [],
  edges: [],
});

const TABLE_ASSOCIATIONS_GRAPH_ID = "TABLE_ASSOCIATIONS_GRAPH_ID";

const TeleportContainer = useTeleport(TABLE_ASSOCIATIONS_GRAPH_ID);

const tableGraph = ref(null);

const graph = ref();

const tempGraphData = ref();

const save = () => {
  const data = graph.value.toJSON();
  tempGraphData.value = data;
  const nodes = graph.value.getNodes();
  nodes.forEach((node: any) => {
    graph.value.removeNode(node.id);
  });
  console.log("保存图像：", data);
};

const restore = () => {
  graph.value.fromJSON(tempGraphData.value);
};

Graph.registerVueComponent(
  "table-field",
  {
    render: () => {
      return createVNode(TableField, {
        associate: handleAssociate,
        deleteNode: handleDeleteNode,
        selectField: handleSelectField,
      });
    },
  },
  true
);

// 选择字段
const handleSelectField = (data: { tableName: string; tableFields: any[] }) => {
  emit("select", data);
};

// 删除节点
const handleDeleteNode = (data: { tableName: string; tableFields: any[] }) => {
  const cell = graph.value.getCellById(data.tableName);
  const edges = graph.value.getConnectedEdges(cell);
  // 删除节点所有的边
  edges.forEach((edge: any) => {
    graph.value.removeEdge(edge.id);
  });
  // 删除node
  graph.value.removeNode(data.tableName);
  emit("delete", { type: "cell", tableName: data.tableName });
};

const handleAssociate = (data: any) => {
  console.log("关联字段：", data);
  emit("associate", data);
};

// 获取节点的高度
const getNodeHeight = (data: any) => {
  return data.tableFields.length * 36 + 140; // TODO 最后一个36需要替换为查询+header的高度
};

const portGroupConfig = {
  // 链接桩位置-矩形节点右侧均匀分布
  right: {
    // 链接桩DOM结构定义
    markup: [
      {
        tagName: "rect", // svg html 元素
        selector: "body", // 该元素的选择器，通过选择器来定位该元素或为该元素指定样式
      },
    ],
    position: {
      name: "absolute",
      args: { x: 198, y: 0 },
    },
    attrs: {
      // 设置桩的样式
      body: {
        width: 2,
        height: 10,
        fill: "transparent", // TODO test 明确观察桩的位置，去掉注释
      },
    },
  },
  left: {
    // 链接桩DOM结构定义
    markup: [
      {
        tagName: "rect", // svg html 元素
        selector: "body", // 该元素的选择器，通过选择器来定位该元素或为该元素指定样式
      },
    ],
    position: {
      name: "absolute",
      args: { x: 0, y: 0 },
    },
    attrs: {
      // 设置桩的样式
      body: {
        width: 2,
        height: 10,
        fill: "transparent", // TODO test 明确观察桩的位置，去掉注释
      },
    },
  },
};

// 获取ports
const getNodePorts = (tableName: string, tableFields: IColumn[]) => {
  const items: any = [
    {
      id: `${tableName}-out`, // 头部的桩
      group: "right",
      args: {
        x: 198,
        y: 25,
      },
    },
    {
      id: `${tableName}-in`, // 头部的桩
      group: "left",
      args: {
        x: 0,
        y: 25,
      },
    },
  ];

  tableFields.forEach((item: IColumn, index: number) => {
    items.push({
      id: `${tableName}-${item.column}-out`, // portId: 当前标识-属性field
      group: "right",
      args: {
        x: 198,
        y: index * 36 + 88 + 16, // 其他选项的中间位置，140（头部高度）, 36（当前高度）,12(中间位置)
      },
      field: item.column, // 链接装所在的属性索引
    });
    items.push({
      id: `${tableName}-${item.column}-in`, // portId: 当前标识-属性field
      group: "left",
      args: {
        x: 0,
        y: index * 36 + 88 + 16, // 其他选项的中间位置，140（头部高度）, 36（当前高度）,12(中间位置)
      },
      field: item.column, // 链接装所在的属性索引
    });
  });

  return items;
};

// 设置节点
const setNode = (data: any) => {
  const { tableName, tableFields } = data;
  // 获取所有的桩
  const items = getNodePorts(tableName, tableFields);

  const nodes = graph.value.getNodes(); // 所有已绘制的节点

  let maxX = 0;

  nodes.forEach((node: any) => {
    const pos = node.position();
    if (pos.x > maxX) {
      maxX = pos.x;
    }
  });

  const translateX = maxX === 0 ? 30 : maxX + 200 + 60;
  const translateY = 30;

  const node = {
    id: tableName,
    shape: "vue-shape",
    width: 200,
    height: getNodeHeight(data),
    view: TABLE_ASSOCIATIONS_GRAPH_ID,
    component: "table-field",
    data,
    ports: {
      // 链接桩组定义
      groups: portGroupConfig,
      // 链接桩
      items,
    },
    attrGroup: data.attrGroup, // 属于的属性组，属性组id: 所在handle-属性的field
    options: true,
  };

  graph.value.addNode(node);
  const cell = graph.value.getCell(node.data.tableName);
  cell.position(translateX, translateY);
};

const addNode = (data: { tableName: string; tableFields: any[] }) => {
  setNode(data);
};

const addEdge = (data: {
  currentTable: string;
  currentTableField: string;
  associationTable: string;
  associationTableField: string;
}) => {
  const {
    currentTable,
    currentTableField,
    associationTable,
    associationTableField,
  } = data;
  console.log("====新建线===:", data);
  const currentTableCell = graph.value.getCell(currentTable);
  const associationTableCell = graph.value.getCell(associationTable);
  console.log("====新建线 currentTableCell===:", currentTableCell);
  console.log("====新建线 associationTableCell===:", associationTableCell);
  const currentTablePos = currentTableCell.position();
  const associationTablePos = associationTableCell.position();

  const sourceDirection =
    associationTablePos.x > currentTablePos.x ? "out" : "in";
  const targetDirection =
    associationTablePos.x > currentTablePos.x ? "in" : "out";
  const edge = {
    source: {
      cell: currentTable, // 源节点
      port: `${currentTable}-${currentTableField}-${sourceDirection}`,
      realPortId: `${currentTable}-${currentTableField}`, // 用来高亮
    },
    target: {
      cell: associationTable,
      port: `${associationTable}-${associationTableField}-${targetDirection}`,
    },
    connector: {
      name: "rounded",
    },
  };
  graph.value.addEdge(edge);
};

// 删除线回调事件
const removeEdge = (edge: any) => {
  const { source, target } = edge;
  const sourceField = source.port.split("-")[1];
  const targetField = target.port.split("-")[1];
  emit("delete", {
    type: "edge",
    tableName: source.cell,
    field: sourceField,
    target: target.cell,
    targetField,
  });
};

onMounted(() => {
  nextTick(() => {
    if (graph.value) {
      graph.value.dispose(); // 销毁组件
    }
    graph.value = new Graph({
      container: document.getElementById("tableGraph")!,
      autoResize: true,
      grid: false,
      panning: {
        // 拖拽画布
        enabled: true,
        modifiers: "shift", // 需要按shift键才可以进行拖拽画布，避免和其他操作冲突
      },
      connecting: {
        // 连接线的设置
        router: {
          name: "er", // 实体关系，Z字行的斜角线段
          args: {
            offset: 25,
            direction: "H", // 方向
          },
        },
      },
      scroller: {
        enabled: true, // 可以滚动
        pannable: true, // 平移动画
      },
      mousewheel: {
        // 鼠标滚动
        enabled: true,
        modifiers: ["ctrl", "meta"], // ctrl键 + 滚轮，缩放
        minScale: 0.1,
        maxScale: 4,
      },
    });
    graph.value.on("edge:removed", ({ edge }: any) => {
      removeEdge(edge);
    });
    graph.value.on("edge:mouseenter", ({ edge }: any) => {
      edge.addTools([
        {
          name: "button-remove",
          args: {
            distance: -30,
          },
        },
      ]);
    });
    // 连接线鼠标移出
    graph.value.on("edge:mouseleave", ({ edge }: any) => {
      edge.removeTools();
    });
  });
});

defineExpose({
  addNode,
  addEdge,
});
</script>

<style scoped lang="scss">
.table-associations {
  height: 100%;
  width: 100%;
}
.graph {
  width: 100%;
  height: 100%;
}
</style>
