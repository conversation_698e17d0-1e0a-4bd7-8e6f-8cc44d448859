<template>
  <el-dialog
    v-model="dialogVisible"
    title="标记TID"
    width="600px"
    @close="handleClose"
    destroy-on-close
    align-center
  >
    <el-form
      class="associations-form"
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="表" prop="table">
        <el-select
          v-model="formData.table"
          placeholder="请选择"
          @change="changeTable"
          clearable
        >
          <el-option
            v-for="item in tables"
            :key="item.modelId"
            :value="item.modelId"
            :label="item.tableName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="表字段" prop="field">
        <el-select
          v-model="formData.field"
          :disabled="!formData.table"
          placeholder="请选择"
          clearable
        >
          <el-option
            v-for="item in fieldList"
            :key="item.column"
            :value="item.column"
            :label="item.column"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import type { IColumn, IDatabase, ITidMark } from "@/types/tool";
import { ApiDatabaseDetail } from "@/api/tool/index";

const props = defineProps({
  handleId: String,
  tables: Array<IDatabase>,
  field: String,
});

const formRef = ref();

const emit = defineEmits(["close", "confirm"]);

const dialogVisible = ref(true);

const fieldList = ref<IColumn[]>([]);

const formData = ref<{
  table: string;
  field: string;
}>({
  table: "",
  field: "",
});

const rules = {
  table: [
    {
      required: true,
      message: "请选择表",
      trigger: "blur",
    },
  ],
  field: [
    {
      required: true,
      message: "请选择表字段",
      trigger: "blur",
    },
  ],
};

const changeTable = (val: string) => {
  formData.value.field = "";
  if (!val) {
    fieldList.value = [];
    return;
  }
  ApiDatabaseDetail({ modelId: val, handleId: props?.handleId || "" }).then(
    (res: any) => {
      fieldList.value = res.tableColumnVOS || [];
    }
  );
};

const handleClose = () => {
  emit("close");
};

const handleConfirm = () => {
  formRef.value.validate((valid: any) => {
    if (valid) {
      const selectedTable = props.tables?.filter(
        (item: IDatabase) => item.modelId === formData.value.table
      )[0];
      const selectedColum = fieldList.value.filter(
        (item: IColumn) => item.column === formData.value.field
      )[0];
      const data: ITidMark = {
        modelId: selectedTable?.modelId || "", // 表modelId
        tableName: selectedTable?.tableName || "", // 表名
        column: selectedColum.column, // 字段
        columnAlias: selectedColum.columnAlias, // 字段别名
      };
      emit("confirm", data);
    }
  });
};
</script>

<style scoped lang="scss">
.associations-form {
  .el-select {
    width: 100%;
  }
}
</style>
