<template>
  <div class="parsing-statements">
    <div class="parsing-statements-left" v-loading="treeLoading">
      <div class="parsing-statements-catalog">表目录</div>
      <el-tree
        :data="dataSource"
        node-key="id"
        default-expand-all
        :expand-on-click-node="false"
      >
        <template #default="{ data }">
          <div
            class="custom-tree-node"
            :class="{
              'is-selected': selectedTableIds.includes(data.modelId),
            }"
            @click="add(data)"
          >
            <span class="custom-tree-node-label">
              <div style="width: 182px; display: inline-block">
                <ellipsisText
                  :value="data.tableName"
                  :copy="false"
                ></ellipsisText>
              </div>
            </span>
            <el-icon v-if="!data.children"><ArrowRight /></el-icon>
          </div>
        </template>
      </el-tree>
    </div>
    <div class="parsing-statements-right" v-loading="buildingBoxLoading">
      <div class="building-associations">
        <div>构建关联</div>
        <div class="parsing-statements-sql-btn">
          <el-button size="small" class="mark-tid" @click="handleMarkTid"
            >标记TID</el-button
          >
          <el-button
            type="primary"
            size="small"
            :loading="buildLoading"
            @click="handleBuild"
          >
            构建
          </el-button>
        </div>
      </div>
      <div class="building-associations-graph">
        <TableAssociations
          ref="tableAssociationsRef"
          :selected-table-map="selectedTableMap"
          @associate="handleAssociate"
          @delete="handleDelete"
          @select="handleSelectField"
        ></TableAssociations>
      </div>
      <div class="parsing-statements-sql">
        <div class="parsing-statements-sql-title">解析语句</div>
        <div class="no-code" v-if="!code">
          <img src="@/assets/blank.svg" />
          <div class="no-code-text">选择需要关联的字段构建解析语句</div>
        </div>
        <el-input v-else v-model="code" resize="none" type="textarea" />
      </div>
    </div>
  </div>
  <AssociationsDialog
    v-if="showAssociationDialog"
    :handleId="data?.id"
    :table="associateTable"
    :fields="associateTableFields"
    :selected-tables="selectedTables"
    @close="showAssociationDialog = false"
    @confirm="associationConfirm"
  ></AssociationsDialog>
  <MarkTid
    v-if="showMarkTid"
    :handleId="data?.id"
    :tables="selectedTables"
    @close="showMarkTid = false"
    @confirm="markTidConfirm"
  ></MarkTid>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, provide } from "vue";
import { ElMessage } from "element-plus";
import {
  ApiDatabaseList,
  ApiDatabaseDetail,
  ApiBuildSql,
} from "@/api/tool/index";
import type { IDatabase, ITidMark, IAssociation } from "@/types/tool";
import ellipsisText from "@/components/ellipsisText/index.vue";
import { allTables, fieldMap } from "../constant";
import TableAssociations from "./table-associations.vue";
import AssociationsDialog from "./associations-dialog.vue";
import MarkTid from "./mark-tid.vue";

const props = defineProps({
  data: Object, // 通道信息
});

const tid = ref<ITidMark>();

provide("tid", tid);

const selectedTables = ref<IDatabase[]>([]); // 选中的表

const selectedTableMap = ref<Record<string, any>>({}); // 关联关系

const selectedTableFieldMap = ref<Record<string, any>>({}); // 表对应的字段选中

const tableAssociationsRef = ref();

const dataSource = ref<
  { modelId: string; tableName: string; children?: IDatabase[] }[]
>([]);

const fields: Record<string, any> = {};

const showAssociationDialog = ref(false); // 展示关联表字段弹窗

const showMarkTid = ref(false); // 标识TID

const associateTable = ref(""); // 关联表字段弹窗-当前表
const associateTableFields = ref<any[]>([]); // 关联表字段弹窗-当前表字段

const buildLoading = ref(false);

const buildingBoxLoading = ref(false);

const treeLoading = ref(false);

// 选中表的ID
const selectedTableIds = computed(() =>
  selectedTables.value.map((item: any) => item.modelId)
);

const code = ref("");

// 选择字段
const handleSelectField = (data: { table: string; tableFields: any[] }) => {
  console.log("====选中字段======:", data);
  const { table, tableFields } = data;
  if (!selectedTableFieldMap.value[table]) {
    selectedTableFieldMap.value[table] = tableFields;
  } else {
    if (tableFields.length) {
      selectedTableFieldMap.value[table] = tableFields;
    } else {
      delete selectedTableFieldMap.value[table];
    }
  }
};

// 标记TID
const handleMarkTid = () => {
  showMarkTid.value = true;
};

// 标记TID成功
const markTidConfirm = (data: ITidMark) => {
  showMarkTid.value = false;
  tid.value = data;
};
// 删除表/字段
const handleDelete = (data: {
  type: string;
  tableName: string;
  target: string;
  field: string;
  targetField: string;
}) => {
  console.log("==删除节点==:", data);
  // 删除节点
  if (data.type === "cell") {
    // 如果tid就在所删除的表里，就需要把tid也删除
    if (tid.value?.tableName && data.tableName === tid.value?.tableName) {
      tid.value = undefined;
    }
    delete selectedTableMap.value[data.tableName]; // 删除存的表
    delete selectedTableFieldMap.value[data.tableName]; // 删除表选择的字段
    selectedTables.value = JSON.parse(
      JSON.stringify(selectedTables.value)
    ).filter((item: any) => item.tableName !== data.tableName);
    return;
  }
  // 删除线
  const edges = selectedTableMap.value[data.tableName];
  const tempEdges: any = [];
  console.log("线集合:", edges);
  edges.forEach((edge: any) => {
    const {
      currentTable,
      associationTable,
      currentTableField,
      associationTableField,
    } = edge;
    if (
      `${currentTable}-${associationTable}` !==
        `${data.tableName}-${data.target}` ||
      `${currentTableField}-${associationTableField}` !==
        `${data.field}-${data.targetField}`
    ) {
      tempEdges.push(edge);
    }
  });
  selectedTableMap.value[data.tableName] = tempEdges;
  console.log("===删除之后的数据===：", selectedTableMap.value);
};

// 关联字段开始
const handleAssociate = (data: { tableName: string; tableFields: any[] }) => {
  if (selectedTables.value.length < 2) {
    ElMessage.warning("至少有2张表才能进行管理，请添加表");
    return;
  }
  associateTable.value = data.tableName;
  associateTableFields.value = data.tableFields;
  showAssociationDialog.value = true;
};

// 关联字段成功
const associationConfirm = (data: {
  currentTable: string;
  currentTableField: string;
  associationTable: string;
  associationTableField: string;
}) => {
  // 相同的线不能重复连接
  showAssociationDialog.value = false;
  if (selectedTableMap.value[data.currentTable]?.length) {
    const currentTableFields = selectedTableMap.value[data.currentTable].map(
      (item: any) => item.currentTableField
    );
    const associationTableFields = selectedTableMap.value[
      data.currentTable
    ].map((item: any) => item.associationTableField);
    if (
      currentTableFields.includes(data.currentTableField) &&
      associationTableFields.includes(data.associationTableField)
    ) {
      ElMessage.warning("不能重复关联");
      return;
    }

    selectedTableMap.value[data.currentTable].push(data);
  } else {
    selectedTableMap.value[data.currentTable] = [data];
  }

  tableAssociationsRef.value.addEdge(data);
};

// 增加表
const add = async (data: { modelId: string; tableName: string }) => {
  if (selectedTableIds.value.includes(data.modelId)) {
    ElMessage.warning("该表已存在，不能重复添加");
    return;
  }
  // 没有modelId，表示是数据库，点击不需要获取表字段
  if (!data.modelId) {
    return;
  }
  selectedTableMap.value[data.tableName] = [];
  buildingBoxLoading.value = true;
  ApiDatabaseDetail({ modelId: data.modelId, handleId: props?.data?.id })
    .then((res: any) => {
      if (
        !res.tableColumnVOS ||
        !Array.isArray(res.tableColumnVOS) ||
        !res.tableColumnVOS.length
      ) {
        ElMessage.warning("获取表详情失败");
        return;
      }

      const selectedFields = res.tableColumnVOS.filter(
        (item: any) => item.isSelected
      );
      if (selectedFields.length > 0) {
        selectedTableFieldMap.value[data.tableName] = selectedFields;
      }

      selectedTables.value.push(data);
      tableAssociationsRef.value?.addNode({
        tableName: data.tableName,
        tableFields: res.tableColumnVOS,
      });
    })
    .finally(() => {
      buildingBoxLoading.value = false;
    });
};

// 构建
const handleBuild = () => {
  console.log("构建：", selectedTables.value);
  if (!selectedTables.value.length) {
    ElMessage.warning("请选择表");
    return;
  }
  if (!tid?.value?.tableName) {
    ElMessage.warning("请标记TID");
    return;
  }

  const queryFieldsDTOList: any = []; // 表字段勾选状态
  const tableAssociationDTOList: any = []; // 关联关系
  const selectedTableLength = Object.keys(selectedTableMap.value).length; // 选中表的个数
  const associationTables: any = []; // 记录关联表
  Object.keys(selectedTableMap.value).forEach((key) => {
    selectedTableMap.value[key].forEach((item: IAssociation) => {
      associationTables.push(item.currentTable);
      associationTables.push(item.associationTable);
      tableAssociationDTOList.push({
        tableLeft: {
          table: item.currentTable,
          column: item.currentTableField,
          columnAlias: item.currentTableFieldAlias,
        },
        tableRight: {
          table: item.associationTable,
          column: item.associationTableField,
          columnAlias: item.associationTableFieldAlias,
        },
      });
    });
  });

  // 判断是否建立了关联关系
  let associationError = false;
  if (selectedTables.value.length > 1) {
    selectedTables.value.forEach((data: any) => {
      if (!associationTables.includes(data.tableName)) {
        associationError = true;
      }
    });
    if (associationError) {
      ElMessage.warning("请建立所选表之间的关联关系");
      return;
    }
  }

  Object.keys(selectedTableFieldMap.value).forEach((key: string) => {
    if (selectedTableFieldMap.value[key].length) {
      selectedTableFieldMap.value[key].forEach((item: any) => {
        queryFieldsDTOList.push({
          table: key,
          column: item.column,
          columnAlias: item.columnAlias,
        });
      });
    }
  });
  const params = {
    mainTable: tid?.value?.tableName,
    queryFieldsDTOList,
    tableAssociationDTOList,
    queryConditionDTO: {
      column: tid?.value?.column,
      columnAlias: tid?.value?.columnAlias,
    },
  };
  buildLoading.value = true;
  ApiBuildSql(params)
    .then((res) => {
      code.value = res;
    })
    .finally(() => {
      buildLoading.value = false;
    });
};
// 获取数据库列表
const getDatabaseList = () => {
  treeLoading.value = true;
  const params = {
    id: props.data?.id,
    dataServiceId: props.data?.dataServiceId,
    databaseId: props.data?.databaseId,
  };
  ApiDatabaseList(params)
    .then((res) => {
      dataSource.value = [
        {
          modelId: "",
          tableName: props.data?.databaseName,
          children: res.databaseVOS,
        },
      ];
    })
    .finally(() => {
      treeLoading.value = false;
    });
};

onMounted(() => {
  fields.value = fieldMap;
  getDatabaseList();
});

defineExpose({
  code,
  getDatabaseList,
});
</script>

<style scoped lang="scss">
.parsing-statements {
  display: flex;
  flex-direction: row;
  border: 1px solid #dfe4e3;
  border-radius: 2px;
  height: 680px;
}
.parsing-statements-left {
  border-right: 1px solid #dfe4e3;
  min-width: 260px;
  width: 260px;
  .el-tree {
    :deep(.el-tree-node) {
      .el-tree-node__children {
        .is-leaf {
          display: none;
        }
        .custom-tree-node {
          padding-left: 48px;
          height: 36px;
          &.is-selected {
            background-color: #e8f1ef !important;
          }
        }
        .el-tree-node__content {
          padding-left: 0 !important;
        }
      }
      .el-tree-node__content {
        height: 36px;
        padding-left: 12px !important;
        display: flex;
        flex-direction: row;
      }
    }
    :deep(.is-current) {
      background-color: #e8f1ef !important;
      &::before {
        width: 0;
      }
    }
  }
}

.parsing-statements-catalog {
  height: 40px;
  border-bottom: 1px solid #dfe4e3;
  padding-left: 10px;
  line-height: 40px;
  background-color: #eef2f1;
  color: #1d2129;
}

.custom-tree-node {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  flex: 1;
  min-width: 0;
  padding-right: 16px;
  .is-leaf {
    display: none;
  }
  .custom-tree-node-label {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
.parsing-statements-right {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
}
.building-associations {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #dfe4e3;
  height: 40px;
  line-height: 40px;
  padding-left: 10px;
  background-color: #eef2f1;
  color: #1d2129;
}
.building-associations-graph {
  flex: 1;
  min-height: 0;
  position: relative;
}
.parsing-statements-sql-btn {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  padding-right: 16px;
  .el-button {
    height: 28px;
    &.mark-tid {
      border-color: #c1c9c7;
      background-color: #f6f7fb;
      color: #272e2c;
    }
  }
}
.parsing-statements-sql {
  height: 200px;
  min-height: 200px;
  display: flex;
  flex-direction: column;

  .parsing-statements-sql-title {
    height: 40px;
    line-height: 40px;
    color: #1d2129;
    background-color: #eef2f1;
    padding-left: 16px;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
  }
  .no-code {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 160px;

    img {
      width: 100px;
    }

    .no-code-text {
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
      color: #7b9790;
    }
  }

  .el-textarea {
    padding: 16px;
    height: 100%;
  }
  :deep(.el-textarea__inner) {
    border: none !important;
    height: 100% !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    overflow: auto !important;
    background-color: #fff !important;
  }
}
</style>
