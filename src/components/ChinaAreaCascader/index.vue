<template>
  <el-cascader
    :options="reactiveData.areasCas"
    :model-value="modelValue"
    @change="changeArea"
    clearable
    filterable
  ></el-cascader>
</template>

<script setup lang="ts">
import { onMounted, reactive } from "vue";
import { provinceListApi } from "@/api/systemManage/operation";

const emit = defineEmits(["change"]);
const props = defineProps(["modelValue"]);

const reactiveData = reactive<any>({
  areasCas: [],
});

onMounted(() => {
  getAreasCas();
});

const getAreasCas = () => {
  provinceListApi().then((data: any) => {
    const areaCas = [];
    const provinces = data[86];
    for (const province in provinces) {
      const province_item: any = {
        label: provinces[province],
        value: province,
      };
      const provinceChild = [];
      // 如果省下面有市
      if (Object.keys(data).includes(province)) {
        const cities = data[province];
        for (const city in cities) {
          const city_item: any = {
            label: cities[city],
            value: city,
          };
          const cityChild = [];
          // 如果市下面有区
          if (Object.keys(data).includes(city)) {
            const countrys = data[city];
            for (const country in countrys) {
              const country_item = {
                label: countrys[country],
                value: country,
              };
              cityChild.push(country_item);
            }
          }
          if (cityChild.length > 0) {
            city_item.children = cityChild;
          }
          provinceChild.push(city_item);
        }
      }
      if (provinceChild.length > 0) {
        province_item.children = provinceChild;
      }
      areaCas.push(province_item);
    }
    reactiveData.areasCas = areaCas;
  });
};

const changeArea = (val: any) => {
  emit("change", val);
};
</script>
