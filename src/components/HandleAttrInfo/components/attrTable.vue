<template>
  <el-table :key="tableKey" :data="table" size="small" border>
    <el-table-column label="序号" min-width="54">
      <template #default="scope">
        <span>{{ scope.$index + 1 }}</span>
      </template>
    </el-table-column>
    <el-table-column label="中文名称" min-width="264">
      <template #default="scope">
        <ellipsisText
          :refresh="refresh"
          :value="scope.row.description"
        ></ellipsisText>
      </template>
    </el-table-column>
    <el-table-column label="英文名称" min-width="252">
      <template #default="scope">
        <ellipsisText
          :refresh="refresh"
          :value="scope.row.field"
        ></ellipsisText>
      </template>
    </el-table-column>
    <el-table-column label="属性类型" min-width="148">
      <template #default="scope">
        <div>{{ (ATTR_TYPE_NAME_MAP as any)[`${scope.row.fieldType}`] }}</div>
      </template>
    </el-table-column>
    <el-table-column label="所属表" min-width="120">
      <template #default="scope">
        <ellipsisText
          :refresh="refresh"
          :value="scope.row?.tableName"
        ></ellipsisText>
      </template>
    </el-table-column>
    <el-table-column label="所属字段" min-width="120">
      <template #default="scope">
        <ellipsisText
          :refresh="refresh"
          :value="scope.row?.columnName"
        ></ellipsisText>
      </template>
    </el-table-column>

    <el-table-column label="属性值" min-width="140">
      <template #default="scope">
        <ellipsisText
          :refresh="refresh"
          :value="getReferenceDis(scope.row)"
        ></ellipsisText>
      </template>
    </el-table-column>
    <el-table-column label="权限组" min-width="140">
      <template #default="scope">
        <ellipsisText
          :refresh="refresh"
          :value="getAuthGroupDes(scope.row)"
        ></ellipsisText>
      </template>
    </el-table-column>
    <el-table-column label="备注" min-width="148">
      <template #default="scope">
        <ellipsisText
          :refresh="refresh"
          :value="scope.row.remark"
        ></ellipsisText>
      </template>
    </el-table-column>
    <el-table-column label="操作" min-width="160">
      <template #default="scope">
        <el-button
          size="small"
          type="primary"
          text
          @click="handleEdit(scope.$index)"
          >编辑</el-button
        >
        <el-popconfirm
          confirm-button-text="删除"
          cancel-button-text="取消"
          :icon="InfoFilled"
          title="是否删除该属性?"
          @confirm="handleDelete(scope.$index)"
        >
          <template #reference>
            <el-button size="small" type="primary" text>删除</el-button>
          </template>
        </el-popconfirm>
        <el-button
          v-if="scope.row.fieldType !== 3"
          size="small"
          type="primary"
          text
          @click="handleAssociate(scope.$index)"
          >关联权限组</el-button
        >
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { InfoFilled } from "@element-plus/icons-vue";
import { ATTR_TYPE_NAME_MAP } from "@/utils/dataPlatform";
import ellipsisText from "@/components/ellipsisText/index.vue";

const emit = defineEmits(["delete", "edit", "associate"]);

const props = defineProps({
  table: {
    type: Array,
    default: () => [],
  },
  tableKey: {
    type: String,
    default: () => "attrTable",
  },
  refresh: {
    type: Boolean,
    default: false,
  },
});

const getAuthGroupDes = (data: any) => {
  console.log(data.authGroupListVOList);
  if (data.authGroupListVOList && data.authGroupListVOList.length) {
    return data.authGroupListVOList
      .map((authGroup: any) => authGroup.authGroupName)
      .join(",");
  }
  return "-";
};
const getReferenceDis = (data: any) => {
  if (data.references && data.references.length) {
    return data.references
      .map((reference: any) => reference.referenceHandle)
      .join(",");
  }
  return data.fieldValue || "-";
};

// 编辑
const handleEdit = (index: number) => {
  emit("edit", index);
};

// 删除
const handleDelete = (index: number) => {
  emit("delete", index);
};

// 删除
const handleAssociate = (index: number) => {
  emit("associate", index);
};
</script>

<style scoped>
:deep(.el-popper) {
  &.is-dark {
    line-break: break-all;
    max-width: 800px !important;
  }
}
</style>
