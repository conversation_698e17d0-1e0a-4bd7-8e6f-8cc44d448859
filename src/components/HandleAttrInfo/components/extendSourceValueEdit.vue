<template>
  <el-form
    style="width: 100%"
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="100px"
  >
    <el-form-item class="form-item" label="所属表" prop="tableName">
      <el-select
        :validate-event="false"
        :popper-append-to-body="false"
        v-model="form.tableName"
        placeholder="请选择"
        filterable
        clearable
        @change="handleTableNameChange(form)"
      >
        <el-option
          v-for="item in table"
          :key="item.modelCode"
          :value="item.modelCode"
          :label="item.modelCode"
        >
          <span>{{ item.modelName }}/{{ item.modelCode }}</span>
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item class="form-item" label="所属字段" prop="columnMap">
      <el-select
        :validate-event="false"
        :popper-append-to-body="false"
        v-model="form.columnMap"
        placeholder="请选择"
        filterable
        clearable
        value-key="columnName"
        :disabled="!form.tableName"
        @focus="handleColumnFocus(form)"
      >
        <el-option
          v-for="item in columns"
          :key="item.columnName"
          :value="item"
          :label="item.columnCode"
        >
          <span>{{ item.columnName }}/{{ item.columnCode }}</span>
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item class="form-item" label="中文名称" required>
      <ellipsisText :value="form.columnMap.columnName"></ellipsisText>
    </el-form-item>
    <el-form-item class="form-item" label="英文名称" required>
      <ellipsisText :value="form.columnMap.columnCode"></ellipsisText>
    </el-form-item>
    <el-form-item class="form-item form-item-pl10" label="属性值">
      <HandleTag v-model="form.fieldValue"></HandleTag>
    </el-form-item>
    <el-form-item class="form-item form-item-pl10" label="备注">
      <ellipsisText :value="form.columnMap.columnDesc"></ellipsisText>
    </el-form-item>
  </el-form>
</template>
<script setup lang="ts">
import { reactive, ref, onMounted } from "vue";
import {
  ApiObjectHandleDataModelDetail,
  ApiEntityObjectDetailList,
} from "@/api/handleRegister/index";
import ellipsisText from "@/components/ellipsisText/index.vue";
import HandleTag from "./handleTag.vue";

const formRef = ref();

const props = defineProps({
  fields: Object,
  descriptions: Object,
  data: Object,
  entityObjectId: String, // 实体对象ID
});

const rules = reactive({
  remark: [
    {
      required: false,
      max: 100,
      message: "最大长度为100字符",
    },
  ],
  tableName: [
    {
      required: true,
      validator: tableNameRulesValidate,
    },
  ],
  columnMap: [
    {
      required: true,
      validator: columnNameRulesValidate,
    },
  ],
});

const form = reactive<any>({
  tableName: "",
  columnMap: {
    columnName: "",
    columnCode: "",
    columnDesc: "",
  },
  remark: "",
  fieldValue: "",
});

const table = ref<any[]>([]);
const columns = ref<any[]>([]);

const handleColumnFocus = (data: any) => {
  const id = table.value.filter(
    (item: any) => item.modelCode === data.tableName
  )[0].modelId;
  return ApiObjectHandleDataModelDetail(id).then((res) => {
    try {
      columns.value = res.columnList;
      if (form.columnMap.columnName) {
        try {
          form.columnMap.columnCode = columns.value.filter(
            (item: any) => item.columnName === form.columnMap.columnName
          )[0].columnCode;
        } catch (error) {
          console.log(error);
        }
      }
    } catch (error) {
      columns.value = [];
    }
  });
};

const handleTableNameChange = (data: any) => {
  form.columnMap = {
    columnName: "",
    columnCode: "",
    columnDesc: "",
  };
};

async function columnNameRulesValidate(rule: any, val: any, callback: any) {
  if (!val) {
    callback("请选择所属字段");
    return;
  }
  callback();
}

async function tableNameRulesValidate(rule: any, val: any, callback: any) {
  if (!val) {
    callback("请选择所属表");
    return;
  }
  callback();
}

const formValidate = async () => {
  let isError = false;
  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  return isError;
};

const getData = () => {
  const oldData = props.data ? { ...props.data } : {};
  const newData = {
    field: form.columnMap.columnCode, // 英文名称
    description: form.columnMap.columnName, // 中文名称
    remark: form.columnMap.columnDesc, // 属性描述
    tableName: form.tableName, // 所属表
    columnName: form.columnMap.columnCode, // 所属字段
    fieldValue: form.fieldValue.join(","), // 属性值
  };
  return Object.assign(oldData, newData);
};
// 获取实体对象
const getDetail = () => {
  return ApiEntityObjectDetailList(props?.entityObjectId || "").then(
    (res: any) => {
      try {
        table.value = res.relationDataModel;
      } catch (error) {
        table.value = [];
      }
    }
  );
};

onMounted(async () => {
  console.log("====扩展属性-标识解析数据源-编辑弹窗=====:", props.data);
  await getDetail();
  if (props?.data) {
    const { data } = props;
    form.fieldValue = data.fieldValue ? data.fieldValue.split(",") : []; // 属性值
    form.tableName = data.tableName;
    form.remark = data.remark;
    form.columnMap = {
      columnName: data.description,
      columnCode: data.field,
      columnDesc: data.remark,
    };
    if (form.tableName) {
      await handleColumnFocus(form);
    }
  }
});

defineExpose({
  formValidate,
  getData,
});
</script>
<style lang="scss" scoped>
.form-item {
  :deep(.el-select) {
    width: 100%;
  }
}
</style>
