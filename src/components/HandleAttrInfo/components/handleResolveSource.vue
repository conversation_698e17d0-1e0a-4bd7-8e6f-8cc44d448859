<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="100px"
    v-loading="loading"
    style="width: 100%"
  >
    <el-form-item label="数据库" prop="database">
      <div class="data-service-wrap">
        <el-select
          v-model="form.dataServiceId"
          placeholder="请选择数据服务"
          @change="handleServiceIdChange"
        >
          <el-option
            v-for="item in serviceList"
            :key="item.id"
            :value="item.id"
            :label="item.dataServiceName"
          ></el-option>
        </el-select>
        <el-select
          v-model="form.databaseId"
          @change="handleDatabaseChange"
          placeholder="请选择数据库"
        >
          <el-option
            v-for="item in databaseList"
            :key="item.databaseId"
            :value="item.databaseId"
            :label="item.databaseName"
          ></el-option>
        </el-select>
      </div>
    </el-form-item>
    <el-form-item>
      <div class="data-channels" v-if="form.databaseId">
        <div class="data-channels-left">
          <div class="data-channels-left-header">可选表</div>
          <div class="data-channels-left-body">
            <div class="table-search">
              <el-input
                v-model="filterText"
                placeholder="请输入表"
                :suffix-icon="Search"
              />
            </div>

            <el-tree
              ref="treeRef"
              :data="dataChannels"
              :props="defaultProps"
              :load="loadNode"
              node-key="id"
              lazy
              show-checkbox
              :filter-node-method="filterNode"
              @check="handleCheck"
            >
              <template #default="{ node }">
                <span class="custom-tree-node">
                  <div style="width: 100px; display: inline-block">
                    <ellipsisText :value="node.label"></ellipsisText>
                  </div>
                </span> </template
            ></el-tree>
          </div>
        </div>

        <div class="data-channels-right">
          <div class="data-channels-right-header">
            <div class="">已选择项</div>
            <div
              class="delete-wrap"
              :class="{ 'is-disabled': !form.fieldList.length }"
              @click="handleDelAll"
            >
              <el-icon>
                <Delete />
              </el-icon>
            </div>
          </div>
          <div class="data-channels-right-body">
            <div
              class="data-channels-right-blank"
              v-show="!dataChannels.length || !form.fieldList.length"
            >
              <img src="@/assets/blank.svg" />
              <div
                class="blank-text"
                v-if="dataChannels.length && !form.fieldList.length"
              >
                请选择表
              </div>
            </div>
            <el-table
              v-show="form.fieldList.length"
              :data="form.fieldList"
              size="small"
              height="394px"
              border
            >
              <el-table-column min-width="168">
                <template #header>
                  <div class="table-required">
                    <span class="table-required-icon">*</span
                    ><span>英文名称</span>
                  </div>
                </template>
                <template #default="scope">
                  <el-form-item
                    inline-message
                    :prop="'fieldList.' + scope.$index + '.field'"
                    :rules="[{ required: true, validator: fieldValidate }]"
                  >
                    <el-input
                      v-model.trim="scope.row.field"
                      placeholder="输入英文名称"
                      clearable
                      :validate-event="false"
                    ></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column
                :min-width="category === ATTR_CATEGORY.EXTEND ? '142' : '168'"
              >
                <template #header>
                  <div class="table-required">
                    <span class="table-required-icon">*</span
                    ><span>中文名称</span>
                  </div>
                </template>
                <template #default="scope">
                  <el-form-item
                    inline-message
                    :prop="'fieldList.' + scope.$index + '.description'"
                    :rules="[
                      { required: true, validator: descriptionValidate },
                    ]"
                  >
                    <el-input
                      v-model.trim="scope.row.description"
                      placeholder="输入中文名称"
                      clearable
                      :validate-event="false"
                    ></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="表" min-width="74">
                <template #default="scope">
                  <ellipsisText :value="scope.row.tableName"></ellipsisText>
                </template>
              </el-table-column>
              <el-table-column
                label="字段"
                :min-width="category === ATTR_CATEGORY.EXTEND ? '144' : '172'"
              >
                <template #default="scope">
                  <ellipsisText :value="scope.row.columnName"></ellipsisText>
                </template>
              </el-table-column>

              <el-table-column
                v-if="category === ATTR_CATEGORY.EXTEND"
                min-width="182"
                label="属性值"
              >
                <template #default="scope">
                  <HandleTag v-model="scope.row.fieldValue"></HandleTag>
                </template>
              </el-table-column>
              <el-table-column min-width="182" label="备注">
                <template #default="scope">
                  <el-form-item
                    :prop="'fieldList.' + scope.$index + '.remark'"
                    :rules="[
                      {
                        required: false,
                        max: 100,
                        message: '最大长度为100字符',
                      },
                    ]"
                  >
                    <el-input
                      v-model.trim="scope.row.remark"
                      placeholder="输入备注"
                      clearable
                    ></el-input>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column min-width="67" label="操作">
                <template #default="scope">
                  <el-button
                    type="primary"
                    text
                    @click="handleDel(scope.$index)"
                    >删除</el-button
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </el-form-item>
  </el-form>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, reactive, watch } from "vue";
import type Node from "element-plus/es/components/tree/src/model/node";
import { ElMessage } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import {
  ApiServiceList,
  ApiDataChannelDatabaseList,
  ApiDatabaseTableList,
  ApiTableColumnList,
} from "@/api/tool/index";
import ellipsisText from "@/components/ellipsisText/index.vue";
import type { IServiceList, IChannelDatabase } from "@/types/tool";
import { ATTR_CATEGORY } from "@/utils/dataPlatform";
import HandleTag from "./handleTag.vue";

const props = defineProps({
  type: {
    type: Number,
  },
  category: String, // 属性类别-基础属性/扩展属性
  columnNames: Array, // 所属字段
  fields: Object, // 英文名称
  descriptions: Object, // 中文名称
});

const formRef = ref();

const loading = ref(false);

const serviceList = ref<IServiceList[]>([]);

const databaseList = ref<IChannelDatabase[]>([]);

const filterText = ref("");

const treeRef = ref();

// 可选通道
const dataChannels = ref<any[]>([]);

// 存储fieldData
const fieldIdMap = ref<any>({});

const databaseValidate = (rule: any, value: any, callback: any) => {
  if (!form.dataServiceId) {
    return callback("请选择数据服务");
  }
  if (!form.databaseId) {
    return callback("请选择数据库");
  }
  callback();
};

const fieldValidate = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback("请填写英文名称");
  }
  if (value.length > 255) {
    return callback("最大长度为255字符");
  }
  if (props.fields?.includes(value)) {
    return callback("英文名称已存在");
  }

  const currentFields = form.fieldList.filter(
    (item: any) => item.field === value
  );
  if (currentFields.length > 1) {
    return callback("英文名称已存在");
  }

  callback();
};

const descriptionValidate = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback("请填写中文名称");
  }
  if (value.length > 255) {
    return callback("最大长度为255字符");
  }
  if (props.descriptions?.includes(value)) {
    return callback("中文名称已存在");
  }
  const currentDescriptions = form.fieldList.filter(
    (item: any) => item.description === value
  );
  if (currentDescriptions.length > 1) {
    return callback("中文名称已存在");
  }

  callback();
};

const defaultProps = {
  children: "children",
  label: "name",
  disabled: "disabled",
  isLeaf: "leaf",
};

const form = reactive<any>({
  fieldList: [],
  dataServiceId: "",
  databaseId: "",
});

const rules = {
  database: {
    required: true,
    validator: databaseValidate,
  },
};

// 存储通道与字段的map
const channelFieldMap = computed(() => {
  const newObj = {};
  dataChannels.value.forEach((item: any) => {
    (newObj as any)[`${item.id}`] = true;
  });
  return newObj;
});

// 记录之前选中的nodes
const oldCheckedKeys = ref<any>([]);

watch(
  () => filterText.value,
  (val) => {
    treeRef.value!.filter(val);
  }
);

const filterNode = (value: string, data: any) => {
  if (!value) return true;
  return data.name.includes(value);
};

const getTempId = (id: any, field: any) => {
  return `${id}::${field}`;
};

const loadNode = (node: Node, resolve: (data: any[]) => void) => {
  if (node.level === 0) {
    return resolve(dataChannels.value);
  }
  if (node.level >= 1) {
    ApiTableColumnList(node.data.id).then((res) => {
      if (Array.isArray(res) && res.length) {
        const result = res.map((resItem) => {
          const tempId = getTempId(node.data.id, resItem.columnCode);
          const tempResult = {
            leaf: true,
            name: resItem.columnCode, // 字段
            field: resItem.columnCode, // 英文名称
            description: resItem.columnName, // 中文名称
            tableName: node.data.name, // 表
            columnName: resItem.columnCode,
            id: tempId,
            disabled: props.columnNames?.includes(resItem.columnCode),
          };
          if (!fieldIdMap.value[tempId]) {
            fieldIdMap.value[tempId] = tempResult;
          }

          return { ...tempResult };
        });
        dataChannels.value.forEach((item: any) => {
          if (item.id === node.data.id) {
            item.disabled = false; // 全选恢复为可选状态
          }
        });
        resolve(result);
      } else {
        resolve([]);
      }
    });
  }
};

// 重置表格数据
const resetTableData = () => {
  filterText.value = "";
  dataChannels.value = [];
  form.fieldList = [];
};
// 数据库更新
const handleDatabaseChange = () => {
  resetTableData();
  if (!form.databaseId) return;
  ApiDatabaseTableList(form.databaseId).then((res) => {
    if (Array.isArray(res) && res.length > 0) {
      dataChannels.value = res.map((item) => {
        return {
          id: item.tableId,
          name: item.tableName,
          disabled: true, // 默认是置灰
        };
      });
    } else {
      dataChannels.value = [];
    }
  });
};

// 数据服务更新
const handleServiceIdChange = (val: any) => {
  databaseList.value = [];
  form.databaseId = "";
  resetTableData();
  if (val) {
    getDatabaseList();
  }
};

// 获取数据服务列表
const getServiceList = () => {
  ApiServiceList().then((res) => {
    serviceList.value = Array.isArray(res) ? res : [];
  });
};

const getDatabaseList = () => {
  ApiDataChannelDatabaseList(form.dataServiceId).then((res) => {
    if (!res) {
      databaseList.value = [];
      return;
    }
    databaseList.value = Array.isArray(res) ? res : [];
  });
};

const handleCheck = (currentNode: any, selectNodes: any) => {
  let currentNodeCopy: any = {};
  try {
    currentNodeCopy = JSON.parse(JSON.stringify(currentNode));
  } catch (error) {
    console.log("error:", error);
  }

  console.log("当前选中的节点:", currentNodeCopy);
  console.log("所有选中的值:", selectNodes);

  const { checkedKeys, checkedNodes } = selectNodes;
  // 判断当前选择的是渠道
  const isChannel = (channelFieldMap.value as any)[`${currentNodeCopy.id}`];
  if (isChannel) {
    const isSelectAll = checkedKeys.length > oldCheckedKeys.value.length;
    // 全选
    if (isSelectAll) {
      // 找出需要添加的值, 存的是key
      const addFields: any = [];
      // 记录已存在的值
      const existFields: any = [];
      // 找出能添加的值
      const ableFields: any = [];
      checkedKeys.forEach((checkedItem: any) => {
        if (!oldCheckedKeys.value.includes(checkedItem)) {
          addFields.push(checkedItem);
        }
      });
      addFields.forEach((fieldItem: any) => {
        const isExist = isExistFieldFn(fieldItem);
        if (!isExist) {
          const matchFieldItem = checkedNodes.filter(
            (checkItem: any) => checkItem.id === fieldItem
          );
          if (matchFieldItem[0].field) {
            ableFields.push(matchFieldItem[0]);
          }
        }
        if (isExist) {
          ElMessage.warning("字段已存在，不能重复添加");
          existFields.push(fieldItem);
        }
      });
      if (existFields.length) {
        // 不能全选
        existFields.forEach((existItem: any) => {
          const checkedIndex = selectNodes.checkedKeys.indexOf(existItem);
          selectNodes.checkedKeys.splice(checkedIndex, 1);
        });
        treeRef.value.setCheckedKeys(selectNodes.checkedKeys);
      }
      if (ableFields.length) {
        form.fieldList = form.fieldList.concat(ableFields);
      }
    }
    // 取消全选
    if (!isSelectAll) {
      // 找到需要删除的值
      const delFields: any = [];
      oldCheckedKeys.value.forEach((checkedItem: any) => {
        if (!checkedKeys.includes(checkedItem)) {
          delFields.push(checkedItem);
        }
      });
      const tempFields: any = [];
      form.fieldList.forEach((fieldItem: any) => {
        if (!delFields.includes(fieldItem.id)) {
          tempFields.push(fieldItem);
        }
      });
      form.fieldList = tempFields;
    }
  }
  if (!isChannel) {
    // 选中
    const isSelected = selectNodes.checkedKeys.includes(currentNodeCopy.id);
    if (!isSelected) {
      // 取消选择
      const index = getIndexInArray(form.fieldList, currentNodeCopy.id);
      form.fieldList.splice(index, 1);
    } else {
      // 选中
      const isExistField = isExistFieldFn(currentNodeCopy.columnCode);
      if (!isExistField) {
        // 不存在-增加
        form.fieldList.push(currentNodeCopy);
      }
      if (isExistField) {
        // 存在-不增加
        ElMessage.warning("字段已存在，不能重复添加");
        const checkedIndex = selectNodes.checkedKeys.indexOf(
          currentNodeCopy.id
        );
        selectNodes.checkedKeys.splice(checkedIndex, 1);
        treeRef.value.setCheckedKeys(selectNodes.checkedKeys);
      }
    }
  }
  // 记录选中的keys，用于比较当前是全选还是非全选
  oldCheckedKeys.value = treeRef.value.getCheckedKeys();
};

// 判断是否包含field
const isExistFieldFn = (field: string) => {
  let isExist = false;
  form.fieldList.forEach((fieldItem: any) => {
    if (fieldItem.id === field) {
      isExist = true;
    }
  });
  return isExist;
};

// 获取数组中的index
const getIndexInArray = (data: any, id: any) => {
  let index = -1;
  data.forEach((item: any, itemIndex: any) => {
    if (id === item.id) {
      index = itemIndex;
    }
  });
  return index;
};

const formValidate = async () => {
  let isError = false;
  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  if (!form.fieldList.length) {
    ElMessage.error("请选择表");
    isError = true;
  }

  return isError;
};

const handleDel = (index: number) => {
  const { id } = form.fieldList[index];
  form.fieldList.splice(index, 1);
  formValidate();
  const treeCheckedKeys = treeRef.value.getCheckedKeys();
  const checkedIndex = treeCheckedKeys.indexOf(id);
  treeCheckedKeys.splice(checkedIndex, 1);
  // 拿到父级，需要将父级也取消选中
  const dataSourceId = id.split("::")[0];
  let dataSourceIdIndex = -1;
  treeCheckedKeys.forEach((keyItem: any, index: any) => {
    if (`${keyItem}` === `${dataSourceId}`) {
      dataSourceIdIndex = index;
    }
  });
  if (dataSourceIdIndex !== -1) {
    treeCheckedKeys.splice(dataSourceIdIndex, 1);
  }

  treeRef.value.setCheckedKeys(treeCheckedKeys);
  oldCheckedKeys.value = treeCheckedKeys;
};

const resetForm = () => {
  form.dataServiceId = "";
  form.dataService = "";
  form.fieldList = [];
  formRef.value.resetFields();
};

const getData = () => {
  console.log("form.databaseId:", form.databaseId);
  console.log("databaseList:", databaseList.value);
  const selectedDatabase = databaseList.value.filter((item) => {
    return `${item.databaseId}` === `${form.databaseId}`;
  })[0];
  console.log("选中的selectedDatabase==：", selectedDatabase);
  return form.fieldList.map((fieldItem: any) => {
    const result: any = {
      field: fieldItem.field,
      description: fieldItem.description,
      remark: fieldItem.remark,
      tableName: fieldItem.tableName,
      columnName: fieldItem.columnName,
      // databaseName: selectedDatabase?.databaseName,
      // databaseIp: (selectedDatabase as any)?.databaseIp,
      // dataServiceId: form.dataServiceId,
    };
    result.fieldValue =
      Array.isArray(fieldItem.fieldValue) && fieldItem.fieldValue.length > 0
        ? fieldItem.fieldValue.join(",")
        : "";
    return {
      ...result,
    };
  });
};

const handleDelAll = () => {
  if (!form.fieldList.length) {
    return;
  }
  form.fieldList = [];
  treeRef.value.setCheckedKeys([]);
  oldCheckedKeys.value = [];
};

onMounted(() => {
  getServiceList();
});

defineExpose({
  getData,
  resetForm,
  formValidate,
});
</script>

<style lang="scss" scoped>
.el-table {
  .is-error {
    height: 70px;
    :deep(.el-form-item__error) {
      top: 50px;
    }
  }
}
.data-service-wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .el-select {
    width: 216px;
    margin-right: 16px;
  }
}
.data-channels {
  height: 436px;
  width: 1160px;
  display: flex;
  flex-direction: row;
  border: 1px solid #dfe4e3;
  border-radius: 2px;
  margin-left: -100px;
}

.data-channels-left {
  width: 200px;
  border-right: 1px solid #dfe4e3;
  display: flex;
  flex-direction: column;
  .data-channels-left-header {
    height: 40px;
    line-height: 40px;
    width: 100%;
    padding: 0 16px;
    background: #eef2f1;
    color: #1d2129;
    font-weight: 500;
    font-size: 12px;
  }
  .data-channels-left-body {
    flex: 1;
    min-height: 0;

    .table-search {
      padding: 8px 12px;
    }
  }
}
.data-channels-right {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  .data-channels-right-header {
    background: #eef2f1;
    height: 40px;
    width: 100%;
    padding: 0 16px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
    font-size: 12px;
    color: #1d2129;

    .delete-wrap {
      cursor: pointer;
      width: 20px;
      height: 20px;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      &:hover {
        background: #c7d5d1;
        border-radius: 50%;
      }

      &.is-disabled {
        cursor: not-allowed;
      }
    }
  }
  .data-channels-right-body {
    flex: 1;
    min-height: 0;

    .data-channels-right-blank {
      height: 100%;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      img {
        width: 100px;
      }

      .blank-text {
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        color: #7b9790;
      }
    }
  }
}
.custom-tree-node {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 100px;
}

.el-tree {
  height: calc(100% - 50px);
  overflow: auto;

  .is-current::before {
    width: 0;
  }

  .el-tag {
    background: #e8f4ff;
    color: #0057fe;
    border-color: #e8f4ff;
    margin-left: 2px;
  }
}
</style>
