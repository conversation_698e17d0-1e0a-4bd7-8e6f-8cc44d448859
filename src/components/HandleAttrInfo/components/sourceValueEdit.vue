<template>
  <el-form
    style="width: 100%"
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="100px"
  >
    <el-form-item label="属性编码" required>
      <div>{{ form?.entityObjectFieldCode || "-" }}</div>
    </el-form-item>
    <el-form-item label="属性名称" required>
      <div>{{ form?.description || "-" }}</div>
    </el-form-item>
    <el-form-item label="英文名称" required>
      <div>{{ form?.field || "-" }}</div>
    </el-form-item>
    <el-form-item label="备注" required>
      <div>{{ form.remark || "-" }}</div>
    </el-form-item>
    <el-form-item label="所属表" required>
      <el-select
        v-model="form.tableName"
        style="width: 100%"
        filterable
        clearable
        @change="handleTableNameChange"
      >
        <el-option
          v-for="item in tableNames"
          :key="item.modelName"
          :value="item.modelId"
          :label="item.modelCode"
        >
          <span>{{ item.modelName }}/{{ item.modelCode }}</span>
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="所属字段" required>
      <el-select
        v-model="form.columnName"
        style="width: 100%"
        required
        filterable
        clearable
        :disabled="!form.tableName"
      >
        <el-option
          v-for="item in columnNames"
          :key="item.columnName"
          :value="item.columnCode"
          :label="item.columnCode"
        >
          <span>{{ item.columnName }}/{{ item.columnCode }}</span></el-option
        >
      </el-select>
    </el-form-item>
  </el-form>
</template>
<script setup lang="ts">
import { reactive, ref, onMounted } from "vue";
import {
  ApiEntityObjectDetail,
  ApiEntityObjectDetailList,
  ApiDataModelDetail,
} from "@/api/handleRegister/index";

const formRef = ref();

const props = defineProps({
  fields: Object,
  descriptions: Object,
  data: Object,
  entityObjectId: String,
});

// 实体对象
const entityObject = ref<any>();

const tableNames = ref<
  { modelName: string; modelId: string; modelCode: string; modelDesc: string }[]
>([]);

const columnNames = ref<any[]>([]);

const form = ref<any>({
  tableName: "",
  columnName: "",
});

const rules = reactive({
  tableName: [{ required: true, message: "请选择所属表" }],
  columnName: [{ required: true, message: "请选择所属表" }],
});

const getDetail = () => {
  return ApiEntityObjectDetail(props?.entityObjectId || "").then((res: any) => {
    entityObject.value = res[0]; // 返回一条数据
  });
};

// 获取属性列表
const getAttrList = () => {
  return ApiEntityObjectDetailList(entityObject.value.id || "").then((res) => {
    try {
      tableNames.value = res.relationDataModel;
    } catch (error) {
      tableNames.value = [];
    }
  });
};

// 获取属性字段列表
const getColumns = () => {
  ApiDataModelDetail(form.value.tableName).then((res) => {
    columnNames.value = res.columnList;
  });
};

const handleTableNameChange = (val: any) => {
  form.value.columnName = "";
  if (!val) {
    columnNames.value = [];
    return;
  }
  getColumns();
};

const formValidate = async () => {
  let isError = false;

  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  return isError;
};

const getData = () => {
  const temp = { ...form.value };
  let tableName = "";
  try {
    tableName = tableNames.value.filter(
      (modelItem: any) => modelItem.modelId === form.value.tableName
    )[0].modelCode;
  } catch (error) {
    tableName = "";
  }
  temp.tableName = tableName;
  return { ...temp };
};

onMounted(async () => {
  form.value = props.data;
  console.log("sourceValueEdit onMounted data:", form.value);
  console.log(
    "sourceValueEdit onMounted entityObjectId:",
    props.entityObjectId
  );
  await getDetail();
  await getAttrList();
  console.log("===tableNames===:", tableNames.value);
  form.value.tableName = tableNames.value.filter(
    (item: any) => item.modelCode === props.data?.tableName
  )[0].modelId;
  getColumns();
});

defineExpose({
  formValidate,
  getData,
});
</script>
