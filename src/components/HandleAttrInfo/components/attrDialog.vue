<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    append-to-body
    destroy-on-close
    @close="handleDialogCancel"
    width="1200px"
    class="attr-dialog"
    align-center
  >
    <el-form
      ref="formRef"
      label-position="left"
      :model="form"
      label-width="100px"
      width="1200"
      class="handle-attr-info-dialog"
    >
      <el-form-item label="属性类型" required>
        <el-radio-group v-model="form.fieldType" :disabled="!!isEdit">
          <el-radio
            v-for="item in fieldTypeList"
            :key="item.id"
            :label="item.id"
            >{{ item.name }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <!-- 固定值 -->
      <el-row v-if="form.fieldType === ATTR_TYPE_MAP.FIXED">
        <FixedValue
          ref="fixedValueRef"
          :fields="fields"
          :descriptions="descriptions"
          :data="data"
        ></FixedValue>
      </el-row>
      <!-- 标识解析数据源-中台应用-基础属性 -->
      <el-row
        v-if="
          form.fieldType === ATTR_TYPE_MAP.SOURCE &&
          appType === APP_TYPE.DMM &&
          category === ATTR_CATEGORY.BASIC
        "
      >
        <SourceValue
          v-if="!isEdit"
          ref="sourceValueRef"
          :fields="fields"
          :descriptions="descriptions"
          columnNames="columnNames"
          :data="data"
          :entityObjectId="basicInfo?.entityObjectId"
        ></SourceValue>
        <SourceValueEdit
          ref="sourceValueEditRef"
          v-if="isEdit"
          :data="data"
          :entityObjectId="basicInfo?.entityObjectId"
        ></SourceValueEdit>
      </el-row>
      <!-- 标识解析数据源-中台应用-扩展属性 -->
      <el-row
        v-if="
          form.fieldType === ATTR_TYPE_MAP.SOURCE &&
          appType === APP_TYPE.DMM &&
          category === ATTR_CATEGORY.EXTEND
        "
      >
        <ExtendSourceValue
          v-if="!isEdit"
          ref="extendSourceValueRef"
          :fields="fields"
          :descriptions="descriptions"
          :columnNames="columnNames"
          :data="data"
          :entityObjectId="basicInfo?.entityObjectId"
        ></ExtendSourceValue>
        <ExtendSourceValueEdit
          v-if="isEdit"
          ref="extendSourceValueEditRef"
          :fields="fields"
          :descriptions="descriptions"
          :columnNames="columnNames"
          :data="data"
          :entityObjectId="basicInfo?.entityObjectId"
        ></ExtendSourceValueEdit>
      </el-row>
      <!-- 标识解析数据源-非中台应用-基础属性 -->
      <el-row
        v-if="
          form.fieldType === ATTR_TYPE_MAP.SOURCE &&
          appType === APP_TYPE.NORMAL &&
          category === ATTR_CATEGORY.BASIC
        "
      >
        <HandleResolveSource
          v-if="!isEdit"
          ref="handleResolveSourceRef"
          :category="category"
          :fields="fields"
          :descriptions="descriptions"
          :columnNames="columnNames"
        ></HandleResolveSource>
        <HandleResolveSourceEdit
          v-if="isEdit"
          ref="handleResolveSourceEditRef"
          :category="category"
          :data="data"
          :fields="fields"
          :descriptions="descriptions"
          :columnNames="columnNames"
        ></HandleResolveSourceEdit>
      </el-row>
      <!-- 标识解析数据源-非中台应用-扩展属性 -->
      <el-row
        v-if="
          form.fieldType === ATTR_TYPE_MAP.SOURCE &&
          appType === APP_TYPE.NORMAL &&
          category === ATTR_CATEGORY.EXTEND
        "
      >
        <ExtendHandleResolveSource
          v-if="!isEdit"
          ref="extendHandleResolveSourceRef"
          :category="category"
          :fields="fields"
          :descriptions="descriptions"
          :columnNames="columnNames"
        ></ExtendHandleResolveSource>
        <HandleResolveSourceEdit
          v-if="isEdit"
          ref="handleResolveSourceEditRef"
          :category="category"
          :data="data"
          :fields="fields"
          :descriptions="descriptions"
          :columnNames="columnNames"
        ></HandleResolveSourceEdit>
      </el-row>
      <!-- 关联标识 -->
      <el-row v-if="form.fieldType === ATTR_TYPE_MAP.RELATE_HANDLE">
        <RelateHandle
          ref="relateHandleRef"
          :basicInfo="basicInfo"
          :fields="fields"
          :referenceHandles="referenceHandles"
          :relatedFields="relatedFields"
          :data="data"
        ></RelateHandle>
      </el-row>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleDialogCancel">{{ "取消" }}</el-button>
        <el-button type="primary" @click="handleDialogConfirm">
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, ref, toRefs } from "vue";
import { useStore } from "vuex";
import {
  DIALOG_TYPE,
  ATTR_TYPE_MAP,
  ATTR_CATEGORY,
  BASIC_ATTR_TYPE_LIST,
  EXTEND_ATTR_TYPE_LIST,
  APP_TYPE,
} from "@/utils/dataPlatform";
import FixedValue from "./fixedValue.vue";
import SourceValue from "./sourceValue.vue";
import SourceValueEdit from "./sourceValueEdit.vue";
import RelateHandle from "./relateHandle.vue";
import ExtendSourceValue from "./extendSourceValue.vue";
import ExtendSourceValueEdit from "./extendSourceValueEdit.vue";
import HandleResolveSource from "./handleResolveSource.vue";
import HandleResolveSourceEdit from "./handleResolveSourceEdit.vue";
import ExtendHandleResolveSource from "./extendHandleResolveSource.vue";

const store = useStore();
const { appType } = toRefs(store.getters); // 1-中台应用，2-非中台应用

const emit = defineEmits(["close", "add", "edit"]);

const props = defineProps({
  category: String, // 属性类别-基础属性/扩展属性
  type: String, // 属性类型-固定值/标识解析数据源/关联标识
  fields: Object, // 英文名称集合
  descriptions: Object, // 中文名称集合
  data: Object, // 编辑之前的数据
  basicInfo: Object, // 基本信息
  referenceHandles: Array, // 关联标识
  relatedFields: Array, // 关联标识
  columnNames: Array, // 所属字段
  dataNames: Object, // 数据库集合-非中台-标识解析数据源
  dataServiceIds: Object, // 数据服务集合-非中台-标识解析数据源
});

const fixedValueRef = ref();
const sourceValueRef = ref();
const sourceValueEditRef = ref();
const relateHandleRef = ref();
const extendSourceValueRef = ref();
const extendSourceValueEditRef = ref();
const handleResolveSourceRef = ref();
const handleResolveSourceEditRef = ref();
const extendHandleResolveSourceRef = ref();

const dialogVisible = true;
const dialogTitle = ref("");
const isEdit = ref(false);
const fieldTypeList = ref<any[]>([]);

const form = ref({
  fieldType: ATTR_TYPE_MAP.FIXED, // 属性类型
});

const handleDialogCancel = () => {
  emit("close");
};

// 标识解析数据源-非中台应用
const formatNormalHandleSourceData = async () => {
  let isError = false;
  let otherData = {};
  // 标识解析数据源
  if (isEdit.value) {
    isError = await handleResolveSourceEditRef.value.formValidate();
    if (isError) {
      return;
    }
    otherData = handleResolveSourceEditRef.value.getData();
    console.log("===标识解析数据源编辑返回值===:", otherData);
    emit("edit", { fieldType: form.value.fieldType, ...otherData });
    return;
  }
  const addRef: any =
    props.category === ATTR_CATEGORY.BASIC
      ? handleResolveSourceRef.value
      : extendHandleResolveSourceRef.value;

  isError = await addRef.formValidate();
  if (isError) {
    return;
  }
  otherData = addRef.getData().map((item: any) => {
    return { ...item, fieldType: form.value.fieldType };
  });
  console.log("===标识解析数据源新增返回值===:", otherData);
  emit("add", otherData);
};

// 标识解析数据源-中台应用
const formatDMMHandleSourceData = async () => {
  let isError = false;
  let otherData = {};
  if (props.category === ATTR_CATEGORY.BASIC) {
    if (isEdit.value) {
      isError = await sourceValueEditRef.value.formValidate();
      if (isError) {
        return;
      }
      otherData = sourceValueEditRef.value.getData();
      console.log("===标识解析数据源编辑返回值===:", otherData);
      emit("edit", { fieldType: form.value.fieldType, ...otherData });
    } else {
      isError = await sourceValueRef.value.formValidate();
      if (isError) {
        return;
      }
      otherData = sourceValueRef.value.getData().map((item: any) => {
        return { ...item, fieldType: form.value.fieldType };
      });
      console.log("===标识解析数据源新增返回值===:", otherData);
      emit("add", otherData);
    }
  } else {
    if (isEdit.value) {
      isError = await extendSourceValueEditRef.value.formValidate();
      if (isError) {
        return;
      }
      otherData = extendSourceValueEditRef.value.getData();
      emit("edit", { fieldType: form.value.fieldType, ...otherData });
      console.log("===扩展属性编辑的值===:", otherData);
    } else {
      isError = await extendSourceValueRef.value.formValidate();
      if (isError) {
        return;
      }
      otherData = extendSourceValueRef.value.getData().map((item: any) => {
        return { ...item, fieldType: form.value.fieldType };
      });
      console.log("===扩展属性新增的值===:", otherData);
      emit("add", otherData);
    }
  }
};

const handleDialogConfirm = async () => {
  let otherData = {};
  let isError = false;
  switch (form.value.fieldType) {
    case ATTR_TYPE_MAP.FIXED:
      isError = await fixedValueRef.value.formValidate();
      if (isError) {
        return;
      }
      otherData = fixedValueRef.value.getData();
      if (isEdit.value) {
        emit("edit", { fieldType: form.value.fieldType, ...otherData });
      } else {
        emit("add", [{ fieldType: form.value.fieldType, ...otherData }]);
      }

      break;
    case ATTR_TYPE_MAP.SOURCE:
      if (appType.value === APP_TYPE.DMM) {
        formatDMMHandleSourceData();
      } else {
        formatNormalHandleSourceData();
      }
      break;
    case ATTR_TYPE_MAP.RELATE_HANDLE:
      isError = await relateHandleRef.value.formValidate();
      if (isError) {
        return;
      }
      otherData = relateHandleRef.value.getData();
      console.log("====关联标识确定按钮====:", otherData);
      if (isEdit.value) {
        emit("edit", { fieldType: form.value.fieldType, ...otherData });
      } else {
        emit("add", [{ fieldType: form.value.fieldType, ...otherData }]);
      }
      break;
    default:
      break;
  }
};

onMounted(() => {
  console.log("=====新增/编辑属性=====:", props);
  const { type, category, data } = props;
  dialogTitle.value = type === DIALOG_TYPE.ADD ? "添加属性" : "编辑属性";
  isEdit.value = type !== DIALOG_TYPE.ADD;
  console.log("是否是编辑:", isEdit.value);
  fieldTypeList.value =
    category === ATTR_CATEGORY.BASIC
      ? BASIC_ATTR_TYPE_LIST
      : EXTEND_ATTR_TYPE_LIST;
  form.value.fieldType =
    category === ATTR_CATEGORY.BASIC
      ? ATTR_TYPE_MAP.FIXED
      : ATTR_TYPE_MAP.SOURCE;
  if (isEdit.value) {
    form.value.fieldType = data?.fieldType;
  }
});
</script>

<style scoped lang="scss">
.handle-attr-info-dialog {
  height: calc(100% - 48px);
  overflow: auto !important;
}
</style>
