<template>
  <el-form
    ref="formRef"
    label-position="left"
    :model="form"
    label-width="100px"
    :rules="rules"
    style="width: 100%"
  >
    <el-form-item label="关联标识" prop="handleValue">
      <template #label>
        <div class="handle-label-tips">
          <span class="handle-label-tips-text">关联标识</span>
          <el-tooltip placement="top-start" effect="light">
            <template #content>
              <img
                alt=""
                style="width: 650px; height: 300px"
                fit="contain"
                src="@/assets/images/handle/handleReferanceDemo.jpg"
            /></template>
            <span
              ><el-icon><Warning /></el-icon
            ></span>
          </el-tooltip>
        </div>
      </template>
      <el-radio-group v-model="relateType" size="small" class="relate-type">
        <el-radio-button :label="RelateType.BASELINE">基准</el-radio-button>
        <el-radio-button :label="RelateType.COMPATIBLE">兼容</el-radio-button>
      </el-radio-group>
      <el-table :data="form.references" size="small" border>
        <el-table-column label="关联标识">
          <template #default="scope">
            <el-form-item
              :prop="'references.' + scope.$index + '.referenceHandle'"
              :rules="[
                {
                  required: true,
                  validator: referenceHandleValidate,
                },
              ]"
            >
              <el-input
                :validate-event="false"
                v-model.trim="scope.row.referenceHandle"
                @input="() => (scope.row.queryProp = '')"
                @blur="() => getHandleInfo(scope.row.referenceHandle)"
                placeholder="输入关联标识"
                clearable
              ></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="关联标识属性">
          <template #default="scope">
            <el-form-item
              :prop="'references.' + scope.$index + '.queryProp'"
              :rules="[
                {
                  required: true,
                  validator: queryPropRulesValidate,
                },
              ]"
            >
              <span style="margin-right: 10px">-</span>
              <el-select
                :validate-event="false"
                :disabled="fixDisabled || !scope.row.referenceHandle"
                :loading="loading"
                :popper-append-to-body="false"
                v-model="scope.row.queryProp"
                placeholder="请选择关联标识属性"
                :no-data-text="
                  scope.row.referenceHandle ? '无数据' : '请选择关联标识属性'
                "
                filterable
                clearable
              >
                <el-option
                  v-for="item in queryFieldList"
                  :key="item.field"
                  :value="item.field"
                  :label="item.field"
                >
                  <span style="float: left">{{ item.field }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="当前标识">
          <template #default>
            <span>{{ basicInfo?.handle || "" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="当前标识属性"
          v-if="relateType === RelateType.COMPATIBLE"
        >
          <template #default="scope">
            <el-form-item
              :prop="'references.' + scope.$index + '.paramProp'"
              :rules="[
                {
                  required: true,
                  validator: paramPropRulesValidate,
                },
              ]"
            >
              <el-select
                :validate-event="false"
                v-model="scope.row.paramProp"
                placeholder="请选择标识属性"
                :disabled="fixDisabled"
                filterable
                clearable
              >
                <el-option
                  v-for="(item, index) in paramFieldList"
                  :key="index"
                  :value="item"
                  :label="item"
                >
                  <span style="float: left">{{ item }}</span>
                </el-option>
              </el-select></el-form-item
            >
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100px">
          <template #default="scope">
            <el-form style="display: inline">
              <el-button
                type="primary"
                text
                @click="resolveHandle(scope.row.referenceHandle)"
                >解析</el-button
              >
            </el-form>
          </template>
        </el-table-column>
      </el-table>
    </el-form-item>
    <el-form-item label="中文名称" class="form-item-pl10">
      <ellipsisText :value="form.description"></ellipsisText>
    </el-form-item>
    <el-form-item label="英文名称" class="form-item-pl10">
      <ellipsisText :value="form.field"></ellipsisText>
    </el-form-item>
    <el-form-item label="备注" prop="remark" class="form-item-pl10">
      <el-input v-model.trim="form.remark" clearable />
    </el-form-item>
  </el-form>
  <el-dialog
    v-model="dialogResolve"
    title="解析结果"
    append-to-body
    width="800"
    @close="dialogResolve = false"
  >
    <div class="relate-handle-matrix-graph">
      <matrix-graph ref="matrixGraphRef"></matrix-graph>
    </div>
    <template #footer>
      <el-button type="primary" @click="dialogResolve = false">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { reactive, ref, nextTick, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { idResolve } from "@/api/idRes/index";
import matrixGraph from "@/views/idRes/components/matrix-graph.vue";
import { ApiGetResolveField } from "@/api/objHandle/manager";
import { ATTR_TYPE_MAP } from "@/utils/dataPlatform";
import ellipsisText from "@/components/ellipsisText/index.vue";
import { isDuplicateValidate } from "../common";

const props = defineProps({
  basicInfo: Object, // 基本信息
  data: Object, // 编辑之前的数据
  fields: Object, // 所有的属性英文名称
  referenceHandles: Array, // 关联标识
  relatedFields: Array, // 关联标识字段
});

const formRef = ref();
const matrixGraphRef = ref();
const loading = ref(false);

const RelateType = {
  BASELINE: "BASELINE",
  COMPATIBLE: "COMPATIBLE",
};
const relateType = ref(RelateType.BASELINE);
const allFieldList = ref<PropField[]>([]);
// 定义查询属性下拉框
const queryFieldList = ref<PropField[]>([]);
const paramFieldList = ref<any[]>([]);

interface PropField {
  valueKey?: string;
  remark?: string;
  field?: string;
  fieldType?: number;
}

const dialogResolve = ref(false);

// 筛选基础属性字段类型为标识解析数据源的行，获取英文名作为关联属性下拉框内容

const fixDisabled = ref(false);

const fieldType = ATTR_TYPE_MAP.RELATE_HANDLE;

const form = reactive<Record<string, any>>({
  field: "",
  description: "",
  remark: "",
  fieldValue: "",
  references: [
    {
      referenceHandle: "",
      queryProp: "",
      paramProp: "",
    },
  ],
  authGroupListVOList: [],
});

// 关联标识校验
async function referenceHandleValidate(
  rule: any,
  handleVal: any,
  callback: any
) {
  if (!handleVal) return callback("请选择关联标识");
  if (props.basicInfo?.handle === handleVal) {
    // 判断不能关联本身
    return callback("关联标识和当前标识重复");
  }

  const params = {
    oldVal: props.data?.references[0]?.referenceHandle || "",
    newVal: handleVal,
    list: props.referenceHandles,
  };
  console.log("====params======:", params);
  if (isDuplicateValidate(params)) {
    return callback("标识已被关联，请重新填写");
  }
  callback();
}

const handleValueValidate = (rule: any, value: any, callback: any) => {
  callback();
};

// 校验标识下属性是否都为固定值
async function getFixedFilter(handleVal: any) {
  // 查询
  await getIdResolve(handleVal);
  fixDisabled.value = false;
  if (allFieldList.value.length > 0) {
    // 过滤类型为固定值
    const filterArray = allFieldList.value.filter(
      (item: { fieldType?: number }) => item.fieldType === ATTR_TYPE_MAP.FIXED
    );
    // 若输入的关联标识属性都为固定值时,返回true;参数、查询下拉框置灰，可保存，完成关联；
    if (filterArray.length === allFieldList.value.length) {
      fixDisabled.value = true;
      return true;
    }
  }
}

// 根据标识查询属性
function getIdResolve(handleVal: any) {
  if (handleVal) {
    allFieldList.value = [];
    return idResolve({ handle: handleVal })
      .then((response: any) => {
        // 遍历标识解析结果，获取关联属性下拉框数据
        response.values.forEach(
          (item: { fieldType: any; remark: any; field: any }) => {
            // 只关联固定值和数据源
            if (item.fieldType === ATTR_TYPE_MAP.SOURCE) {
              allFieldList.value.push({
                fieldType: item.fieldType,
                remark: item.remark ? item.remark : "",
                field: item.field,
              });
            }
          }
        );
      })
      .finally(() => {
        // 初始化数据源下拉框
        getProperty([ATTR_TYPE_MAP.SOURCE]);
      });
  }
}

// 关联标识属性校验规则生成
async function queryPropRulesValidate(rule: any, val: any, callback: any) {
  if (fixDisabled.value && !val) {
    callback();
    return;
  }
  if (!val) {
    callback("请选择关联标识属性");
    return;
  }
  callback();
}

// 当前关联标识属性校验规则生成
async function paramPropRulesValidate(rule: any, val: any, callback: any) {
  if (fixDisabled.value && !val) {
    callback();
    return;
  }
  if (!val) {
    callback("请选择标识属性");
    return;
  }
  callback();
}

const rules = reactive({
  remark: [
    {
      required: false,
      max: 100,
      message: "最大长度为100字符",
      trigger: "blur",
    },
  ],
  handleValue: {
    required: true,
    validator: handleValueValidate,
    trigger: "blur",
  },
});

// 过滤出指定类型的属性
function getProperty(fieldType: number[]) {
  queryFieldList.value = allFieldList.value.filter((item: any) =>
    fieldType.includes(item.fieldType)
  );
}

function resolveHandle(id: any) {
  if (id) {
    idResolve({ handle: id }).then((response: any) => {
      dialogResolve.value = true;
      nextTick(() => {
        matrixGraphRef.value.refresh(response);
      });
    });
  } else {
    ElMessage({
      message: "请输入标识",
      type: "error",
    });
  }
}

async function validateData() {
  let isError = false;
  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  return isError;
}

function getData() {
  const result = { ...form };
  if (
    relateType.value !== RelateType.COMPATIBLE ||
    !result.references[0].paramProp
  ) {
    result.references[0].paramProp = "";
  }
  return { ...result };
}

const getHandleInfo = (handle: any) => {
  if (!handle) {
    form.field = "";
    form.description = "";
    return;
  }
  getFixedFilter(handle);
  ApiGetResolveField({
    handle,
    fieldType,
  })
    .then((res: any) => {
      form.field = res.field;
      form.description = res.description;
    })
    .catch(() => {
      form.field = "";
      form.description = "";
    });
};

const formValidate = async () => {
  let isError = false;
  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  return isError;
};

const resetForm = () => {
  form.field = "";
  form.description = "";
  form.remark = "";
  form.fieldValue = "";
  form.references = [
    {
      referenceHandle: "",
      queryProp: "",
      paramProp: "",
    },
  ];
  form.authGroupListVOList = props.data?.authGroupListVOList;
  formRef.value.resetFields();
};

// 封装当前标识
onMounted(() => {
  const { data } = props;
  console.log("=====关联标识 onMounted=====:", props);
  if (data && data.references) {
    const { field, description, remark, fieldValue, references } = data;
    form.field = field;
    form.description = description;
    form.remark = remark;
    form.fieldValue = fieldValue;
    form.references = references;
    form.authGroupListVOList = props.data?.authGroupListVOList;
  }
  if (form.references?.length) {
    getFixedFilter(form.references[0].referenceHandle);
    // 根据paramProp字段来判断是基准还是兼容
    if (form.references[0].paramProp) {
      relateType.value = RelateType.COMPATIBLE;
    }
  }
  paramFieldList.value = (props.fields || []).filter(
    (item: any) => !props.relatedFields?.includes(item)
  );
});

defineExpose({
  form,
  validateData,
  getData,
  formValidate,
  resetForm,
});
</script>
<style lang="scss" scoped>
.el-table {
  .is-error {
    height: 70px;
    :deep(.el-form-item__error) {
      top: 50px;
    }
  }
}

.mb10 {
  margin-bottom: 10px;
}
.mb11 {
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 5px;
  color: #929292;
}
.mb12 {
  float: right;
}
.mb13 {
  margin-top: 5px;
  letter-spacing: 0.5px;
  // text-align: center;
  margin: 0 30px;
  > div {
    display: flex;
    align-items: baseline;
    span {
      flex: 1;
      &:first-child {
        text-align: right;
      }
    }
  }
}
.mb14 {
  margin-left: 10px;
}
.mb15 {
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 8px;
  letter-spacing: 0.5px;
  // text-align: center;
  margin: 40px 30px 10px;
  > div {
    display: flex;
    align-items: baseline;
    span {
      flex: 1;
      &:first-child {
        text-align: right;
      }
    }
  }
}
:deep(.el-select-dropdown__empty) {
  padding: 10px 10px !important;
}

:deep(.add-item-btn) {
  width: 100%;
  border: 1px solid #dfe4e3;
  border-top: 0;
  height: 44px;
  border-radius: 0;
  color: #1664ff;
  font-size: 12px;
  line-height: 20px;
  &:hover {
    color: #4086ff !important;
    background-color: transparent !important;
  }
}

.relate-handle-matrix-graph {
  height: 600px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.el-form-item:nth-child(1)) {
  position: relative;
  flex-direction: column;

  .relate-type {
    position: absolute;
    top: -32px;
    right: 0;
    z-index: 100;
  }
}
.handle-label-tips {
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;

  .el-tooltip__trigger {
    font-size: 12px;
    margin-left: 4px;
    cursor: pointer;
    vertical-align: middle;
  }
}
</style>
