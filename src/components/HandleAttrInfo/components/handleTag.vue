<template>
  <div class="handle-tag-wrap">
    <el-tag
      class="attr-tag"
      v-for="tag in showTags"
      :key="tag"
      closable
      :disable-transitions="false"
      @close="handleClose(tag)"
    >
      <EllipsisText :value="tag" v-copy="tag"></EllipsisText>
    </el-tag>

    <el-popover
      v-if="extTags.length > 0"
      ref="popover"
      placement="right"
      :width="200"
      trigger="focus"
      :visible="visible"
    >
      <div class="tag-count-wrap">
        <el-tag
          v-for="(item, index) of extTags"
          :key="index"
          class="attr-tag"
          closable
          @close="handleClose(item)"
        >
          <EllipsisText :value="item" v-copy="item"></EllipsisText>
        </el-tag>
      </div>
      <template #reference>
        <el-tag class="attr-tag ext-tag" key="ext-tag" @click="handleView">
          {{ `+${extTags.length}` }}
        </el-tag>
      </template>
    </el-popover>

    <el-tag
      class="tag-add-btn"
      :class="{ 'is-disabled': disabled }"
      @click="handleAdd"
    >
      + 添加
    </el-tag>
    <el-dialog
      v-model="dialogVisible"
      title="添加属性值"
      width="640px"
      top="100px"
      append-to-body
      center
      align-center
    >
      <el-form ref="formRef" :model="form" :rules="rules">
        <el-form-item label="属性值" prop="handle">
          <el-input
            v-model="form.handle"
            placeholder="请输入对象标识"
          ></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button
          type="primary"
          @click="handleNextAdd"
          :loading="addNextLoading"
        >
          新增下一个
        </el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="addLoading">
          添加
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { nextTick, ref, watch, toRefs, reactive, inject } from "vue";
import { cloneDeep } from "@antv/x6/lib/util/object/object";
import { ElMessage } from "element-plus";
import { idResolve } from "@/api/idRes/index";
import EllipsisText from "@/components/ellipsisText/index.vue";

const emit = defineEmits(["update:modelValue"]);
const props = defineProps({
  modelValue: Array,
  disabled: {
    type: Boolean,
  },
  handle: String,
});

const visible = ref(false);

const addLoading = ref(false);

const addNextLoading = ref(false);

const { modelValue } = toRefs(props);

const dynamicTags = ref<any>([]);
const showTags = ref<any>([]);
const extTags = ref<any>([]);

const form = reactive<{ handle: string }>({ handle: "" });

const dialogVisible = ref(false);

const currentHandle = ref("");

const handleValidate = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback("请输入属性值");
  }
  if (currentHandle.value === value) {
    return callback("标识值和当前标识重复");
  }
  if (dynamicTags.value.includes(form.handle)) {
    return callback("属性值重复，请重新输入");
  }
  callback();
};

const rules = {
  handle: [{ required: true, validator: handleValidate }],
};

watch(
  () => modelValue,
  (val: any) => {
    console.log("====watch====");
    currentHandle.value = props?.handle || "";
    dynamicTags.value = [];
    showTags.value = [];
    extTags.value = [];
    if (val.value && Array.isArray(val.value)) {
      const array: Array<any> = cloneDeep(val.value);
      dynamicTags.value = array;
      for (let index = 0; index < array.length; index += 1) {
        const tag = array[index];
        if (index < 2) {
          showTags.value.push(tag);
        } else {
          extTags.value.push(tag);
        }
      }
    }
  },
  {
    immediate: true,
    deep: true,
  }
);

const formRef = ref();

const handleAdd = () => {
  if (props.disabled) {
    return;
  }
  dialogVisible.value = true;
  nextTick(() => {
    form.handle = "";
    formRef.value.resetFields();
  });
};

const handleClose = (tag: string) => {
  dynamicTags.value.splice(dynamicTags.value.indexOf(tag), 1);
  emit("update:modelValue", dynamicTags.value);
};

const check = () => {
  return new Promise((resolve, reject) => {
    formRef.value.validate((valid: any) => {
      if (valid) {
        idResolve({ handle: form.handle })
          .then((response: any) => {
            const { type } = response;
            if (type === 1) {
              ElMessage.error("标识值不可以填写实例标识TID");
              reject("");
            }
            resolve("");
          })
          .catch((error) => {
            reject(error);
          });
      } else {
        reject();
      }
    });
  });
};

const handleConfirm = () => {
  addLoading.value = true;
  check()
    .then(() => {
      dynamicTags.value.push(form.handle);
      emit("update:modelValue", dynamicTags.value);
      dialogVisible.value = false;
    })
    .finally(() => {
      addLoading.value = false;
    });
};

const handleNextAdd = () => {
  addNextLoading.value = true;
  check()
    .then(() => {
      dynamicTags.value.push(form.handle);
      emit("update:modelValue", dynamicTags.value);
      form.handle = "";
      formRef.value.resetFields();
    })
    .finally(() => {
      addNextLoading.value = false;
    });
};

const handleView = () => {
  visible.value = !visible.value;
};
</script>
<style lang="scss" scoped>
.handle-tag-wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
}
.attr-tag {
  width: 79px;
  padding: 4px 6px;
  display: inline-block;
  margin-right: 8px;
  :deep(.el-tag__content) {
    width: 50px;
    display: inline-block;
  }
  &.ext-tag {
    width: 30px;
    cursor: pointer;
  }
}

.tag-add-btn {
  width: 48px;
  height: 24px;
  background: #f5f7f6;
  border: 1px dashed #c1c9c7;
  border-radius: 2px;
  color: #535f5c;
  font-weight: 400;
  font-size: 12px;
  text-align: center;
  line-height: 24px;
  cursor: pointer;

  &.is-disabled {
    cursor: not-allowed;
  }
}

:deep(.el-dialog) {
  margin-top: 40%;
}
.tag-count-wrap {
  width: 174px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  max-height: 140px;
  overflow-y: auto;
  .attr-tag {
    width: 174px;
    margin-bottom: 8px;
    &:last-child {
      margin-bottom: 0;
    }
    :deep(.el-tag__content) {
      width: 140px;
      display: inline-block;
    }
  }
}
</style>
