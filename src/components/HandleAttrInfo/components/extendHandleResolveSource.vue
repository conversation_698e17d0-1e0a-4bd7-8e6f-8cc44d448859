<template>
  <el-form
    ref="formRef"
    label-position="left"
    :model="form"
    style="width: 100%"
  >
    <el-form-item
      label="数据库"
      prop="database"
      :rules="[
        {
          required: true,
          validator: databaseValidate,
        },
      ]"
    >
      <div class="data-service-wrap">
        <el-select
          v-model="form.dataServiceId"
          placeholder="请选择数据服务"
          @change="handleServiceIdChange"
        >
          <el-option
            v-for="item in serviceList"
            :key="item.id"
            :value="item.id"
            :label="item.dataServiceName"
          ></el-option>
        </el-select>
        <el-select
          v-model="form.databaseId"
          @change="handleDatabaseChange"
          placeholder="请选择数据库"
        >
          <el-option
            v-for="item in databaseList"
            :key="item.databaseId"
            :value="item.databaseId"
            :label="item.databaseName"
          ></el-option>
        </el-select>
      </div>
    </el-form-item>
    <el-form-item>
      <el-table ref="tableRef" :data="form.attrTable" size="small" border>
        <el-table-column min-width="138" label="所属表">
          <template #default="scope">
            <el-form-item
              :prop="'attrTable.' + scope.$index + '.tableName'"
              :rules="[
                {
                  required: true,
                  validator: tableNameRulesValidate,
                },
              ]"
            >
              <el-select
                :validate-event="false"
                :popper-append-to-body="false"
                v-model="scope.row.tableName"
                placeholder="请选择"
                filterable
                clearable
                @change="handleTableNameChange(scope.row)"
              >
                <el-option
                  v-for="item in table"
                  :key="item.tableId"
                  :value="item.tableName"
                  :label="item.tableName"
                >
                  <span>{{ item.tableName }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="所属字段" min-width="138">
          <template #default="scope">
            <el-form-item
              :prop="'attrTable.' + scope.$index + '.columnName'"
              :rules="[
                {
                  required: true,
                  validator: columnNameRulesValidate,
                },
              ]"
            >
              <el-select
                :validate-event="false"
                :popper-append-to-body="false"
                v-model="scope.row.columnName"
                placeholder="请选择"
                filterable
                clearable
                :disabled="!scope.row.tableName"
                @focus="handleColumnFocus(scope.row)"
                @change="handleColumnChange(scope.row)"
              >
                <el-option
                  v-for="item in columns"
                  :key="item.columnName"
                  :value="item.columnCode"
                  :label="item.columnCode"
                >
                  <span>{{ item.columnName }}/{{ item.columnCode }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="中文名称">
          <template #default="scope">
            <el-form-item
              inline-message
              :prop="'attrTable.' + scope.$index + '.description'"
              :rules="[{ required: true, validator: descriptionValidate }]"
            >
              <el-input
                v-model.trim="scope.row.description"
                placeholder="输入中文名称"
                clearable
                :validate-event="false"
              ></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="英文名称">
          <template #default="scope">
            <el-form-item
              inline-message
              :prop="'attrTable.' + scope.$index + '.field'"
              :rules="[{ required: true, validator: fieldValidate }]"
            >
              <el-input
                v-model.trim="scope.row.field"
                placeholder="输入英文名称"
                clearable
                :validate-event="false"
              ></el-input>
            </el-form-item>
          </template>
        </el-table-column>

        <el-table-column min-width="138" label="属性值">
          <template #default="scope">
            <HandleTag
              :handle="data?.handle"
              v-model="scope.row.fieldValue"
            ></HandleTag>
          </template>
        </el-table-column>
        <el-table-column min-width="138" label="备注">
          <template #default="scope">
            <el-form-item
              :prop="'attrTable.' + scope.$index + '.remark'"
              :rules="[
                {
                  required: false,
                  max: 100,
                  message: '最大长度为100字符',
                },
              ]"
            >
              <el-input v-model="scope.row.remark" placeholder="请输入" />
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column min-width="50" label="操作">
          <template #default="scope">
            <el-button type="primary" text @click="handleDel(scope.$index)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="add-extend-attr" @click="handleAdd">
        <el-button type="primary" text>+ 新增扩展属性</el-button>
      </div>
    </el-form-item>
  </el-form>
</template>
<script setup lang="ts">
import { reactive, ref, onMounted } from "vue";
import { ElMessage } from "element-plus";
import {
  ApiServiceList,
  ApiDataChannelDatabaseList,
  ApiDatabaseTableList,
  ApiTableColumnList,
} from "@/api/tool/index";
import ellipsisText from "@/components/ellipsisText/index.vue";
import HandleTag from "./handleTag.vue";

const props = defineProps({
  fields: Object,
  descriptions: Object,
  data: Object,
  entityObjectId: String, // 实体对象ID
});

const table = ref<any[]>([]);

const attrTable = ref<any[]>([]);

const columns = ref<any[]>([]);

const formRef = ref();

const tableRef = ref();

const form = reactive<any>({
  attrTable: [],
  dataServiceId: null,
  databaseId: null,
});

const serviceList = ref<any[]>([]);
const databaseList = ref<any[]>([]);

const fieldValidate = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback("请填写英文名称");
  }
  if (value.length > 255) {
    return callback("最大长度为255字符");
  }
  if (props.fields?.includes(value)) {
    return callback("英文名称已存在");
  }

  const currentFields = form.attrTable.filter(
    (item: any) => item.field === value
  );
  if (currentFields.length > 1) {
    return callback("英文名称已存在");
  }

  callback();
};

const descriptionValidate = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback("请填写中文名称");
  }
  if (value.length > 255) {
    return callback("最大长度为255字符");
  }
  if (props.descriptions?.includes(value)) {
    return callback("中文名称已存在");
  }
  const currentDescriptions = form.attrTable.filter(
    (item: any) => item.description === value
  );
  if (currentDescriptions.length > 1) {
    return callback("中文名称已存在");
  }

  callback();
};

const handleTableNameChange = (data: any) => {
  data.columnName = "";
  data.field = "";
  data.description = "";
  columns.value = [];
};

const handleColumnChange = (data: any) => {
  if (!data) {
    data.field = "";
    data.description = "";
    return;
  }
  try {
    const columnMap = columns.value.filter(
      (item) => item.columnCode === data.columnName
    )[0];
    data.field = columnMap.columnCode;
    data.description = columnMap.columnName;
    data.remark = columnMap.columnDesc;
  } catch (error) {
    console.log("handleColumnChange:", error);
  }
};

const handleAdd = () => {
  form.attrTable.push({
    tableName: "",
    field: "", // 英文名称
    description: "", // 中文名称
    columnName: "",
    fieldValue: [],
    remark: "",
  });
};

const handleDel = (index: number) => {
  form.attrTable.splice(index, 1);
};

const handleColumnFocus = (data: any) => {
  if (data.tableName) {
    const tableId = table.value.filter(
      (item) => item.tableName === data.tableName
    )[0].tableId;
    ApiTableColumnList(tableId).then((res) => {
      if (Array.isArray(res) && res.length) {
        columns.value = res;
      }
    });
  }
};

async function tableNameRulesValidate(rule: any, val: any, callback: any) {
  if (!val) {
    callback("请选择所属表");
    return;
  }
  callback();
}

async function columnNameRulesValidate(rule: any, val: any, callback: any) {
  if (!val) {
    callback("请选择所属字段");
    return;
  }

  callback();
}

const databaseValidate = (rule: any, value: any, callback: any) => {
  if (!form.dataServiceId) {
    return callback("请选择数据服务");
  }
  if (!form.databaseId) {
    return callback("请选择数据库");
  }
  callback();
};

const formValidate = async () => {
  let isError = false;
  if (!form.attrTable.length) {
    ElMessage.warning("请新增扩展属性");
    return true;
  }
  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });

  return isError;
};

const resetForm = () => {
  formRef.value.resetFields();
};

const getDatabaseList = () => {
  ApiDataChannelDatabaseList(form.dataServiceId).then((res) => {
    if (!res) {
      databaseList.value = [];
      return;
    }
    databaseList.value = Array.isArray(res) ? res : [];
  });
};

// 数据服务更新
const handleServiceIdChange = (val: any) => {
  databaseList.value = [];
  form.databaseId = "";
  if (val) {
    getDatabaseList();
  }
};

const handleDatabaseChange = () => {
  form.attrTable = [];
  table.value = [];
  if (!form.databaseId) return;
  ApiDatabaseTableList(form.databaseId).then((res) => {
    if (Array.isArray(res) && res.length > 0) {
      table.value = res;
    }
  });
};

// 获取数据服务列表
const getServiceList = () => {
  ApiServiceList().then((res) => {
    serviceList.value = Array.isArray(res) ? res : [];
  });
};

const getData = () => {
  return form.attrTable.map((item: any) => {
    return {
      field: item.field, // 英文名称
      description: item.description, // 中文名称
      remark: item.remark, // 属性描述
      tableName: item.tableName, // 所属表
      columnName: item.columnName, // 所属字段
      fieldValue: item.fieldValue.join(","), // 属性值
    };
  });
};

onMounted(async () => {
  table.value = [];
  serviceList.value = [];
  attrTable.value = [];
  columns.value = [];
  form.attrTable = [];
  getServiceList();
});

defineExpose({
  formValidate,
  getData,
  resetForm,
});
</script>
<style lang="scss" scoped>
.add-extend-attr {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 12px;
  border: 1.5px solid #dfe4e3;
  border-top: 0;
  width: 100%;
}
.el-table {
  .is-error {
    height: 70px;
    :deep(.el-form-item__error) {
      top: 50px;
    }
  }
}
.source-value-line {
  height: 1px;
  width: 100%;
  margin: 24px 0;
  background-color: #dfe4e3;
}
.entity-object {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.attr-object {
  width: 100%;
  .attr-object-search {
    margin-left: -14px;
  }
}
.data-service-wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .el-select {
    width: 216px;
    margin-right: 16px;
  }
}
</style>
