<template>
  <div class="handle-attr-source-value" v-loading="loading">
    <div class="entity-object">
      <div class="section-title">
        <div class="section-title-line"></div>
        <span class="section-title-text"> 实体对象 </span>
      </div>
      <el-table :data="table" size="small" border>
        <el-table-column label="序号" width="55">
          <template #default="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="实体对象编码" min-width="210">
          <template #default="scope">
            <ellipsisText :value="scope.row.code"></ellipsisText>
          </template>
        </el-table-column>
        <el-table-column label="实体对象名称" min-width="200">
          <template #default="scope">
            <ellipsisText :value="scope.row.name"></ellipsisText>
          </template>
        </el-table-column>
        <el-table-column label="版本" min-width="100">
          <template #default="scope">
            <ellipsisText :value="scope.row.version"></ellipsisText>
          </template>
        </el-table-column>
        <el-table-column label="描述" min-width="270">
          <template #default="scope">
            <ellipsisText :value="scope.row.description"></ellipsisText>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="source-value-line"></div>
    <div class="attr-object">
      <div class="section-title">
        <div class="section-title-line"></div>
        <span class="section-title-text"> 属性列表 </span>
      </div>
      <el-form
        class="attr-object-search"
        :inline="true"
        :model="searchForm"
        @submit.prevent
      >
        <el-form-item style="width: 276px; max-width: 276px">
          <el-input
            v-model.trim="searchForm.code"
            clearable
            placeholder="请输入"
          >
            <template #prefix>属性编码：</template></el-input
          >
        </el-form-item>
        <el-form-item style="width: 276px; max-width: 276px"
          ><el-input
            v-model.trim="searchForm.name"
            clearable
            placeholder="请输入"
          >
            <template #prefix>属性名称：</template></el-input
          ></el-form-item
        >
        <el-form-item style="width: 276px; max-width: 276px"
          ><el-input v-model="searchForm.enName" placeholder="请输入" clearable>
            <template #prefix>英文名称：</template>
          </el-input></el-form-item
        >
        <el-form-item style="width: 60px; max-width: 60px">
          <el-button
            type="primary"
            @click="handleSearch"
            :loading="searchLoading"
          >
            搜索
          </el-button>
        </el-form-item>
      </el-form>
      <el-form
        ref="formRef"
        label-position="left"
        :model="form"
        style="width: 100%"
      >
        <el-form-item class="source-value-table">
          <el-table
            ref="tableRef"
            key="sourceValueTable"
            :data="form.attrTable"
            size="small"
            border
            height="300"
            v-el-table-infinite-scroll="load"
            @selection-change="handleSelectable"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column label="序号" width="55">
              <template #default="scope">
                <span>{{ scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column label="属性编码">
              <template #default="scope">
                <ellipsisText :value="scope.row.code"></ellipsisText>
              </template>
            </el-table-column>
            <el-table-column label="属性名称">
              <template #default="scope">
                <ellipsisText :value="scope.row.name"></ellipsisText>
              </template>
            </el-table-column>
            <el-table-column label="英文名称">
              <template #default="scope">
                <ellipsisText :value="scope.row.enName"></ellipsisText>
              </template>
            </el-table-column>
            <el-table-column label="备注">
              <template #default="scope">
                <ellipsisText :value="scope.row.description"></ellipsisText>
              </template>
            </el-table-column>
            <el-table-column label="所属表">
              <template #default="scope">
                <el-form-item
                  :prop="'attrTable.' + scope.$index + '.modelId'"
                  :rules="(selectedTableName as any).includes(scope.row.name) ? [
                    {
                      required: true,
                      validator: tableNameRulesValidate,
                    },
                  ]: []"
                >
                  <el-select
                    :validate-event="false"
                    :popper-append-to-body="false"
                    v-model="scope.row.modelId"
                    placeholder="请选择"
                    filterable
                    clearable
                    @change="handleTableNameChange(scope.row, scope.$index)"
                  >
                    <el-option
                      v-for="item in model.modelTable"
                      :key="item.modelId"
                      :value="item.modelId"
                      :label="item.modelCode"
                    >
                      <span>{{ item.modelName }}/{{ item.modelCode }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column label="所属字段">
              <template #default="scope">
                <el-form-item
                  :prop="'attrTable.' + scope.$index + '.columnName'"
                  :rules="
                    (selectedTableName as any).includes(scope.row.name)
                      ? [
                          {
                            required: true,
                            validator: columnNameRulesValidate,
                          },
                        ]
                      : []
                  "
                >
                  <el-select
                    :validate-event="false"
                    :popper-append-to-body="false"
                    v-model="scope.row.columnName"
                    placeholder="请选择"
                    filterable
                    clearable
                    :disabled="!scope.row.modelId"
                    @focus="handleColumnFocus(scope.row)"
                  >
                    <el-option
                      v-for="item in scope.row.columnList"
                      :key="item.columnId"
                      :value="item.columnCode"
                      :label="item.columnCode"
                    >
                      <span>{{ item.columnName }}/ {{ item.columnCode }}</span>
                    </el-option>
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
          </el-table>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script setup lang="ts">
import { reactive, ref, onMounted, onUnmounted, nextTick } from "vue";
import { ElMessage } from "element-plus";
// eslint-disable-next-line import/no-named-default
import { default as vElTableInfiniteScroll } from "el-table-infinite-scroll";

import {
  ApiEntityObjectDetail,
  ApiEntityObjectDetailList,
  ApiDataModelDetail,
} from "@/api/handleRegister/index";
import ellipsisText from "@/components/ellipsisText/index.vue";

const props = defineProps({
  fields: Object,
  descriptions: Object,
  data: Object,
  entityObjectId: String, // 实体对象ID
});

const table = ref<any[]>([]);

const attrTable = ref<any[]>([]);

const formRef = ref();

const tableRef = ref();

const tableSize = 10; // 属性列表一次加载的数量
const searchLoading = ref(false);
const startNum = ref(0); // 数据开始的值
const currentAttrData = ref<any[]>([]); // 存储当前属性列表【通过接口获取的，搜索的】
const isAttrRequested = ref(false); // 属性列表接口请求完成

const searchForm = ref({
  code: "",
  name: "",
  enName: "",
});

const form = reactive<any>({
  attrTable: [],
});
const model = reactive<any>({
  modelTable: [],
});

const timer = ref();

const loading = ref(false);
const selectedTableName = ref([]);

const handleSelectable = (data: any) => {
  selectedTableName.value = data.map((item: any) => item.name);
};

const handleSearch = () => {
  searchLoading.value = true;
  const { code, name, enName } = searchForm.value;
  let searchData = [];
  if (!code && !name && !enName) {
    searchData = JSON.parse(JSON.stringify(attrTable.value));
  } else {
    searchData = attrTable.value.filter((item) => {
      const isCodeError = code
        ? !item.code.toLowerCase().includes(code.toLowerCase())
        : false;
      const isNameError = name
        ? !item.name.toLowerCase().includes(name.toLowerCase())
        : false;
      const isEnNameError = enName
        ? !item.enName.toLowerCase().includes(enName.toLowerCase())
        : false;
      return !isCodeError && !isNameError && !isEnNameError;
    });
  }
  setTimeout(() => {
    searchLoading.value = false;
  }, 1 * 500);
  startNum.value = 0;
  currentAttrData.value = JSON.parse(JSON.stringify(searchData));
  form.attrTable = [];
  showTableData();
};

const handleTableNameChange = (data: any, index: number) => {
  form.attrTable[index].columnList = [];
  data.columnName = "";
  if (!data.tableName) {
    form.attrTable[index].columnList = [];
    return;
  }
  ApiDataModelDetail(data.modelId).then((res) => {
    const { columnList } = res;
    form.attrTable[index].columnList = columnList;
  });
};

const handleColumnFocus = (data: any) => {
  ApiDataModelDetail(data.modelId).then((res) => {
    data.columnList = res.columnList;
  });
};

async function tableNameRulesValidate(rule: any, val: any, callback: any) {
  if (!val) {
    callback("请选择所属表");
    return;
  }
  callback();
}

async function columnNameRulesValidate(rule: any, val: any, callback: any) {
  if (!val) {
    callback("请选择所属字段");
    return;
  }
  callback();
}

const formValidate = async () => {
  let isError = false;
  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  const selectedData = tableRef.value.getSelectionRows();
  if (!selectedData.length) {
    ElMessage.warning("请勾选属性列表");
    return true;
  }
  return isError;
};

const resetForm = () => {
  formRef.value.resetFields();
};

// 获取实体对象
const getDetail = () => {
  return ApiEntityObjectDetail(props?.entityObjectId || "").then((res: any) => {
    table.value = [...res];
  });
};

const load = () => {
  if (!isAttrRequested.value) {
    // 没有接口请求之前，不进行操作
    return;
  }
  if (form.attrTable.length >= attrTable.value.length) {
    return;
  }
  startNum.value++;
  showTableData();
};

const showTableData = () => {
  const startIndex = startNum.value * tableSize;
  const endIndex = (startNum.value + 1) * tableSize;
  const currentData = currentAttrData.value.slice(startIndex, endIndex);
  currentData.forEach((item) => {
    form.attrTable.push({ ...item });
  });
};

// 获取属性列表
const getAttrList = () => {
  loading.value = true;
  return ApiEntityObjectDetailList(props?.entityObjectId || "")
    .then((res) => {
      const { entityObjectAttribute, relationDataModel } = res;
      const entityObjectAttributeTemp =
        props.fields?.length > 0
          ? entityObjectAttribute.filter((item: any) => {
              return (
                !props.fields?.includes(item.enName) &&
                !props.descriptions?.includes(item.name)
              );
            })
          : entityObjectAttribute;
      attrTable.value = entityObjectAttributeTemp.map((item: any) => {
        return {
          ...item,
          modelId: "",
          columnName: "",
        };
      });
      currentAttrData.value = JSON.parse(JSON.stringify(attrTable.value));
      form.attrTable = [];
      showTableData();

      model.modelTable = [...relationDataModel];
    })
    .finally(() => {
      loading.value = false;
      isAttrRequested.value = true;
    });
};

const getData = () => {
  const selectedData = tableRef.value.getSelectionRows();

  return selectedData.map((item: any) => {
    let tableName = "";
    try {
      tableName = model.modelTable.filter(
        (modelItem: any) => item.modelId === modelItem.modelId
      )[0].modelCode;
    } catch (error) {
      tableName = "";
    }

    return {
      entityObjectFieldCode: item.code,
      field: item.enName, // 英文名称-英文名称
      description: item.name, // 中文名称-属性名称
      remark: item.description, // 属性描述
      tableName, // 所属表
      columnName: item.columnName, // 所属字段
      fieldValue: "", // 属性值
    };
  });
};

onMounted(async () => {
  table.value = [];
  attrTable.value = [];
  searchForm.value.code = "";
  searchForm.value.name = "";
  searchForm.value.enName = "";
  form.attrTable = [];
  await getDetail();
  await getAttrList();
});

onUnmounted(() => {
  if (timer.value) {
    window.clearInterval(timer.value);
    loading.value = false;
  }
});

defineExpose({
  formValidate,
  getData,
  resetForm,
});
</script>
<style lang="scss" scoped>
.data-loading {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding-top: 10px;
}
.handle-attr-source-value {
  width: 100%;
}

.source-value-table {
  max-height: 440px;
  overflow-y: auto;
  margin-bottom: 0 !important;
}
.el-table {
  .is-error {
    height: 70px;
    :deep(.el-form-item__error) {
      top: 50px;
    }
  }
}
.source-value-line {
  height: 1px;
  width: 100%;
  margin: 24px 0;
  background-color: #dfe4e3;
}
.entity-object {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.attr-object {
  width: 100%;
  .attr-object-search {
    margin-left: -12px;
  }
}
</style>
