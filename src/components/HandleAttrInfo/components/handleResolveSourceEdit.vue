<template>
  <el-form
    style="width: 100%"
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="100px"
  >
    <el-form-item class="form-item" label="所属表">
      <el-input v-model="form.tableName" placeholder="请输入" />
    </el-form-item>
    <el-form-item class="form-item" label="所属字段">
      <el-input v-model="form.columnName" placeholder="请输入" />
    </el-form-item>
    <el-form-item class="form-item" prop="description" label="中文名称">
      <el-input v-model="form.description" placeholder="请输入" />
    </el-form-item>
    <el-form-item class="form-item" prop="field" label="英文名称">
      <el-input v-model="form.field" placeholder="请输入" />
    </el-form-item>
    <el-form-item
      class="form-item form-item-pl10"
      label="属性值"
      v-if="category === ATTR_CATEGORY.EXTEND"
    >
      <HandleTag v-model="form.fieldValue"></HandleTag>
    </el-form-item>
    <el-form-item class="form-item form-item-pl10" label="备注">
      <el-input v-model="form.remark" placeholder="请输入" />
    </el-form-item>
  </el-form>
</template>
<script setup lang="ts">
import { reactive, ref, onMounted } from "vue";
import ellipsisText from "@/components/ellipsisText/index.vue";
import { ATTR_CATEGORY } from "@/utils/dataPlatform";
import HandleTag from "./handleTag.vue";

const formRef = ref();

const props = defineProps({
  fields: Object,
  descriptions: Object,
  data: Object,
  category: String,
});

const fieldValidate = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback("请填写英文名称");
  }
  if (value.length > 255) {
    return callback("最大长度为255字符");
  }
  if (props.data?.field !== value && props.fields?.includes(value)) {
    return callback("英文名称已存在");
  }

  callback();
};

const descriptionValidate = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback("请填写中文名称");
  }
  if (value.length > 255) {
    return callback("最大长度为255字符");
  }
  if (
    props.data?.description !== value &&
    props.descriptions?.includes(value)
  ) {
    return callback("中文名称已存在");
  }

  callback();
};

const rules = reactive({
  remark: [
    {
      required: false,
      max: 100,
      message: "最大长度为100字符",
    },
  ],
  field: [
    {
      required: true,
      validator: fieldValidate,
    },
  ],
  description: [
    {
      required: true,
      validator: descriptionValidate,
    },
  ],
});

const form = reactive<any>({
  tableName: "",
  columnName: "",
  field: "",
  description: "",
  remark: "",
  fieldValue: "",
});

const formValidate = async () => {
  let isError = false;
  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  return isError;
};

const getData = () => {
  const oldData = props.data ? { ...props.data } : {};
  const newData = {
    field: form.field, // 英文名称
    description: form.description, // 中文名称
    remark: form.remark, // 属性描述
    tableName: form.tableName, // 所属表
    columnName: form.columnName, // 所属字段
    fieldValue:
      props.category === ATTR_CATEGORY.EXTEND ? form.fieldValue.join(",") : "", // 属性值
  };
  return Object.assign(oldData, newData);
};

onMounted(async () => {
  console.log("====扩展属性-标识解析数据源-编辑弹窗=====:", props.data);
  if (props?.data) {
    const { data } = props;
    form.fieldValue = data.fieldValue ? data.fieldValue.split(",") : []; // 属性值
    form.tableName = data.tableName;
    form.remark = data.remark;
    form.columnName = data.columnName;
    form.field = data.field;
    form.description = data.description;
  }
});

defineExpose({
  formValidate,
  getData,
});
</script>
<style lang="scss" scoped>
.form-item {
  :deep(.el-select) {
    width: 100%;
  }
}
</style>
