<!-- 属性信息 -->
<template>
  <div class="handle-attr-info-wrap">
    <el-button type="primary" class="attr-info-add-btn" @click="handleAddAttr"
      >新增属性</el-button
    >
    <el-tabs v-model="activeName">
      <el-tab-pane label="基础属性" :name="ATTR_CATEGORY.BASIC">
        <AttrTable
          table-key="basicAttrTable"
          :table="basicTableData"
          @delete="basicTableDelete"
          @edit="basicTableEdit"
        ></AttrTable>
      </el-tab-pane>
      <el-tab-pane label="扩展属性" :name="ATTR_CATEGORY.EXTEND">
        <AttrTable
          table-key="extendAttrTable"
          :table="extendTableData"
          @delete="extendTableDelete"
          @edit="extendTableEdit"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
  <AttrDialog
    v-if="showAttrDialog"
    :type="attrDialogType"
    :category="activeName"
    :data="attrDataTemp"
    :fields="attrFields"
    :relatedFields="relatedFields"
    :descriptions="attrDescriptions"
    :basicInfo="basicInfo"
    :referenceHandles="referenceHandles"
    @add="handleAttrDialogAdd"
    @edit="handleAttrDialogEdit"
    @close="showAttrDialog = false"
  ></AttrDialog>
</template>

<script lang="ts" setup>
import { defineProps, onMounted, ref, computed } from "vue";
import {
  DIALOG_TYPE,
  ATTR_CATEGORY,
  ATTR_TYPE_MAP,
} from "@/utils/dataPlatform";
import AttrTable from "./components/attrTable.vue";
import AttrDialog from "./components/attrDialog.vue";

const emit = defineEmits(["close", "confirm"]);
const props = defineProps({
  handleDetail: Object,
});

const basicInfo = ref();

const basicTableData = ref<any[]>([]);
const extendTableData = ref<any[]>([]);

const activeName = ref(ATTR_CATEGORY.BASIC);

const attrDataTemp = ref(); // 编辑数据
const attrDataTempIndex = ref(-1); // 编辑数据的index

// 属性弹窗
const showAttrDialog = ref(false);

// 属性弹窗类型-新增/编辑
const attrDialogType = ref(DIALOG_TYPE.ADD);

// 所有的属性
const referenceHandles = computed(() => {
  try {
    const basicAttrReference = basicTableData.value
      .filter((item) => item.fieldType === ATTR_TYPE_MAP.RELATE_HANDLE)
      .map((item: any) => item.references[0].referenceHandle);
    const extendAttrReference = extendTableData.value
      .filter((item) => item.fieldType === ATTR_TYPE_MAP.RELATE_HANDLE)
      .map((item: any) => item.references[0].referenceHandle);
    return [...basicAttrReference, ...extendAttrReference];
  } catch (error) {
    return [];
  }
});

const attrFields = computed(() => {
  const basicFields = basicTableData.value.map((item: any) => item.field);
  const extendFields = extendTableData.value.map((item: any) => item.field);
  return [...basicFields, ...extendFields];
});

// 关联标识字段
const relatedFields = computed(() => {
  return extendTableData.value
    .filter((item: any) => item.fieldType === ATTR_TYPE_MAP.RELATE_HANDLE)
    .map((item: any) => item.field);
});

const attrDescriptions = computed(() => {
  const basicDescriptions = basicTableData.value.map(
    (item: any) => item.description
  );
  const extendDescriptions = extendTableData.value.map(
    (item: any) => item.description
  );
  return [...basicDescriptions, ...extendDescriptions];
});

// 新增属性
const handleAddAttr = () => {
  showAttrDialog.value = true;
  attrDataTemp.value = null;
  attrDataTempIndex.value = -1;
  attrDialogType.value = DIALOG_TYPE.ADD;
};
// 删除基础属性
const basicTableDelete = (index: number) => {
  basicTableData.value.splice(index, 1);
};

// 编辑基础属性
const basicTableEdit = (index: number) => {
  showAttrDialog.value = true;
  attrDialogType.value = DIALOG_TYPE.EDIT;
  attrDataTemp.value = JSON.parse(JSON.stringify(basicTableData.value[index]));
  attrDataTempIndex.value = index;
};

// 删除扩展属性
const extendTableDelete = (index: number) => {
  extendTableData.value.splice(index, 1);
};

// 编辑扩展属性
const extendTableEdit = (index: number) => {
  showAttrDialog.value = true;
  attrDialogType.value = DIALOG_TYPE.EDIT;
  attrDataTemp.value = JSON.parse(JSON.stringify(extendTableData.value[index]));
  attrDataTempIndex.value = index;
};

// 新增属性弹窗确认
const handleAttrDialogAdd = (data: any) => {
  showAttrDialog.value = false;
  if (activeName.value === ATTR_CATEGORY.BASIC) {
    data.forEach((item: any) => {
      item.databaseName = item.databaseName || "";
      item.tableName = item.tableName || "";
      item.columnName = item.columnName || "";
      basicTableData.value.push(item);
    });
  }
  if (activeName.value === ATTR_CATEGORY.EXTEND) {
    data.forEach((item: any) => {
      item.databaseName = item.databaseName || "";
      item.tableName = item.tableName || "";
      item.columnName = item.columnName || "";
      extendTableData.value.push(item);
    });
  }
};

// 编辑属性弹窗确认
const handleAttrDialogEdit = (data: any) => {
  showAttrDialog.value = false;
  if (activeName.value === ATTR_CATEGORY.BASIC) {
    basicTableData.value[attrDataTempIndex.value] = JSON.parse(
      JSON.stringify(data)
    );
  }
  if (activeName.value === ATTR_CATEGORY.EXTEND) {
    extendTableData.value[attrDataTempIndex.value] = JSON.parse(
      JSON.stringify(data)
    );
  }
};
onMounted(() => {
  console.log("=====step2 onMounted=====:", props.handleDetail);
  try {
    if (props.handleDetail) {
      const { handleDetail } = props;
      basicInfo.value = {
        name: handleDetail.name,
        handle: handleDetail.handle || handleDetail.name,
        handleType: handleDetail.handleType,
        entityObjectId: handleDetail.entityObjectId,
      };
      basicTableData.value = handleDetail.items
        ? JSON.parse(JSON.stringify(handleDetail.items))
        : [];
      extendTableData.value = handleDetail.extendItems
        ? JSON.parse(JSON.stringify(handleDetail.extendItems))
        : [];
    }
  } catch (error) {
    console.error("=====step2 onMounted error=====:", error);
  }
});

defineExpose({
  basicTableData,
  extendTableData,
});
</script>
<style scoped lang="scss">
:deep(.el-tabs__nav-wrap::after) {
  content: none;
}
:deep(.el-tabs__active-bar) {
  display: none;
}
:deep(.el-tabs__item) {
  height: auto;
  line-height: normal;
}
:deep(.el-tabs--top .el-tabs__item.is-top:nth-child(2)) {
  border-right: 2px solid #c1c9c7;
}
.handle-attr-info-wrap {
  position: relative;
  padding: 32px 0;
  width: 100%;
  min-height: 300px;
  height: calc(100vh - 240px);
}

.attr-info-add-btn {
  position: absolute;
  right: 0;
  top: 26px;
  z-index: 1;
}
</style>
