<template>
  <div class="yc-search-box">
    <div v-if="slotLeft" class="yc-search-left">
      <slot name="left"></slot>
    </div>
    <div class="yc-search-right">
      <slot name="right"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSlots, ref } from "vue";

const slotLeft = ref(!!useSlots().left);
</script>

<style lang="scss" scoped>
.yc-search-box {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  .yc-search-left {
    padding-right: 160px;
  }
  .yc-search-right {
    flex: 1;
    min-width: 0;
    text-align: right;
  }

  :deep(.el-form) {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    .el-form-item {
      margin-bottom: 0 !important;
      flex: 1 !important;
      min-width: 0 !important;
      &:first-child {
        margin-left: 0;
      }
      .el-select {
        width: 100%;
      }
      .el-form-item__content {
        .el-input {
          width: 100%;
        }
      }
    }
  }
}
</style>
