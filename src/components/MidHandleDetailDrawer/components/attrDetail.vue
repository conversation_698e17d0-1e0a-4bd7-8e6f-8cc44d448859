<template>
  <el-drawer
    v-model="drawerVisible"
    custom-class="handle-item-drawer"
    @closed="handleClosed"
    title="标识属性详情"
  >
    <div class="yc-description">
      <el-descriptions :column="1" direction="horizontal">
        <el-descriptions-item label="属性类型">{{
          FIELD_TYPE_NAME_MAP[props.data?.fieldType] || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="中文名称">
          <ellipsisText :value="props.data?.description"></ellipsisText
        ></el-descriptions-item>
        <el-descriptions-item label="英文名称">
          <ellipsisText :value="props.data?.field"></ellipsisText>
        </el-descriptions-item>
        <el-descriptions-item
          label="属性值"
          v-if="props.data?.fieldType === 1 || props.data?.fieldType === 2"
          ><ellipsisText :value="getReferenceDis(data)"></ellipsisText
        ></el-descriptions-item>
        <!-- 标识值/标识-属性 -->
        <el-descriptions-item
          v-if="props.data?.fieldType === 3 || props.data?.fieldType === 4"
          label="属性值"
        >
          <el-table
            class="handle-item-drawer-table"
            :data="props.data.references"
            size="small"
          >
            <el-table-column
              label="关联标识"
              header-align="center"
              class-name="table-column-style"
            >
              <el-table-column
                label="标识"
                property="referenceHandle"
                :class-name="
                  props.data?.fieldType === 3 ? 'table-column-style' : ''
                "
              />
              <el-table-column
                class-name="table-column-style"
                label="属性"
                property="referenceHandleProp"
                v-if="props.data?.fieldType === 4"
              >
                <template #default="scope">
                  <div>
                    {{
                      scope.row.referenceHandleProp
                        ? scope.row.referenceHandleProp.field
                        : "-"
                    }}
                  </div>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column
              header-align="center"
              class-name="table-column-style"
            >
              <template #header>
                <span>映射关系</span>
                <el-tooltip effect="light" placement="top-start">
                  <template #content>
                    <img
                      alt=""
                      style="width: 500px; height: auto"
                      src="@/assets/images/handleReferanceDemo.svg"
                  /></template>
                  <span
                    ><el-icon size="18" style="vertical-align: text-bottom"
                      ><Warning /></el-icon
                  ></span>
                </el-tooltip>
              </template>
              <el-table-column label="关联标识属性" show-overflow-tooltip>
                <template #default="scope">
                  <div>
                    {{ scope.row.queryProp ? scope.row.queryProp.field : "-" }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                class-name="table-column-style"
                label="标识属性"
                show-overflow-tooltip
              >
                <template #default="scope">
                  <div>
                    {{ scope.row.paramProp ? scope.row.paramProp.field : "-" }}
                  </div>
                </template>
              </el-table-column>
            </el-table-column>
            <el-table-column label="操作" width="60">
              <template #default="scope">
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click="resolveHandle(scope.row.referenceHandle)"
                  >解析</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-descriptions-item>
        <el-descriptions-item label="备注">
          <ellipsisText :value="props.data?.remark"></ellipsisText
        ></el-descriptions-item>
      </el-descriptions>
    </div>
  </el-drawer>
  <el-dialog
    v-model="dialogResolve"
    title="解析结果"
    append-to-body
    width="800"
    @close="dialogResolve = false"
  >
    <div class="relate-handle-matrix-graph">
      <matrix-graph ref="matrixGraphRef"></matrix-graph>
    </div>
    <template #footer>
      <el-button type="primary" @click="dialogResolve = false">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { defineProps, ref, defineEmits, nextTick } from "vue";
import { ElMessage } from "element-plus";
import matrixGraph from "@/views/idRes/components/matrix-graph.vue";
import { idResolve } from "@/api/idRes/index";
import { FIELD_TYPE_NAME_MAP, FIELD_TYPE_MAP } from "@/utils/constant";
import ellipsisText from "@/components/ellipsisText/index.vue";

const drawerVisible = ref(true);
const dialogResolve = ref(false);
const matrixGraphRef = ref();

const props = defineProps({
  data: {
    type: Object,
    default: () => null,
  },
});

const emit = defineEmits(["close"]);

function handleClosed() {
  emit("close");
}

function resolveHandle(id: any) {
  if (id) {
    idResolve({ handle: id }).then((response: any) => {
      dialogResolve.value = true;
      nextTick(() => {
        matrixGraphRef.value?.refresh(response);
      });
    });
  } else {
    ElMessage({
      message: "请输入标识",
      type: "error",
    });
  }
}

function getReferenceDis(data: any) {
  if (data.fieldType === FIELD_TYPE_MAP.fixed) {
    return data.fieldValue;
  }
  if (data.fieldType === FIELD_TYPE_MAP.source) {
    return data.fieldValue && typeof data.fieldValue === "string"
      ? JSON.parse(data.fieldValue).join(",")
      : "-";
  }

  return "-";
}
</script>
<style lang="scss" scoped>
:deep(.el-descriptions__label) {
  width: 72px;
  min-width: 72px;
  display: inline-block;
}

:deep(.el-descriptions__content) {
  display: inline-block;
  word-wrap: word-break;
  word-break: break-all;
  width: 560px;
  max-width: 560px;
  min-width: 560px;
  display: inline-block;
  vertical-align: middle;
}
.relate-handle-matrix-graph {
  height: 600px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
:deep(.handle-item-drawer-table) {
  margin-top: 16px;
}
.handle-item-tooltip {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  .el-icon {
    margin-left: 4px;
  }
}

:deep(th.el-table__cell.is-leaf) {
  border-right: none;
}
:deep(.table-column-style) {
  border-right: 1px solid #dfe4e3 !important;
}
.yc-description {
  width: 700px;
}
</style>
