<template>
  <el-drawer
    custom-class="handle-detail-drawer"
    v-model="drawerVisible"
    :title="isMasterData ? '主数据标识详情' : '标识详情'"
    @closed="handleClose"
  >
    <div class="yc-description-basic" v-loading="loading">
      <div class="yc-description-title">
        <div class="yc-description-title-tip"></div>
        <div class="yc-description-title-text">基本信息</div>
      </div>
      <el-row v-if="appType === APP_TYPE.DMM">
        <el-row v-if="isMasterData">
          <el-col :span="8">
            <el-descriptions :column="1" direction="horizontal">
              <el-descriptions-item label="对象标识名称">
                <div style="width: 280px">
                  <ellipsisText :value="detail?.name"></ellipsisText>
                </div>
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
          <el-col :span="8">
            <el-descriptions :column="1" direction="horizontal">
              <el-descriptions-item label="对象标识编码">
                <div style="width: 280px">
                  <ellipsisText :value="detail?.handle"> </ellipsisText>
                </div>
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row>
        <el-row v-else>
          <el-col :span="8">
            <el-descriptions :column="1" direction="horizontal">
              <el-descriptions-item label="标识类型">
                <div style="width: 280px">
                  {{ getHandleType(detail?.handleType) }}
                </div>
              </el-descriptions-item>
              <el-descriptions-item label="对象标识编码">
                <div style="width: 280px">
                  <ellipsisText :value="detail?.handle"></ellipsisText>
                </div>
              </el-descriptions-item>
              <el-descriptions-item label="实体对象类型">
                <div style="width: 280px">{{ entityObjTypeName || "-" }}</div>
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
          <el-col :span="8">
            <el-descriptions :column="1" direction="horizontal">
              <el-descriptions-item
                :label="isMasterData ? '主数据名称' : '对象标识名称'"
              >
                <div style="width: 280px">
                  <ellipsisText :value="detail?.name"></ellipsisText>
                </div>
              </el-descriptions-item>
              <el-descriptions-item label="实体类型">
                <div style="width: 280px">
                  {{ getEntityTypeName(detail?.entityType) || "-" }}
                </div>
              </el-descriptions-item>
              <el-descriptions-item label="实体对象">
                <div style="width: 280px">
                  {{ entityObjName || "-" }}
                </div>
              </el-descriptions-item>
            </el-descriptions>
          </el-col>
        </el-row></el-row
      >
      <el-row v-if="appType === APP_TYPE.NORMAL">
        <el-col :span="8">
          <el-descriptions :column="1" direction="horizontal">
            <el-descriptions-item label="对象标识名称">
              <div style="width: 500px">
                <ellipsisText :value="detail?.name"></ellipsisText>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="对象标识">
              <div style="width: 500px">
                <ellipsisText :value="detail?.handle"></ellipsisText>
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="实体类型">
              <div style="width: 500px">
                {{ getEntityTypeName(detail?.entityType) || "-" }}
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>
    </div>
    <el-divider />

    <div class="yc-description">
      <div class="yc-description-title">
        <div class="yc-description-title-tip"></div>
        <div class="yc-description-title-text">属性列表</div>
      </div>
      <el-tabs v-model="activeName">
        <el-tab-pane label="基础属性" name="basic">
          <el-table :data="detail?.items" border size="small">
            <el-table-column label="序号" type="index" />
            <el-table-column label="中文名称" property="description">
              <template #default="scope">
                <ellipsisText :value="scope.row.description">{{
                  scope.row.description || "-"
                }}</ellipsisText>
              </template>
            </el-table-column>
            <el-table-column label="英文名称" property="field">
              <template #default="scope">
                <ellipsisText :value="scope.row.field">{{
                  scope.row.field || "-"
                }}</ellipsisText>
              </template>
            </el-table-column>
            <el-table-column label="属性类型" property="length">
              <template #default="scope">
                <div>{{ FIELD_TYPE_NAME_MAP[scope.row.fieldType] }}</div>
              </template>
            </el-table-column>
            <el-table-column label="关联通道">
              <template #default="scope">
                <ellipsisText
                  :value="scope.row.dataSourceName"
                ></ellipsisText> </template
            ></el-table-column>
            <el-table-column label="所属数据库">
              <template #default="scope">
                <ellipsisText :value="scope.row.databaseName"></ellipsisText>
              </template>
            </el-table-column>
            <el-table-column label="所属表">
              <template #default="scope">
                <ellipsisText :value="scope.row.tableName"></ellipsisText>
              </template>
            </el-table-column>
            <el-table-column label="所属字段">
              <template #default="scope">
                <ellipsisText :value="scope.row.columnName"></ellipsisText>
              </template>
            </el-table-column>
            <el-table-column label="数据库IP">
              <template #default="scope">
                <ellipsisText :value="scope.row.databaseIp"></ellipsisText>
              </template>
            </el-table-column>
            <el-table-column label="属性值">
              <template #default="scope">
                <ellipsisText :value="scope.row.fieldValue"></ellipsisText>
              </template>
            </el-table-column>
            <el-table-column label="备注">
              <template #default="scope">
                <ellipsisText :value="scope.row.remark">{{
                  scope.row.remark || "-"
                }}</ellipsisText>
              </template></el-table-column
            >
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="扩展属性" name="extend">
          <el-table
            v-if="activeName === 'extend'"
            :data="detail?.extendItems"
            border
          >
            <el-table-column label="序号" type="index" />
            <el-table-column label="中文名称" property="description">
              <template #default="scope">
                <ellipsisText :value="scope.row.description">{{
                  scope.row.description || "-"
                }}</ellipsisText>
              </template>
            </el-table-column>
            <el-table-column label="英文名称" property="field">
              <template #default="scope">
                <ellipsisText :value="scope.row.field">{{
                  scope.row.field || "-"
                }}</ellipsisText>
              </template>
            </el-table-column>
            <el-table-column label="属性类型" property="length">
              <template #default="scope">
                <div>{{ FIELD_TYPE_NAME_MAP[scope.row.fieldType] }}</div>
              </template>
            </el-table-column>
            <el-table-column label="关联通道">
              <template #default="scope">
                <ellipsisText :value="scope.row.dataSourceName">{{
                  scope.row.dataSourceName || "-"
                }}</ellipsisText>
              </template></el-table-column
            >
            <el-table-column label="所属数据库">
              <template #default="scope">
                <ellipsisText :value="scope.row.databaseName">{{
                  scope.row.databaseName || "-"
                }}</ellipsisText>
              </template></el-table-column
            >
            <el-table-column label="所属表">
              <template #default="scope">
                <ellipsisText :value="scope.row.tableName">{{
                  scope.row.tableName || "-"
                }}</ellipsisText>
              </template></el-table-column
            >
            <el-table-column label="所属字段">
              <template #default="scope">
                <ellipsisText :value="scope.row.columnName">{{
                  scope.row.columnName || "-"
                }}</ellipsisText>
              </template></el-table-column
            >
            <el-table-column label="数据库IP">
              <template #default="scope">
                <ellipsisText :value="scope.row.databaseIp">{{
                  scope.row.databaseIp || "-"
                }}</ellipsisText>
              </template></el-table-column
            >
            <el-table-column label="属性值">
              <template #default="scope">
                <ellipsisText
                  :value="getFieldValueDisplay(scope.row)"
                ></ellipsisText>
              </template>
            </el-table-column>
            <el-table-column label="备注">
              <template #default="scope">
                <ellipsisText :value="scope.row.remark">{{
                  scope.row.remark || "-"
                }}</ellipsisText>
              </template></el-table-column
            >
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { onMounted, ref, toRefs } from "vue";
import { useStore } from "vuex";
import { FIELD_TYPE_NAME_MAP } from "@/utils/constant";
import { APP_TYPE, ATTR_TYPE_MAP } from "@/utils/dataPlatform";
import { IMidHandleDetail } from "@/types/handle";
import {
  ApiGetEntityObjList,
  ApiGetEntityTypeTree,
  ApiGetMidHandleDetail,
} from "@/api/objHandle/manager";
import ellipsisText from "@/components/ellipsisText/index.vue";

const activeName = ref("basic");
const store = useStore();
const { appType, isProvinceUser } = toRefs(store.getters);

const emit = defineEmits(["close"]);

const props = defineProps({
  id: {
    type: String,
    default: undefined,
  },
  handle: {
    type: String,
    default: undefined,
  },
  isMasterData: {
    type: Boolean,
    default: false,
  },
});

const drawerVisible = ref(true);

const loading = ref(false);

const detail = ref<IMidHandleDetail>();

const entityObjTypeName = ref("");
const entityObjName = ref("");

const getFieldValueDisplay = (row: any) => {
  if (row.fieldType === ATTR_TYPE_MAP.RELATE_HANDLE) {
    if (row.references && row.references.length > 0) {
      return row.references[0].referenceHandle;
    }
    return "-";
  }
  return row.fieldValue;
};

const getHandleType = (id: number | undefined) => {
  if (id === 1) return "主数据标识";
  if (id === 2) return "非主数据标识";
  return "-";
};
function getEntityTypeName(type: number | undefined) {
  if (`${type}` === `1`) return "业务实体";
  if (`${type}` === `2`) return "资源实体";
  return "-";
}

// 获取标识详情接口
async function getHandleDetail() {
  const params = isProvinceUser.value
    ? { handle: props.handle }
    : { id: props.id };
  loading.value = true;
  await ApiGetMidHandleDetail(params, isProvinceUser.value)
    .then((res: any) => {
      detail.value = res;
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleClose() {
  emit("close");
}

// 递归查找
function findPathById(node: any, targetId: string | undefined): string {
  console.log(node, targetId);
  // 递归遍历树结构并构建路径
  function findInTree(node: any, path: string[] = []): string {
    path.push(node.categoryName); // 将当前节点的 name 添加到路径中
    if (node.id === targetId) {
      return path.join("/"); // 如果找到目标节点，返回路径
    }

    if (node.children && node.children.length > 0) {
      // 继续遍历子节点
      for (const child of node.children) {
        const result = findInTree(child, path.slice()); // 使用副本，以避免影响其他路径
        if (result) {
          return result; // 如果找到匹配，立即返回结果
        }
      }
    }

    return ""; // 没有找到匹配的节点
  }
  return findInTree(node, []);
}

// 解析获得--实体对象类型和实体对象字段
async function getBaseInfo(
  entityObjTypeId: string | undefined,
  entityObjId: string | undefined
) {
  ApiGetEntityTypeTree().then(async (res: any) => {
    console.log("tree", res);
    entityObjTypeName.value = findPathById(res, entityObjTypeId);
    if (entityObjTypeId) {
      await ApiGetEntityObjList({
        categoryId: entityObjTypeId,
      }).then((ObjectRes: any) => {
        const foundItem = ObjectRes.find(
          (item: any) => item.id === entityObjId
        );
        entityObjName.value = foundItem?.name ?? "";
      });
    }
  });
}

onMounted(async () => {
  await getHandleDetail();
  if (!props.isMasterData) {
    getBaseInfo(detail.value?.entityObjectTypeId, detail.value?.entityObjectId);
  }
});
</script>
<style lang="scss" scoped>
.el-divider {
  margin: 8px 0 24px 0;
}
:deep(.el-descriptions__cell) {
  display: flex;
}
:deep(.el-descriptions__label) {
  width: 72px;
  min-width: 72px;
  display: inline-block;
}
:deep(.el-tabs__nav-wrap::after) {
  content: none;
}
:deep(.el-tabs__active-bar) {
  display: none;
}
:deep(.el-tabs__item) {
  height: auto;
  line-height: normal;
}
:deep(.el-tabs--top .el-tabs__item.is-top:nth-child(2)) {
  border-right: 2px solid #c1c9c7;
}

.yc-description-basic {
  width: 1136px;
}

:deep(.el-descriptions__content) {
  word-break: break-all;
  display: inline-block;
  vertical-align: middle;
}
</style>
