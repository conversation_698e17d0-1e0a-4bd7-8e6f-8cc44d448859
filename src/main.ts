import { createApp, Directive } from "vue";
import Clipboard from "v-clipboard";

import Cookies from "js-cookie";

import ElementPlus from "element-plus";
import "element-plus/dist/index.css";

import locale from "element-plus/es/locale/lang/zh-cn";

import "@/assets/styles/index.scss"; // global css

import App from "./App.vue";
import store from "./store/index";
import router from "./router/index";

// 使用rem
import "@/utils/rem";

// 注册指令
import plugins from "./plugins"; // plugins
import { download } from "@/utils/request";
import echarts from "./utils/echarts";
// svg图标
import "virtual:svg-icons-register";
import SvgIcon from "@/components/SvgIcon/index.vue";
import elementIcons from "@/components/SvgIcon/svgicon";

import "./permission"; // permission control
import * as directives from "@/directives";

import {
  parseTime,
  resetForm,
  addDateRange,
  handleTree,
  selectDictLabel,
} from "@/utils/ruoyi";

// 页面标题组件
import TitleBar from "@/components/titleBar/index.vue";

const app = createApp(App);

// 全局方法挂载
app.config.globalProperties.download = download;
app.config.globalProperties.parseTime = parseTime;
app.config.globalProperties.resetForm = resetForm;
app.config.globalProperties.handleTree = handleTree;
app.config.globalProperties.addDateRange = addDateRange;
app.config.globalProperties.selectDictLabel = selectDictLabel;
app.config.globalProperties.$echarts = echarts;
// 全局组件挂载
app.component("TitleBar", TitleBar);

// 自定义全局指令
// app.directive("permission", permission);
Object.keys(directives).forEach((key) => {
  // Object.keys() 返回一个数组，值是所有可遍历属性的key名
  // key是自定义指令名字；后面应该是自定义指令的值，值类型是string
  app.directive(key, (directives as { [key: string]: Directive })[key]);
});

app.use(router);
app.use(store);
app.use(plugins);
app.use(elementIcons);
app.component("svg-icon", SvgIcon);

// 使用element-plus 并且设置全局的大小
app.use(ElementPlus, {
  locale,
  // 支持 large、default、small
  size: Cookies.get("size") || "default",
});

app.mount("#app");
