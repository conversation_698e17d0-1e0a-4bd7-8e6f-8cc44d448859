import request from "@/utils/request";

// 获取对象列表
export function getObjectList(query: {
  name: string;
  type: number;
  page: number;
  size: number;
}) {
  return request({
    url: `/v1/object/handle/page`,
    method: "get",
    params: query,
  });
}

// 获取对象详情
export function getObjectDetail(query: { id: number; type: number }) {
  return request({
    url: `/v1/object/handle/detail`,
    method: "get",
    params: query,
  });
}

// 标识注册
export function identityCreate(query: {
  handle: string;
  objectHandle: string;
  values: { value: string }[];
}) {
  return request({
    url: `/v1/handle/create`,
    method: "post",
    data: query,
  });
}

// 标识查询
export function identitySearch(query: { handle: string }) {
  return request({
    url: `/v1/handle/query`,
    method: "get",
    params: query,
  });
}

// 标识维护
export function identityUpdate(query: {
  handle: string;
  objectHandle: string;
  values: { value: string }[];
}) {
  return request({
    url: `/v1/handle/update`,
    method: "put",
    data: query,
  });
}

// 标识删除
export function identityDelete(query: { handle: string }) {
  return request({
    url: `/v1/handle/delete`,
    method: "delete",
    params: query,
  });
}
