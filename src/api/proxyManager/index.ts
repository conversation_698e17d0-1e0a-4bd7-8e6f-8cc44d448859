import request from "@/utils/request";
import { IManagerList } from "@/types/proxyManage";

// 获取前缀
// export function getPrefix(query?: { prefix: string }) {
//   return request<IPrefixList>({
//     url: `/v1/entPrefix/getEntPrefixList`,
//     method: "get",
//     params: query,
//   });
// }

// 获取服务
// export function getserver(query?: { srvName: string }) {
//   return request<IServerList>({
//     url: `/v1/hostingServer/getHostingServer`,
//     method: "get",
//     params: query,
//   });
// }

// 获取托管列表
export function getManagerList(query: {
  srvName: string;
  page: number;
  size: number;
}) {
  return request<IManagerList>({
    url: `/v1/entProxy/getEntProxy`,
    method: "get",
    params: query,
  });
}

// 添加托管
export function addManagerData(query: {
  srvName: string;
  ip: string;
  ipType: number;
  tcpPort: number | null;
  udpPort: number | null;
  httpPort: number | null;
  ipTypeReslove: number;
  tcpPortReslove: number | null;
  udpPortReslove: number | null;
  httpPortReslove: number | null;
  ipReslove: string;
}) {
  return request({
    url: `/v1/entProxy/config`,
    method: "post",
    data: query,
  });
}

// 编辑托管
export function editManagerData(query: {
  srvName: string;
  ip: string;
  ipType: number;
  tcpPort: number | null;
  udpPort: number | null;
  httpPort: number | null;
  ipTypeReslove: number;
  tcpPortReslove: number | null;
  udpPortReslove: number | null;
  httpPortReslove: number | null;
  ipReslove: string;
}) {
  return request({
    url: `/v1/entProxy/config`,
    method: "put",
    data: query,
  });
}

// 删除托管
export function deleteManagerData(query: { id: number }) {
  return request({
    url: `/v1/entProxy/config`,
    method: "delete",
    params: query,
  });
}
