import request from "@/utils/request";
import { IApplySearchData } from "@/types/entAllot";

// 申请列表
export function getApplyList(query: {
  entPrefix: string;
  startTime: string;
  endTime: string;
  page: number;
  size: number;
}) {
  return request<IApplySearchData>({
    url: "/v1/prefix/hosting/apply/pageApplyList",
    method: "get",
    params: query,
  });
}

// 申请托管
export function apiApplayAllot(data: { entPrefix: string }) {
  return request({
    url: "/v1/prefix/hosting/apply/applyHostingServer",
    method: "post",
    data,
  });
}

// 取消托管
export function apiCancelAllot(data: { entPrefix: string }) {
  return request({
    url: "/v1/prefix/hosting/audit/cancelHostingServer",
    method: "post",
    data,
  });
}
