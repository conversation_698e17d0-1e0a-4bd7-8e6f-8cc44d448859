import request from "@/utils/request";
import { ISearchData, IServerSearchData, IAllotDetail } from "@/types/entAllot";

// 审核列表
export function getAuditList(query: {
  entPrefix: string;
  entName: string;
  auditState: string;
  startTime: string;
  endTime: string;
  page: number;
  size: number;
}) {
  return request<ISearchData>({
    url: "v1/prefix/hosting/audit/pageAuditList",
    method: "get",
    params: query,
  });
}

// 服务器列表
export function getSeverList(query: { page: number; size: number }) {
  return request<IServerSearchData>({
    url: "/v1/hostingServer/page",
    method: "get",
    params: query,
  });
}

// 分配服务器
export function allotHostingSever(data: {
  entPrefix: string;
  hostingServerId: string;
}) {
  return request({
    url: "/v1/prefix/hosting/audit/distributionHostingServer",
    method: "post",
    data,
  });
}

// 取消托管
export function apiCancelAllot(data: { entPrefix: string }) {
  return request({
    url: "/v1/prefix/hosting/audit/cancelHostingServer",
    method: "post",
    data,
  });
}

// 托管详情
export function apiGetAllotDetail(query: { entPrefix: string }) {
  return request<IAllotDetail>({
    url: "/v1/prefix/hosting/audit/detail",
    method: "get",
    params: query,
  });
}

// 驳回申请
export function apiRejectApply(data: { entPrefix: string }) {
  return request({
    url: "/v1/prefix/hosting/audit/rejectlHostingServer",
    method: "post",
    data,
  });
}
