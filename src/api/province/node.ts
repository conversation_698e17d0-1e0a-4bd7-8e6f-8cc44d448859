/*
 * @Author: <PERSON>@gmail.com
 * @Date: 2022-11-08 10:41:10
 * @LastEditors: <PERSON> q<PERSON>@gmail.com
 * @LastEditTime: 2022-11-08 16:53:22
 * @FilePath: /id-yc-province-web/src/province/node.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from "@/utils/request";

// 节点统计
export function nodeSummary(query?: any) {
  return request({
    url: "/v1/node_info/statistics",
    method: "get",
    params: query,
  });
}

// 节点详情
export function nodeInfo(query?: any) {
  return request({
    url: "/v1/node_info",
    method: "get",
    params: query,
  });
}

// 节点修改
export function editNode(data: {
  entName: string;
  nodeName: string;
  nodePrefix: string;
  nodeAddress: string;
}) {
  return request({
    url: "/v1/node_info",
    method: "post",
    data,
  });
}

// 账户查询
export function accountInfo(query?: any) {
  return request({
    url: "/v1/self_info",
    method: "get",
    params: query,
  });
}

// 账户修改
export function editAccount(data: {
  remark: string;
  address: string;
  phone: string;
  email: string;
}) {
  return request({
    url: "/v1/self_info",
    method: "post",
    data,
  });
}

// 修改密码
export function editPwd(data: { oldPwd: string; newPwd: string }) {
  return request({
    url: "/v1/self_info/pwd",
    method: "post",
    data,
  });
}
