/*
 * @Author: <PERSON>@gmail.com
 * @Date: 2022-11-08 10:41:10
 * @LastEditors: <PERSON> q<PERSON>@gmail.com
 * @LastEditTime: 2022-11-08 16:53:22
 * @FilePath: /id-yc-province-web/src/province/node.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from "@/utils/request";

// 申请列表
export function applyList(query: {
  entPrefix: string;
  page: number;
  size: number;
}) {
  return request({
    url: "/v1/prefix/apply/page",
    method: "get",
    params: query,
  });
}

// 查看详情
export function applyItemDetail(query: { id: number }) {
  return request({
    url: "/v1/prefix/apply/detail",
    method: "get",
    params: query,
  });
}

// 申请详情
export function applyDetail(query?: any) {
  return request({
    url: "/v1/prefix/apply/info",
    method: "get",
    params: query,
  });
}

// 申请前缀
export function applyPrefix(data: any) {
  return request({
    url: "/v1/prefix/apply",
    method: "post",
    data,
  });
}
