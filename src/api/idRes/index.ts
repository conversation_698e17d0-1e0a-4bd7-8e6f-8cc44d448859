import request from "@/utils/request";
import { getPublicKey, encrypt } from "@/utils/forgeEncrypt";

// 标识解析
export function idResolve(query: any) {
  const publicKey = getPublicKey();
  return request({
    url: "/v1/public/handle/resolve",
    method: "get",
    params: query,
  });
}

// 标识解析
export function ApiGetRelateList(query: Record<string, any>) {
  return request({
    url: "/v1/public/handle/resolve/much",
    method: "get",
    params: query,
  });
}
