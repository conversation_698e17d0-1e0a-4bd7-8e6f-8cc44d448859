import request from "@/utils/request";
import { IAppList, IPrefixData } from "@/types/appManage";

// 获取应用列表
export function getAppListApi(params: any) {
  return request<IAppList>({
    url: "/v1/appInfo/pageAppInfo",
    method: "get",
    params,
  });
}

// 新增应用
export function addAppInfoApi(params: {
  appName: string;
  deployAddress: string;
  entId: number | null;
  prefixId: number | null;
  sysVersion: string;
}) {
  return request({
    url: "/v1/appInfo/createAppInfo",
    method: "post",
    data: params,
  });
}

// 编辑应用
export function editAppInfoApi(params: any) {
  return request({
    url: "/v1/appInfo/updateAppInfo",
    method: "post",
    data: params,
  });
}

// 查看详情
export function getAppDetailApi(id: any) {
  return request({
    url: "/v1/appInfo/detail",
    method: "get",
    params: { id },
  });
}

// 查看详情
export function getKeyPair() {
  return request({
    url: "/v1/appInfo/createKeyPair",
    method: "get",
  });
}

// 更新秘钥
export function updateKey(params: any) {
  return request({
    url: "/v1/appInfo/securityCertificate",
    method: "post",
    data: params,
  });
}

// 删除应用
export function deleteAppApi(params: { id: number }) {
  return request({
    url: "/v1/appInfo/removeAppInfo",
    method: "delete",
    params,
  });
}

// 获取前缀
export function getPrefix(query?: { prefix: string }) {
  return request<IPrefixData>({
    url: `/v1/appInfo/queryPrefixList`,
    method: "get",
    params: query,
  });
}

// 获取企业中台应用树
export function getMpDmmAppTree() {
  return request<Record<string, any>>({
    url: `/v1/appInfo/mp-dmm-app/tree`,
    method: "get",
  });
}
