import request from "@/utils/request";

// 应用身份列表
export function apiIdentifyList(params: any) {
  return request({
    url: "/v1/appHandle/page",
    method: "get",
    params,
  });
}

// 应用身份列表
export function apiIdentifyGenKey() {
  return request({
    url: "/v1/appHandle/createSecretKey",
    method: "get",
  });
}

// 新增
export function apiIdentifyAdd(data: any) {
  return request({
    url: "/v1/appHandle/createAppHandle",
    method: "post",
    data,
  });
}

// 编辑
export function apiIdenfityEdit(data: any) {
  return request({
    url: "/v1/appHandle/updateAppHandle",
    method: "put",
    data,
  });
}

// 详情
export function apiIdentifyDetail(id: any) {
  return request({
    url: "/v1/appHandle/detail",
    method: "get",
    params: { id },
  });
}

// 删除
export function apiIdentifyDelete(id: any) {
  return request({
    url: "/v1/appHandle/deleteAppHandle",
    method: "delete",
    params: { id },
  });
}

// 分配授权
export function apiIdentifyGrant(data: any) {
  return request({
    url: "/v1/appHandle/allotAuthGroup",
    method: "post",
    data,
  });
}

// 权限组列表2
export function apiIdentifyGrantGroupList(id: any) {
  return request({
    url: "/v1/appHandle/authGroupList",
    method: "get",
    params: { appHandleAuthId: id },
  });
}

// 权限组列表
export function apiAuthGroupList() {
  return request({
    url: "/v2/group/addHandleGroupList",
    method: "get",
  });
}

// 身份授权列表
export function apiGrantIdentifyList(params: any) {
  return request({
    url: "/v1/appHandle/authPage",
    method: "get",
    params,
  });
}
