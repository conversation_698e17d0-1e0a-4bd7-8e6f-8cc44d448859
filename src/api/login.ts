import request from "@/utils/request";
import { IConfig, ILogin, IUserInfo, IBind } from "@/types/global";

// 登录方法
export function login(
  username: string,
  password: any,
  captcha: any,
  provinceId: string
) {
  return request<ILogin>({
    url: "/v1/login/name",
    headers: {
      isToken: false,
    },
    method: "post",
    data: { username, password, captcha, provinceId },
  });
}

// 业务中台登录
export function loginWithMiddle(
  username: string,
  password: any,
  captcha: any,
  provinceId: any
) {
  return request<ILogin>({
    url: "/v1/user/center/login/name",
    // url: "/v1/social/user/login/exist",
    headers: {
      isToken: false,
    },
    method: "post",
    data: { username, password, captcha, provinceId },
  });
}

// 关联系统绑定
export function systemBind(data: {
  username: string;
  password: any;
  userCenterId: any;
}) {
  return request<ILogin>({
    url: "/v1/social/user/login/binding",
    headers: {
      isToken: false,
    },
    method: "post",
    data,
  });
}

// 判断中台用户是否绑定
export function systemBindExist(data: { username: string; password: any }) {
  return request<IBind>({
    url: "/v1/social/user/login/exist",
    headers: {
      isToken: false,
    },
    method: "post",
    data,
  });
}

// 公钥查询
export function ApiGetPublicKey() {
  return request({
    url: "/v1/public/publicKey",
    headers: {
      isToken: false,
    },
    method: "get",
  });
}

// 刷新方法
export function refreshToken(data: any) {
  return request<{ data: any }>({
    url: "/auth/refresh",
    method: "post",
    data,
  });
}

// 获取登录用户信息
export function getInfo() {
  return request<IUserInfo>({
    url: "/v1/users/user/info",
    method: "get",
  });
}

// 退出方法
export function logout(data: any) {
  return request({
    url: "/v1/logout",
    method: "delete",
    data,
  });
}

// 获取滑块验证码
export function getCaptcha(data: any) {
  return request({
    url: "/v1/public/captcha/get",
    method: "post",
    data,
  });
}

// 验证滑块验证码
export function checkCaptcha(data: any) {
  return request({
    url: "/v1/public/captcha/check",
    method: "post",
    data,
  });
}

// 获取全局信息配置
export function getGlobalConfig() {
  return request<IConfig>({
    url: "/v1/public/config",
    method: "get",
  });
}

// 发送邮件获取验证码
export function sendEmail(param: any) {
  return request({
    url: "/v1/users/forget/sendEmail",
    method: "post",
    data: param,
  });
}

// 重置密码
export function forget(param: any) {
  return request({
    url: "/v1/users/forget/submit",
    method: "post",
    data: param,
  });
}

// 修改资料
export function editInfo(param: any) {
  return request({
    url: "/v1/users/updateBySelf/info",
    method: "post",
    data: param,
  });
}

// 省级租户列表（登录前）
export function getProvinceTenantList(params: any) {
  return request({
    url: "/v1/province_tenant/public/list",
    headers: {
      isToken: false,
    },
    method: "get",
    params,
  });
}
