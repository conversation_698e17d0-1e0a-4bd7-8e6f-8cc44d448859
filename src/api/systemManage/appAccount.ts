import request from "@/utils/request";
import {
  IAppAccountList,
  FormData,
  IAppDetail,
} from "@/types/system/appAccount";

// 应用账户列表
export function accountList(params: {
  username: string;
  appName: string;
  currentPage: number;
  pageSize: number;
}) {
  return request<IAppAccountList>({
    url: "/v1/appuser",
    method: "get",
    params,
  });
}

// 新增应用账户
export function addAppApi(data: FormData) {
  return request({
    url: "/v1/appuser",
    method: "post",
    data,
  });
}

// 编辑账户
export function updateAppApi(data: {
  username: string | null;
  remark: string | null;
  appId: number | null;
}) {
  return request({
    url: "/v1/appuser",
    method: "put",
    data,
  });
}

// 获取应用账户详情
export function getAppDetailApi(params: { id: number | null }) {
  return request<IAppDetail>({
    url: "/v1/appuser/detail",
    method: "get",
    params,
  });
}

// 删除应用用户
export function deleteAccountApi(params: { id: number }) {
  return request({
    url: "/v1/appuser",
    method: "delete",
    params,
  });
}
