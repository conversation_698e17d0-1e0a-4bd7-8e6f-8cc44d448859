import request from "@/utils/request";
import { IRoleList, RoleListItem, IRoleListItem } from "@/types/system/role";

const prefixUrl = "/v1/role";
// 角色列表
export function roleListApi(params: {
  roleName: string;
  currentPage: number;
  pageSize: number;
}) {
  return request<IRoleList>({
    url: `${prefixUrl}/list`,
    method: "get",
    params,
  });
}

// 删除角色
export function deleteRoleApi(data: { id: number }) {
  return request({
    url: `${prefixUrl}/delete`,
    method: "post",
    data,
  });
}

// 新增角色
export function addRoleApi(data: {
  roleName: string | null;
  roleCode: string | null;
  sort: number;
}) {
  return request({
    url: `${prefixUrl}/add`,
    method: "post",
    data,
  });
}

// 更新角色
export function updateRoleApi(data: {
  roleName: string | null;
  roleCode: string | null;
  sort: number;
  id?: number;
}) {
  return request({
    url: `${prefixUrl}/update`,
    method: "post",
    data,
  });
}

// 角色详情
export function getDetailRoleApi(params: { id: number }) {
  return request<RoleListItem>({
    url: `${prefixUrl}/detail`,
    method: "get",
    params,
  });
}

// 获取排序列表
// export function sortListApi(data) {
//   return request({
//     url: `${prefixUrl}/sort`,
//     method: "post",
//     data,
//   });
// }

// 角色下拉框
export function roleDropdown(params?: string) {
  return request<IRoleListItem[]>({
    url: "/v1/role/all",
    method: "get",
    params,
  });
}

// 角色下拉框---无应用角色
export function roleDropdownNoApp(params?: string) {
  return request<IRoleListItem[]>({
    url: "/v1/role/user/roleList",
    method: "get",
    params,
  });
}
