import request from "@/utils/request";
import {
  IProvinceList,
  IFormData,
  IOrgInfo,
} from "@/types/system/provinceManager";

// 账户列表
export function apiGetProvinceList(params: {
  prefix: string;
  nodeName: string;
  startTime: string;
  endTime: string;
  page: number;
  size: number;
}) {
  return request<IProvinceList>({
    url: "/v1/province_tenant/page",
    method: "get",
    params,
  });
}

// 新增
export function apiAddNode(data: IFormData) {
  return request({
    url: "/v1/province_tenant/save",
    method: "post",
    data,
  });
}

// 获取组织机构信息
export function apiGetOrgInfo(params: { bizCode: string }) {
  return request<IOrgInfo>({
    url: "/v1/province_tenant/bizInfo",
    method: "get",
    params,
  });
}
// 删除用户
export function apiDeleteProvince(params: { id: number }) {
  return request({
    url: "/v1/province_tenant/delete",
    method: "delete",
    params,
  });
}
