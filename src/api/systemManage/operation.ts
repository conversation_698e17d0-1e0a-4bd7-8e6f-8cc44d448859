import request from "@/utils/request";
import {
  INodeDetail,
  IOptBodyDetail,
  IContact,
  ITech,
  IBuss,
} from "@/types/system/operation";

// 节点详情接口
export function nodeDetailApi(query?: string) {
  return request<INodeDetail>({
    url: "/v1/node_info",
    method: "get",
    params: query,
  });
}

// 获取省市接口
export function provinceListApi() {
  return request({
    url: "/v1/dict/findLinkage",
    method: "get",
    params: {
      first: 86,
      key: "PROVINCE",
    },
  });
}

// 获取运营主体和通讯信息
export function optBodyDetailApi(query?: string) {
  return request<IOptBodyDetail>({
    url: "/v1/manage/detail",
    method: "get",
    params: query,
  });
}

// 保存运营主体和通讯信息
export function saveOptBodyApi(data: {
  manageOrgAddr: string;
  manageOrgCity: string;
  manageOrgCrtCode: string;
  manageOrgDesc: string;
  manageOrgIndustry: string;
  manageOrgName: string;
  manageOrgNature: string;
  manageOrgProvince: string;
  manageWebsite: string;
  manageContactAddr: string;
}) {
  return request({
    url: "/v1/manage/ope",
    method: "post",
    data,
  });
}

// 获取技术详情接口
export function getTechDetailApi(params?: string) {
  return request<ITech>({
    url: "/v1/manage/tech",
    method: "get",
    params,
  });
}
// 保存技术详情接口
export function saveTechDetailApi(data: {
  id: number;
  codeType: string;
  networkBandWidth: string;
  opeLine: string;
  serviceIp: string;
  servicePort: number;
  techSupporter: string;
}) {
  return request({
    url: "/v1/manage/tech",
    method: "post",
    data,
  });
}

// 获取业务运营
export function getBussDetailApi(params?: string) {
  return request<IBuss>({
    url: "/v1/manage/buss",
    method: "get",
    params,
  });
}
// 保存业务运营
export function saveBussDetailApi(data: {
  capitalSource: string;
  inputPeople: number;
  receivedAwards: string;
}) {
  return request({
    url: "/v1/manage/buss",
    method: "post",
    data,
  });
}

// 获取联系人列表
export function getContactApi(params?: string) {
  return request<IContact[]>({
    url: "/v1/manage/contact",
    method: "get",
    params,
  });
}
// 保存联系人
export function saveContactApi(data: any) {
  return request({
    url: "/v1/manage/contact",
    method: "post",
    data,
  });
}
