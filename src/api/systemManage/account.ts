import request from "@/utils/request";
import {
  IAccountList,
  IEntInfo,
  IAdminDetail,
  IBindInfo,
} from "@/types/system/account";

const prefixUrl = "/v1/users";
// 账户列表
export function accountList(params: {
  provinceId?: string;
  username: string;
  entName: string;
  type: number;
  currentPage: number;
  pageSize: number;
}) {
  return request<IAccountList>({
    url: `${prefixUrl}/list`,
    method: "get",
    params,
  });
}

// 新增管理员账户
export function addAdminApi(data: {
  roles?: Array<number>;
  username: string;
  nickName: string | null;
  phone: string;
  email: string | null;
  address: string | null;
}) {
  return request({
    url: `${prefixUrl}/add/admin`,
    method: "post",
    data,
  });
}

// 新增企业账户
export function addEntApi(data: {
  entId: number | null;
  username: string;
  nickName: string;
  phone: string;
  email: string;
  address: string;
}) {
  return request({
    url: `${prefixUrl}/add/ent`,
    method: "post",
    data,
  });
}

// 编辑账户
export function updateAdminApi(data: {
  roles?: Array<number>;
  username: string;
  nickName: string | null;
  phone: string;
  email: string | null;
  address: string | null;
}) {
  return request({
    url: `${prefixUrl}/update/info`,
    method: "post",
    data,
  });
}

// 获取账户详情
export function getAdminDetailApi(params: { id: number }) {
  return request<IAdminDetail>({
    url: `${prefixUrl}/detail`,
    method: "get",
    params,
  });
}

// 修改密码
export function updatePwdApi(data: {
  id: number;
  oldPwd: string;
  newPwd: string;
  duplicatePwd: string;
}) {
  return request({
    url: `${prefixUrl}/update/pwd`,
    method: "post",
    data,
  });
}

// 绑定账号
export function bindHdlApi(data: any) {
  return request({
    url: `v1/social/user/binding`,
    method: "post",
    data,
  });
}

// 解除绑定
export function unbindHdlApi(data: { id: number | null }) {
  return request({
    url: `v1/social/user/unbinding`,
    method: "put",
    data,
  });
}

// 删除用户
export function deleteAccountApi(data: { id: number }) {
  return request({
    url: `${prefixUrl}/delete`,
    method: "post",
    data,
  });
}

// 获取企业信息
export function getEntInfoApi(params?: string) {
  return request<IEntInfo>({
    url: `${prefixUrl}/user/ent/info`,
    method: "get",
    params,
  });
}

// 获取绑定用户详情
export function ApiGetBindInfo(params: { id: number }) {
  return request<IBindInfo>({
    url: `v1/social/user/detail`,
    method: "get",
    params,
  });
}

// 省级节点列表
export function getProvinceList(params: any) {
  return request({
    url: "/v1/province_tenant/list",
    method: "get",
    params,
  });
}

// 获取企业列表
export function apiEntList() {
  return request({
    url: "/v1/ent/list",
    method: "get",
  });
}
/**
 * 2.2.0
 */

// 获取行政组织根列表
export function apiRootOrgList() {
  return request({
    url: "/v1/middleground/listOrgRootOrgUnits",
    method: "get",
  });
}

// 获取组织子列表
export function apiOrgList(parentId: string, orgBizCode: string) {
  return request({
    url: "/v1/middleground/pageQueryOrgUnitByAttr",
    method: "post",
    data: { orgUnitId: parentId, orgBizCode },
  });
}

// 获取组织子列表
export function apiExtOrgList(orgBizCode: string) {
  return request({
    url: "/v1/middleground/pageQueryOrgUnitByAttr",
    method: "post",
    data: { orgBizCode },
  });
}

// 获取人员列表
export function apiEmployeeList(
  bizOrgCode: string,
  orgUnitId: string,
  searchTerm: string,
  pageNumber: number
) {
  return request({
    url: "/v1/middleground/pageQueryEmployee",
    method: "post",
    data: {
      orgUnitIds: [orgUnitId],
      bizOrgCode,
      pageNumber,
      pageSize: 5,
      searchTerm,
    },
  });
}

// 新增中台用户
export function apiAddSocialUser(data: any) {
  return request({
    url: `${prefixUrl}/add/social/user`,
    method: "post",
    data,
  });
}
// 更新中台用户
export function apiUpdateSocialUser(data: any) {
  return request({
    url: `${prefixUrl}/update/social/user`,
    method: "post",
    data,
  });
}

// 获取角色列表 /api/v1/role/all
export function apiRoleAll(type: number) {
  return request({
    url: "/v1/role/all",
    method: "get",
    params: { type },
  });
}

// 新增内部账号
export function apiAddInternalAccount(data: any) {
  return request({
    url: `${prefixUrl}/add/internalAccount`,
    method: "post",
    data,
  });
}

// 重置密码/api/v1/users/reset/pwd
export function apiUsersRestPwd(data: any) {
  return request({
    url: `${prefixUrl}/reset/pwd`,
    method: "post",
    data,
  });
}

// 查询企业下的应用列表/api/v1/appInfo/list
export function apiAppInfoList(entId: string | number) {
  return request({
    url: "/v1/appInfo/list",
    method: "get",
    params: {
      entId,
    },
  });
}

// 新增企业 /api/v1/ent/add
export function apiEntAdd(data: any) {
  return request({
    url: "/v1/ent/add",
    method: "post",
    data,
  });
}

export function apiAppList(entId: string) {
  return request({
    url: "/v1/appInfo/list",
    method: "get",
    params: { entId },
  });
}

// 获取用户详情/api/v1/users/detail
export function apiUsersDetail(id: any) {
  return request({
    url: `${prefixUrl}/detail`,
    method: "get",
    params: { id },
  });
}

export function apiPrefixList(entId: any) {
  return request({
    url: `v1/appInfo/entPrefixList`,
    method: "get",
    params: { entId },
  });
}
