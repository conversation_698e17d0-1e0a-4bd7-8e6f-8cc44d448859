import request from "@/utils/request";
import { IpermList } from "@/types/system/auth";

const prefixUrl = "/v1/auth/tree";

// 权限树列表
export function permListApi(params: { roleId: number }) {
  return request<IpermList[]>({
    url: `${prefixUrl}/list`,
    method: "get",
    params,
  });
}

// 更新树
export function updatePermApi(data: { roleId: number; authIdList: string[] }) {
  return request({
    url: `${prefixUrl}/update`,
    method: "post",
    data,
  });
}
