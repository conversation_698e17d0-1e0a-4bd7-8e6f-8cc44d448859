import request, { download } from "@/utils/request";

// 新增
export function ApiAdd(param: any) {
  return request({
    url: "/v1/data/service/create",
    method: "post",
    data: param,
  });
}

// 列表查询
export function ApiQueryList(params: any) {
  return request({
    url: "/v1/data/service/list",
    method: "get",
    params,
  });
}

// 修改
export function ApiEdit(data: any) {
  return request({
    url: "/v1/data/service/update",
    method: "put",
    data,
  });
}

// 删除
export function ApiDeleteById(id: any) {
  return request({
    url: "/v1/data/service/delete",
    method: "delete",
    params: { id },
  });
}

// 详情查询
export function ApiQueryDetail(params: { id: number }) {
  return request({
    url: "/v1/prefix/prepare/detail",
    method: "get",
    params,
  });
}

// 生成token-/api/v1/data/service/generate-token
export function apiGenerateToken() {
  return request({
    url: "/v1/data/service/generate-token",
    method: "get",
  });
}
