import request from "@/utils/request";
import {
  ISearchParams,
  ISearchData,
  IUpdateInstanceHandleParams,
  IInstanceHandleDetail,
} from "@/types/instanceHandle";

// 获取实例标识列表
export function ApiGetHandlePage(params: ISearchParams) {
  return request<ISearchData>({
    url: "/v1/handle/instance",
    method: "get",
    params,
  });
}

// 新增实例标识
export function ApiAddHandleInstance(params: IUpdateInstanceHandleParams) {
  return request({
    url: "/v1/handle/instance",
    method: "post",
    data: params,
  });
}

// 编辑实例标识
export function ApiUpdateHandleInstance(params: IUpdateInstanceHandleParams) {
  return request({
    url: "/v1/handle/instance",
    method: "put",
    data: params,
  });
}

// 获取实例标识详情
export function ApiGetHandleInstanceDetail(id: number) {
  return request<IInstanceHandleDetail>({
    url: `/v1/handle/instance/detail`,
    method: "get",
    params: { id },
  });
}

// 删除实例标识
export function ApiDeleteHandleInstance(id: number) {
  return request({
    url: `/v1/handle/instance`,
    method: "delete",
    params: { id },
  });
}
