import request from "@/utils/request";

// 构建解析SQL-数据库表列表
export function ApiDatabaseList(params: any) {
  return request<any>({
    url: "/v1/data-channel/resolve/database-list",
    method: "get",
    params,
  });
}

// /api/v1/data-channel/database/list
export function ApiDataChannelDatabaseList(id: string) {
  return request<any>({
    url: "/v1/data-channel/database/list",
    method: "get",
    params: { dataServiceId: id },
  });
}

// 构建解析SQL-数据库表详情
export function ApiDatabaseDetail(params: {
  modelId: string;
  handleId: string;
}) {
  return request<any>({
    url: "/v1/data-channel/resolve/table-detail",
    method: "get",
    params,
  });
}

// 构建解析SQL-构建解析sql
export function ApiBuildSql(params: any) {
  return request<any>({
    url: "/v1/data-channel/resolve/build-sql",
    method: "post",
    data: params,
  });
}

// 获取数据通道详情/api/v1/data-channel/detail
export function ApiDataChannelDetail(id: number | string) {
  return request<any>({
    url: "/v1/data-channel/detail",
    method: "get",
    params: { id },
  });
}

// 创建数据通道/api/v1/data-channel/create
export function ApiDataChannelCreate(params: any) {
  return request<any>({
    url: "/v1/data-channel/create",
    method: "post",
    data: params,
  });
}

// 创建数据通道/api/v1/data-channel/create
export function ApiDataChannelUpdate(params: any) {
  return request<any>({
    url: "/v1/data-channel/update",
    method: "post",
    data: params,
  });
}

// 获取数据服务列表-/api/v1/data/service/list
export function ApiServiceList() {
  return request<any>({
    url: "/v1/data/service/list",
    method: "get",
  });
}

// 数据通道-列表分页/api/v1/data-channel/page
export function ApiDataChannelPage(params: any) {
  return request<any>({
    url: "/v1/data-channel/page",
    method: "get",
    params,
  });
}

// 根据标识类型获取对象标识api/v1/data-channel/object-handle
export function ApiObjectHandle(params: any) {
  return request<any>({
    url: "v1/data-channel/object-handle",
    method: "get",
    params,
  });
}
// 下载
export function ApiDownload(id: number) {
  return request<any>({
    url: "/v1/data-channel/download",
    method: "get",
    params: { id },
  });
}
// 下发通道
export function ApiDeliveryChannel(id: number) {
  return request<any>({
    url: "/v1/data-channel/send",
    method: "get",
    params: { id },
  });
}
// 删除实例标识
export function ApiDeleteHandle(id: number) {
  return request({
    url: `/v1/data-channel/delete`,
    method: "delete",
    params: { id },
  });
}

// 共享通道
export function ApiShareChannel(id: number) {
  return request<any>({
    url: "/v1/data-channel/share",
    method: "get",
    params: { id },
  });
}

// 取消共享通道
export function ApiCancelShareChannel(id: number) {
  return request<any>({
    url: "/v1/data-channel/cancelShare",
    method: "get",
    params: { id },
  });
}

// 取消共享通道
export function ApiIdentifyOfShareChannel(params: any) {
  return request<any>({
    url: "/v1/data-channel/objectHandlePage",
    method: "get",
    params,
  });
}

// 查询数据服务中的通道信息
export function ApiGetChannelInfo(
  dataChannelId: string,
  dataServiceId: string
) {
  return request<any>({
    url: "/v1/data-channel/query",
    method: "get",
    params: { dataChannelId, dataServiceId },
  });
}

// 标识解析数据源-获取数据库/api/v2/object-handle/database-table/list
export function ApiDatabaseTableList(databaseId: string) {
  return request<any>({
    url: "/v2/object-handle/database-table/list",
    method: "get",
    params: { databaseId },
  });
}

// 标识解析数据源-根据数据库获取字段/api/v2/object-handle/table-column/list
export function ApiTableColumnList(tableId: any) {
  return request<any>({
    url: "/v2/object-handle/table-column/list",
    method: "get",
    params: { tableId },
  });
}
