import request from "@/utils/request";

// 获取元数据列表
export function getDataListApi(params: any) {
  return request({
    url: "/v1/meta-data/page",
    method: "get",
    params,
  });
}

// 删除元数据
export function deleteMetaDataApi(params: { id: number }) {
  return request({
    url: "/v1/meta-data/delete",
    method: "delete",
    params,
  });
}

// 查看元数据详情
export function getMetaDataDetailApi(id: number) {
  return request({
    url: "/v1/meta-data/detail",
    method: "get",
    params: { id },
  });
}

// 元数据对象-获取数据库下面的所有表/api/v1/meta-data/table/list
export function getMetaDataTableListApi(params: any) {
  return request({
    url: "/v1/meta-data/table/list",
    method: "get",
    params,
  });
}

// 获取数据服务列表
export function getDataServiceApi() {
  return request({
    url: "/v1/data/service/list",
    method: "get",
  });
}

// 获取数据库列表
export function getDatabaseListApi(id: number) {
  return request<any>({
    url: "/v1/data-channel/database/list",
    method: "get",
    params: { dataServiceId: id },
  });
}

// 新增元数据
export function addMetaDataApi(params: any) {
  return request({
    url: "/v1/meta-data/add",
    method: "post",
    data: params,
  });
}

// 更新元数据
export function updateMetaDataApi(params: any) {
  return request({
    url: "/v1/meta-data/update",
    method: "put",
    data: params,
  });
}

// 获取元数据表列表
export function getMetTableListApi(params: {
  tableName?: string;
  page: string;
  size: string;
  dbId: number;
}) {
  return request({
    url: "/v1/meta-data/table/page",
    method: "get",
    params,
  });
}

// 获取元数据表列表
export function getMetaDetailListApi(params: any) {
  return request({
    url: "/v1/meta-data/table/detail",
    method: "get",
    params,
  });
}
