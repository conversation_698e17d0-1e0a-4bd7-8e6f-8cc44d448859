import request from "@/utils/request";
import { ISearchData, ISysSearchData } from "@/types/logs";

// 获取系统日志列表
export function sysLogInfo(query: {
  pageNo: number;
  pageSize: number;
  userName: string;
}) {
  return request<ISysSearchData>({
    url: "/v1/log/sysLogInfo",
    method: "get",
    params: query,
  });
}

// 获取数据日志列表
export function dataLogInfo(query: { pageNo: number; pageSize: number }) {
  return request<ISearchData>({
    url: "/v1/log/dataLogInfo",
    method: "get",
    params: query,
  });
}
