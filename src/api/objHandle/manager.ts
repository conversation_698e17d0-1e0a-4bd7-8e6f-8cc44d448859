import request from "@/utils/request";
import { IHandleDetail, IDataServiceItem } from "@/types/handle";

export function ApiGetHandlePage(params: {
  name: string;
  handle?: string;
  page: number;
  size: number;
  orgName?: string;
  appId: string;
}) {
  return request({
    url: "/v1/object/handle/page",
    method: "get",
    params,
  });
}

// 获取前缀
export function ApiGetHandlePrefixDropDownList() {
  return request({
    url: "/v1/object/handle/prefix/drop-down-list",
    method: "get",
  });
}

// 查看对象标识详情
export function ApiGetHandleDetail(params: any) {
  return request<IHandleDetail>({
    url: "/v1/object/handle/detail",
    method: "get",
    params,
  });
}

// 新增对象标识
export function ApiAddObjectHandle(data: any) {
  return request({
    url: "v1/object/handle/create",
    method: "post",
    data,
  });
}

// 删除对象标识
export function ApiDeleteObjectHandle(id: number) {
  return request({
    url: "/v1/object/handle/delete",
    method: "DELETE",
    params: { id },
  });
}

// 更新对象标识
export function ApiUpdateObjectHandle(params: any) {
  return request({
    url: "/v1/object/handle/update",
    method: "put",
    data: params,
  });
}

// 省级-查看对象标识列表
export function ApiGetProvinceObjHandle(params: {
  name: string;
  handle?: string;
  page: number;
  size: number;
  orgName?: string;
  appId: string;
}) {
  return request({
    url: "/v1/province/object/handle/page",
    method: "get",
    params,
  });
}

// 查看详情
export function ApiGetProvinceObjHandleDetail(params: any) {
  return request<IHandleDetail>({
    url: "/v1/province/object/handle/detail",
    method: "get",
    params,
  });
}

// 所属应用下拉框
export function appList(params?: string) {
  return request({
    url: "/v1/appInfo/appList",
    method: "get",
    params,
  });
}

// 测试数据库连接
// export function testConnection(params: any) {
//   return request({
//     url: "/v1/object/handle/testConnection",
//     method: "post",
//     data: params,
//   });
// }

// 数据服务列表
export function getServiceListApi(param?: any) {
  return request<IDataServiceItem[]>({
    url: "/v1/data/service/list",
    method: "get",
  });
}

// 自研数据库列表
export function getSelfDevelopListApi(params?: any) {
  return request({
    url: "/v1/data/service/database/page",
    method: "get",
    params,
  });
}

// 新增自研数据库
export function addSelfDevelopApi(params: any) {
  return request({
    url: "/v1/data/service/database/add",
    method: "post",
    data: params,
  });
}
// 编辑自研数据库
export function editSelfDevelopApi(params: any) {
  return request({
    url: "/v1/data/service/database/update",
    method: "put",
    data: params,
  });
}
// 删除自研数据库
export function deleteSelfDevelopApi(params: { id: number | null }) {
  return request({
    url: "/v1/data/service/database/del",
    method: "delete",
    params,
  });
}
// 数据服务列表
export function getServiceListPageApi(params?: any) {
  return request<IDataServiceItem[]>({
    url: "/v1/data/service/page",
    method: "get",
    params,
  });
}

// 新增数据服务
export function addServiceApi(params: any) {
  return request({
    url: "/v1/data/service/create",
    method: "post",
    data: params,
  });
}

// 服务器详情
export function getServiceDetailApi(params: { id: number | null }) {
  return request({
    url: "/v1/data/service/detail",
    method: "get",
    params,
  });
}

// 编辑数据服务
export function editServiceApi(params: any) {
  return request({
    url: "/v1/data/service/update",
    method: "put",
    data: params,
  });
}

// 删除数据服务
export function deleteServiceApi(params: { id: number | null }) {
  return request({
    url: "/v1/data/service/delete",
    method: "delete",
    params,
  });
}

// 获取数据源
export function getDataSourceApi(params: Record<string, any>) {
  return request({
    url: "/v1/data/service/dataSourcePage",
    method: "get",
    params,
  });
}

// 获取监测通道列表
export function getMonitorPageApi(params: Record<string, any>) {
  return request({
    url: "/v1/data/service/monitorChannelPage",
    method: "get",
    params,
  });
}

// 根据数据服务+标识tid获取数据源
export function getDataSourceNewApi(params: {
  handle: string;
  dataServiceId: number | null;
}) {
  return request({
    url: "/v1/data/service/queryDataSourceList",
    method: "get",
    params,
  });
}

// 获取数据通道英文名称列表
export function getDataSourceFieldListApi(params: {
  dataSourceId: string;
  dataServiceId: string;
}) {
  return request({
    url: "/v1/data/service/dataSourceFieldList",
    method: "get",
    params,
  });
}

// 获取英文名称
export function getFieldListApi(params: {
  dataServiceId: number | null;
  dataSourceId: number | null;
}) {
  return request({
    url: "/v1/data/service/dataSourceConnectTest",
    method: "get",
    params,
  });
}

// 企业-节点标识列表
export function ApiGetHandleEnterprise(params: {
  name: string;
  handle: string;
  page: number;
  size: number;
  orgName: string;
  appId: string;
  appName: string;
}) {
  return request({
    url: "/v1/handle/pageEnt",
    method: "get",
    params,
  });
}

// 省级-节点标识列表
export function ApiGetHandleProvince(params: {
  name: string;
  handle: string;
  page: number;
  size: number;
  orgName: string;
  appId: string;
  appName: string;
}) {
  return request({
    url: "/v1/report/handle/page",
    method: "get",
    params,
  });
}

// 应用账号-节点标识列表
export function ApiGetHandlePageApp(params: {
  name: string;
  handle: string;
  page: number;
  size: number;
  orgName: string;
  appId: string;
  appName: string;
}) {
  return request({
    url: "/v1/handle/page",
    method: "get",
    params,
  });
}

// 新增标识
export function ApiAddHandle(data: any) {
  return request({
    url: "v1/handle/create",
    method: "post",
    data,
  });
}
// 更新标识
export function ApiUpdateHandle(params: any) {
  return request({
    url: "/v1/handle/update",
    method: "put",
    data: params,
  });
}

// 删除标识
export function ApiDeleteHandle(handle: number) {
  return request({
    url: "/v1/handle/delete",
    method: "DELETE",
    params: { handle },
  });
}

// 查看标识详情
export function ApiGetHandleDetailNew(params: any) {
  return request<IHandleDetail>({
    url: "/v1/handle/detail",
    method: "get",
    params,
  });
}

// 省级查看详情
export function ApiGetProvinceHandleDetail(params: any) {
  return request<IHandleDetail>({
    url: "/v1/report/handle/detail",
    method: "get",
    params,
  });
}

// 上传文件模板下载
export function ApiModelDownload() {
  return request({
    url: "/v1/handle/batch/template",
    method: "get",
    responseType: "blob",
  });
}

// 上传文件
export function ApiBatchUpload(data: any) {
  return request({
    url: "/v1/handle/batch/upload",
    method: "post",
    headers: { "Content-Type": "multipart/form-data" },
    data,
  });
}

// 错误文件模板下载
export function ApiErrorDownload(params: any) {
  return request({
    url: "/v1/handle/batch/record/download",
    method: "get",
    responseType: "blob",
    params,
  });
}

// 列表查询
export function ApiRecordQueryList(params: any) {
  return request({
    url: "/v1/handle/batch/record/list",
    method: "get",
    params,
  });
}

// 列表查询
export function ApiGetResolveField(params: any) {
  return request({
    url: "/v1/public/handle/resolve/field",
    method: "get",
    params,
  });
}
// 中台对象标识列表
export function ApiGetMidHandlePage(
  type: string,
  params: {
    name: string;
    handle?: string;
    page: number;
    size: number;
    orgName?: string; // 企业
    appId?: string; // 企业
    appName?: string; // 企业
  }
) {
  const urlMap: Record<string, string> = {
    province: "/v1/report/handle/page", // 省级账号
    ent: "/v1/handle/pageEnt", // 企业账号
    app: "/v2/object-handle/page", // 应用账号
  };
  return request({
    url: urlMap[type],
    method: "get",
    params,
  });
}

// 中台对象标识主数据标识列表
export function ApiGetMidHandleMainDataPage(params: {
  // name: string;
  // handle?: string;
  // parentId?: string;
  id: number;
  page: number;
  size: number;
}) {
  return request({
    url: "/v2/object-handle/master-data-page",
    method: "get",
    params,
  });
}
// 中台对象标识实体对象类型分类树
export function ApiGetEntityTypeTree() {
  return request({
    url: "/v2/object-handle/entity-object-classify/tree",
    method: "get",
  });
}
// 实体对象列表
export function ApiGetEntityObjList(params: { categoryId: string }) {
  return request({
    url: "/v2/object-handle/entity-object/list",
    method: "get",
    params,
  });
}
/// api/
// 获取中台标识详情
export function ApiGetMidHandleDetail(
  params: { id?: string },
  isProvince = false
) {
  const url = isProvince
    ? "/v1/report/handle/detail"
    : "/v2/object-handle/detail";
  return request({
    url,
    method: "get",
    params,
  });
}

// 标识暂存
export function ApiSaveHandle(params: any) {
  return request({
    url: "/v2/object-handle/storage",
    method: "post",
    data: params,
  });
}

// 通道列表 /api/v2/object-handle/data-channel/list
export function ApiGetHandleChannelList(id: any) {
  return request({
    url: "/v2/object-handle/data-channel/list",
    method: "get",
    params: { id },
  });
}

// 删除中台标识
export function ApiDeleteMidHandle(id: number) {
  return request({
    url: "/v2/object-handle/delete",
    method: "DELETE",
    params: { id },
  });
}

// 中台-对象表示v2/object-handle/release
export function ApiObjectHandleRelease(id: number) {
  return request({
    url: "/v2/object-handle/release",
    method: "GET",
    params: { id },
  });
}

// 中台-测试链接/api/v2/object-handle/test-connection
export function ApiTestConnection(params: any) {
  return request({
    url: "/v2/object-handle/test-connection",
    method: "POST",
    data: params,
  });
}

// 数据服务监测

export function ApiServiceMonitor(id: number) {
  return request({
    url: "/v1/data/service/dataServiceMonitor",
    method: "GET",
    params: { id },
  });
}

// 获取数据源列表
export function ApiServiceBasicInfoList(id: number) {
  return request({
    url: "/v1/data/service/basic-info-list",
    method: "GET",
    params: { id },
  });
}
