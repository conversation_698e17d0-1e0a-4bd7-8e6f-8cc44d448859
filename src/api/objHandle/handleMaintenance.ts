import request from "@/utils/request";
import {
  ISearchData,
  IHandleSearchData,
  IUpdateData,
} from "@/types/handleMaintenance";
import { IHandleRecordData } from "@/types/handleMaintain/record";

// 对象标识维护列表
export function ApiGetTableDate(params: {
  handle: string;
  appName: string;
  description: string;
  referenceHandle: string;
  maintainState: number | null;
  page: number;
  size: number;
}) {
  return request<ISearchData>({
    url: "/v1/handle/maintain/page",
    method: "get",
    params,
  });
}

// 维护详情
export function ApiGetHandleDetail(params: { handle: string }) {
  return request<IHandleSearchData>({
    url: "/v1/handle/maintain/query",
    method: "get",
    params,
  });
}

// 维护查询
export function ApiHandleSearch(params: { handle: string }) {
  return request<IHandleSearchData>({
    url: "/v1/handle/maintain/query",
    method: "get",
    params,
  });
}

// 标识维护
export function ApiHandleUpdate(data: IUpdateData) {
  return request({
    url: "/v1/handle/maintain/item/handleMaintain",
    method: "put",
    data,
  });
}

// 维护记录
export function ApiHandleRecordDetail(params: {
  handle: string | undefined;
  maintainState: number | null;
  page: number;
  size: number;
}) {
  return request<IHandleRecordData>({
    url: "/v1/handle/maintain/record",
    method: "get",
    params,
  });
}
