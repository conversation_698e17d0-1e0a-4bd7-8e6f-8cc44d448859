import request from "@/utils/request";
import { IHandleDetail, IDataServiceItem } from "@/types/handle";

// 查看对象标识详情
export function ApiGetAutoHandleList(params: any) {
  return request<IHandleDetail>({
    url: "/v1/handle/auto-maintain/page",
    method: "get",
    params,
  });
}

// 查看对象标识详情
export function ApiGetAutoHandleDetail(params: any) {
  return request<IHandleDetail>({
    url: "/v1/handle/auto-maintain/detail",
    method: "get",
    params,
  });
}
