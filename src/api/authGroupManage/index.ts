import request from "@/utils/request";

// 权限组查询
export function ApiAuthGroupList(authGroupName: string) {
  return request<any>({
    url: "/v2/group/authGroupList",
    method: "get",
    params: { authGroupName },
  });
}

// 权限组新增
export function ApiAddAuthGroup(authGroupName: any) {
  return request<any>({
    url: "/v2/group/addAuthGroup",
    method: "post",
    data: authGroupName,
  });
}
// 权限组编辑
export function ApiEditAuthGroup(params: any) {
  return request<any>({
    url: "/v2/group/editAuthGroup",
    method: "post",
    data: params,
  });
}

// 权限组删除
export function ApiDeleteAuthGroup(params: { id: number }) {
  return request<any>({
    url: "/v2/group/delete",
    method: "delete",
    params,
  });
}

// 权限组下的标识分页
export function ApiGroupPage(params: any) {
  return request<any>({
    url: "/v2/group/groupHandle/page",
    method: "get",
    params,
  });
}

// 添加---标识
export function ApiHandleList(params: any) {
  return request<any>({
    url: "/v2/group/handleList",
    method: "get",
    params,
  });
}

// 删除---标识
export function ApiDelHandleList(params: { id: number }) {
  return request<any>({
    url: "/v2/group/deleteHandleAuth",
    method: "delete",
    params,
  });
}

// 添加---属性
export function ApiItemList(params: { handleId: number; authGroupId: number }) {
  return request<any>({
    url: "/v2/group/itemList",
    method: "get",
    params,
  });
}

// 添加标识授权
export function ApiAddAuth(params: any) {
  return request<any>({
    url: "/v2/group/addHandleAuth",
    method: "post",
    data: params,
  });
}
// 编辑权限标识
export function ApiEditAuth(params: any) {
  return request<any>({
    url: "/v2/group/editHandleAuth",
    method: "post",
    data: params,
  });
}
