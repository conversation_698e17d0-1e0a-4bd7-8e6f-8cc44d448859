import request from "@/utils/request";

// 实体对象列表--标识解析数据源-实体对象
export function ApiEntityObjectDetail(categoryId: string) {
  return request<any>({
    url: "/v2/object-handle/entity-object/list",
    method: "get",
    params: { categoryId },
  });
}

// 实体分类下属实体对象详情--标识解析数据源-属性列表/v2/object-handle/entity-object/detail
export function ApiEntityObjectDetailList(entityObjectId: string) {
  return request<any>({
    url: "/v2/object-handle/entity-object/detail",
    method: "get",
    params: { entityObjectId },
  });
}

// 扩展属性-所属字段/api/v2/object-handle/data-model/detail
export function ApiObjectHandleDataModelDetail(dataModelId: string) {
  return request<any>({
    url: "/v2/object-handle/data-model/detail",
    method: "get",
    params: { dataModelId },
  });
}

// 数据模型详情--标识解析数据源-属性列表-所属字段
export function ApiDataModelDetail(tableId: string) {
  return request<any>({
    url: "/v2/object-handle/data-model/detail",
    method: "get",
    // params: { tableId },
    params: { dataModelId: tableId },
  });
}

//  标识暂存v2/object-handle/storage
export function ApiObjectHandleStorage(params: any) {
  return request<any>({
    url: "/v2/object-handle/storage",
    method: "post",
    data: params,
  });
}

// 获取应用下的主数据范围/api/v2/object-handle/master-data-scope
export function ApiMasterDataScope() {
  return request<any>({
    url: "/v2/object-handle/master-data-scope",
    method: "get",
  });
}
