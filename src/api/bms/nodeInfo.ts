/*
 * @Author: <PERSON>@gmail.com
 * @Date: 2022-11-08 16:32:11
 * @LastEditors: <PERSON> q<PERSON>@gmail.com
 * @LastEditTime: 2022-11-08 16:45:08
 * @FilePath: /id-yc-province-web/src/api/bms/nodeInfo.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import request from "@/utils/request";

// 查询省级节点列表
export function listNodeInfo(query: any) {
  return request({
    url: "/dms/nodeInfo/list",
    method: "get",
    params: query,
  });
}

// 查询省级节点详细
export function getNodeInfo(id: any) {
  return request({
    url: "/dms/nodeInfo/" + id,
    method: "get",
  });
}

// 新增省级节点
export function addNodeInfo(data: any) {
  return request({
    url: "/dms/nodeInfo",
    method: "post",
    data,
  });
}

// 修改省级节点
export function updateNodeInfo(data: any) {
  return request({
    url: "/dms/nodeInfo",
    method: "put",
    data,
  });
}

// 删除省级节点
export function delNodeInfo(id: any) {
  return request({
    url: "/dms/nodeInfo/" + id,
    method: "delete",
  });
}
