import request from "@/utils/request";
import { IClaimSearchData } from "@/types/nodeManage/prefixClaim";

// 获取前缀认领列表
export function ApiGetSearchData(params: {
  entPrefix: string;
  status: number | null;
  size: number;
  page: number;
}) {
  return request<IClaimSearchData>({
    url: "/v1/prefix/prepare/list/ent",
    method: "get",
    params,
  });
}

// 认领前缀
export function ApiGetPrefixData(params: { id: number }) {
  return request({
    url: "/v1/prefix/prepare/detail",
    method: "get",
    params,
  });
}
// 认领前缀提交
export function ApiPrefixClaim(data: any) {
  return request({
    url: "/v1/prefix/prepare/claim",
    method: "post",
    data,
  });
}

// 认领前缀取消
export function ApiCancelClaim(data: any) {
  return request({
    url: "/v1/prefix/prepare/cancel",
    method: "post",
    data,
  });
}
