import request from "@/utils/request";

// 获取前缀审核列表
export function fetchAuditList(params: {
  entPrefix: string;
  size: number;
  page: number;
}) {
  return request({
    url: "/v1/prefix/audit/page",
    method: "get",
    params,
  });
}

// 查看前缀审核详情
export function prefixAuditdetail(params: { id: number }) {
  return request({
    url: "/v1/prefix/audit/detail",
    method: "get",
    params,
  });
}

// 前缀审核
export function PrefixAudit(params: { id: number; auditPass: boolean }) {
  return request({
    url: "/v1/prefix/audit",
    method: "post",
    data: params,
  });
}
