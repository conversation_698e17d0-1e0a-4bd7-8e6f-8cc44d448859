import request from "@/utils/request";
// 获取前缀列表
export function fetchList(params: {
  entPrefix: string;
  size: number;
  page: number;
}) {
  return request({
    url: "/v1/prefix/manage/page",
    method: "get",
    params,
  });
}

// 查看前缀详情
export function prefixDetail(params: { id: number }) {
  return request({
    url: "/v1/prefix/manage/detail",
    method: "get",
    params,
  });
}

// 分配前缀
export function allotPrefix(params: {
  entPrefix: string;
  entId: number | null;
}) {
  return request({
    url: "/v1/prefix/manage/allot",
    method: "post",
    data: params,
  });
}

// 前缀管理页面--删除企业前缀
export function deletePrefix(params: { id: number }) {
  return request({
    url: "/v1/prefix/manage/delete",
    method: "delete",
    params,
  });
}

// 前缀管理页面--撤销删除企业前缀
// export function stateChangeByHistory(params) {
//   return request({
//     url: "/v3/ent/prefix/manage/stateChangeByHistory",
//     method: "post",
//     params: params,
//     paramsSerializer: function (params) {
//       return Qs.stringify(params, { arrayFormat: "repeat" });
//     },
//   });
// }

// 前缀管理页面--启用停用
export function changeState(params: { id: number; state: number }) {
  return request({
    url: "/v1/prefix/manage/change-state",
    method: "post",
    data: params,
  });
}
// 前缀管理页面--配置页面
// export function configPage(params) {
//   return request({
//     url: "/v3/ent/prefix/manage/configPage",
//     method: "get",
//     params: params,
//     paramsSerializer: function (params) {
//       return Qs.stringify(params, { arrayFormat: "repeat" });
//     },
//   });
// }

// 前缀管理--配置
export function prefixConfig(body: any) {
  return request({
    url: "/v1/prefix/manage/config",
    method: "post",
    data: body,
  });
}

// 前缀管理-配置详情
export function configDetail(params: { entPrefixId: number | undefined }) {
  return request({
    url: "/v1/prefix/manage/config/detail",
    method: "get",
    params,
  });
}

// 分配企业下拉框
export function getEntList(params?: string) {
  return request({
    url: "/v1/users/user/ent/info",
    method: "get",
    params,
  });
}

// 企业系统-获取前缀列表
export function entFetchList(params: {
  entPrefix: string;
  size: number;
  page: number;
}) {
  return request({
    url: "/v1/ent/prefix/page",
    method: "get",
    params,
  });
}

// 企业系统-查看前缀详情
export function entPrefixDetail(params: { id: number }) {
  return request({
    url: "/v1/ent/prefix/detail",
    method: "get",
    params,
  });
}

// 企业系统-新增前缀
export function prefixAdd(body: any) {
  return request({
    url: "/v1/ent/prefix/add",
    method: "post",
    data: body,
  });
}

// 企业系统-配置
export function entPrefixConfig(body: any) {
  return request({
    url: "/v1/ent/prefix/config",
    method: "put",
    data: body,
  });
}

// 企业系统--删除企业前缀
export function entDeletePrefix(params: { id: number }) {
  return request({
    url: "/v1/ent/prefix/del",
    method: "delete",
    params,
  });
}

// 企业系统-获取服务器列表
export function entGetServerList() {
  return request({
    url: "/v1/ent/hosting/server/list",
    method: "get",
  });
}
