import request, { download } from "@/utils/request";

// 新增前缀
export function ApiAdd(param: any) {
  return request({
    url: "/v1/prefix/prepare/add",
    method: "post",
    data: param,
  });
}

// 列表查询
export function ApiQueryList(params: any) {
  return request({
    url: "/v1/prefix/prepare/list/province",
    method: "get",
    params,
  });
}

// 修改
export function ApiEdit(data: any) {
  return request({
    url: "/v1/prefix/prepare",
    method: "put",
    data,
  });
}

// 删除
export function ApiDeleteById(id: any) {
  return request({
    url: "/v1/prefix/prepare",
    method: "delete",
    params: { id },
  });
}

// 详情查询
export function ApiQueryDetail(params: { id: number }) {
  return request({
    url: "/v1/prefix/prepare/detail",
    method: "get",
    params,
  });
}

// 前缀预分配模板下载
export function ApiDownload() {
  return request({
    url: "/v1/prefix/prepare/template",
    method: "get",
    responseType: "blob",
  });
}

// 前缀预分配上传导入
export function ApiUpload(param: any) {
  return request({
    url: "/v1/prefix/prepare",
    method: "post",
    data: param,
  });
}

// 前缀审核
export function ApiPrefixAudit(param: any) {
  return request({
    url: "/v1/prefix/prepare/audit",
    method: "post",
    data: param,
  });
}

// 上传文件
export function ApiBatchUpload(data: any) {
  return request({
    url: "/v1/prefix/prepare/upload",
    method: "post",
    headers: { "Content-Type": "multipart/form-data" },
    data,
  });
}
