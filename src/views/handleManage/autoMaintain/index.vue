<template>
  <div class="page-search">
    <search-layout>
      <template #right>
        <el-form :inline="true" :model="searchForm" @submit.prevent>
          <el-form-item>
            <el-input
              v-model.trim="searchForm.monitorHandle"
              clearable
              @clear="handleSearch"
              placeholder="请输入监测标识"
            >
              <template #prefix>监测标识：</template></el-input
            >
          </el-form-item>
          <el-form-item
            ><el-input
              v-model.trim="searchForm.monitorHandleName"
              clearable
              @clear="handleSearch"
              placeholder="请输入监测标识名称"
            >
              <template #prefix>监测标识名称：</template></el-input
            ></el-form-item
          >
          <el-form-item v-if="isEnterprise && !isAppRole"
            ><el-input
              v-model.trim="searchForm.monitorApplication"
              clearable
              @clear="handleSearch"
              placeholder="请输入监测应用"
            >
              <template #prefix>监测应用：</template></el-input
            ></el-form-item
          >
          <el-form-item
            ><el-input
              v-model="searchForm.maintainHandle"
              placeholder="请输入自动维护标识"
              clearable
              @clear="handleSearch"
            >
              <template #prefix>自动维护标识：</template>
            </el-input></el-form-item
          >

          <el-form-item
            ><el-date-picker
              v-model="searchForm.maintainTime"
              type="daterange"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled-date="disabledDate"
              clearable
              @change="handleTimeClear()"
          /></el-form-item>
          <el-form-item style="width: 60px; max-width: 60px"
            ><el-button
              type="primary"
              :loading="data.searchLoading"
              @click="handleSearch"
              >搜索</el-button
            ></el-form-item
          >
        </el-form>
      </template>
    </search-layout>
  </div>
  <div class="page-search-body">
    <el-table
      :data="data.tableData"
      v-loading="data.tableLoading"
      border
      size="small"
    >
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column
        property="monitorHandle"
        label="监测标识"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span v-copy="scope.row.monitorHandle">{{
            scope.row.monitorHandle || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        property="monitorHandleName"
        label="监测标识名称"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <span v-copy="scope.row.monitorHandleName">{{
            scope.row.monitorHandleName || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        property="monitorApplication"
        label="监测应用"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <span v-copy="scope.row.monitorApplication">{{
            scope.row.monitorApplication || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        property="maintainHandle"
        label="自动维护标识"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span v-copy="scope.row.maintainHandle">{{
            scope.row.maintainHandle || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        property="maintainHandleName"
        label="自动维护标识名称"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span>{{ scope.row.maintainHandleName || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        property="maintainHandleAppName"
        label="所属应用"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span>{{ scope.row.maintainHandleAppName || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column property="maintainTime" label="自动维护时间">
        <template #default="scope">
          <span>{{ scope.row.maintainTime || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="200">
        <template #default="scope">
          <el-button
            type="primary"
            text
            @click="openDetailDrawer(scope.row, DetailType.MONITOR)"
            >监测标识详情</el-button
          >
          <el-button
            type="primary"
            text
            @click="openDetailDrawer(scope.row, DetailType.MAINTAIN)"
            >自动维护标识详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      v-model:currentPage="data.page"
      v-model:page-size="data.size"
      :page-sizes="[10, 20, 30, 40]"
      small
      layout="total, sizes, prev, pager, next, jumper"
      :total="data.totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>

  <HandleDetailDrawer
    v-if="detailDrawerVisible"
    :id="detailHandle"
    :source="HANDLE_SOURCE.AUTO"
    @close="detailDrawerVisible = false"
  ></HandleDetailDrawer>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, formContextKey } from "element-plus";
import { useStore } from "vuex";
import { LEVEL_TYPE, ROLE_TYPE, HANDLE_SOURCE } from "@/utils/constant";
import { ApiGetAutoHandleList } from "@/api/objHandle/autoMaintain";
import { AUTH_CODE } from "@/utils/authCode";
import SearchLayout from "@/components/searchLayout/index.vue";
import HandleDetailDrawer from "@/components/HandleDetailDrawer/index.vue";
import { DetailType } from "./constant";

const store = useStore();
const userInfo = computed(() => store.getters.userInfo);

const detailDrawerVisible = ref(false);
const detailHandle = ref(); // 是监测标识，还是自动维护标识

const isProvince = computed(
  () => userInfo.value.levelType === LEVEL_TYPE.PROVINCE
);

const isEnterprise = computed(
  () => userInfo.value.levelType === LEVEL_TYPE.PROXY
);

// id=3,为应用账号
const isAppRole = computed(() =>
  userInfo.value.roleInfos.some((item: any) => item.roleType === ROLE_TYPE.APP)
);

const disabledDate = (time: Date) => {
  return time.getTime() > new Date().getTime();
};

const searchForm = reactive({
  monitorHandle: "",
  monitorHandleName: "",
  maintainHandle: "",
  monitorApplication: "",
  maintainTime: [] as any,
});

const data = reactive<Record<string, any>>({
  tableData: [],
  page: 1,
  size: 10,
  totalCount: 0,

  dialogTitle: "",
  dialogVisible: false,
  selectHandleData: null,
  dialogDisabled: false,
  deleteLoading: false,
  tableLoading: false,
  searchLoading: false,
  confirmLoading: false,
});

function getTableData() {
  data.tableLoading = true;
  data.tableData = [];

  let formatTime = {};
  if (searchForm.maintainTime?.length) {
    const [startTime, endTime] = searchForm.maintainTime;
    formatTime = {
      startTime,
      endTime: endTime.replace("00:00:00", "23:59:59"),
    };
  }

  const { page, size } = data;
  const params = {
    ...searchForm,
    ...formatTime,
    page: page - 1,
    size,
  };

  ApiGetAutoHandleList(params)
    .then((response: any) => {
      const result = response;
      const { content, pageNumber, totalCount } = response;
      data.tableData = content || [];
      data.page = pageNumber || 1;
      data.totalCount = totalCount || 0;
    })
    .finally(() => {
      data.tableLoading = false;
      data.searchLoading = false;
    });
}

// 详情按钮
function openDetailDrawer(item: any, detailType: string) {
  const { monitorHandle, maintainHandle } = item;
  const temp = {
    [DetailType.MONITOR]: monitorHandle,
    [DetailType.MAINTAIN]: maintainHandle,
  };

  detailHandle.value = temp[detailType];
  detailDrawerVisible.value = true;
}

// 清空操作时间，查询数据
function handleTimeClear() {
  if (!searchForm.maintainTime || searchForm.maintainTime.length === 0) {
    handleSearch();
  }
}

function handleSearch() {
  data.searchLoading = true;
  data.page = 1;
  getTableData();
}

function handleSizeChange(num: number) {
  data.size = num;
  data.page = 1;
  getTableData();
}

function handleCurrentChange(num: number) {
  data.page = num;
  getTableData();
}

onMounted(() => {
  getTableData();
});
</script>
<style lang="scss" scoped>
.popover-delete-tips {
  margin-bottom: 20px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .popover-delete-tips-text {
    margin-left: 10px;
    font-size: 14px;
  }
}
.popover-delete-wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
.infoAlert {
  display: flex;
  align-items: center;
  height: 30px;
  margin: -44px -44px 14px;
  background-color: rgb(232, 244, 255);
}
.handleUploadInfo {
  width: 20px;
  height: 20px;
  margin-left: 20px;
}

.handleRecordClass {
  margin-left: 4px;
  margin-right: 4px;
  color: rgb(0, 183, 255);
  cursor: pointer;
}
.svgClose {
  height: 100%;
  width: 25px;
  margin-left: auto;
  display: flex;
  align-items: center;
}
.handleUploadClose {
  width: 15px;
  height: 15px;
  cursor: pointer;
}
</style>
