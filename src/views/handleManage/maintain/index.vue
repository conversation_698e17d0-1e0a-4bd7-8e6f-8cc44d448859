<template>
  <div class="page-search" v-if="!data.showRecord">
    <div class="handle-maintain-search" v-if="isAppRole">
      <div class="section-title">
        <div class="section-title-line"></div>
        <span class="section-title-text"> 新增标识维护 </span>
      </div>
      <SearchLayout>
        <template #right>
          <el-form :inline="true" @submit.prevent>
            <el-form-item
              ><el-input v-model="handleTID" placeholder="请输入" clearable>
                <template #prefix>标识TID：</template>
              </el-input></el-form-item
            >
            <el-form-item style="width: 60px; max-width: 60px"
              ><el-button
                type="primary"
                :disabled="!handleTID"
                @click="handleUpdate(handleTID)"
                >维护</el-button
              ></el-form-item
            >
          </el-form>
        </template>
      </SearchLayout>
    </div>
    <div v-if="isAppRole">
      <el-divider />
      <div class="section-title">
        <div class="section-title-line"></div>
        <span class="section-title-text"> 标识维护列表 </span>
      </div>
    </div>
    <SearchLayout>
      <template #right>
        <el-form
          v-permission="AUTH_CODE.HANDLE_MAINTAIN_PAGE"
          :inline="true"
          :model="searchForm"
          @submit.prevent
        >
          <el-form-item>
            <el-input
              v-model="searchForm.handle"
              placeholder="请输入"
              clearable
              @clear="handleSearch"
            >
              <template #prefix>维护标识：</template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              v-model="searchForm.appName"
              placeholder="请输入"
              clearable
              @clear="handleSearch"
            >
              <template #prefix>应用：</template>
            </el-input>
          </el-form-item>
          <el-form-item
            ><el-input
              v-model="searchForm.description"
              placeholder="请输入"
              clearable
              @clear="handleSearch"
            >
              <template #prefix>属性：</template>
            </el-input></el-form-item
          >
          <el-form-item
            ><el-input
              v-model="searchForm.referenceHandle"
              placeholder="请输入"
              clearable
              @clear="handleSearch"
            >
              <template #prefix>属性值：</template>
            </el-input></el-form-item
          >
          <el-form-item
            ><el-select
              v-model="searchForm.maintainState"
              placeholder="请选择状态"
              clearable
              @clear="handleSearch"
              style="width: 100%"
            >
              <template #prefix>状态：</template>
              <el-option
                v-for="item in STATUS_LIST"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              /> </el-select
          ></el-form-item>
          <el-form-item style="width: 60px; max-width: 60px"
            ><el-button
              type="primary"
              :loading="data.searchLoading"
              @click="handleSearch"
              >搜索</el-button
            ></el-form-item
          >
        </el-form>
      </template>
    </SearchLayout>
    <div class="page-search-body">
      <el-table
        :data="data.tableData"
        v-loading="data.tableLoading"
        border
        size="small"
        table-layout="auto"
      >
        <el-table-column label="序号" type="index" width="55" />
        <el-table-column
          property="orgName"
          label="企业名称"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-copy="scope.row.orgName">
              {{ scope.row.orgName || "-" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          property="name"
          label="标识名称"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-copy="scope.row.name">
              {{ scope.row.name || "-" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          property="handle"
          label="维护标识"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-copy="scope.row.handle">
              {{ scope.row.handle || "-" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="维护属性"
          property="maintainField"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-copy="scope.row.maintainField">
              {{ scope.row.maintainField || "-" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          property="appName"
          label="所属应用"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-copy="scope.row.appName">
              {{ scope.row.appName || "-" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column property="updatedTime" label="操作时间" width="136">
          <template #default="scope">
            <span>
              {{ scope.row.updatedTime ? scope.row.updatedTime : "-" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          property="updatedBy"
          label="维护人"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-copy="scope.row.updatedBy">
              {{ scope.row.updatedBy ? scope.row.updatedBy : "-" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column property="maintainState" label="状态" width="55">
          <template #default="scope">
            <span>
              {{ scope.row.maintainState ? scope.row.maintainState : "-" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="152">
          <template #default="scope">
            <el-button
              type="primary"
              text
              @click="handleView(scope.row)"
              v-permission="AUTH_CODE.HANDLE_MAINTAIN_DETAIL"
              >详情</el-button
            >
            <el-button
              v-if="isAppRole"
              type="primary"
              text
              @click="handleUpdate(scope.row.handle)"
              v-permission="AUTH_CODE.HANDLE_MAINTAIN_MAINTAIN"
              >维护</el-button
            >
            <el-button
              type="primary"
              text
              @click="handleRecord(scope.row)"
              v-permission="AUTH_CODE.HANDLE_MAINTAIN_RECORD"
              >操作记录</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        v-model:currentPage="data.page"
        v-model:page-size="data.size"
        :page-sizes="[10, 20, 30, 40]"
        small
        layout="total, sizes, prev, pager, next, jumper"
        :total="data.totalCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <HandleDetailDrawer
      v-if="data.drawerVisible"
      :id="selectedHandle"
      :source="HANDLE_SOURCE.MAINTAIN"
      @close="data.drawerVisible = false"
    ></HandleDetailDrawer>
    <Detail
      v-if="data.updateHandleVisible"
      @update-table="getTableData()"
      @close-dialog="closeDialog()"
      :handle="handleTID"
    ></Detail>
  </div>
  <div v-if="data.showRecord">
    <record :handle="data.recordHandle" @backToList="backToList" />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from "vue";
import { useStore } from "vuex";
import HandleDetailDrawer from "@/components/HandleDetailDrawer/index.vue";
import Detail from "./detail/index.vue";
import record from "./record/index.vue";
import { TableDataItem } from "@/types/handleMaintenance";
import { ApiGetTableDate } from "@/api/objHandle/handleMaintenance";
import { STATUS_LIST, ROLE_TYPE, HANDLE_SOURCE } from "@/utils/constant";
import { AUTH_CODE } from "@/utils/authCode";
import SearchLayout from "@/components/searchLayout/index.vue";

const store = useStore();
const userInfo = computed(() => store.getters.userInfo);
// id=3,为应用账号
const isAppRole = computed(() =>
  userInfo.value.roleInfos.some((item: any) => item.roleType === ROLE_TYPE.APP)
);

// 标识TID
const handleTID = ref("");
const searchForm = ref<{
  handle: string;
  appName: string;
  description: string;
  referenceHandle: string;
  maintainState: number | null;
}>({
  handle: "",
  appName: "",
  description: "",
  referenceHandle: "",
  maintainState: null,
});
const data = reactive<{
  tableData: TableDataItem[];
  page: number;
  size: number;
  totalCount: number;
  tableLoading: boolean;
  searchLoading: boolean;
  drawerVisible: boolean;
  updateHandleVisible: boolean;
  showRecord: boolean;
  recordHandle: string;
}>({
  tableData: [],
  page: 1,
  size: 10,
  totalCount: 1,
  tableLoading: false,
  searchLoading: false,
  drawerVisible: false,
  updateHandleVisible: false,
  showRecord: false,
  recordHandle: "",
});

const selectedHandle = ref<any>("");

function handleSearch() {
  data.page = 1;
  getTableData();
}

function handleUpdate(handle: string) {
  if (handle) {
    handleTID.value = handle;
    data.updateHandleVisible = true;
  }
}

function handleView(row: TableDataItem) {
  selectedHandle.value = row.handle;
  data.drawerVisible = true;
}

function handleRecord(row: TableDataItem) {
  data.showRecord = true;
  data.recordHandle = row.handle;
}

function backToList() {
  data.showRecord = false;
  getTableData();
}

function getTableData() {
  data.tableLoading = true;
  data.tableData = [];
  const params = {
    handle: searchForm.value.handle,
    appName: searchForm.value.appName,
    description: searchForm.value.description,
    referenceHandle: searchForm.value.referenceHandle,
    maintainState: searchForm.value.maintainState,
    page: data.page - 1,
    size: data.size,
  };
  ApiGetTableDate(params)
    .then((response) => {
      const result = response;
      data.tableData = result?.content || [];
      data.page = result?.pageNumber || 1;
      data.totalCount = result?.totalCount || 0;
    })
    .finally(() => {
      data.tableLoading = false;
      data.searchLoading = false;
    });
}

function handleSizeChange(num: number) {
  data.size = num;
  data.page = 1;
  getTableData();
}

function handleCurrentChange(num: number) {
  data.page = num;
  getTableData();
}

function closeDialog() {
  data.updateHandleVisible = false;
  handleTID.value = "";
}

onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (AllPermission.includes(AUTH_CODE.HANDLE_MAINTAIN_PAGE)) {
    getTableData();
  }
});
</script>
<style lang="scss" scoped>
.el-divider--horizontal {
  margin: 16px 0;
}
:deep(.dialogClass) {
  display: flex;
  flex-direction: column;
  .el-dialog__body {
    flex: 1;
  }
  .el-dialog__footer {
    line-height: 63px;
  }
}
.el-button.is-disabled {
  background-color: #00a57c;
}
</style>
