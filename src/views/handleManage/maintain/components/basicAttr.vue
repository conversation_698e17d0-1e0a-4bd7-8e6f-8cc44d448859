<template>
  <div>
    <el-table :data="basicTableData" size="small" border>
      <!-- <el-table-column label="索引" width="55">
        <template #default="scope">
          <div>{{ scope.row.fieldIndex || "-" }}</div>
        </template>
      </el-table-column> -->
      <el-table-column
        label="中文名称"
        prop="description"
        show-overflow-tooltip
      >
        <template #default="scope">
          <ellipsisText :value="scope.row.description"></ellipsisText>
        </template>
      </el-table-column>
      <el-table-column label="英文名称" prop="field" show-overflow-tooltip>
        <template #default="scope">
          <ellipsisText :value="scope.row.field"></ellipsisText>
        </template>
      </el-table-column>
      <el-table-column label="属性类型" prop="fieldType">
        <template #default="scope">
          <div>{{ FIELD_TYPE_NAME_MAP[scope.row.fieldType] }}</div>
        </template>
      </el-table-column>
      <el-table-column label="属性值" prop="references">
        <template #default="scope">
          <ellipsisText :value="getReferenceDis(scope.row)"></ellipsisText>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script setup lang="ts">
import { FIELD_TYPE_NAME_MAP, FIELD_TYPE_MAP } from "@/utils/constant";
import { IAttrItem } from "@/types/handleMaintenance";
import ellipsisText from "@/components/ellipsisText/index.vue";

const props = defineProps({
  basicTableData: {
    type: Array<IAttrItem>,
    default: [],
  },
});

function getReferenceDis(data: any) {
  if (data.fieldType === FIELD_TYPE_MAP.fixed) {
    return data.fieldValue;
  }
  if (data.fieldType === FIELD_TYPE_MAP.source) {
    return typeof data.fieldValue === "string" && data.fieldValue
      ? JSON.parse(data.fieldValue).join(",")
      : "-";
  }
  if (data.references && data.references.length) {
    return data.references
      .map((reference: any) => reference.referenceHandle)
      .join(",");
  }
}
</script>
<style></style>
