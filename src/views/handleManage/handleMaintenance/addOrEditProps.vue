<template>
  <el-form
    ref="formRef"
    label-position="left"
    :model="form"
    label-width="100px"
    :rules="rules"
  >
    <el-form-item label="属性类型" prop="fieldType">
      <el-radio-group v-model="form.fieldType" @change="handleFieldTypeChange">
        <el-radio
          v-for="item in data.fieldTypeList"
          :key="item.value"
          :label="item.value"
          >{{ item.name }}
        </el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="中文名称" prop="description">
      <el-input v-model.trim="form.description" placeholder="请输入" />
    </el-form-item>
    <el-form-item label="英文名称" prop="field">
      <el-input v-model.trim="form.field" placeholder="请输入" />
    </el-form-item>
    <el-form-item
      label="属性值"
      prop="fieldValue"
      v-if="form.fieldType === FIELD_TYPE_MAP.fixed"
    >
      <el-input v-model.trim="form.fieldValue" placeholder="请输入" />
    </el-form-item>
    <el-form-item label="备注" prop="remark">
      <el-input v-model.trim="form.remark" placeholder="请输入" />
    </el-form-item>
    <!-- 标识值 -->
    <el-form-item
      label="属性值"
      prop="references"
      v-if="form.fieldType === FIELD_TYPE_MAP.handleValue"
    >
      <el-table :data="form.references" size="small" border>
        <el-table-column label="关联标识" show-overflow-tooltip>
          <template #default="scope">
            <el-form-item
              :key="scope.row.key"
              :prop="'references.' + scope.$index + '.referenceHandle'"
              :rules="[
                {
                  validator: referenceHandleValidate,
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                v-model.trim="scope.row.referenceHandle"
                @input="clearMapRelate(scope.row)"
                @blur="
                  isMapRelateDisabled(scope.row.referenceHandle, scope.$index)
                "
                placeholder="请输入关联标识"
              ></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="映射关系">
          <template #header>
            <span>映射关系</span>
            <el-tooltip effect="light" placement="top">
              <template #content>
                <img
                  style="width: 650px; height: 300px"
                  fit="contain"
                  src="@/assets/images/handle/handleReferanceDemo.jpg"
                />
              </template>
              <span
                ><el-icon size="18" style="vertical-align: text-bottom"
                  ><Warning /></el-icon
              ></span>
            </el-tooltip>
          </template>
          <template #default="scope">
            <el-row>
              <el-form-item
                :key="scope.row.key"
                :prop="'references.' + scope.$index + '.queryProp.field'"
                :rules="queryPropRule(scope.$index)"
              >
                <el-select
                  :loading="loading"
                  :popper-append-to-body="false"
                  v-model="scope.row.queryProp"
                  placeholder="请选择关联标识属性"
                  :no-data-text="
                    scope.row.referenceHandle ? '无数据' : '请输入关联标识'
                  "
                  :disabled="scope.row.disabled"
                  value-key="fieldIndex"
                  @click="getProperty(scope.row.referenceHandle, scope.$index)"
                >
                  <el-option
                    v-for="item in scope.row.propertyList"
                    :key="item.fieldIndex"
                    :value="item"
                    :label="item.field"
                  >
                    <span>{{ item.field }}</span>
                    <span style="margin-left: 5px">{{
                      (item.fieldIndex && item.fieldIndex > 0) || item.remark
                        ? "-"
                        : ""
                    }}</span>
                    <span style="margin-left: 5px">{{
                      item.fieldIndex && item.fieldIndex > 0
                        ? item.fieldIndex
                        : ""
                    }}</span>
                    <span style="margin-left: 5px">{{
                      item.remark ? "(" + item.remark + ")" : ""
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                :key="scope.row.key"
                :prop="'references.' + scope.$index + '.paramProp.field'"
                :rules="paramPropRule(scope.$index)"
                style="margin-left: 12px"
              >
                <el-select
                  v-model="scope.row.paramProp"
                  placeholder="请选择标识属性"
                  :disabled="scope.row.disabled"
                  value-key="fieldIndex"
                >
                  <el-option
                    v-for="(item, index) in fieldList"
                    :key="index"
                    :value="item"
                    :label="item.field"
                  >
                    <span>{{ item.field }}</span>
                    <span style="margin-left: 5px">{{
                      (item.fieldIndex && item.fieldIndex > 0) || item.remark
                        ? "-"
                        : ""
                    }}</span>
                    <span style="margin-left: 5px">{{
                      item.fieldIndex && item.fieldIndex > 0
                        ? item.fieldIndex
                        : ""
                    }}</span>
                    <span style="margin-left: 5px">{{
                      item.remark ? "(" + item.remark + ")" : ""
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-row>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100px">
          <template #default="scope">
            <el-popconfirm
              confirm-button-text="删除"
              cancel-button-text="取消"
              :icon="InfoFilled"
              title="是否执行删除操作?"
              @confirm="handleReferenceDelete(scope.$index)"
            >
              <template #reference>
                <el-button type="primary" text>删除</el-button>
              </template>
            </el-popconfirm>
            <el-form style="display: inline; margin-left: 12px">
              <el-button
                type="primary"
                text
                @click="resolveHandle(scope.row.referenceHandle)"
                >解析</el-button
              >
            </el-form>
          </template>
        </el-table-column>
      </el-table>
      <el-button class="add-item-btn" text @click="handleReferenceAdd"
        >新增关联标识</el-button
      >
    </el-form-item>
    <!-- 标识-属性 -->
    <el-form-item
      label="属性值"
      prop="references"
      v-if="form.fieldType === FIELD_TYPE_MAP.handleWithAttr"
    >
      <el-table :data="form.references" border>
        <el-table-column label="关联标识">
          <template #default="scope">
            <el-row>
              <el-form-item
                :key="scope.row.key"
                :prop="'references.' + scope.$index + '.referenceHandle'"
                :rules="[
                  {
                    validator: referenceHandleValidate,
                    trigger: 'blur',
                  },
                ]"
              >
                <el-input
                  v-model.trim="scope.row.referenceHandle"
                  @input="clearMapRelate(scope.row)"
                  @blur="
                    isMapRelateDisabled(scope.row.referenceHandle, scope.$index)
                  "
                  placeholder="请输入关联标识"
                ></el-input>
              </el-form-item>
              <el-form-item
                :prop="
                  'references.' + scope.$index + '.referenceHandleProp.field'
                "
                :rules="[
                  {
                    required: true,
                    message: '请选择关联标识属性',
                    trigger: 'change',
                  },
                ]"
                style="margin-left: 12px"
              >
                <el-select
                  :loading="loading"
                  :popper-append-to-body="false"
                  v-model="scope.row.referenceHandleProp"
                  placeholder="请选择关联标识属性"
                  :no-data-text="
                    scope.row.referenceHandle ? '无数据' : '请输入关联标识'
                  "
                  value-key="fieldIndex"
                  @click="getProperty(scope.row.referenceHandle, scope.$index)"
                >
                  <el-option
                    v-for="item in scope.row.handlePropList"
                    :key="item.fieldIndex"
                    :value="item"
                    :label="item.field"
                  >
                    <span>{{ item.field }}</span>
                    <span style="margin-left: 5px">{{
                      (item.fieldIndex && item.fieldIndex > 0) || item.remark
                        ? "-"
                        : ""
                    }}</span>
                    <span style="margin-left: 5px">{{
                      item.fieldIndex && item.fieldIndex > 0
                        ? item.fieldIndex
                        : ""
                    }}</span>
                    <span style="margin-left: 5px">{{
                      item.remark ? "(" + item.remark + ")" : ""
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-row>
          </template>
        </el-table-column>
        <el-table-column label="映射关系">
          <template #header>
            <span>映射关系</span>
            <el-tooltip effect="light" placement="top">
              <template #content>
                <img
                  style="width: 650px; height: 300px"
                  fit="contain"
                  src="@/assets/images/handle/handleReferanceDemo.jpg"
                />
              </template>
              <span>
                <el-icon size="18" style="vertical-align: text-bottom"
                  ><Warning
                /></el-icon>
              </span>
            </el-tooltip>
          </template>
          <template #default="scope">
            <el-row>
              <el-form-item
                :key="scope.row.key"
                :prop="'references.' + scope.$index + '.queryProp.field'"
                :rules="queryPropRule(scope.$index)"
              >
                <el-select
                  :loading="loading"
                  :popper-append-to-body="false"
                  v-model="scope.row.queryProp"
                  placeholder="请选择关联标识属性"
                  :no-data-text="
                    scope.row.referenceHandle ? '无数据' : '请输入关联标识'
                  "
                  :disabled="scope.row.disabled"
                  value-key="fieldIndex"
                  @click="getProperty(scope.row.referenceHandle, scope.$index)"
                >
                  <el-option
                    v-for="item in scope.row.propertyList"
                    :key="item.fieldIndex"
                    :value="item"
                    :label="item.field"
                  >
                    <span>{{ item.field }}</span>
                    <span style="margin-left: 5px">{{
                      (item.fieldIndex && item.fieldIndex > 0) || item.remark
                        ? "-"
                        : ""
                    }}</span>
                    <span style="margin-left: 5px">{{
                      item.fieldIndex && item.fieldIndex > 0
                        ? item.fieldIndex
                        : ""
                    }}</span>
                    <span style="margin-left: 5px">{{
                      item.remark ? "(" + item.remark + ")" : ""
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                :key="scope.row.key"
                :prop="'references.' + scope.$index + '.paramProp.field'"
                :rules="paramPropRule(scope.$index)"
                style="margin-left: 12px"
              >
                <el-select
                  v-model="scope.row.paramProp"
                  placeholder="请选择标识属性"
                  :disabled="scope.row.disabled"
                  value-key="fieldIndex"
                >
                  <el-option
                    v-for="item in fieldList"
                    :key="item.key"
                    :value="item"
                    :label="item.field"
                  >
                    <span>{{ item.field }}</span>
                    <span style="margin-left: 5px">{{
                      (item.fieldIndex && item.fieldIndex > 0) || item.remark
                        ? "-"
                        : ""
                    }}</span>
                    <span style="margin-left: 5px">{{
                      item.fieldIndex && item.fieldIndex > 0
                        ? item.fieldIndex
                        : ""
                    }}</span>
                    <span style="margin-left: 5px">{{
                      item.remark ? "(" + item.remark + ")" : ""
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </el-row>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100px">
          <template #default="scope">
            <el-popconfirm
              confirm-button-text="删除"
              cancel-button-text="取消"
              :icon="InfoFilled"
              title="是否执行删除操作?"
              @confirm="handleReferenceDelete(scope.$index)"
            >
              <template #reference>
                <el-button type="primary" text>删除</el-button>
              </template>
            </el-popconfirm>
            <el-form style="display: inline; margin-left: 12px">
              <el-button
                type="primary"
                text
                @click="resolveHandle(scope.row.referenceHandle)"
                >解析</el-button
              >
            </el-form>
          </template>
        </el-table-column>
      </el-table>
      <el-button class="add-item-btn" text @click="handleReferenceAdd"
        >新增关联标识</el-button
      >
    </el-form-item>
  </el-form>

  <el-dialog
    v-model="data.dialogResolve"
    title="解析结果"
    append-to-body
    width="800"
    @close="data.dialogResolve = false"
  >
    <div class="relate-handle-matrix-graph">
      <matrix-graph ref="matrixGraphRef"></matrix-graph>
    </div>
    <template #footer>
      <el-button type="primary" @click="data.dialogResolve = false">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, watch, ref, computed, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { InfoFilled } from "@element-plus/icons-vue";
import { deepClone } from "@/utils/auth";
import { idResolve } from "@/api/idRes/index";
import matrixGraph from "@/views/idRes/components/matrix-graph.vue";
import { FIELD_TYPE_MAP, DIALOG_TYPE_MAP } from "@/utils/constant";
import {
  Form,
  PropertyList,
  FieldTypeList,
} from "@/types/handleMaintain/handleProps";

const props = defineProps({
  basicTableData: {
    type: Array,
    default: () => [],
  },
  extendTableData: {
    type: Array,
    default: () => [],
  },
  handle: {
    type: String,
    default: "",
  },
  tableDataItem: {
    type: Object,
    default: null,
  },
  dialogType: {
    type: Number,
  },
});
const formRef = ref();
const matrixGraphRef = ref();
const loading = ref(false);

const form = reactive<Form>({
  fieldIndex: "",
  field: "",
  fieldValue: "",
  description: "",
  fieldType: null,
  editFlag: true,
  remark: "",
  references: [],
});

// 关联属性英文字段列表
const fieldList = computed(() => {
  const fieldList: any[] = [];
  const tableData = deepClone(props.basicTableData);
  tableData.forEach((item: any, index: any) => {
    if (
      item.fieldType === FIELD_TYPE_MAP.fixed ||
      item.fieldType === FIELD_TYPE_MAP.source
    ) {
      item.key = Date.now() + index;
      fieldList.push({
        fieldIndex: item.fieldIndex,
        field: item.field,
        fieldType: item.fieldType,
        remark: item.remark,
      });
    }
  });
  return fieldList;
});

const data: {
  propertyList: PropertyList[];
  dialogResolve: boolean;
  fieldTypeList: FieldTypeList[];
  mapRelateDisabled: boolean;
  referencesHandleList: string[];
  handlePropList: PropertyList[];
} = reactive({
  fieldTypeList: [
    { value: 1, name: "固定值" },
    { value: 3, name: "标识值" },
    { value: 4, name: "标识-属性" },
  ],
  dialogResolve: false,
  mapRelateDisabled: false, // 映射关系是否可选
  propertyList: [],
  referencesHandleList: [],
  handlePropList: [],
});

// 属性都是固定值 映射关系disabled
async function isMapRelateDisabled(val: string, index: number) {
  await getProperty(val, index);
  if (data.handlePropList.length) {
    // 过滤类型为固定值
    const filterArray = data.handlePropList.filter(
      (item) => item.fieldType === FIELD_TYPE_MAP.fixed
    );
    // 若输入的关联标识属性都为固定值时,返回true
    form.references[index].disabled =
      filterArray.length === data.handlePropList.length;
  }
}

// 设置关联标识属性校验规则
function paramPropRule(index: number) {
  if (form.references[index].disabled) {
    return [{ required: false }];
  }
  return [{ required: true, message: "请选择标识属性", trigger: "change" }];
}

function queryPropRule(index: number) {
  if (form.references[index].disabled) {
    return [{ required: false }];
  }
  return [{ required: true, message: "请选择关联标识属性", trigger: "change" }];
}
// 校验关联标识
async function referenceHandleValidate(
  rule: any,
  handleVal: any,
  callback: any
) {
  // 获取所有属性的关联标识，用于校验
  const allReferenceHandleList: string[] = [];
  const tableData = [...props.basicTableData, ...props.extendTableData];
  tableData.forEach((item: any) => {
    console.log("item:", item);
    if (item.references.length) {
      item.references.forEach((item: any) => {
        allReferenceHandleList.push(item.referenceHandle);
      });
    }
  });
  // 去除本身得关联标识List
  const list = allReferenceHandleList.filter((item: any) => item !== handleVal);
  if (!handleVal) {
    return callback("请填写关联标识");
  }
  // 判断不能关联本身
  if (props.handle === handleVal) {
    return callback("关联标识和当前标识重复");
  }
  // 判断其他属性是否关联此标识
  // if (
  //   // 编辑模式
  //   props.dialogType === DIALOG_TYPE_MAP.editor &&
  //   list.includes(handleVal)
  // ) {
  //   return callback("该关联标识已被其他属性使用");
  // }
  // if (
  //   // 新增模式
  //   props.dialogType !== DIALOG_TYPE_MAP.editor &&
  //   allReferenceHandleList.includes(handleVal)
  // ) {
  //   return callback("该关联标识已被其他属性使用");
  // }
  // 判断关联标识是否重复
  if (
    form.references.filter((item: any) => item.referenceHandle === handleVal)
      .length >= 2
  ) {
    return callback("关联标识不可重复");
  }
  callback();
}

const handleValidate = (rule: any, value: any, callback: any) => {
  if (!form.references.length) {
    return callback("内容需填写完整");
  }
  callback();
};

const fieldValidate = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback("请填写英文名称");
  }
  const reg = /^[a-zA-Z\d_]+$/;
  if (!reg.test(value)) {
    return callback("英文名称格式错误，请重新填写");
  }
  callback();
};

const rules = reactive({
  field: [
    { required: true, validator: fieldValidate, trigger: "blur" },
    { max: 30, message: "最大长度为30字符", trigger: "blur" },
  ],
  description: [
    { required: true, message: "请填写中文名称", trigger: "blur" },
    { max: 30, message: "最大长度为30字符", trigger: "blur" },
  ],
  fieldType: [{ required: true, message: "请选择字段类型", trigger: "blur" }],
  fieldValue: [
    { required: true, message: "请填写属性值", trigger: "blur" },
    { max: 255, message: "最大长度为255字符", trigger: "blur" },
  ],
  references: [{ required: true, validator: handleValidate }],
  remark: [
    {
      required: false,
      max: 100,
      message: "最大长度为100字符",
      trigger: "blur",
    },
  ],
});

watch(
  props,
  (val) => {
    console.log("props:", props);
    const tempData: any = deepClone(val);
    const { dialogType, tableDataItem } = tempData;
    if (dialogType === DIALOG_TYPE_MAP.add) {
      // 新增模式，设置属性类型为固定值
      form.fieldType = FIELD_TYPE_MAP.fixed;
    } else if (dialogType === DIALOG_TYPE_MAP.addNext) {
      // 新增下一个模式，停留在当前属性类型，重置form
      form.field = "";
      form.description = "";
      form.fieldValue = "";
      form.editFlag = true;
      form.remark = "";
      form.references = [];
    } else if (dialogType === DIALOG_TYPE_MAP.editor) {
      // 编辑模式，数据回显
      form.fieldIndex = tableDataItem?.fieldIndex || "";
      form.field = tableDataItem?.field || "";
      form.description = tableDataItem?.description || "";
      form.fieldType = tableDataItem?.fieldType || 1;
      form.fieldValue = tableDataItem?.fieldValue || "";
      form.remark = tableDataItem?.remark || "";
      form.editFlag = tableDataItem?.editFlag || true;
      tableDataItem.references.forEach((item: any, index: any) => {
        getProperty(item.referenceHandle, index);
      });
      form.references =
        (tableDataItem?.references && deepClone(tableDataItem.references)) ||
        [];
    }
  },
  {
    immediate: true,
    deep: true,
  }
);

// 切换字段类型清空关联标识表格
function handleFieldTypeChange() {
  form.field = "";
  form.fieldValue = "";
  form.description = "";
  form.remark = "";
  form.references = [];
}

// 关联标识变化 清空映射关系
function clearMapRelate(row: any) {
  row.queryProp = {
    field: "",
    fieldIndex: null,
    remark: "",
  };
  row.paramProp = {
    field: "",
    fieldIndex: null,
    remark: "",
  };
  row.referenceHandleProp = {
    field: "",
    fieldIndex: null,
    remark: "",
  };
}

function handleReferenceAdd() {
  form.references.push({
    propertyList: [],
    handlePropList: [],
    referenceHandle: "",
    referenceHandleProp: {
      field: "",
      fieldIndex: null,
      remark: "",
    },
    queryProp: {
      field: "",
      fieldIndex: null,
      remark: "",
    },
    paramProp: {
      field: "",
      fieldIndex: null,
      remark: "",
    },
    disabled: false,
    key: Date.now(),
  });
}
// 获取查询属性
function getProperty(handleVal: any, index: any) {
  if (handleVal) {
    loading.value = true;
    return idResolve({ handle: handleVal })
      .then((response: any) => {
        form.references[index].propertyList = [];
        form.references[index].handlePropList = [];
        const res = response.values;
        form.references[index].propertyList = res.filter((item: any) => {
          return item.fieldType === FIELD_TYPE_MAP.source;
        });
        form.references[index].handlePropList = res.filter((item: any) => {
          return (
            item.fieldType === FIELD_TYPE_MAP.fixed ||
            item.fieldType === FIELD_TYPE_MAP.source
          );
        });
        loading.value = false;
      })
      .catch(() => {
        form.references[index].propertyList = [];
        form.references[index].handlePropList = [];
        loading.value = false;
      });
  }
  form.references[index].propertyList = [];
  form.references[index].handlePropList = [];
  loading.value = false;
}

// 解析按钮
function resolveHandle(id: any) {
  if (id) {
    idResolve({ handle: id }).then((response: any) => {
      data.dialogResolve = true;
      nextTick(() => {
        matrixGraphRef.value.refresh(response);
      });
    });
  } else {
    ElMessage({
      message: "请输入标识",
      type: "error",
    });
  }
}

function handleReferenceDelete(index: any) {
  form.references.splice(index, 1);
  validateData();
}

async function validateData() {
  let isError = false;
  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  return isError;
}

function getData() {
  return { ...form };
}

defineExpose({
  form,
  validateData,
  getData,
});
</script>
<style lang="scss" scoped>
.mb10 {
  margin-bottom: 10px;
}
.mb11 {
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 5px;
  color: #929292;
}
.mb12 {
  float: right;
}
.mb13 {
  margin-top: 5px;
  letter-spacing: 0.5px;
  // text-align: center;
  margin: 0 30px;
  > div {
    display: flex;
    align-items: baseline;
    span {
      flex: 1;
      &:first-child {
        text-align: right;
      }
    }
  }
}
.mb14 {
  margin-left: 10px;
}
.mb15 {
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 8px;
  letter-spacing: 0.5px;
  // text-align: center;
  margin: 40px 30px 10px;
  > div {
    display: flex;
    align-items: baseline;
    span {
      flex: 1;
      &:first-child {
        text-align: right;
      }
    }
  }
}
:deep(.el-select-dropdown__empty) {
  padding: 10px 10px !important;
}

:deep(.add-item-btn) {
  width: 100%;
  border: 1px solid #dfe4e3;
  border-top: 0;
  height: 44px;
  border-radius: 0;
  color: #1664ff;
  font-size: 12px;
  line-height: 20px;
  &:hover {
    color: #4086ff !important;
    background-color: transparent !important;
  }
}

.relate-handle-matrix-graph {
  height: 600px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
.el-table {
  border-bottom: 0;
  .is-error {
    height: 70px;
    :deep(.el-form-item__error) {
      top: 50px;
    }
  }
}
.el-row {
  flex-wrap: nowrap;
}
</style>
