<template>
  <el-dialog
    class="dialogClass"
    v-model="updateHandleVisible"
    title="维护标识"
    fullscreen
    @close="$emit('close-dialog')"
  >
    <div class="handle-query-result-result" v-loading="dialogLoading">
      <el-form ref="formRef" :model="searchForm" :rules="rules">
        <el-form-item prop="handle">
          <div class="handle-tid-wrap">
            <el-input
              v-model.trim="searchForm.handle"
              placeholder="请输入标识TID"
            />
            <el-button
              type="primary"
              :disabled="!searchForm.handle"
              @click="searchHandle"
              >查询</el-button
            >
          </div>
        </el-form-item>
      </el-form>
      <div class="yc-description">
        <div class="yc-description-title">
          <div class="yc-description-title-tip"></div>
          <div class="yc-description-title-text">基本信息</div>
        </div>
        <el-descriptions :column="1" direction="horizontal">
          <el-descriptions-item label="标识TID">{{
            handleResult?.handle || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="标识名称">{{
            handleResult?.name || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="实体类型">{{
            getEntityTypeName(handleResult?.entityType) || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="所属应用">{{
            handleResult?.appName || "-"
          }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <el-divider />
      <div class="yc-description">
        <div class="yc-description-title">
          <div class="yc-description-title-tip"></div>
          <div class="yc-description-title-text">属性信息</div>
        </div>
        <el-tabs v-model="activeName">
          <el-tab-pane label="基础属性" name="basic">
            <basicAttr :basicTableData="data.tableData" />
          </el-tab-pane>
          <el-tab-pane label="扩展属性" name="extend">
            <extendAttr
              :form="handleResult"
              :data="data"
              source="maintenance"
              @getDeleteProps="getDeleteProps"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="updateHandleVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="updateConfirm"
          :loading="confirmLoading"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {
  onMounted,
  ref,
  defineProps,
  reactive,
  computed,
  nextTick,
  provide,
} from "vue";
import { ElMessage, FormInstance } from "element-plus";
import basicAttr from "./basicAttr.vue";
import extendAttr from "./extendAttr.vue";
import {
  ApiHandleSearch,
  ApiHandleUpdate,
} from "@/api/objHandle/handleMaintenance";
import { IHandleSearchData, IAttrItem } from "@/types/handleMaintenance";
import { deepClone } from "@/utils/auth";

const emit = defineEmits(["update-table", "close-dialog"]);

const props = defineProps({
  handle: {
    type: String || null,
    default: null,
  },
});

const activeName = ref("basic");
const updateHandleVisible = ref(true);
const dialogLoading = ref(false);
const confirmLoading = ref(false);
const formRef = ref<FormInstance>();

const searchForm = ref<{ handle: string }>({
  handle: props.handle,
});
const handleResult = ref<IHandleSearchData>({
  name: "",
  orgName: "",
  handle: "",
  entityType: "",
  entPrefix: "",
  appName: "",
  appId: 0,
  appCode: "",
  version: 0,
  basicsItems: [],
  extendItems: [],
});

provide("handleData", handleResult);

const rules = reactive({
  handle: [{ required: true, message: "请输入标识", trigger: "blur" }],
});

const data = reactive<{
  tableData: IAttrItem[];
  extendTableData: IAttrItem[];
  basicDelList: IAttrItem[];
}>({
  tableData: [],
  extendTableData: [],
  basicDelList: [], // 记录删除的属性
});

function getEntityTypeName(type: string | null) {
  if (type === "1") return "业务实体";
  if (type === "2") return "资源实体";
  return "-";
}

function searchHandle() {
  dialogLoading.value = true;
  if (!formRef.value) return;
  formRef.value.validate((valid) => {
    if (valid) {
      ApiHandleSearch({ handle: searchForm.value.handle })
        .then((res) => {
          handleResult.value = res;
          data.tableData = handleResult.value.basicsItems;
          data.extendTableData = handleResult.value.extendItems;
        })
        .catch(() => {
          handleResult.value.name = "";
          handleResult.value.handle = "";
          handleResult.value.entityType = "";
          handleResult.value.appName = "";
          data.tableData = [];
          data.extendTableData = [];
        })
        .finally(() => {
          dialogLoading.value = false;
        });
    }
  });
}

function getDeleteProps(value: any) {
  if (value.fieldIndex > 0) {
    data.basicDelList.push(value);
  }
}

// 提交维护
async function updateConfirm() {
  const validateError = await validateData();
  if (validateError) return;
  confirmLoading.value = true;
  // 组装添加/编辑/删除操作数组
  const addItems = deepClone(data.extendTableData).filter((item: any) => {
    return item.fieldIndex < 0;
  });
  addItems.forEach((item: any) => {
    item.fieldIndex = -1;
    // 将关联的标识属性的filedindex设置为-1
    item.references.forEach((item: any) => {
      if (item.paramProp.fieldIndex < 0) {
        item.paramProp.fieldIndex = -1;
      }
    });
  });
  const updateItems = deepClone(data.extendTableData).filter((item: any) => {
    return item.fieldIndex > 0;
  });
  const deleteItems = data.basicDelList;
  const params = {
    name: handleResult.value.name,
    orgName: handleResult.value.orgName,
    entPrefix: handleResult.value.entPrefix,
    handle: handleResult.value.handle,
    appCode: handleResult.value.appCode,
    appName: handleResult.value.appName,
    version: handleResult.value.version,
    add: addItems,
    edit: updateItems,
    del: deleteItems,
  };
  ApiHandleUpdate(params)
    .then(() => {
      ElMessage.success("维护成功");
      emit("update-table");
      updateHandleVisible.value = false;
    })
    .finally(() => {
      confirmLoading.value = false;
    });
}

async function validateData() {
  let isError = false;
  await formRef.value?.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  return isError;
}

onMounted(() => {
  nextTick(() => {
    searchHandle();
  });
});
</script>
<style lang="scss" scoped>
.handle-tid-wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  .el-input {
    flex: 1;
    min-width: 0;
  }
  .el-button {
    margin-left: 12px;
  }
}
.handle-query-result-blank {
  position: absolute;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  img {
    width: 120px;
    height: auto;
  }
  .handle-query-result-blank-text {
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    margin-top: 4px;
    color: #7b9790;
  }
  .handle-search-bar {
    margin-top: 10px;
  }
}
.el-divider {
  margin: 8px 0 24px 0;
}
:deep(.el-descriptions__label) {
  width: 72px;
  min-width: 72px;
  display: inline-block;
}
.yc-description {
  position: relative;
}
:deep(.el-tabs__nav-wrap::after) {
  content: none;
}
:deep(.el-tabs__active-bar) {
  display: none;
}
:deep(.el-tabs__item) {
  height: auto;
  line-height: normal;
}
:deep(.el-tabs--top .el-tabs__item.is-top:nth-child(2)) {
  border-right: 2px solid #c1c9c7;
}
.el-button.is-disabled {
  background-color: #00a57c;
}
</style>
