<template>
  <el-drawer
    v-model="drawerVisible"
    size="60%"
    title="维护详情"
    v-loading="loading"
    @closed="handleClose"
  >
    <div class="yc-description">
      <div class="yc-description-title">
        <div class="yc-description-title-tip"></div>
        <div class="yc-description-title-text">基本信息</div>
      </div>
      <el-descriptions :column="1" direction="horizontal">
        <el-descriptions-item label="标识对象TID">{{
          detail?.handle || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="标识对象名称">{{
          detail?.name || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="实例类型">{{
          getEntityTypeName(detail?.entityType) || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="所属应用">{{
          detail?.appName || "-"
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <el-divider />
    <div class="yc-description">
      <div class="yc-description-title">
        <div class="yc-description-title-tip"></div>
        <div class="yc-description-title-text">属性信息</div>
      </div>
      <el-tabs v-model="activeName">
        <el-tab-pane label="基础属性" name="basic">
          <el-table size="small" :data="detail?.basicsItems" border>
            <el-table-column label="索引" prop="fieldIndex" width="55" />
            <el-table-column
              label="中文名称"
              prop="description"
            ></el-table-column>
            <el-table-column
              label="英文名称"
              prop="field"
              show-overflow-tooltip
            >
            </el-table-column>
            <el-table-column label="属性类型" prop="fieldType">
              <template #default="scope">
                <div>{{ FIELD_TYPE_NAME_MAP[scope.row.fieldType] }}</div>
              </template>
            </el-table-column>
            <el-table-column label="属性值" show-overflow-tooltip>
              <template #default="scope">
                <span
                  v-if="
                    scope.row.fieldType === FIELD_TYPE_MAP.handleValue ||
                    scope.row.fieldType === FIELD_TYPE_MAP.handleWithAttr
                  "
                  >{{ getReferenceDis(scope.row) }}</span
                >
                <span v-else>{{ scope.row.fieldValue || "-" }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="140">
              <template #default="scope">
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click="handleViewReference(scope.row)"
                  >详情</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="扩展属性" name="extend">
          <el-table :data="detail?.extendItems" border>
            <el-table-column label="索引" prop="fieldIndex" width="55" />
            <el-table-column
              label="中文名称"
              prop="description"
            ></el-table-column>
            <el-table-column
              label="英文名称"
              show-overflow-tooltip
              prop="field"
            >
            </el-table-column>
            <el-table-column label="属性类型" prop="fieldType">
              <template #default="scope">
                <div>{{ FIELD_TYPE_NAME_MAP[scope.row.fieldType] }}</div>
              </template>
            </el-table-column>
            <el-table-column label="属性值">
              <template #default="scope">
                <span
                  v-if="
                    scope.row.fieldType === FIELD_TYPE_MAP.handleValue ||
                    scope.row.fieldType === FIELD_TYPE_MAP.handleWithAttr
                  "
                  >{{ getReferenceDis(scope.row) }}</span
                >
                <span v-else>{{ scope.row.fieldValue || "-" }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="140">
              <template #default="scope">
                <el-button
                  size="small"
                  type="primary"
                  text
                  @click="handleViewReference(scope.row)"
                  >详情</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-drawer>
  <handleItemDrawer
    v-if="handleItemDrawerVisible"
    :data="handleItemData"
    @close="handleItemDrawerVisible = false"
  ></handleItemDrawer>
</template>

<script lang="ts" setup>
import { onMounted, ref, defineProps, reactive } from "vue";
import { IHandleSearchData } from "@/types/handleMaintenance";
import { ApiGetHandleDetail } from "@/api/objHandle/handleMaintenance";
import handleItemDrawer from "./handleItemDrawer.vue";
import { IHandleItem } from "@/types/handle";
import { FIELD_TYPE_NAME_MAP, FIELD_TYPE_MAP } from "@/utils/constant";

const props = defineProps({
  handle: {
    type: String || null,
    default: null,
  },
});
const emit = defineEmits(["close"]);

const handleItemDrawerVisible = ref(false);
const handleItemData = ref<IHandleItem>();
const drawerVisible = ref(true);

const loading = ref(false);
const activeName = ref("basic");

const detail = ref<IHandleSearchData>();

// 基础属性详情按钮
function handleViewReference(data: any) {
  handleItemData.value = data;
  handleItemDrawerVisible.value = true;
}

// 获取实例详情
function getHandleDetail() {
  if (!props.handle) return;
  loading.value = true;
  ApiGetHandleDetail({ handle: props.handle })
    .then((response) => {
      detail.value = response;
    })
    .finally(() => {
      loading.value = false;
    });
}

function getEntityTypeName(type: string | undefined) {
  if (type === "1") return "业务实体";
  if (type === "2") return "资源实体";
  return "-";
}

function getReferenceDis(data: any) {
  if (!data.references || !data.references.length) {
    return "-";
  }
  return data.references
    .map((reference: any) => reference.referenceHandle)
    .join(",");
}
function handleClose() {
  emit("close");
}
onMounted(() => {
  getHandleDetail();
});
</script>
<style lang="scss" scoped>
.el-divider {
  margin: 8px 0 24px 0;
}
:deep(.el-descriptions__label) {
  width: 72px;
  min-width: 72px;
  display: inline-block;
}
:deep(.el-tabs__nav-wrap::after) {
  content: none;
}
:deep(.el-tabs__active-bar) {
  display: none;
}
:deep(.el-tabs__item) {
  height: auto;
  line-height: normal;
}
:deep(.el-tabs--top .el-tabs__item.is-top:nth-child(2)) {
  border-right: 2px solid #c1c9c7;
}
</style>
