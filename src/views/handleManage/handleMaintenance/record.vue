<template>
  <SearchLayout>
    <template #left
      ><el-button type="primary" @click="backToList">返回</el-button></template
    >
    <template #right>
      <el-form :model="searchForm" :inline="true" size="small" @submit.prevent>
        <el-form-item style="width: 240px; max-width: 240px">
          <el-select
            v-model="searchForm.maintainState"
            placeholder="请选择"
            clearable
            @clear="handleSearch"
          >
            <template #prefix>状态：</template>
            <el-option
              v-for="item in STATUS_LIST"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="width: 60px; max-width: 60px">
          <el-button
            type="primary"
            :loading="data.searchLoading"
            @click="handleSearch"
            >搜索</el-button
          >
        </el-form-item>
      </el-form></template
    >
  </SearchLayout>

  <el-table
    :data="data.tableData"
    size="small"
    v-loading="data.tableLoading"
    border
  >
    <el-table-column label="序号" type="index" width="55" />
    <el-table-column property="handle" label="标识" show-overflow-tooltip>
      <template #default="scope">
        <span v-copy="scope.row.handle">
          {{ scope.row.handle ? scope.row.handle : "-" }}
        </span>
      </template>
    </el-table-column>
    <el-table-column
      property="maintainField"
      label="维护属性"
      :show-overflow-tooltip="true"
    >
      <template #default="scope">
        <span>
          {{ scope.row.maintainField ? scope.row.maintainField : "-" }}
        </span>
      </template>
    </el-table-column>
    <el-table-column property="appName" label="所属应用" show-overflow-tooltip>
      <template #default="scope">
        <span v-copy="scope.row.appName">
          {{ scope.row.appName ? scope.row.appName : "-" }}
        </span>
      </template>
    </el-table-column>
    <el-table-column property="updatedTime" label="操作时间">
      <template #default="scope">
        <span>
          {{ scope.row.updatedTime ? scope.row.updatedTime : "-" }}
        </span>
      </template>
    </el-table-column>
    <el-table-column
      property="updateBy"
      label="维护人"
      :show-overflow-tooltip="true"
    >
      <template #default="scope">
        <span v-copy="scope.row.updatedBy">
          {{ scope.row.updateBy ? scope.row.updateBy : "-" }}
        </span>
      </template>
    </el-table-column>
    <el-table-column property="maintainState" label="状态" width="55">
      <template #default="scope">
        <span>
          {{ scope.row.maintainState ? scope.row.maintainState : "-" }}
        </span>
      </template>
    </el-table-column>
    <el-table-column label="操作">
      <template #default="scope">
        <el-button type="primary" text @click="handleView(scope.row)"
          >详情</el-button
        >
      </template>
    </el-table-column>
  </el-table>
  <el-pagination
    background
    v-model:currentPage="data.page"
    v-model:page-size="data.size"
    :page-sizes="[10, 20, 30, 40]"
    small
    layout="total, sizes, prev, pager, next, jumper"
    :total="data.totalCount"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  />
  <recordDrawer
    v-if="data.drawerVisible"
    :item="data.recordItem"
    @close="data.drawerVisible = false"
  />
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import recordDrawer from "./recordDrawer.vue";
import {
  IHandleRecord,
  IHandleRecordItems,
} from "@/types/handleMaintain/record";
import { STATUS_LIST, STATUS_NAME_MAP } from "@/utils/constant";
import { ApiHandleRecordDetail } from "@/api/objHandle/handleMaintenance";
import SearchLayout from "@/components/searchLayout/index.vue";

const emit = defineEmits(["backToList"]);
const props = defineProps({
  handle: {
    type: String,
    default: "",
  },
});
const searchForm = ref({
  maintainState: null,
});
const data = reactive<{
  tableData: IHandleRecord[];
  page: number;
  size: number;
  totalCount: number;
  tableLoading: boolean;
  searchLoading: boolean;
  drawerVisible: boolean;
  selectedHandle: string;
  recordItem: IHandleRecordItems[];
}>({
  tableData: [],
  page: 1,
  size: 10,
  totalCount: 1,
  tableLoading: false,
  searchLoading: false,
  drawerVisible: false,
  selectedHandle: "",
  recordItem: [],
});
function handleSearch() {
  data.page = 1;
  getTableData();
}
function getTableData() {
  data.tableLoading = true;
  data.tableData = [];
  const params = {
    handle: props.handle,
    maintainState: searchForm.value.maintainState,
    page: data.page - 1,
    size: data.size,
  };
  ApiHandleRecordDetail(params)
    .then((res) => {
      data.tableData = res?.content || [];
      data.page = res?.pageNumber || 1;
      data.totalCount = res?.totalCount || 0;
    })
    .finally(() => {
      data.tableLoading = false;
      data.searchLoading = false;
    });
}
function handleView(row: IHandleRecord) {
  data.drawerVisible = true;
  data.recordItem = row.items;
}

function handleSizeChange(num: number) {
  data.size = num;
  data.page = 1;
  getTableData();
}

function handleCurrentChange(num: number) {
  data.page = num;
  getTableData();
}

function backToList() {
  emit("backToList");
}
onMounted(() => {
  getTableData();
});
</script>
<style lang="scss" scoped></style>
