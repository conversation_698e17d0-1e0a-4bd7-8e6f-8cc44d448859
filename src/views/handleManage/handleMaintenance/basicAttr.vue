<template>
  <div>
    <el-table :data="basicTableData" size="small" border>
      <el-table-column label="索引" width="55">
        <template #default="scope">
          <div>{{ scope.row.fieldIndex || "-" }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="中文名称"
        prop="description"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span v-copy="scope.row.description">{{
            scope.row.description || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="英文名称" prop="field" show-overflow-tooltip>
        <template #default="scope">
          <span v-copy="scope.row.field">{{ scope.row.field || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="属性类型" prop="fieldType">
        <template #default="scope">
          <div>{{ FIELD_TYPE_NAME_MAP[scope.row.fieldType] }}</div>
        </template>
      </el-table-column>
      <el-table-column label="属性值" prop="references" show-overflow-tooltip>
        <template #default="scope">
          <span
            v-if="
              scope.row.fieldType === FIELD_TYPE_MAP.handleValue ||
              scope.row.fieldType === FIELD_TYPE_MAP.handleWithAttr
            "
            >{{ getReferenceDis(scope.row) }}</span
          >
          <span v-else>{{ scope.row.fieldValue || "-" }}</span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script setup lang="ts">
import { FIELD_TYPE_NAME_MAP, FIELD_TYPE_MAP } from "@/utils/constant";
import { IAttrItem } from "@/types/handleMaintenance";

const props = defineProps({
  basicTableData: {
    type: Array<IAttrItem>,
    default: [],
  },
});

function getReferenceDis(data: any) {
  if (!data.references || !data.references.length) {
    return "-";
  }
  return data.references
    .map((reference: any) => reference.referenceHandle)
    .join(",");
}
</script>
<style></style>
