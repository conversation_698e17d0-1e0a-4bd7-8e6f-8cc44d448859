<template>
  <div>
    <div class="addbtn">
      <el-button type="primary" @click="addHandleProps">新增标识属性</el-button>
    </div>
    <el-table size="small" :data="extendTableData" border>
      <el-table-column label="索引" width="55">
        <template #default="scope">
          <span>{{
            scope.row.fieldIndex < 0 ? "-" : scope.row.fieldIndex
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="中文名称"
        prop="description"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span v-copy="scope.row.description">{{
            scope.row.description || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="英文名称" prop="field" show-overflow-tooltip>
        <template #default="scope">
          <span v-copy="scope.row.field">{{ scope.row.field || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="属性类型" prop="fieldType">
        <template #default="scope">
          <div>{{ FIELD_TYPE_NAME_MAP[scope.row.fieldType] }}</div>
        </template>
      </el-table-column>
      <el-table-column label="属性值" prop="references" show-overflow-tooltip>
        <template #default="scope">
          <span
            v-if="
              scope.row.fieldType === FIELD_TYPE_MAP.handleValue ||
              scope.row.fieldType === FIELD_TYPE_MAP.handleWithAttr
            "
            >{{ getReferenceDis(scope.row) }}</span
          >
          <span v-else>{{ scope.row.fieldValue || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="140">
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            text
            :disabled="!scope.row.editFlag"
            @click="handleEdit(scope.$index, scope.row)"
            >编辑</el-button
          >
          <el-popconfirm
            confirm-button-text="删除"
            cancel-button-text="取消"
            :icon="InfoFilled"
            title="是否删除该扩展属性?"
            @confirm="handleDelete(scope.$index)"
          >
            <template #reference>
              <el-button
                size="small"
                type="primary"
                text
                :disabled="!scope.row.editFlag"
                >删除</el-button
              >
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      v-model="data.dialogVisible"
      title="维护扩展属性"
      append-to-body
      @close="handleDialogCancel"
      width="70%"
      align-center
    >
      <addOrEditProps
        v-if="data.dialogVisible"
        ref="relateHandleFormRef"
        :extendTableData="props.extendTableData"
        :basicTableData="props.basicTableData"
        :handle="handle"
        :tableDataItem="data.tableDataItem"
        :dialogType="data.dialogType"
      ></addOrEditProps>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            v-if="data.dialogType !== DIALOG_TYPE_MAP.editor"
            type="primary"
            @click="handleDialogConfirm('addNext')"
          >
            新增下一个
          </el-button>
          <el-button @click="data.dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleDialogConfirm">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { computed, reactive, ref } from "vue";
import { InfoFilled } from "@element-plus/icons-vue";
import { useStore } from "vuex";
import addOrEditProps from "./addOrEditProps.vue";
import { IAttrItem } from "@/types/handleMaintenance";
import {
  FIELD_TYPE_NAME_MAP,
  DIALOG_TYPE_MAP,
  FIELD_TYPE_MAP,
} from "@/utils/constant";
import { deepClone } from "@/utils/auth";

const store = useStore();
const userInfo = computed(() => store.getters.userInfo);

const props = defineProps({
  extendTableData: {
    type: Array<IAttrItem>,
    default: [],
  },
  basicTableData: {
    type: Array<IAttrItem>,
    default: [],
  },
  handle: {
    type: String,
    default: "",
  },
});
const emit = defineEmits(["getDeleteProps"]);

const relateHandleFormRef = ref();
const data = reactive<{
  dialogVisible: boolean;
  dialogType: number;
  referenceIndex: number;
  tableDataItem: any;
}>({
  dialogVisible: false,
  dialogType: DIALOG_TYPE_MAP.add,
  referenceIndex: 0,
  tableDataItem: null,
});

function getReferenceDis(data: any) {
  if (!data.references || !data.references.length) {
    return "-";
  }
  return data.references
    .map((reference: any) => reference.referenceHandle)
    .join(",");
}

// 增加属性
function addHandleProps() {
  data.dialogType = DIALOG_TYPE_MAP.add;
  data.dialogVisible = true;
}
// 编辑属性
function handleEdit(index: any, row: any) {
  data.dialogVisible = true;
  data.referenceIndex = index;
  data.tableDataItem = row;
  data.dialogType = DIALOG_TYPE_MAP.editor;
}
// 删除属性
function handleDelete(index: number) {
  emit("getDeleteProps", props.extendTableData[index]);
  props.extendTableData.splice(index, 1);
}
function handleDialogCancel() {
  data.dialogVisible = false;
}
async function handleDialogConfirm(addNext?: string) {
  const validateError = await relateHandleFormRef.value.validateData();
  if (validateError) return;
  const formData = relateHandleFormRef.value.getData();
  data.dialogVisible = false;
  if (addNext === "addNext") {
    // 新增下一个
    data.dialogType = DIALOG_TYPE_MAP.addNext;
    data.dialogVisible = true;
  }
  if (data.dialogType === DIALOG_TYPE_MAP.editor) {
    // 将维护人修改为当前账号
    formData.updateBy = userInfo.value.username;
    props.extendTableData[data.referenceIndex] = deepClone(formData);
    return;
  }
  formData.fieldIndex = -Date.parse(new Date().toString());
  props.extendTableData.push(deepClone(formData));
}
</script>
<style>
.addbtn {
  text-align: right;
  margin-bottom: 10px;
}
</style>
