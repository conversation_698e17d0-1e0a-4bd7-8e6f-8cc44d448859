<template>
  <el-drawer
    v-model="drawerVisible"
    size="70%"
    @closed="$emit('close')"
    title="记录详情"
  >
    <el-table size="small" :data="data.tableData" border>
      <el-table-column property="fieldIndex" label="索引" width="55">
        <template #default="scope">
          <div>
            {{ scope.row.fieldIndex === -1 ? "-" : scope.row.fieldIndex }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        property="description"
        label="中文名称"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span v-copy="scope.row.description">{{
            scope.row.description || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column property="field" label="英文名称" show-overflow-tooltip>
        <template #default="scope">
          <span v-copy="scope.row.field">{{ scope.row.field || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column property="fieldType" label="属性类型">
        <template #default="scope">
          <div>{{ FIELD_TYPE_NAME_MAP[scope.row.fieldType] }}</div>
        </template>
      </el-table-column>
      <el-table-column
        property="fieldValue"
        label="属性值"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <span
            v-if="
              scope.row.fieldType === FIELD_TYPE_MAP.handleValue ||
              scope.row.fieldType === FIELD_TYPE_MAP.handleWithAttr
            "
            >{{ getReferenceDis(scope.row) }}</span
          >
          <span v-else>{{ scope.row.fieldValue || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column property="type" label="操作类型" />
      <el-table-column
        property="updateBy"
        label="维护人"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <span v-copy="scope.row.updatedBy">
            {{ scope.row.updateBy ? scope.row.updateBy : "-" }}
          </span>
        </template>
      </el-table-column>
    </el-table>
  </el-drawer>
</template>

<script lang="ts" setup>
import { defineProps, ref, defineEmits, reactive } from "vue";
import { FIELD_TYPE_NAME_MAP, FIELD_TYPE_MAP } from "@/utils/constant";
import { IHandleRecordItems } from "@/types/handleMaintain/record";

const props = defineProps({
  item: {
    type: Array<IHandleRecordItems>,
  },
});
const emit = defineEmits(["close"]);
const drawerVisible = ref(true);
const data = reactive<{
  tableData: IHandleRecordItems[] | undefined;
}>({
  tableData: props.item,
});

function getReferenceDis(data: any) {
  if (!data.references || !data.references.length) {
    return "-";
  }
  return data.references
    .map((reference: any) => reference.referenceHandle)
    .join(",");
}
</script>
<style lang="scss" scoped></style>
