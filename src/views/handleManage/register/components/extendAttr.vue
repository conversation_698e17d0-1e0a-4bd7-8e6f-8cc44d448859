<template>
  <div>
    <el-table :data="data.extendTableData" size="small" border>
      <el-table-column label="索引" prop="fieldIndex" />
      <el-table-column
        label="中文名称"
        prop="description"
        show-overflow-tooltip
      >
        <template #default="scope">
          <ellipsisText :value="scope.row.description"></ellipsisText>
        </template>
      </el-table-column>
      <el-table-column label="英文名称" prop="field" show-overflow-tooltip>
        <template #default="scope">
          <ellipsisText :value="scope.row.field"></ellipsisText>
        </template>
      </el-table-column>
      <el-table-column label="属性类型" prop="fieldType" show-overflow-tooltip>
        <template #default="scope">
          <div>{{ FIELD_TYPE_NAME_MAP[scope.row.fieldType] }}</div>
        </template>
      </el-table-column>
      <el-table-column label="属性值">
        <template #default="scope">
          <ellipsisText :value="getReferenceDis(scope.row)"></ellipsisText>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="140">
        <template #default="scope">
          <el-popconfirm
            confirm-button-text="删除"
            cancel-button-text="取消"
            :icon="InfoFilled"
            title="是否删除该扩展属性?"
            @confirm="handleDelete(scope.$index)"
          >
            <template #reference>
              <el-button size="small" type="primary" text>删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script setup lang="ts">
import { InfoFilled } from "@element-plus/icons-vue";
import { FIELD_TYPE_NAME_MAP, FIELD_TYPE_MAP } from "@/utils/constant";
import ellipsisText from "@/components/ellipsisText/index.vue";

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

function getReferenceDis(data: any) {
  if (data.fieldType === FIELD_TYPE_MAP.fixed) {
    return data.fieldValue;
  }
  if (data.fieldType === FIELD_TYPE_MAP.source) {
    return typeof data.fieldValue === "string" && data.fieldValue
      ? JSON.parse(data.fieldValue).join(",")
      : "-";
  }
  if (data.references && data.references.length) {
    return data.references
      .map((reference: any) => reference.referenceHandle)
      .join(",");
  }
}

// 删除基础属性
function handleDelete(index: number) {
  // 记录删除属性
  props.data.extendDelList.push(props.data.extendTableData[index]);
  props.data.extendTableData.splice(index, 1);
}
</script>
<style></style>
