<!-- 第 4 步发布标识 -->
<template>
  <div class="handle-publish-box">
    <div class="header">
      <div class="header-right">
        <el-input
          v-model.trim="data.ids"
          placeholder="请输入标识"
          style="width: 396px"
          clearable
        >
          <template #prepend> 标识: </template>
        </el-input>
        <el-button
          type="primary"
          :disabled="!data.ids"
          @click="handleSearch"
          class="handle-query-search-form-btn"
          >查询</el-button
        >
      </div>
    </div>
    <div class="handle-query-result" v-loading="loading">
      <div v-if="!data.searchData" class="handle-query-result-blank">
        <img src="@/assets/blank.svg" />
        <div class="handle-query-result-blank-text">暂无数据</div>
      </div>
      <matrix-graph v-else ref="matrixGraphRef"></matrix-graph>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, reactive, ref, nextTick, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { ApiTestConnection } from "@/api/objHandle/manager";
import matrixGraph from "@/views/idRes/components/matrix-graph.vue";

const props = defineProps({
  handleDetail: Object,
});
const loading = ref(false);
const matrixGraphRef = ref();
const data = reactive({
  ids: "",
  searchData: null,
});
const isTest = ref(false);

function handleSearch() {
  loading.value = true;
  ApiTestConnection({ id: props.handleDetail?.id || "", handle: data.ids })
    .then((response: any) => {
      // ToDo 解析失败还需要怎么判断
      if (!response.handle) {
        isTest.value = false;
        ElMessage.error("测试连接失败，请重新输入标识进行查询");
        return;
      }
      data.searchData = response;
      isTest.value = true;
      nextTick(() => {
        matrixGraphRef.value.refresh(data.searchData);
      });
    })
    .catch(() => {
      isTest.value = false;
      data.searchData = null;
    })
    .finally(() => {
      loading.value = false;
    });
}

onMounted(() => {
  console.log("====第四步 onMounted====：", props.handleDetail);
});

const isTestSuccess = () => {
  return isTest.value;
};

defineExpose({
  isTestSuccess,
});
</script>
<style scoped lang="scss">
.handle-query-result-blank-text {
  padding-top: 8px;
  text-align: center;
  color: #7b9790;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
.handle-publish-box {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  padding: 0 20px;
  .header {
    position: relative;
    width: 100%;
    background-color: red;
    .header-right {
      position: absolute;
      right: 0;
      top: 17px;
      bottom: 10px;
      .handle-query-search-form-btn {
        margin-left: 8px;
      }
      :deep(.el-input__wrapper) {
        box-shadow: none;
        border-radius: 2px;
        background-color: #eef2f1;
        border: none;
      }
      :deep(.el-input-group__prepend) {
        box-shadow: none;
        background-color: #eef2f1;
        padding-right: 0;
        color: #535f5c;
        font-size: 12px;
      }
    }
  }
  .handle-query-result {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 609px;
    margin-top: 58px;
    margin-bottom: 35px;
    background-color: #eef2f1;
  }
}
</style>
