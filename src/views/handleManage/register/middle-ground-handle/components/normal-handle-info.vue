<template>
  <el-form
    label-position="left"
    label-width="100px"
    :model="form"
    ref="formRef"
    :rules="rules"
    style="width: 850px; margin-top: 24px; text-align: center"
  >
    <el-form-item label="对象标识名称" prop="name">
      <el-input
        :disabled="isEdit"
        v-model.trim="form.name"
        placeholder="请输入"
        style="width: 746px"
        clearable
      >
      </el-input>
    </el-form-item>
    <el-form-item
      class="form-item-handle"
      required
      label="对象标识"
      :disabled="isEdit"
    >
      <div class="handle-tid-wrap">
        <el-form-item prop="prefix" style="width: 122px">
          <el-select
            v-model="form.prefix"
            disabled="true"
            placeholder="请选择"
            filterable
            clearable
          >
            <el-option
              v-for="item in entPrefixList"
              :key="item"
              :value="item"
              :label="item"
            ></el-option>
          </el-select>
        </el-form-item>
        <div class="handle-tid-line" style="width: 14px">/</div>
        <el-form-item class="handle-tid-suffix" prop="suffix">
          <el-input
            v-model.trim="form.suffix"
            placeholder="请输入"
            clearable
            :disabled="isEdit"
            style="width: 100%"
          >
          </el-input>
        </el-form-item>
      </div>
    </el-form-item>
    <el-form-item label="实体类型" prop="entityType">
      <el-select
        v-model="form.entityType"
        placeholder="请选择"
        style="width: 746px"
        filterable
        clearable
      >
        <el-option
          v-for="item in entityTypeList"
          :key="item.value"
          :value="item.value"
          :label="item.name"
        ></el-option>
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from "vue";
import { ApiGetHandlePrefixDropDownList } from "@/api/objHandle/manager";

const props = defineProps({
  isEdit: Boolean,
  formData: Object,
});

const formRef = ref();

const form = reactive({
  name: "",
  prefix: "",
  suffix: "",
  entityType: "",
});

const validatePrefix = (rule: any, value: any, callback: any) => {
  if (value === "") {
    return callback(new Error("对象标识前缀不能为空"));
  }
  const regex = /^\d+\.\d+\.\d+$/;
  if (!regex.test(value)) {
    return callback(new Error("对象标识前缀格式不正确"));
  }

  callback();
};

const validateSuffix = (rule: any, value: any, callback: any) => {
  if (value === "") {
    return callback(new Error("对象标识后缀不能为空"));
  }
  const maxLength = 255 - (form.prefix.length + 1);
  if (value.length > maxLength) {
    return callback(new Error("最大长度为255字符"));
  }

  callback();
};

const rules = {
  name: [
    {
      required: true,
      message: "标识名称不能为空",
      trigger: "blur",
    },
    {
      max: 255,
      message: "最大长度为255字符",
    },
  ],
  prefix: [
    {
      required: true,
      validator: validatePrefix,
      trigger: "blur",
    },
  ],
  suffix: [
    {
      required: true,
      validator: validateSuffix,
      trigger: "blur",
    },
  ],
  entityType: [
    { required: true, message: "实体类型不能为空", trigger: "blur" },
  ],
};

const entPrefixList = ref<any[]>([]);

const entityTypeList = [
  { value: 1, name: "业务实体" },
  { value: 2, name: "资源实体" },
];

const getEntPrefixList = () => {
  return ApiGetHandlePrefixDropDownList().then((response: any) => {
    entPrefixList.value = Array.isArray(response) ? response : [];
    if (entPrefixList.value.length > 0) {
      form.prefix = entPrefixList.value[0];
    }
  });
};

const formValidate = async () => {
  let isError = true;
  await formRef.value.validate((valid: any) => {
    if (valid) {
      isError = false;
    }
  });
  return isError;
};

const getData = () => {
  return {
    name: form.name,
    entityType: form.entityType,
    handle: `${form.prefix}/${form.suffix}`,
  };
};

onMounted(() => {
  getEntPrefixList();
  if (props.formData) {
    const { handle, name, entityType } = props.formData;
    form.name = name;
    form.entityType = entityType;
    if (handle) {
      const [prefix, suffix] = handle.split("/");
      form.prefix = prefix || "";
      form.suffix = suffix || "";
    }
  }
});

defineExpose({
  formValidate,
  getData,
});
</script>

<style scoped lang="scss">
.form-item-handle {
  :deep(.el-input-group__prepend) {
    padding-right: 0;
  }
  .form-item-handle-line {
    border-left: 1px solid #dcdfe6;
    padding: 0 10px;
    margin-left: 20px;
  }
}
.handle-tid-wrap {
  display: flex;
  align-items: center;
  justify-content: start;
  width: 100%;
  .handle-tid-line {
    margin: 0 0px;
  }
  .handle-tid-suffix {
    flex: 1;
    min-width: 0;
  }
}
</style>
