<template>
  <el-dialog
    v-model="visibleass"
    class="ass-dialog"
    title="关联权限组"
    width="580px"
    append-to-body
    destroy-on-close
    @close="handleClose"
    align-center
  >
    <div class="prompt">
      标识数据关联权限组后，即可作为权限组的数据范围被分配给企业应用用户。
    </div>
    <el-form label-width="120px" label-position="left">
      <el-form-item label="系统内置权限组" required>
        <el-radio-group v-model="groupId">
          <el-radio :label="1">行业内公开</el-radio>
          <el-radio :label="2">省级内公开</el-radio>
          <el-radio :label="3">企业内公开</el-radio>
          <el-radio :label="4">不公开</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <div v-if="tableData.length > 0" class="custom">
      自定义权限组（支持多选）
    </div>
    <el-table
      v-if="tableData.length > 0"
      ref="tableRef"
      :data="tableData"
      :row-key="(row: any) => row.id"
      v-loading="tableLoading"
      :show-header="false"
      border
      style="width: 517px; margin-left: 9px"
      max-height="450px"
      @update="handleDataUpdate"
      @changed="handleDataUpdate"
      @row-click="handleRowClick"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="" property="name">
        <template #default="scope">
          {{ scope.row.authGroupName || "-" }}
        </template>
      </el-table-column>
    </el-table>

    <template #footer>
      <span>
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="requestAPI" :loading="btnLoading">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { nextTick, ref, onMounted, defineEmits, defineProps } from "vue";
import { ElMessage } from "element-plus";
import { apiAuthGroupList } from "@/api/grantManage/identify";

const props = defineProps({
  oriData: {
    type: Array<any>,
    default: [],
  },
});
const emit = defineEmits(["close-associate", "success"]);
const tableLoading = ref(false);
const tableRef = ref<any>();
const tableData = ref<any>([]);
const btnLoading = ref(false);
const visibleass = ref(true);
const handleClose = () => {
  emit("close-associate");
};

const groupId = ref(1);
const customGroupList = ref<any>([]);
const handleRowClick = (row: any) => {
  tableRef.value.toggleRowSelection(row);
};

const requestAPI = () => {
  if (tableData.value.length === 0) {
    customGroupList.value = [];
  } else {
    customGroupList.value = tableRef.value.getSelectionRows();
  }
  emit("success");
};

// const validForm = async () => {
//   const valid = await formRef.value.validate();
//   if (valid) {
//     requestAPI();
//   } else {
//     return false;
//   }
// };

const loadData = async () => {
  tableLoading.value = true;
  const res = await apiAuthGroupList();
  if (res instanceof Array) {
    tableData.value = res as Array<any>;
  }
  tableLoading.value = false;
};

const handleDataUpdate = () => {
  for (const item of props.oriData) {
    if (item.type === 1) {
      groupId.value = item.id;
    } else {
      const t = tableData.value.filter((one: any) => {
        return one.id === item.id;
      });
      if (t && t.length > 0) {
        tableRef.value.toggleRowSelection(t[0], true);
      }
    }
  }
};

onMounted(async () => {
  customGroupList.value = [];
  await loadData();
  nextTick(() => {
    handleDataUpdate();
  });
});
defineExpose({
  groupId,
  customGroupList,
});
</script>

<style lang="scss">
.ass-dialog {
  margin: 24px 20px;
  .prompt {
    font-size: 12px;
    color: #7b9790;
    margin-bottom: 20px;
  }
  .el-form-item {
    margin-bottom: 0px;
  }
  .custom {
    margin-left: 9px;
    margin-top: 20px;
    margin-bottom: 12px;
    font-size: 12px;
    line-height: 20px;
    color: #272e2c;
  }
  .el-radio {
    margin-right: 20px;
  }
  // .el-form-item__label {
  //   font-size: 12px;
  //   line-height: 20px;
  //   color: #272e2c;
  // }
  // .el-form-item__content {
  //   font-size: 12px;
  //   line-height: 20px;
  //   color: #272e2c;
  // }
}
</style>
