<!-- 中台标识注册 -->
<template>
  <div class="page-search">
    <search-layout>
      <template #left v-if="isAppUser">
        <el-button
          type="primary"
          @click="handleAdd"
          v-permission="AUTH_CODE.HANDLE_REGISTER_ADD"
          >+添加</el-button
        >
      </template>
      <template #right>
        <el-form
          v-permission="[
            AUTH_CODE.HANDLE_REGISTER_PAGE,
            AUTH_CODE.HANDLE_WATCH_PAGE,
          ]"
          :inline="true"
          :model="searchForm"
          @submit.prevent
        >
          <el-form-item>
            <el-input
              v-model.trim="searchForm.name"
              clearable
              @clear="handleSearch"
              placeholder="请输入"
            >
              <template #prefix>对象标识名称：</template></el-input
            >
          </el-form-item>
          <el-form-item>
            <el-input
              v-model.trim="searchForm.handle"
              clearable
              @clear="handleSearch"
              placeholder="请输入"
            >
              <template #prefix>对象标识：</template></el-input
            ></el-form-item
          >
          <el-form-item v-if="isProvinceUser">
            <el-input
              v-model="searchForm.orgName"
              placeholder="请输入企业"
              clearable
              @clear="handleSearch"
            >
              <template #prefix>企业名称：</template>
            </el-input>
          </el-form-item>
          <el-form-item v-if="isEntUser && !isAppUser">
            <el-input
              v-model="searchForm.appName"
              placeholder="请输入应用"
              clearable
              @clear="handleSearch"
            >
              <template #prefix>应用名称：</template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-date-picker
              v-model="searchForm.maintainTime"
              type="daterange"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled-date="disabledDate"
              clearable
              @change="handleTimeClear()"
            />
          </el-form-item>
          <el-form-item style="width: 60px; max-width: 60px">
            <el-button
              type="primary"
              :loading="data.searchLoading"
              @click="handleSearch"
              >搜索</el-button
            >
          </el-form-item>
        </el-form>
      </template>
    </search-layout>
  </div>
  <div class="page-search-body" v-if="!recordVisible">
    <el-table
      :data="data.tableData"
      v-loading="data.tableLoading"
      border
      size="small"
    >
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column property="name" label="对象标识名称">
        <template #default="scope">
          <ellipsisText :value="scope.row.name">{{
            scope.row.name || "-"
          }}</ellipsisText>
        </template>
      </el-table-column>
      <el-table-column property="handle" label="对象标识">
        <template #default="scope">
          <ellipsisText :value="scope.row.handle">{{
            scope.row.handle || "-"
          }}</ellipsisText>
        </template>
      </el-table-column>
      <el-table-column
        v-if="appType === APP_TYPE.DMM"
        property="handleType"
        label="标识分类"
      >
        <template #default="scope">
          <span>
            {{
              scope.row.handleType
                ? scope.row.handleType === 1
                  ? "主数据"
                  : "非主数据"
                : "-"
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column property="appName" label="所属应用">
        <template #default="scope">
          <ellipsisText :value="scope.row.appName"></ellipsisText>
        </template>
      </el-table-column>
      <el-table-column
        v-if="isProvinceUser"
        property="orgName"
        label="企业名称"
      >
        <template #default="scope">
          <ellipsisText :value="scope.row.orgName"></ellipsisText>
        </template>
      </el-table-column>
      <el-table-column v-if="isAppUser" label="发布状态">
        <template #default="scope">
          {{ getHandleStatusName(scope.row.handleStatus) }}
        </template>
      </el-table-column>
      <el-table-column v-if="isAppUser" label="注册状态">
        <template #default="scope">
          {{ getRegisterStatus(scope.row.registerStatus) }}
        </template>
      </el-table-column>
      <el-table-column property="updatedTime" label="操作时间" />

      <el-table-column label="操作" :width="isAppUser ? 200 : 100">
        <template #default="scope">
          <el-button
            v-if="isAppUser"
            type="primary"
            text
            :disabled="scope.row.handleStatus === 2"
            @click="handleRelatedChannel(scope.row)"
            >关联通道</el-button
          >
          <el-button
            type="primary"
            text
            @click="showDetailView(scope.row)"
            v-permission="[
              AUTH_CODE.HANDLE_REGISTER_DETAIL,
              AUTH_CODE.HANDLE_WATCH_DETAIL,
            ]"
            >详情</el-button
          >
          <el-popconfirm
            v-if="isAppUser && scope.row.handleStatus === 2"
            :width="200"
            confirm-button-text="确定"
            cancel-button-text="取消"
            title="编辑发布状态的对象标识，会变为草稿状态，不能进行实例标识解析，需要关联通道重新发布！"
            @confirm="handleEdit(scope.$index, scope.row)"
          >
            <template #reference>
              <el-button
                text
                type="primary"
                v-permission="AUTH_CODE.HANDLE_REGISTER_EDIT"
                >编辑</el-button
              >
            </template>
          </el-popconfirm>
          <el-button
            v-if="isAppUser && scope.row.handleStatus !== 2"
            type="primary"
            text
            @click="handleEdit(scope.$index, scope.row)"
            v-permission="AUTH_CODE.HANDLE_REGISTER_EDIT"
            >编辑</el-button
          >
          <el-popconfirm
            v-if="isAppUser"
            :width="200"
            confirm-button-text="确定"
            cancel-button-text="取消"
            title="删除标识会导致标识解析失败，请谨慎删除，是否删除该标识？"
            @confirm="handleDeleteConfirm(scope.row.id)"
          >
            <template #reference>
              <el-button
                text
                type="primary"
                v-permission="AUTH_CODE.HANDLE_REGISTER_DELETE"
                >删除</el-button
              >
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      v-model:currentPage="data.page"
      v-model:page-size="data.size"
      :page-sizes="[10, 20, 30, 40]"
      small
      layout="total, sizes, prev, pager, next, jumper"
      :total="data.totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>

  <DetailDrawer
    v-if="detailDrawerVisible"
    :id="data.selectHandleData.id"
    :handle="data.selectHandleData.handle"
    :is-master-data="false"
    @close="detailDrawerVisible = false"
  ></DetailDrawer>
  <MainDataDrawer
    v-if="mainDataDrawerVisible"
    :id="data.selectHandleData.id"
    @close="mainDataDrawerVisible = false"
  ></MainDataDrawer>
  <HandleRegister
    :is-edit="isEdit"
    :handle-id="handleId"
    :is-related="isRelated"
    v-if="showHandleRegister"
    @close-dialog="closeDialog"
  ></HandleRegister>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, toRefs } from "vue";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
import {
  ApiGetMidHandlePage,
  appList,
  ApiDeleteMidHandle,
} from "@/api/objHandle/manager";
import DetailDrawer from "@/components/MidHandleDetailDrawer/index.vue";
import MainDataDrawer from "@/components/MainDataDrawer/index.vue";
import { AUTH_CODE } from "@/utils/authCode";
import SearchLayout from "@/components/searchLayout/index.vue";
import ellipsisText from "@/components/ellipsisText/index.vue";
import { APP_TYPE } from "@/utils/dataPlatform";
import HandleRegister from "./handle-register.vue";

const store = useStore();
const { isProvinceUser, isEntUser, isAppUser, appType } = toRefs(store.getters);

const detailDrawerVisible = ref(false); // 非主数据详情
const mainDataDrawerVisible = ref(false); // 主数据详情
const recordVisible = ref(false);
const showHandleRegister = ref(false);
const handleId = ref("");
const isRelated = ref(false);

const getHandleStatusName = (status: number) => {
  if (status === 1) return "草稿";
  if (status === 2) return "发布";
  return "-";
};

const getRegisterStatus = (status: number) => {
  if (status === 1) return "注册成功";
  if (status === 2) return "注册失败";
  return "-";
};

const disabledDate = (time: Date) => {
  return time.getTime() > new Date().getTime();
};

// 注册对话框关闭
function closeDialog() {
  showHandleRegister.value = false;
  getTableData();
}

const searchForm = reactive({
  name: "",
  handle: "",
  maintainTime: [] as any,
  orgName: "",
  appName: "",
});
const isEdit = ref(false);

interface Data {
  tableData: {
    appName: string;
    createdBy: string;
    createdTime: string;
    entityType: number;
    handle: string;
    id: number;
    name: string;
    type: number;
    reportName: string;
    updatedTime: string;
    uploadState: number;
  }[];
  appList: {
    id: number;
    appName: string;
  }[];
  page: number;
  size: number;
  totalCount: number;
  dialogTitle: string;
  dialogVisible: boolean;
  selectHandleData: any;
  dialogDisabled: boolean;
  deleteLoading: boolean;
  tableLoading: boolean;
  searchLoading: boolean;
  confirmLoading: boolean;
}
const data = reactive<Data>({
  tableData: [],
  appList: [],
  page: 1,
  size: 10,
  totalCount: 1,
  dialogTitle: "",
  dialogVisible: false,
  selectHandleData: null,
  dialogDisabled: false,
  deleteLoading: false,
  tableLoading: false,
  searchLoading: false,
  confirmLoading: false,
});

function getTableData() {
  data.tableLoading = true;
  data.tableData = [];
  const params = {
    name: searchForm.name,
    handle: searchForm.handle,
    orgName: searchForm.orgName,
    appName: searchForm.appName,
    page: data.page - 1,
    size: data.size,
    startTime: "",
    endTime: "",
  };
  if (searchForm.maintainTime && searchForm.maintainTime.length) {
    const [startTime, endTime] = searchForm.maintainTime;
    params.startTime = startTime;
    params.endTime = endTime.replace("00:00:00", "23:59:59");
  }
  let roleType: any = "app";
  if (isProvinceUser.value) {
    roleType = "province";
  } else if (isEntUser.value) {
    roleType = "ent";
  } else {
    roleType = "app";
  }

  ApiGetMidHandlePage(roleType, params)
    .then((response: any) => {
      const result = response;
      data.tableData = result?.content || [];
      data.page = result?.pageNumber || 1;
      data.totalCount = result?.totalCount || 0;
    })
    .finally(() => {
      data.tableLoading = false;
      data.searchLoading = false;
    });
}

const handleRelatedChannel = (data: any) => {
  showHandleRegister.value = true;
  isRelated.value = true;
  handleId.value = data.id;
};

// 详情按钮
function showDetailView(item: any) {
  data.selectHandleData = item;
  if (item.handleType === 1) {
    // 主数据的详情跳列表
    mainDataDrawerVisible.value = true;
  } else {
    // 非主数据的详情跳一般详情页
    detailDrawerVisible.value = true;
  }
}

// 清空操作时间，查询数据
function handleTimeClear() {
  if (!searchForm.maintainTime || searchForm.maintainTime.length === 0) {
    handleSearch();
  }
}

function handleSearch() {
  data.searchLoading = true;
  data.page = 1;
  getTableData();
}

function handleSizeChange(num: number) {
  data.size = num;
  data.page = 1;
  getTableData();
}

function handleCurrentChange(num: number) {
  data.page = num;
  getTableData();
}
// 新增标识
function handleAdd() {
  data.selectHandleData = null;
  showHandleRegister.value = true;
  isRelated.value = false;
  isEdit.value = false;
  handleId.value = "";
}

function handleDeleteConfirm(id: any) {
  data.deleteLoading = true;
  ApiDeleteMidHandle(id)
    .then(() => {
      ElMessage.success("删除成功");
      data.page = 1;
      getTableData();
    })
    .finally(() => {
      data.deleteLoading = false;
    });
}

// 编辑按钮
function handleEdit(index: any, item: any) {
  data.selectHandleData = item;
  showHandleRegister.value = true;
  isEdit.value = true;
  isRelated.value = false;
  handleId.value = item.id;
}

// 获取应用列表下拉框
function getAppList() {
  appList().then((response: any) => {
    data.appList = Array.isArray(response) ? response : [];
  });
}

onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (
    AllPermission.includes(AUTH_CODE.HANDLE_REGISTER_PAGE) ||
    AllPermission.includes(AUTH_CODE.HANDLE_WATCH_PAGE)
  ) {
    getTableData();
    getAppList();
  }
});
</script>
<style lang="scss" scoped>
.page-search {
  .dialogClass {
    background-color: red;

    .mid-box {
      background-color: red;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      :deep(.el-input__inner) {
        width: 500px;
        height: 32px;
        line-height: 32px;
      }
    }
  }
}
.popover-delete-tips {
  margin-bottom: 20px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .popover-delete-tips-text {
    margin-left: 10px;
    font-size: 14px;
  }
}
.popover-delete-wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
.infoAlert {
  display: flex;
  align-items: center;
  height: 30px;
  margin: -44px -44px 14px;
  background-color: rgb(232, 244, 255);
}
.handleUploadInfo {
  width: 20px;
  height: 20px;
  margin-left: 20px;
}

.handleRecordClass {
  margin-left: 4px;
  margin-right: 4px;
  color: rgb(0, 183, 255);
  cursor: pointer;
}
.svgClose {
  height: 100%;
  width: 25px;
  margin-left: auto;
  display: flex;
  align-items: center;
}
.handleUploadClose {
  width: 15px;
  height: 15px;
  cursor: pointer;
}
.clickable-txt-cls {
  color: #1664ff;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
</style>
