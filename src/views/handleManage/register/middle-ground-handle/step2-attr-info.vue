<!-- 属性信息 -->
<template>
  <div class="handle-attr-info-wrap">
    <el-button
      type="primary"
      :class="'attr-info-add-btn'"
      @click="handleAddAttr"
      >新增属性</el-button
    >
    <el-button
      color="#00D39F"
      plain
      class="attr-info-add-btn2"
      @click="handleAssociate"
      >批量关联权限组</el-button
    >

    <el-tabs v-model="activeName">
      <el-tab-pane label="基础属性" :name="ATTR_CATEGORY.BASIC">
        <AttrTable
          table-key="basicAttrTable"
          :table="basicTableData"
          @delete="basicTableDelete"
          @edit="basicTableEdit"
          @associate="basicAssociate"
        ></AttrTable>
      </el-tab-pane>
      <el-tab-pane label="扩展属性" :name="ATTR_CATEGORY.EXTEND">
        <AttrTable
          :refresh="activeName === ATTR_CATEGORY.EXTEND"
          table-key="extendAttrTable"
          :table="extendTableData"
          @delete="extendTableDelete"
          @edit="extendTableEdit"
          @associate="expendAssociate"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
  <AssociateDialog
    v-if="associateVisible"
    ref="associateDialogRef"
    :ori-data="rowGroupList"
    @close-associate="closeAssociateDialog"
    @success="confirmAssociateDialog"
  ></AssociateDialog>
  <AttrDialog
    v-if="showAttrDialog"
    :type="attrDialogType"
    :category="activeName"
    :data="attrDataTemp"
    :fields="attrFields"
    :relatedFields="relatedFields"
    :columnNames="attrColumnNames"
    :descriptions="attrDescriptions"
    :basicInfo="basicInfo"
    :referenceHandles="referenceHandles"
    :dataNames="attrDatabaseNames"
    :dataServiceIds="attrDataServiceIds"
    @add="handleAttrDialogAdd"
    @edit="handleAttrDialogEdit"
    @close="showAttrDialog = false"
  ></AttrDialog>
</template>

<script lang="ts" setup>
import { defineProps, onMounted, ref, computed } from "vue";
import { useStore } from "vuex";
import {
  DIALOG_TYPE,
  ATTR_CATEGORY,
  ATTR_TYPE_MAP,
} from "@/utils/dataPlatform";
import AttrTable from "@/components/HandleAttrInfo/components/attrTable.vue";
import AttrDialog from "@/components/HandleAttrInfo/components/attrDialog.vue";
import AssociateDialog from "./components/associateDialog.vue";

const store = useStore();
const globalConfig = computed(() => store.getters.globalConfig);

const associateDialogRef = ref();
const emit = defineEmits(["close", "confirm"]);
const props = defineProps({
  handleDetail: Object,
});

const basicInfo = ref();

const basicTableData = ref<any[]>([]);
const extendTableData = ref<any[]>([]);

const activeName = ref(ATTR_CATEGORY.BASIC);

const attrDataTemp = ref(); // 编辑数据
const attrDataTempIndex = ref(-1); // 编辑数据的index

const rowGroupList = ref<any[]>([]);
const associateVisible = ref(false);
// 属性弹窗
const showAttrDialog = ref(false);

// 属性弹窗类型-新增/编辑
const attrDialogType = ref(DIALOG_TYPE.ADD);

// 所有的属性
const referenceHandles = computed(() => {
  try {
    const basicAttrReference = basicTableData.value
      .filter((item) => item.fieldType === ATTR_TYPE_MAP.RELATE_HANDLE)
      .map((item: any) => item.references[0].referenceHandle);
    const extendAttrReference = extendTableData.value
      .filter((item) => item.fieldType === ATTR_TYPE_MAP.RELATE_HANDLE)
      .map((item: any) => item.references[0].referenceHandle);
    return [...basicAttrReference, ...extendAttrReference];
  } catch (error) {
    return [];
  }
});

const attrFields = computed(() => {
  const basicFields = basicTableData.value.map((item: any) => item.field);
  const extendFields = extendTableData.value.map((item: any) => item.field);
  return [...basicFields, ...extendFields];
});

// 关联标识字段
const relatedFields = computed(() => {
  return extendTableData.value
    .filter((item: any) => item.fieldType === ATTR_TYPE_MAP.RELATE_HANDLE)
    .map((item: any) => item.field);
});

const attrColumnNames = computed(() => {
  const basicColumnNames = basicTableData.value.map(
    (item: any) => item.columnName
  );
  const extendColumnNames = extendTableData.value.map(
    (item: any) => item.columnName
  );
  return [...basicColumnNames, ...extendColumnNames];
});

const attrDescriptions = computed(() => {
  const basicDescriptions = basicTableData.value.map(
    (item: any) => item.description
  );
  const extendDescriptions = extendTableData.value.map(
    (item: any) => item.description
  );
  return [...basicDescriptions, ...extendDescriptions];
});

// 数据服务ID-非中台应用-标识解析数据源
const attrDataServiceIds = computed(() => {
  const basicDataServices = basicTableData.value
    .filter((item: any) => {
      return !!item.dataServiceId;
    })
    .map((item: any) => item.dataServiceId);
  const extendDataServices = extendTableData.value
    .filter((item: any) => {
      return !!item.dataServiceId;
    })
    .map((item: any) => item.dataServiceId);
  return [...basicDataServices, ...extendDataServices];
});

// 数据库-非中台应用-标识解析数据源
const attrDatabaseNames = computed(() => {
  const basicDatabaseNames = basicTableData.value
    .filter((item: any) => {
      return !!item.databaseName;
    })
    .map((item: any) => item.databaseName);
  const extendDatabaseNames = extendTableData.value
    .filter((item: any) => {
      return !!item.databaseName;
    })
    .map((item: any) => item.databaseName);
  return [...basicDatabaseNames, ...extendDatabaseNames];
});

// 新增属性
const handleAddAttr = () => {
  showAttrDialog.value = true;
  attrDataTemp.value = null;
  attrDataTempIndex.value = -1;
  attrDialogType.value = DIALOG_TYPE.ADD;
};
// 删除基础属性
const basicTableDelete = (index: number) => {
  basicTableData.value.splice(index, 1);
};

// 编辑基础属性
const basicTableEdit = (index: number) => {
  showAttrDialog.value = true;
  attrDialogType.value = DIALOG_TYPE.EDIT;
  attrDataTemp.value = JSON.parse(JSON.stringify(basicTableData.value[index]));
  attrDataTempIndex.value = index;
};

// 删除扩展属性
const extendTableDelete = (index: number) => {
  extendTableData.value.splice(index, 1);
};

// 编辑扩展属性
const extendTableEdit = (index: number) => {
  showAttrDialog.value = true;
  attrDialogType.value = DIALOG_TYPE.EDIT;
  attrDataTemp.value = JSON.parse(JSON.stringify(extendTableData.value[index]));
  attrDataTempIndex.value = index;
};

const basicAssociate = (index: number) => {
  associateVisible.value = true;
  rowGroupList.value = basicTableData.value[index].authGroupListVOList;
  attrDataTempIndex.value = index;
};

const expendAssociate = (index: number) => {
  associateVisible.value = true;
  rowGroupList.value = extendTableData.value[index].authGroupListVOList;
  attrDataTempIndex.value = index;
};

// 关联权限组
const handleAssociate = () => {
  associateVisible.value = true;
  rowGroupList.value = [];
  attrDataTempIndex.value = -1;
};

const getDefaultGroup = (id: number) => {
  if (id === 1) {
    return { authGroupName: "行业内公开", id: 1, type: 1 };
  }
  if (id === 2) {
    return { authGroupName: "省级内公开", id: 2, type: 1 };
  }
  if (id === 3) {
    return { authGroupName: "企业内公开", id: 3, type: 1 };
  }
  return { authGroupName: "不公开", id: 4, type: 1 };
};

const confirmAssociateDialog = () => {
  associateVisible.value = false;
  const { groupId, customGroupList } = associateDialogRef.value;
  const defaultGroup = getDefaultGroup(groupId);
  const list = customGroupList as Array<any>;
  const newList = [defaultGroup, ...list];
  if (attrDataTempIndex.value > -1) {
    if (activeName.value === ATTR_CATEGORY.BASIC) {
      basicTableData.value[attrDataTempIndex.value].authGroupListVOList =
        newList;
    }
    if (activeName.value === ATTR_CATEGORY.EXTEND) {
      extendTableData.value[attrDataTempIndex.value].authGroupListVOList =
        newList;
    }
  } else {
    if (activeName.value === ATTR_CATEGORY.BASIC) {
      basicTableData.value.forEach((item: any) => {
        item.authGroupListVOList = newList;
      });
    }
    if (activeName.value === ATTR_CATEGORY.EXTEND) {
      extendTableData.value.forEach((item: any) => {
        if (item.fieldType !== 3) {
          item.authGroupListVOList = newList;
        }
      });
    }
  }
};

const closeAssociateDialog = () => {
  associateVisible.value = false;
};

// 新增属性弹窗确认
const handleAttrDialogAdd = (data: any) => {
  showAttrDialog.value = false;
  if (activeName.value === ATTR_CATEGORY.BASIC) {
    data.forEach((item: any) => {
      item.databaseName = item.databaseName || "";
      item.tableName = item.tableName || "";
      item.columnName = item.columnName || "";
      item.authGroupListVOList = [
        getDefaultGroup(globalConfig.value.defaultAuthType),
      ];
      basicTableData.value.push(item);
    });
  }
  if (activeName.value === ATTR_CATEGORY.EXTEND) {
    data.forEach((item: any) => {
      item.databaseName = item.databaseName || "";
      item.tableName = item.tableName || "";
      item.columnName = item.columnName || "";
      if (item.fieldType === 3) {
        item.authGroupListVOList = [getDefaultGroup(1)];
      } else {
        item.authGroupListVOList = [
          getDefaultGroup(globalConfig.value.defaultAuthType),
        ];
      }
      extendTableData.value.push(item);
    });
  }
};

// 编辑属性弹窗确认
const handleAttrDialogEdit = (data: any) => {
  showAttrDialog.value = false;
  if (activeName.value === ATTR_CATEGORY.BASIC) {
    basicTableData.value[attrDataTempIndex.value] = JSON.parse(
      JSON.stringify(data)
    );
  }
  if (activeName.value === ATTR_CATEGORY.EXTEND) {
    extendTableData.value[attrDataTempIndex.value] = JSON.parse(
      JSON.stringify(data)
    );
  }
};
onMounted(() => {
  console.log("=====step2 onMounted=====:", props.handleDetail);
  try {
    if (props.handleDetail) {
      const { handleDetail } = props;
      basicInfo.value = {
        name: handleDetail.name,
        handle: handleDetail.handle || handleDetail.name,
        handleType: handleDetail.handleType,
        entityObjectId: handleDetail.entityObjectId,
      };
      basicTableData.value = handleDetail.items
        ? JSON.parse(JSON.stringify(handleDetail.items))
        : [];
      extendTableData.value = handleDetail.extendItems
        ? JSON.parse(JSON.stringify(handleDetail.extendItems))
        : [];
    }
  } catch (error) {
    console.error("=====step2 onMounted error=====:", error);
  }
});

defineExpose({
  basicTableData,
  extendTableData,
});
</script>
<style scoped lang="scss">
:deep(.el-tabs__nav-wrap::after) {
  content: none;
}
:deep(.el-tabs__active-bar) {
  display: none;
}
:deep(.el-tabs__item) {
  height: auto;
  line-height: normal;
}
:deep(.el-tabs--top .el-tabs__item.is-top:nth-child(2)) {
  border-right: 2px solid #c1c9c7;
}
.handle-attr-info-wrap {
  position: relative;
  padding: 32px 20px;
  width: 100%;
}

.attr-info-add-btn {
  position: absolute;
  right: 160px;
  top: 26px;
  z-index: 1;
}
.attr-info-add-btn2 {
  position: absolute;
  right: 20px;
  top: 26px;
  z-index: 1;
}
</style>
