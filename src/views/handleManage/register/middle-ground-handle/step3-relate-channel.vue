<!-- 第三步关联通道 -->
<template>
  <div class="handle-attr-info-wrap">
    <el-form ref="formRef" :model="form" style="width: 100%">
      <div class="box">
        <el-tabs v-model="activeName" style="width: 100%">
          <el-tab-pane label="基础属性" name="basic">
            <div class="channel-box" style="margin-top: 10px">
              <p>数据通道</p>
              <el-select
                v-model="dataChannel"
                placeholder="请选择"
                style="width: 776px; height: 32px"
                filterable
                clearable
                @change="changeDataChannel(dataChannel)"
              >
                <el-option
                  v-for="item in channelOptions"
                  :key="item?.dataChannelId"
                  :label="item?.dataChannelName"
                  :value="item.dataChannelId"
                />
              </el-select>
            </div>
            <div class="tips-cls" v-if="isAppUser">
              <p>没有可用的数据通道时，请前往</p>
              <el-button
                @click="handleGoTool"
                type="primary"
                text
                style="
                  padding: 0;
                  margin-top: 0px;
                  font-size: 12px;
                  color: #1664ff;
                  font-weight: 400;
                "
                >数据通道工具</el-button
              >
              <p>创建</p>
            </div>

            <el-table
              key="relatedChannelBasic"
              :data="form.basicTableData"
              border
              size="default"
            >
              <el-table-column label="序号" type="index" min-width="54" />
              <el-table-column
                label="中文名称"
                property="description"
                min-width="220"
              >
                <template #default="scope">
                  <ellipsisText :value="scope.row.description"></ellipsisText>
                </template>
              </el-table-column>
              <el-table-column
                label="英文名称"
                property="field"
                min-width="200"
              >
                <template #default="scope">
                  <ellipsisText :value="scope.row.field"></ellipsisText>
                </template>
              </el-table-column>
              <el-table-column
                label="属性类型"
                property="length"
                min-width="164"
              >
                <template #default="scope">
                  <div>{{ FIELD_TYPE_NAME_MAP[scope.row.fieldType] }}</div>
                </template>
              </el-table-column>
              <el-table-column label="所属数据库" min-width="180">
                <template #default="scope">
                  <span v-if="scope.row.fieldType === ATTR_TYPE_MAP.FIXED">
                    -
                  </span>
                  <el-form-item
                    v-else
                    :prop="'basicTableData.' + scope.$index + '.databaseName'"
                    :rules="[
                      {
                        required: true,
                        message: '请输入数据库名称',
                      },
                    ]"
                  >
                    <el-input
                      v-model="scope.row.databaseName"
                      placeholder="请输入数据库名称"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="数据库IP" min-width="180">
                <template #default="scope">
                  <span v-if="scope.row.fieldType === ATTR_TYPE_MAP.FIXED">
                    -
                  </span>
                  <el-form-item
                    v-else
                    :prop="'basicTableData.' + scope.$index + '.databaseIp'"
                    :rules="[
                      {
                        required: true,
                        validator: databaseIpRulesValidate,
                      },
                    ]"
                  >
                    <el-input
                      v-model="scope.row.databaseIp"
                      placeholder="请输入数据库IP"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="所属表" min-width="120">
                <template #default="scope">
                  <ellipsisText :value="scope.row.tableName"></ellipsisText>
                </template>
              </el-table-column>
              <el-table-column label="所属字段" min-width="120">
                <template #default="scope">
                  <ellipsisText :value="scope.row.columnName"></ellipsisText>
                </template>
              </el-table-column>
              <el-table-column label="备注" min-width="162">
                <template #default="scope">
                  <ellipsisText :value="scope.row.remark"></ellipsisText>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="扩展属性" name="extend">
            <el-table
              key="relatedChannelExtend"
              :data="form.extendTableData"
              border
              size="default"
              style="margin-top: 10px"
            >
              <el-table-column label="序号" type="index" min-width="54" />
              <el-table-column
                label="中文名称"
                property="description"
                min-width="190"
              >
                <template #default="scope">
                  <ellipsisText
                    :refresh="activeName === 'extend'"
                    :value="scope.row.description"
                  ></ellipsisText>
                </template>
              </el-table-column>
              <el-table-column
                label="英文名称"
                property="field"
                min-width="170"
              >
                <template #default="scope">
                  <ellipsisText
                    :refresh="activeName === 'extend'"
                    :value="scope.row.field"
                  ></ellipsisText>
                </template>
              </el-table-column>
              <el-table-column
                label="属性类型"
                property="length"
                min-width="134"
              >
                <template #default="scope">
                  <div>{{ FIELD_TYPE_NAME_MAP[scope.row.fieldType] }}</div>
                </template>
              </el-table-column>
              <el-table-column label="数据通道" min-width="180">
                <template #default="scope">
                  <span
                    v-if="scope.row.fieldType === ATTR_TYPE_MAP.RELATE_HANDLE"
                  >
                    -
                  </span>
                  <el-form-item
                    v-else
                    :prop="'extendTableData.' + scope.$index + '.dataChannelId'"
                    :rules="[
                      {
                        required: true,
                        message: '请选择数据通道',
                      },
                    ]"
                  >
                    <el-select
                      v-model="scope.row.dataChannelId"
                      placeholder="请选择数据通道"
                      style="height: 32px; width: 100%"
                      filterable
                      clearable
                      @change="handleDataChannelIdChange(scope.row)"
                    >
                      <el-option
                        v-for="item in channelOptions"
                        :key="item?.dataChannelId"
                        :label="item?.dataChannelName"
                        :value="item.dataChannelId"
                      />
                    </el-select>
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="所属数据库" min-width="180">
                <template #default="scope">
                  <span
                    v-if="scope.row.fieldType === ATTR_TYPE_MAP.RELATE_HANDLE"
                  >
                    -
                  </span>
                  <el-form-item
                    v-else
                    :prop="'extendTableData.' + scope.$index + '.databaseName'"
                    :rules="[
                      {
                        required: true,
                        message: '请输入数据库名称',
                      },
                    ]"
                  >
                    <el-input
                      v-model="scope.row.databaseName"
                      placeholder="请输入数据库名称"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="数据库IP" min-width="180">
                <template #default="scope">
                  <span
                    v-if="scope.row.fieldType === ATTR_TYPE_MAP.RELATE_HANDLE"
                  >
                    -
                  </span>
                  <el-form-item
                    v-else
                    :prop="'extendTableData.' + scope.$index + '.databaseIp'"
                    :rules="[
                      {
                        required: true,
                        validator: databaseIpRulesValidate,
                      },
                    ]"
                  >
                    <el-input
                      v-model="scope.row.databaseIp"
                      placeholder="请输入数据库IP"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
              <el-table-column label="所属表" min-width="90">
                <template #default="scope">
                  <ellipsisText
                    :refresh="activeName === 'extend'"
                    :value="scope.row.tableName"
                  ></ellipsisText>
                </template>
              </el-table-column>
              <el-table-column label="所属字段" min-width="90">
                <template #default="scope">
                  <ellipsisText
                    :refresh="activeName === 'extend'"
                    :value="scope.row.columnName"
                  ></ellipsisText>
                </template>
              </el-table-column>
              <el-table-column label="备注" width="132">
                <template #default="scope">
                  <ellipsisText
                    :refresh="activeName === 'extend'"
                    :value="scope.row.remark"
                  ></ellipsisText>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, onMounted, ref, toRefs, reactive, onUpdated } from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
import { FIELD_TYPE_NAME_MAP } from "@/utils/constant";
import { ApiGetHandleChannelList } from "@/api/objHandle/manager";
import { ATTR_TYPE_MAP } from "@/utils/dataPlatform";
import ellipsisText from "@/components/ellipsisText/index.vue";
import router from "@/router";

const store = useStore();
const { isAppUser } = toRefs(store.getters);

const dataChannel = ref("");
const dataBase = ref("");
const dataBaseIp = ref("");

const formRef = ref();

const form = reactive({
  basicTableData: [],
  extendTableData: [],
});

async function databaseIpRulesValidate(rule: any, val: any, callback: any) {
  if (!val) {
    callback("请输入数据库IP");
    return;
  }
  const reg =
    /(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,4})*(\/[a-zA-Z0-9\&%_\.\/-~-]*)?/;
  const domainReg =
    /((?!-)[A-Za-z0-9-]{1,63}(?<!-)\.)+[A-Za-z]{2,6}(\/.+)?(:\d+)?$/; // 域名校验规则：字符串:端口号

  if (!reg.test(val) && !domainReg.test(val)) {
    return callback("数据库IP格式错误");
  }
  callback();
}

const formValidate = async () => {
  let isError = false;
  await formRef.value.validate((valid: any, data: any) => {
    const errorValidateItem = data ? Object.keys(data) : [];
    const isBasicError = errorValidateItem.filter((item: string) =>
      item.includes("basic")
    )?.length;
    const isExtendError = errorValidateItem.filter((item: string) =>
      item.includes("extend")
    )?.length;

    if (activeName.value === "basic" && !!isExtendError) {
      ElMessage.error("扩展属性必填项未填写");
    }
    if (activeName.value === "extend" && !!isBasicError) {
      ElMessage.error("基础属性必填项未填写");
    }

    if (!valid) {
      isError = true;
      return isError;
    }
  });

  const hasSourceItem = form.basicTableData.filter(
    (item: any) => item.fieldType === ATTR_TYPE_MAP.SOURCE
  );

  if (hasSourceItem.length && !dataChannel.value) {
    ElMessage.error("请选择数据通道");
    return true;
  }

  return isError;
};

const changeDataChannel = (id: string) => {
  channelOptions.value?.forEach((item: any) => {
    if (item.dataChannelId === id) {
      dataBaseIp.value = item.databaseIp;
      dataBase.value = item.databaseName;
    }
  });
  form.basicTableData.forEach((item: any) => {
    if (item.fieldType === ATTR_TYPE_MAP.SOURCE) {
      item.databaseIp = dataBaseIp.value;
      item.databaseName = dataBase.value;
      item.dataChannelId = id;
    }
  });
};

interface IConnection {
  dataChannelName: string;
  dataChannelId: string;
  databaseName: string;
  databaseIp: string;
}
const channelOptions = ref<IConnection[]>();
const activeName = ref("basic");
const props = defineProps({
  handleDetail: {
    type: Object,
  },
});

const handleGoTool = () => {
  // 跳转到工具页
  router.push("/tool");
};

const handleDataChannelIdChange = (data: any) => {
  if (!data.dataChannelId) {
    data.databaseName = "";
    data.databaseIp = "";
    return;
  }

  try {
    const selectedDataChannel = channelOptions.value?.filter((item) => {
      return item.dataChannelId === data.dataChannelId;
    })[0];
    data.databaseName = selectedDataChannel?.databaseName;
    data.databaseIp = selectedDataChannel?.databaseIp;
  } catch (error) {
    data.databaseName = "";
    data.databaseIp = "";
  }
};
onMounted(() => {
  console.log("===第三步====：", props.handleDetail);
  let basicItems = [];
  let extendItems = [];
  try {
    basicItems = JSON.parse(JSON.stringify(props.handleDetail?.items || []));
    extendItems = JSON.parse(
      JSON.stringify(props.handleDetail?.extendItems || [])
    );
  } catch (error) {
    console.log("关联通道-error:", error);
  }

  let hasDataChannelIdItem: any = {};
  if (basicItems.length) {
    const hasDataChannel = basicItems.filter((item: any) => {
      return item.fieldType === ATTR_TYPE_MAP.SOURCE && item.dataChannelId;
    });
    if (hasDataChannel.length) {
      hasDataChannelIdItem = hasDataChannel[0];
      dataChannel.value = `${hasDataChannelIdItem.dataChannelId}`; // 赋值数据通道
    }
  }

  form.basicTableData = basicItems.map((item: any) => {
    // 如果关联了数据通道，需要默认赋值
    item.databaseName = !item.databaseName
      ? hasDataChannelIdItem?.databaseName || ""
      : item.databaseName;
    item.databaseIp = !item.databaseIp
      ? hasDataChannelIdItem?.databaseIp || ""
      : item.databaseIp;
    item.dataChannelId = !item.dataChannelId
      ? hasDataChannelIdItem?.dataChannelId || ""
      : `${item.dataChannelId}`;
    return { ...item };
  });

  form.extendTableData = extendItems.map((item: any) => {
    item.databaseName = !item.databaseName
      ? hasDataChannelIdItem?.databaseName || ""
      : item.databaseName;
    item.databaseIp = !item.databaseIp
      ? hasDataChannelIdItem?.databaseIp || ""
      : item.databaseIp;
    item.dataChannelId = !item.dataChannelId
      ? hasDataChannelIdItem?.dataChannelId || ""
      : `${item.dataChannelId}`;
    return { ...item };
  });
  ApiGetHandleChannelList(props.handleDetail?.id).then((res: any) => {
    channelOptions.value = Array.isArray(res) ? res : [];
  });
});

const getData = () => {
  return {
    basicTableData: form.basicTableData,
    extendTableData: form.extendTableData,
  };
};

defineExpose({
  getData,
  formValidate,
});
</script>
<style scoped lang="scss">
.handle-attr-info-wrap {
  position: relative;
  padding: 0 20px;
  width: 100%;
}
.box {
  .el-form-item {
    margin-bottom: 0;
    &.is-error {
      margin-bottom: 20px;
    }
  }
}
:deep(.el-tabs__nav-wrap::after) {
  content: none;
}
:deep(.el-tabs__active-bar) {
  display: none;
}
:deep(.el-tabs__item) {
  height: auto;
  line-height: normal;
}
:deep(.el-tabs--top .el-tabs__item.is-top:nth-child(2)) {
  border-right: 2px solid #c1c9c7;
}
.box {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  min-height: 300px;
  height: 100%;
  padding: 32px 0;
  .channel-box {
    display: flex;
    align-items: center;

    p {
      margin-right: 12px;
      color: var(--t-3535-f-5-c, #535f5c);

      /* 12/CN-Regular-pingfang */
      font-family: PingFang SC;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px; /* 166.667% */
    }
  }
  .tips-cls {
    margin: 0 0 16px 64px;
    display: flex;
    align-items: center;
    p {
      color: var(--t-27-b-9790, #7b9790);
      text-align: center;
      font-family: PingFang SC;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }
  }
}
</style>
