<!-- 标识注册 1、2、3、4 -->
<template>
  <div class="box">
    <el-dialog
      v-model="updateHandleVisible"
      :title="title"
      fullscreen
      destroy-on-close
      @close="$emit('close-dialog')"
    >
      <div class="mid-box" v-loading="midBoxLoading">
        <div class="steps-box">
          <el-steps :active="step" finish-status="success" simple>
            <el-step title="标识信息">
              <template v-slot:icon>
                <el-icon v-if="step > 0" size="16">
                  <SuccessFilled color="#00A57C" />
                </el-icon>
                <el-icon v-if="step == 0" size="16">
                  <img src="@/assets/images/empower/Loading.png" alt="" />
                </el-icon>
              </template>
            </el-step>
            <el-step title="属性信息">
              <template v-slot:icon>
                <el-icon v-if="step > 1" size="16">
                  <SuccessFilled color="#00A57C" />
                </el-icon>
                <el-icon v-if="step == 1" size="16"
                  ><img src="@/assets/images/empower/Loading.png" alt="" />
                </el-icon>
                <el-icon v-if="step < 1" size="16">
                  <RemoveFilled color="#C7D4D1" />
                </el-icon>
              </template>
            </el-step>
            <el-step title="关联通道">
              <template v-slot:icon>
                <el-icon v-if="step > 2" size="16">
                  <SuccessFilled color="#00A57C" />
                </el-icon>
                <el-icon v-if="step == 2" size="16"
                  ><img src="@/assets/images/empower/Loading.png" alt="" />
                </el-icon>
                <el-icon v-if="step < 2" size="16">
                  <RemoveFilled color="#C7D4D1" />
                </el-icon>
              </template>
            </el-step>
            <el-step title="发布标识">
              <template v-slot:icon>
                <el-icon v-if="step == 3" size="16">
                  <img src="@/assets/images/empower/Loading.png" alt="" />
                </el-icon>
                <el-icon v-if="step < 3" size="16">
                  <RemoveFilled color="#C7D4D1" />
                </el-icon>
              </template>
            </el-step>
          </el-steps>
        </div>
        <div v-if="showMidBox" style="margin-top: 86px; width: 100%">
          <HandleInfo
            v-if="step === 0"
            ref="step1"
            :handle-detail="handleDetail"
            @change="handleInfoChange"
          />
          <AttrInfo
            v-if="step === 1"
            ref="step2"
            :handle-detail="handleDetail"
          ></AttrInfo>
          <RelateChannel
            v-if="step === 2"
            ref="step3"
            :handle-detail="handleDetail"
          ></RelateChannel>
          <HandlePublish
            v-if="step === 3"
            ref="step4"
            :handle-detail="handleDetail"
          ></HandlePublish>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="lastStep">{{
            step > 0 ? "上一步" : "取消"
          }}</el-button>
          <el-button type="primary" @click="nextStep" :loading="confirmLoading">
            {{ step >= 3 ? "发布" : "下一步" }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, onMounted, ref } from "vue";
import { SuccessFilled, RemoveFilled, Loading } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import HandleInfo from "./step1-handle-info.vue";
import AttrInfo from "./step2-attr-info.vue";
import RelateChannel from "./step3-relate-channel.vue";
import HandlePublish from "./step4-handle-publish.vue";
import { ApiObjectHandleStorage } from "@/api/handleRegister/index";
import {
  ApiGetMidHandleDetail,
  ApiObjectHandleRelease,
} from "@/api/objHandle/manager";
import { ATTR_TYPE_MAP, APP_TYPE } from "@/utils/dataPlatform";

const emit = defineEmits(["close-dialog"]);

const showMidBox = ref(false);
const midBoxLoading = ref(false);

const title = ref("");

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
  handleId: {
    type: String,
    default: "",
  },
  isRelated: {
    type: Boolean,
    default: false,
  },
});
const currentHandleId = ref();
const step = ref(0);
const step1 = ref();
const step2 = ref();
const step3 = ref();
const step4 = ref();

const updateHandleVisible = ref(true);
const confirmLoading = ref(false);
const handleDetail = ref();

const lastStep = async () => {
  if (step.value === 1) {
    // 当前是第二步，跳转到第一步
    const { basicTableData, extendTableData } = step2.value;
    handleDetail.value.items = JSON.parse(JSON.stringify(basicTableData));
    handleDetail.value.extendItems = JSON.parse(
      JSON.stringify(extendTableData)
    );
  }
  step.value--;
  if (step.value < 0) {
    updateHandleVisible.value = false;
    step.value = 0;
  }
};
// 第一步数据校验成功之后返回的数据
const handleInfoChange = (form: any) => {
  console.log("===基础信息数据===：", form);
  const handleDetailTemp = handleDetail.value
    ? JSON.parse(JSON.stringify(handleDetail.value))
    : {};
  handleDetail.value = {
    ...JSON.parse(JSON.stringify(form)),
    id: handleDetailTemp?.id,
    items: handleDetailTemp?.items || [],
    extendItems: handleDetailTemp?.extendItems || [],
  };
};
const step1Method = async () => {
  const res = await step1.value.submitForm();
  return res ? 1 : step.value;
};

// 发布
const handleRelease = () => {
  ApiObjectHandleRelease(currentHandleId.value).then(() => {
    ElMessage.success("发布成功");
    emit("close-dialog");
  });
};

// 重新请求数据
const getNewData = async (storageParams: any) => {
  console.log(storageParams);
  const res = await ApiObjectHandleStorage(storageParams);
  currentHandleId.value = res;
  if (currentHandleId.value) {
    await ApiGetMidHandleDetail({ id: currentHandleId.value }).then((res) => {
      handleDetail.value = res;
      console.log("====获取最新标识详情====：", handleDetail.value);
    });
  }
};

const nextStep = async () => {
  switch (step.value) {
    case 0:
      step.value = await step1Method();
      break;
    case 1:
      const { basicTableData, extendTableData } = step2.value;
      const sourceBasicItem = basicTableData.filter(
        (item: any) => item.fieldType === ATTR_TYPE_MAP.SOURCE
      );
      if (!sourceBasicItem.length) {
        ElMessage.warning("至少新增一条标识解析数据源属性");
        return;
      }
      const storageParams = {
        ...handleDetail.value,
        items: basicTableData || [],
        extendItems: extendTableData || [],
      };
      await getNewData(storageParams);
      step.value = 2;
      break;
    case 2:
      const isError = await step3.value.formValidate();
      console.log("isError: ", isError);
      if (isError) {
        return;
      }
      const step3ValueData = step3.value.getData();
      const step3storageParams = JSON.parse(JSON.stringify(handleDetail.value));
      step3storageParams.items = step3ValueData.basicTableData;
      step3storageParams.extendItems = step3ValueData.extendTableData;
      await getNewData(step3storageParams);
      step.value = 3;
      break;
    case 3:
      const isTestSuccess = step4.value.isTestSuccess();
      if (!isTestSuccess) {
        ElMessage.error("测试失败，请重新进行标识查询");
        return;
      }
      handleRelease();
      break;
    default:
      step.value = 0;
      break;
  }
  if (step.value > 3) {
    updateHandleVisible.value = false;
    step.value = 3;
  }
};

onMounted(async () => {
  if (props.handleId) {
    currentHandleId.value = props.handleId;
    midBoxLoading.value = true;
    await ApiGetMidHandleDetail({ id: currentHandleId.value })
      .then((res) => {
        handleDetail.value = res;
      })
      .finally(() => {
        midBoxLoading.value = false;
      });
    if (props.isRelated) {
      step.value = 2; // 如果是关联通道，默认跳转到第三步
    }
  }
  title.value = `${props.handleId ? "编辑" : "新增"}标识`;
  showMidBox.value = true;
});
</script>
<style scoped lang="scss">
.box {
  :deep(.el-dialog) {
    overflow: hidden;
    .el-dialog__body {
      padding: 0;
      height: calc(100vh - 46px - 64px);
      overflow: auto;
    }
  }
  .mid-box {
    display: flex;
    position: relative;
    flex-direction: column;
    align-items: center;
    .steps-box {
      position: absolute;
      left: 0px;
      right: 0px;
      height: 86px;
      background-color: #f5f6f6;
      display: flex;
      align-items: center;
      justify-content: center;
      :deep(.el-steps) {
        position: relative;
        width: 60%;
        // background-color: #fff;
        .el-step__main {
          .el-step__title {
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 500;
            line-height: 22px; /* 157.143% */
            color: #535f5c;
          }

          .el-step__arrow::before {
            transform: rotate(0deg) translateY(0px);
            transform-origin: 0 0;
            content: "";
            display: inline-block;
            position: absolute;
            height: 1px;
            width: 66%;
            background: #c1c9c7;
          }
          .el-step__arrow::after {
            transform: rotate(0deg) translateY(0px);
            transform-origin: 0 0;
            content: "";
            display: inline-block;
            position: absolute;
            height: 0px;
            width: 0px;
            background: var(--el-text-color-placeholder);
          }
        }
      }
    }
  }
}
</style>
