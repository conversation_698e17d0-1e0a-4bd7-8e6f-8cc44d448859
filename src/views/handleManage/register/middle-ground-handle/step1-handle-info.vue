<!-- 第一步标识信息 -->
<template>
  <div class="handle-info-box">
    <el-form
      v-if="appType === APP_TYPE.DMM"
      :label-position="labelPosition"
      label-width="100px"
      :model="form"
      ref="ruleFormRef"
      :rules="rules"
      style="width: 850px; margin-top: 24px; text-align: center"
    >
      <el-form-item label="标识类型" prop="handleType">
        <el-select
          v-model="form.handleType"
          placeholder="请选择"
          style="width: 746px"
          :disabled="isEdit"
          filterable
          clearable
          @change="handleTypeChange"
        >
          <el-option
            v-for="item in HANDLE_TYPE_LIST"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="对象标识名称"
        prop="name"
        v-if="form.handleType === HANDLE_TYPE_MAP.NORMAL"
      >
        <el-input
          :disabled="isEdit"
          v-model.trim="form.name"
          placeholder="请输入"
          style="width: 746px"
          clearable
        >
        </el-input>
      </el-form-item>
      <el-form-item
        class="form-item-handle"
        required
        label="对象标识"
        :disabled="isEdit"
        v-if="form.handleType === HANDLE_TYPE_MAP.NORMAL"
      >
        <div class="handle-tid-wrap">
          <el-form-item prop="prefix" style="width: 122px">
            <el-select
              v-model="form.prefix"
              disabled="true"
              placeholder="请选择"
              filterable
              clearable
            >
              <el-option
                v-for="item in entPrefixList"
                :key="item"
                :value="item"
                :label="item"
              ></el-option>
            </el-select>
          </el-form-item>
          <div class="handle-tid-line" style="width: 14px">/</div>
          <el-form-item class="handle-tid-suffix" prop="suffix">
            <el-input
              v-model.trim="form.suffix"
              placeholder="请输入"
              clearable
              :disabled="isEdit"
              style="width: 100%"
            >
            </el-input>
          </el-form-item>
        </div>
      </el-form-item>
      <el-form-item
        label="主数据名称"
        prop="masterDataType"
        v-if="form.handleType === HANDLE_TYPE_MAP.MASTER"
      >
        <el-select
          v-model="form.masterDataType"
          placeholder="请选择"
          style="width: 746px"
          :disabled="isEdit"
          filterable
          clearable
        >
          <el-option
            v-for="item in masterDataList"
            :key="item.key"
            :value="item.key"
            :label="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="实体类型" prop="entityType">
        <el-select
          v-model="form.entityType"
          placeholder="请选择"
          style="width: 746px"
          filterable
          clearable
        >
          <el-option
            v-for="item in data.entityTypeList"
            :key="item.value"
            :value="item.value"
            :label="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="实体对象类型" prop="entityObjectTypeId">
        <el-cascader
          v-model="form.entityObjectTypeId"
          :options="data.entiyObjTree"
          @change="handleChange"
          style="width: 746px"
          :props="cascaderProps"
          :disabled="isEdit"
        />
      </el-form-item>
      <el-form-item label="实体对象" prop="entityObjectId">
        <el-select
          v-model="form.entityObjectId"
          placeholder="请选择"
          style="width: 746px"
          :disabled="isEdit"
          filterable
          clearable
        >
          <el-option
            v-for="item in data.entiyObjectNameList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <NormalHandleInfo
      v-else
      ref="normalHandleInfoRef"
      :is-edit="isEdit"
      :form-data="handleDetail"
    ></NormalHandleInfo>
  </div>
</template>

<script lang="ts" setup>
import { FormInstance, FormProps, FormRules } from "element-plus";
import {
  defineProps,
  reactive,
  onMounted,
  ref,
  defineExpose,
  toRefs,
} from "vue";
import { useStore } from "vuex";

import {
  HANDLE_TYPE_LIST,
  HANDLE_TYPE_MAP,
  APP_TYPE,
} from "@/utils/dataPlatform";
import {
  MASTER_DATA_ENUM,
  MASTER_DATA_MAP,
} from "@/views/applicationManage/constant";
import {
  ApiGetHandlePrefixDropDownList,
  ApiGetEntityTypeTree,
  ApiGetEntityObjList,
} from "@/api/objHandle/manager";
import { ApiMasterDataScope } from "@/api/handleRegister/index";
import NormalHandleInfo from "./components/normal-handle-info.vue";

const store = useStore();
const { appType } = toRefs(store.getters);

const normalHandleInfoRef = ref(); // 非中台应用-基本信息

const emit = defineEmits(["change"]);
const labelPosition = ref<FormProps["labelPosition"]>("left");
const props = defineProps({
  handleDetail: {
    type: Object,
    default: null,
  },
});
const ruleFormRef = ref<FormInstance>();
const form = ref<any>({
  prefix: "",
  suffix: "",
  handle: "",
  name: "",
  entityType: null,
  handleType: null,
  masterDataType: "",
  dataType: 1, // 中台、非中台
  entityObjectId: null,
  entityObjectTypeId: null,
});

const isEdit = ref(false);

const masterDataList = ref<any[]>([]);

const validatePrefix = (rule: any, value: any, callback: any) => {
  if (value === "") {
    return callback(new Error("标识前缀不能为空"));
  }
  const regex = /^\d+\.\d+\.\d+$/;
  if (!regex.test(value)) {
    return callback(new Error("标识前缀格式不正确"));
  }

  callback();
};

const validateSuffix = (rule: any, value: any, callback: any) => {
  if (value === "") {
    return callback(new Error("标识后缀不能为空"));
  }
  const maxLength = 255 - (currentPrefix.value.length + 1);
  if (value.length > maxLength) {
    return callback(new Error("最大长度为255字符"));
  }

  callback();
};

const rules = reactive<FormRules>({
  entityType: [
    { required: true, message: "实体类型不能为空", trigger: "blur" },
  ],
  handleType: [
    {
      required: true,
      message: "标识类型不能为空",
      trigger: "blur",
    },
  ],
  name: [
    {
      required: true,
      message: "标识名称不能为空",
      trigger: "blur",
    },
    {
      max: 255,
      message: "最大长度为255字符",
    },
  ],
  prefix: [
    {
      required: true,
      validator: validatePrefix,
      trigger: "blur",
    },
  ],
  suffix: [
    {
      required: true,
      validator: validateSuffix,
      trigger: "blur",
    },
  ],
  masterDataType: [
    {
      required: true,
      message: "主数据名称不能为空",
      trigger: "blur",
    },
  ],
  entityObjectId: [
    {
      required: true,
      message: "实体对象不能为空",
      trigger: "blur",
    },
  ],
  entityObjectTypeId: [
    {
      required: true,
      message: "实体对象类型不能为空",
      trigger: "blur",
    },
  ],
});

const entPrefixList = ref<any[]>([]);

const currentPrefix = ref("");

const data = reactive<any>({
  masterDataNameList: [],
  handleTypeList: [
    { value: 1, name: "主数据标识" },
    { value: 2, name: "非主数据标识" },
  ],
  entityTypeList: [
    { value: 1, name: "业务实体" },
    { value: 2, name: "资源实体" },
  ],
  entiyObjTree: [],
  entiyObjectNameList: [],
  tableData: [],
  extendTableData: [],
  basicDelList: [],
  extendDelList: [],
});
const cascaderProps = {
  multiple: false,
  checkStrictly: false,
  emitPath: true,
  value: "id",
  label: "categoryName",
  children: "children",
};

const handleTypeChange = (val: any) => {
  if (val === HANDLE_TYPE_MAP.MASTER) {
    form.value.name = "";
    form.value.suffix = "";
  }
  if (val === HANDLE_TYPE_MAP.NORMAL) {
    form.value.masterDataType = "";
    form.value.prefix = currentPrefix.value;
  }
};

const handleChange = async (value: any) => {
  const keys = Object.keys(value);
  const selected = value[keys[keys.length - 1]];
  form.value.entityObjectTypeId = selected;
  const res = await ApiGetEntityObjList({ categoryId: selected });
  data.entiyObjectNameList = res;
};

const submitForm = async () => {
  console.log("第一步信息====：", appType.value);
  if (appType.value === APP_TYPE.NORMAL) {
    return normalInfoSubmit();
  }
  if (!ruleFormRef.value) return;
  let ok = false;
  await ruleFormRef.value.validate((valid: any) => {
    if (valid) {
      ok = true;
      const temp = { ...form.value };
      // 主数据
      if (temp.handleType === HANDLE_TYPE_MAP.MASTER) {
        temp.name = (MASTER_DATA_MAP as any)[`${temp.masterDataType}`];
        temp.handle = "";
        temp.suffix = undefined;
        temp.prefix = undefined;
      }
      if (temp.handleType === HANDLE_TYPE_MAP.NORMAL) {
        temp.handle = `${temp.prefix}/${temp.suffix}`;
        temp.masterDataType = "";
      }
      console.log("====基本信息===：", temp);
      emit("change", temp);
    }
  });
  return ok;
};

const normalInfoSubmit = async () => {
  console.log("===normalInfoSubmit====:");
  const validateError = await normalHandleInfoRef.value.formValidate();
  if (validateError) {
    return false;
  }
  const data = normalHandleInfoRef.value.getData();
  emit("change", data);
  return true;
};

function getEntPrefixList() {
  return ApiGetHandlePrefixDropDownList().then((response: any) => {
    entPrefixList.value = Array.isArray(response) ? response : [];
    if (entPrefixList.value.length > 0) {
      currentPrefix.value = entPrefixList.value[0];
      if (form.value.handleType === HANDLE_TYPE_MAP.NORMAL) {
        form.value.prefix = currentPrefix.value;
      }
    }
  });
}

// 获取主数据范围
const getMasterDataScopes = () => {
  ApiMasterDataScope().then((res) => {
    if (Array.isArray(res)) {
      masterDataList.value = MASTER_DATA_ENUM.filter((item) =>
        res.includes(item.key)
      );
    }
  });
};

onMounted(async () => {
  isEdit.value = props.handleDetail?.id;
  if (appType.value === APP_TYPE.DMM) {
    if (props.handleDetail) {
      form.value = props.handleDetail;
      if (form.value.handle) {
        const [prefix, suffix] = form.value.handle.split("/");
        form.value.prefix = prefix || currentPrefix.value;
        form.value.suffix = suffix || "";
      }
    }
    getEntPrefixList();
    getMasterDataScopes();

    const res = (await ApiGetEntityTypeTree()) as any;
    data.entiyObjTree = res?.children ?? [];
    if (form.value.entityObjectTypeId) {
      const ObjectRes = await ApiGetEntityObjList({
        categoryId: form.value.entityObjectTypeId,
      });
      data.entiyObjectNameList = ObjectRes;
    }
  }
});

defineExpose({
  submitForm,
});
</script>
<style scoped lang="scss">
.handle-info-box {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  .form-item-handle {
    :deep(.el-input-group__prepend) {
      padding-right: 0;
    }
    .form-item-handle-line {
      border-left: 1px solid #dcdfe6;
      padding: 0 10px;
      margin-left: 20px;
    }
  }
  .handle-tid-wrap {
    display: flex;
    align-items: center;
    justify-content: start;
    width: 100%;
    .handle-tid-line {
      margin: 0 0px;
    }
    .handle-tid-suffix {
      flex: 1;
      min-width: 0;
    }
  }
}
</style>
