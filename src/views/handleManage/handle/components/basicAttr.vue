<template>
  <div>
    <div class="addbtn">
      <el-button
        type="primary"
        :disabled="form.handle === '' || form.entPrefix === ''"
        @click="handleAdd"
        >新增基础属性</el-button
      >
    </div>
    <el-table :data="data.tableData" size="small" border>
      <el-table-column label="索引" property="fieldIndex">
        <template #default="scope">
          <span>{{
            scope.row.fieldIndex < 0 ? "-" : scope.row.fieldIndex
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="中文名称"
        property="description"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span v-copy="scope.row.description">{{
            scope.row.description || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="英文名称" property="field">
        <template #default="scope">
          <span v-copy="scope.row.field">{{ scope.row.field || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="属性类型" property="length">
        <template #default="scope">
          <div>{{ FIELD_TYPE_NAME_MAP[scope.row.fieldType] }}</div>
        </template>
      </el-table-column>
      <el-table-column label="属性值" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ getReferenceDis(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="140">
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            text
            @click="handleEdit(scope.$index, scope.row)"
            >{{ "编辑" }}</el-button
          >
          <el-popconfirm
            confirm-button-text="删除"
            cancel-button-text="取消"
            :icon="InfoFilled"
            title="是否删除该基础属性?"
            @confirm="handleDelete(scope.$index)"
          >
            <template #reference>
              <el-button size="small" type="primary" text>删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      v-model="data.dialogVisible"
      :title="dialogTitle"
      append-to-body
      @close="handleDialogCancel"
      width="70%"
      align-center
    >
      <relate-handle-form
        v-if="data.dialogVisible"
        ref="relateHandleFormRef"
        :item="data"
        :dataService="props.choiseDataServiceList"
      ></relate-handle-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            v-if="data.dialogType !== DIALOG_TYPE_MAP.editor"
            type="primary"
            @click="handleDialogConfirm('addNext')"
          >
            新增下一个
          </el-button>
          <el-button @click="data.dialogVisible = false">{{
            "取消"
          }}</el-button>
          <el-button type="primary" @click="handleDialogConfirm">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
import { reactive, computed, defineProps, ref, PropType, watch } from "vue";
import { InfoFilled } from "@element-plus/icons-vue";
import relateHandleForm from "./relateHandleForm.vue";
import { deepClone } from "@/utils/auth";
import {
  FIELD_TYPE_MAP,
  DIALOG_TYPE_MAP,
  DIALOG_TYPE_NAME_MAP,
  FIELD_TYPE_NAME_MAP,
} from "@/utils/constant";

const relateHandleFormRef = ref();

interface DataService {
  id: number;
  dataServiceName: string;
  serviceAddress: string;
  serviceToken: string;
}

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  form: {
    type: Object,
    default: () => ({}),
  },
  choiseDataServiceList: {
    type: Array as PropType<DataService[]>,
    default: () => [],
  },
});

const data = reactive<any>({
  tableData: [{}],
  extendTableData: [],
  basicDelList: [{}],
  referenceIndex: 0,
  dialogVisible: false,
  dialogType: DIALOG_TYPE_MAP.add,
  form: {},
});

watch(
  props,
  (val) => {
    data.tableData = val.data.tableData ? val.data.tableData : [{}];
    data.extendTableData = val.data.extendTableData
      ? val.data.extendTableData
      : [{}];
  },
  {
    immediate: true,
    deep: true,
  }
);

// 弹窗标题命名
const dialogTitle = computed(() => {
  // 当新增下一个模式，标题显示新增
  return data.dialogType === DIALOG_TYPE_MAP.addNext
    ? DIALOG_TYPE_NAME_MAP[DIALOG_TYPE_MAP.add]
    : DIALOG_TYPE_NAME_MAP[data.dialogType] + "基础属性";
});

// 增加基础属性
function handleAdd() {
  data.form = deepClone(props.form);
  data.dialogType = DIALOG_TYPE_MAP.add;
  data.dialogVisible = true;
}

// 编辑基础属性
function handleEdit(index: any, reference: any) {
  data.dialogType = DIALOG_TYPE_MAP.editor;
  data.referenceIndex = index;
  data.form = deepClone(props.form);
  data.dialogVisible = true;
}

// 删除基础属性
function handleDelete(index: number) {
  // 记录删除属性
  if (data.tableData[index].fieldIndex > 0) {
    props.data.basicDelList.push(data.tableData[index]);
  }
  data.tableData.splice(index, 1);
}

// 取消新增/编辑关联属性
function handleDialogCancel() {
  data.dialogVisible = false;
}

// 保存属性/新增下一个
async function handleDialogConfirm(addNext: string) {
  const validateError = await relateHandleFormRef.value.validateData();
  if (validateError) return;
  const formData = relateHandleFormRef.value.getData();
  data.dialogVisible = false;
  if (addNext === "addNext") {
    // 新增下一个
    data.dialogType = DIALOG_TYPE_MAP.addNext;
    data.dialogVisible = true;
  }
  // 保存标识-属性内容至references字段，用于列表显示和保存
  if (formData.fieldType === FIELD_TYPE_MAP.handleWithAttr) {
    formData.references = formData.referencesAttr;
  }
  // 修改模式，更新列表对应行
  if (data.dialogType === DIALOG_TYPE_MAP.editor) {
    data.tableData[data.referenceIndex] = deepClone(formData);
    return;
  }
  // 设定新增标识索引为负数时间戳，防止重复
  formData.fieldIndex = -Date.parse(new Date().toString());
  // 新增、新增下一个模式，列表插入一行;
  data.tableData[data.tableData.length] = deepClone(formData);
}

function getReferenceDis(data: any) {
  if (data.fieldType === FIELD_TYPE_MAP.fixed) {
    return data.fieldValue;
  }
  if (data.fieldType === FIELD_TYPE_MAP.source) {
    return data.dataServiceName + "-" + data.dataSourceName;
  }
  if (data.references && data.references.length) {
    return data.references
      .map((reference: any) => reference.referenceHandle)
      .join(",");
  }
  return "-";
}
</script>
<style>
.addbtn {
  text-align: right;
  margin-bottom: 10px;
}
</style>
