<template>
  <div>
    <el-table :data="data.extendTableData" size="small" border>
      <!-- <el-table-column label="序号" type="index" width="55" /> -->
      <el-table-column label="索引" prop="fieldIndex" />
      <el-table-column
        label="中文名称"
        prop="description"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span v-copy="scope.row.description">{{
            scope.row.description || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="英文名称" prop="field" show-overflow-tooltip>
        <template #default="scope">
          <span v-copy="scope.row.field">{{ scope.row.field || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="属性类型" prop="fieldType" show-overflow-tooltip>
        <template #default="scope">
          <div>{{ FIELD_TYPE_NAME_MAP[scope.row.fieldType] }}</div>
        </template>
      </el-table-column>
      <el-table-column label="属性值" :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{
            scope.row.fieldType === 1
              ? scope.row.fieldValue
              : getReferenceDis(scope.row)
          }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="备注" prop="remark" /> -->
      <!-- <el-table-column label="维护人" prop="createdBy"> </el-table-column> -->
      <el-table-column label="操作" width="140">
        <template #default="scope">
          <el-popconfirm
            confirm-button-text="删除"
            cancel-button-text="取消"
            :icon="InfoFilled"
            title="是否删除该扩展属性?"
            @confirm="handleDelete(scope.$index)"
          >
            <template #reference>
              <el-button size="small" type="primary" text>删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script setup lang="ts">
import { InfoFilled } from "@element-plus/icons-vue";
import { FIELD_TYPE_NAME_MAP } from "@/utils/constant";

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});

function getReferenceDis(data: any) {
  if (data.references && data.references.length) {
    return data.references
      .map((reference: any) => reference.referenceHandle)
      .join(",");
  }
  return "-";
}

// 删除基础属性
function handleDelete(index: number) {
  // 记录删除属性
  props.data.extendDelList.push(props.data.extendTableData[index]);
  props.data.extendTableData.splice(index, 1);
}
</script>
<style></style>
