<template>
  <SearchLayout>
    <template #left
      ><el-button type="primary" @click="backToList">返回</el-button></template
    >
    <template #right>
      <el-form :model="searchForm" :inline="true" @submit.prevent>
        <el-form-item style="width: 240px; max-width: 240px">
          <el-select
            v-model="searchForm.uploadStatus"
            placeholder="请选择"
            clearable
            @clear="handleSearch"
          >
            <template #prefix>状态：</template>
            <el-option
              v-for="item in STATUS_FINISH"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="width: 60px; max-width: 60px">
          <el-button
            type="primary"
            :loading="data.searchLoading"
            @click="handleSearch"
            >搜索</el-button
          >
        </el-form-item>
      </el-form></template
    >
  </SearchLayout>

  <el-table
    :data="data.tableData"
    size="small"
    v-loading="data.tableLoading"
    border
  >
    <el-table-column label="序号" type="index" width="55" />
    <el-table-column property="uploadHandleNum" label="上传标识数量">
      <template #default="scope">
        <span>
          {{ scope.row.uploadHandleNum + "" ? scope.row.uploadHandleNum : "-" }}
        </span>
      </template>
    </el-table-column>
    <el-table-column property="uploadFailNum" label="上传失败标识数量">
      <template #default="scope">
        <span>
          {{ scope.row.uploadFailNum !== null ? scope.row.uploadFailNum : "-" }}
        </span>
      </template>
    </el-table-column>
    <el-table-column property="uploadStatus" label="状态">
      <template #default="scope">
        <span>
          {{
            scope.row.uploadStatus + ""
              ? STATUS_FINISH_NAME_MAP[scope.row.uploadStatus]
              : "-"
          }}
        </span>
      </template>
    </el-table-column>
    <el-table-column property="createdTime" label="操作时间">
      <template #default="scope">
        <span>
          {{ scope.row.createdTime ? scope.row.createdTime : "-" }}
        </span>
      </template>
    </el-table-column>
    <el-table-column property="updatedBy" label="操作人">
      <template #default="scope">
        <span>
          {{ scope.row.updatedBy ? scope.row.updatedBy : "-" }}
        </span>
      </template>
    </el-table-column>

    <el-table-column label="操作">
      <template #default="scope">
        <el-button
          type="primary"
          text
          @click="handleView(scope.row)"
          :disabled="
            scope.row.uploadFailNum === 0 || scope.row.uploadStatus === 0
          "
          >导出报告</el-button
        >
      </template>
    </el-table-column>
  </el-table>
  <el-pagination
    background
    v-model:currentPage="data.page"
    v-model:page-size="data.size"
    :page-sizes="[10, 20, 30, 40]"
    small
    layout="total, sizes, prev, pager, next, jumper"
    :total="data.totalCount"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  />
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { ElMessage } from "element-plus";
import { saveAs } from "file-saver";
import { IHandleBatchRecord } from "@/types/handleMaintain/record";
import { STATUS_FINISH, STATUS_FINISH_NAME_MAP } from "@/utils/constant";
import { ApiRecordQueryList, ApiErrorDownload } from "@/api/objHandle/manager";
import { getInfo } from "@/api/login";
import SearchLayout from "@/components/searchLayout/index.vue";

// 确定按钮点击效果
const downlowdLoading = ref(false);
const emit = defineEmits(["backToList"]);
const searchForm = ref({
  uploadStatus: null,
});
const data = reactive<{
  tableData: IHandleBatchRecord[];
  page: number;
  size: number;
  totalCount: number;
  tableLoading: boolean;
  searchLoading: boolean;
}>({
  tableData: [],
  page: 1,
  size: 10,
  totalCount: 1,
  tableLoading: false,
  searchLoading: false,
});
function handleSearch() {
  data.page = 1;
  getTableData();
}
function getTableData() {
  data.tableLoading = true;
  data.tableData = [];
  const params = {
    uploadStatus: searchForm.value.uploadStatus,
    page: data.page - 1,
    size: data.size,
  };
  ApiRecordQueryList(params)
    .then((res: any) => {
      data.tableData = res?.content || [];
      data.page = res?.pageNumber || 1;
      data.totalCount = res?.totalCount || 0;
    })
    .finally(() => {
      data.tableLoading = false;
      data.searchLoading = false;
    });
}
function handleView(row: IHandleBatchRecord) {
  downlowdLoading.value = true;
  const params = {
    fileAddress: row.fileAddress,
  };
  getInfo();
  ApiErrorDownload(params)
    .then(
      (res: any) => {
        const blob = new Blob([res]);
        saveAs(blob, row.fileName);
      },
      (error) => {
        console.log(error);
        ElMessage.error("下载异常，请稍后再试");
      }
    )
    .finally(() => {
      downlowdLoading.value = false;
    });
}

function handleSizeChange(num: number) {
  data.size = num;
  data.page = 1;
  getTableData();
}

function handleCurrentChange(num: number) {
  data.page = num;
  getTableData();
}

function backToList() {
  emit("backToList");
}
onMounted(() => {
  getTableData();
});
</script>
<style lang="scss" scoped></style>
