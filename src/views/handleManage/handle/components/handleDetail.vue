<template>
  <div class="handle-detail-wrap">
    <div class="section-title">
      <div class="section-title-line"></div>
      <span class="section-title-text"> 基本信息 </span>
    </div>
    <el-form
      :model="form"
      ref="formRef"
      label-width="120px"
      :disabled="disabled"
      :rules="rules"
      label-position="left"
    >
      <el-form-item
        class="form-item-handle"
        required
        label="标识"
        prop="entPrefix"
      >
        <div class="handle-tid-wrap">
          <el-select
            v-model="form.entPrefix"
            disabled="true"
            placeholder="请选择"
          >
            <el-option
              v-for="item in data.entPrefixList"
              :key="item"
              :value="item"
              :label="item"
            ></el-option>
          </el-select>
          <div class="handle-tid-line">/</div>
          <el-form-item prop="handle" style="width: 100%">
            <el-input
              v-model.trim="form.handle"
              :disabled="isEdit"
              placeholder="请输入"
              clearable
            >
            </el-input>
          </el-form-item>
        </div>
      </el-form-item>
      <el-form-item label="标识名称" prop="name">
        <el-input
          :disabled="isEdit"
          v-model.trim="form.name"
          placeholder="请输入"
          clearable
        />
      </el-form-item>
      <el-row>
        <el-form-item label="实体类型" prop="entityType">
          <el-select v-model="form.entityType" placeholder="请选择">
            <el-option
              v-for="item in data.entityTypeList"
              :key="item.value"
              :value="item.value"
              :label="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="所属应用"
          prop="appId"
          label-width="80px"
          style="margin-left: 30px"
        >
          <el-select v-model="form.appId" placeholder="请选择" disabled="true">
            <el-option
              v-for="item in props.appList"
              :key="item.id"
              :value="item.id"
              :label="item.appName"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-row>
    </el-form>

    <div class="components-handle-detail-line"></div>
    <!-- 属性列表组件（基础属性、扩展属性） -->
    <div class="section-title">
      <div class="section-title-line"></div>
      <span class="section-title-text"> 属性信息 </span>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="基础属性" name="basic">
        <basicAttr
          :data="data"
          :choiseDataServiceList="choiseDataServiceList"
          :form="form"
        />
      </el-tab-pane>
      <el-tab-pane label="扩展属性" name="extend">
        <extendAttr :data="data" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script setup lang="ts">
import {
  reactive,
  computed,
  defineProps,
  ref,
  watch,
  defineExpose,
  PropType,
  onMounted,
} from "vue";
import { ElMessage } from "element-plus";
import { deepClone } from "@/utils/auth";
import {
  ApiGetHandlePrefixDropDownList,
  ApiGetHandleDetailNew,
  getServiceListApi,
} from "@/api/objHandle/manager";
import { OBJECT_ENTITY_TYPE_NAME } from "@/utils/constant";
import basicAttr from "./basicAttr.vue";
import extendAttr from "./extendAttr.vue";
import dataServiceEdit from "./dataServiceEdit.vue";
import { DataServiceList } from "@/types/handle";

const activeName = ref("basic");
const version = ref("");

const formRef = ref();

interface AppList {
  id: number;
  appName: string;
}
const props = defineProps({
  handleData: {
    type: Object,
    default: () => null,
  },
  disabled: {
    type: Boolean,
    default: true,
  },
  appList: {
    type: Array as PropType<AppList[]>,
    default: () => [],
  },
});

const isEdit = computed(() => !!props.handleData);

const handleValidate = (rule: any, value: any, callback: any) => {
  if (!form.handle) {
    return callback("请填写标识TID");
  }
  if (form.handle.length > 242) {
    return callback("标识长度最大为255字符");
  }
  callback();
};

const form = reactive<any>({
  entPrefix: "", // 企业前缀
  handle: "",
  name: "",
  type: 1, // 1:业务资源实体 2:本地资源实体
  entityType: 1,
  appId: "",
  dataService: [],
});

const rules = reactive({
  entPrefix: [{ required: true, message: "请选择前缀", trigger: "change" }],
  handle: [{ validator: handleValidate, trigger: "blur" }],
  name: [
    { required: true, message: "请输入标识名称", trigger: "blur" },
    {
      max: 30,
      message: "最大长度为30字符",
      trigger: "blur",
    },
  ],
  entityType: [{ required: true, message: "请选择实体类型", trigger: "blur" }],
  appId: [{ required: true, message: "请选择所属应用", trigger: "blur" }],
});

const data = reactive<any>({
  entPrefixList: [],
  entityTypeList: [
    { value: 1, name: "业务实体" },
    { value: 2, name: "资源实体" },
  ],
  tableData: [],
  extendTableData: [],
  basicDelList: [],
  extendDelList: [],
});

const choiseDataServiceList = ref<DataServiceList[]>([]);
const dataServiceList = ref<DataServiceList[]>([]);
const dataService = reactive<any>({
  dataService: [],
  choiseDataServiceList,
});

// 初始化赋值应用下拉框
function setAppId() {
  if (props.appList.length > 0) {
    form.appId = props.appList[0].id;
  }
}

// 初始化赋值前缀下拉框
function getEntPrefixList() {
  ApiGetHandlePrefixDropDownList().then((response: any) => {
    data.entPrefixList = Array.isArray(response) ? response : [];
    if (data.entPrefixList.length > 0) {
      form.entPrefix = data.entPrefixList[0];
    }
  });
}
onMounted(() => {
  getEntPrefixList();
  getServiceList();
  setAppId();
});

// 获取标识TID（前缀+/+后缀）
function getHandleData(str: string, entPrefix: any) {
  return str.substring(entPrefix.length + 1);
}

function getHandleDetail() {
  const requestParams = {
    handle: props && props.handleData && props.handleData.handle,
    type: 1, // 前端写死1，获取所有详情
  };
  ApiGetHandleDetailNew(requestParams).then(async (response: any) => {
    const constFields = ["entPrefix", "name", "type", "entityType", "appId"];
    constFields.forEach((field) => {
      form[field] = response[field];
    });
    version.value = response.version;
    form.handle = getHandleData(response.handle, response.entPrefix);
    data.tableData = response.items;
    data.extendTableData = response.extendItems;
    // dataService.dataService = response.services ? response.services : []; // 服务数据
    // await getServiceList();
    // // dataServiceList 所有的数据服务
    // choiseDataServiceList.value = [];
    // choiseDataServiceList.value = dataServiceList.value.filter((item) =>
    //   dataService.dataService.includes(item.id)
    // );
  });
}

function initData() {
  const constFields = ["entPrefix", "name", "handle"];
  constFields.forEach((field) => {
    form[field] = "";
  });
  form.type = 1;
  data.tableData = [];
  dataServiceList.value = [];
}

// 获取数据服务列表
async function getServiceList() {
  return getServiceListApi().then((res: any) => {
    if (res.code) {
      dataServiceList.value = [];
    } else {
      dataServiceList.value = res;
    }
  });
}

watch(
  props,
  (val) => {
    if (val.handleData) {
      getHandleDetail();
      return;
    }
    initData();
  },
  {
    immediate: true,
    deep: true,
  }
);

function getAddData() {
  const addList = deepClone(data.tableData);
  addList.forEach(
    (item: { references: any; fieldIndex: number | undefined }) => {
      item.fieldIndex = -1;
      if (
        item.references?.length > 0 &&
        item.references[0].paramProp?.fieldIndex < 0
      ) {
        item.references[0].paramProp.fieldIndex = -1;
      }
    }
  );
  return {
    id: props.handleData?.id,
    entPrefix: form.entPrefix, // 企业前缀
    handle: `${form.entPrefix}/${form.handle}`,
    name: form.name,
    type: form.type, // 1:本地资源实体 2:业务资源实体
    items: addList,
    entityType: form.entityType,
    appId: form.appId,
    // services: dataService.dataService,
  };
}
function getData() {
  const addListFilter = data.tableData.filter(
    (item: { fieldIndex: any }) => item.fieldIndex < 0
  );
  const addList = deepClone(addListFilter);
  addList.forEach(
    (item: { references: any; fieldIndex: number | undefined }) => {
      item.fieldIndex = -1;
      if (
        item.references?.length > 0 &&
        item.references[0].paramProp?.fieldIndex < 0
      ) {
        item.references[0].paramProp.fieldIndex = -1;
      }
    }
  );
  const editListFilter = data.tableData.filter(
    (item: { fieldIndex: any }) => item.fieldIndex > 0
  );
  const editList = deepClone(editListFilter);
  editList.forEach(
    (item: { references: any; fieldIndex: number | undefined }) => {
      if (
        item.references?.length > 0 &&
        item.references[0].paramProp?.fieldIndex < 0
      ) {
        item.references[0].paramProp.fieldIndex = -1;
      }
    }
  );
  return {
    id: props.handleData?.id,
    entPrefix: form.entPrefix, // 企业前缀
    handle: `${form.entPrefix}/${form.handle}`,
    name: form.name,
    type: form.type, // 1:本地资源实体 2:业务资源实体
    items: {
      add: addList,
      edit: editList,
      del: data.basicDelList,
    },
    // 扩展属性
    extendItems: {
      del: data.extendDelList,
    },
    entityType: form.entityType,
    appId: form.appId,
    // services: dataService.dataService,
    version: version.value,
  };
}

async function validateData() {
  let isError = false;
  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  if (!data.tableData.length) {
    ElMessage.error("基础属性不能为空");
    isError = true;
  }
  return isError;
}

defineExpose({
  getAddData,
  getData,
  validateData,
});
</script>
<style lang="scss" scoped>
.handle-detail-wrap {
  padding: 0 40px;
}
.mb10 {
  margin-bottom: 10px;
}
.components-handle-detail-line {
  border-top: 1px solid #e5e8ef;
  margin-bottom: 24px;
}

.handle-detail-line {
  padding: 20px 0 0 0;
  border-top: 1px solid #e5e8ef;
  // border-bottom: 1px solid #e5e8ef;
  margin-bottom: 20px;
}

.form-item-handle {
  :deep(.el-input-group__prepend) {
    padding-right: 0;
  }
  .form-item-handle-line {
    border-left: 1px solid #dcdfe6;
    padding: 0 10px;
    margin-left: 20px;
  }
}

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background-color: #f5f5f5;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #f5f5f5;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
  background-color: #ccc;
}

.handle-tid-wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  .el-select {
    width: 146px;
    min-width: 146px;
  }
  .handle-tid-line {
    font-size: 12px;
    font-size: 12px;
    line-height: 20px;
    margin: 0 4px;
  }
  .el-form-item {
    flex: 1;
    min-width: 0;
  }
}
:deep(.el-tabs__nav-wrap::after) {
  content: none;
}
:deep(.el-tabs__active-bar) {
  display: none;
}
:deep(.el-tabs__item) {
  height: auto;
  line-height: normal;
}
:deep(.el-tabs--top .el-tabs__item.is-top:nth-child(2)) {
  border-right: 2px solid #c1c9c7;
}
</style>
