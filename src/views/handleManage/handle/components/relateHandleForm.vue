<template>
  <el-form
    ref="formRef"
    label-position="left"
    :model="form"
    label-width="100px"
    :rules="rules"
  >
    <el-form-item label="属性类型" prop="fieldType">
      <el-radio-group v-model="form.fieldType" @change="fieldTypeChange">
        <el-radio
          v-for="item in data.fieldTypeList"
          :key="item.value"
          :label="item.value"
          >{{ item.name }}</el-radio
        >
      </el-radio-group>
    </el-form-item>
    <!-- 标识解析数据源 -->
    <el-row v-if="form.fieldType === FIELD_TYPE_MAP.source">
      <el-form-item label="属性值" prop="dataService">
        <el-select
          v-model="form.dataService"
          placeholder="请选择数据服务"
          @change="getDataSourceChange"
        >
          <el-option
            v-for="item in dataService"
            :key="item.id"
            :value="item.id"
            :label="item.dataServiceName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="dataSource" label-width="12px">
        <el-select
          v-model="form.dataSource"
          placeholder="请选择数据源"
          @change="selectChange"
          filterable
        >
          <el-option
            v-for="item in data.dataSource"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-row>
    <el-form-item
      label="英文名称"
      prop="field"
      v-if="form.fieldType === FIELD_TYPE_MAP.source"
    >
      <el-select
        v-model="form.field"
        allow-create
        clearable
        filterable
        placeholder="请选择"
        no-match-text="未获取到数据，请输入正确的英文名称"
      >
        <el-option
          v-for="item in optionalFieldList"
          :key="item"
          :value="item"
          :label="item"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item
      label="英文名称"
      prop="field"
      v-if="form.fieldType !== FIELD_TYPE_MAP.source"
    >
      <el-input v-model.trim="form.field" clearable />
    </el-form-item>
    <el-form-item label="中文名称" prop="description">
      <el-input v-model.trim="form.description" clearable />
    </el-form-item>
    <el-form-item label="备注" prop="remark">
      <el-input v-model.trim="form.remark" clearable />
    </el-form-item>
    <el-form-item
      label="属性值"
      prop="fieldValue"
      v-if="form.fieldType === FIELD_TYPE_MAP.fixed"
    >
      <el-input v-model.trim="form.fieldValue" clearable />
    </el-form-item>
    <el-form-item
      v-if="form.fieldType === FIELD_TYPE_MAP.handleValue"
      label="属性值"
      prop="handleValue"
    >
      <el-table :data="form.references" size="small" border>
        <el-table-column label="关联标识">
          <template #default="scope">
            <el-form-item
              :prop="'references.' + scope.$index + '.referenceHandle'"
              :rules="[
                {
                  required: true,
                  validator: referenceHandleValidate,
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                v-model.trim="scope.row.referenceHandle"
                @input="scope.row.queryProp = {}"
                placeholder="输入关联的标识"
                clearable
              ></el-input>
            </el-form-item>
          </template>
        </el-table-column>
        <el-table-column label="映射关系" width="450">
          <template #header>
            <span>映射关系</span>
            <el-tooltip placement="top-start" effect="light">
              <template #content>
                <img
                  style="width: 650px; height: 300px"
                  fit="contain"
                  src="@/assets/images/handle/handleReferanceDemo.jpg"
              /></template>
              <span
                ><el-icon size="18" style="vertical-align: text-bottom"
                  ><Warning /></el-icon
              ></span>
            </el-tooltip>
          </template>
          <template #default="scope">
            <el-row>
              <el-form-item
                :prop="'references.' + scope.$index + '.queryProp'"
                :rules="queryPropRules(scope.$index)"
              >
                <el-select
                  :disabled="form.fixDisabled"
                  :loading="loading"
                  :popper-append-to-body="false"
                  v-model="scope.row.queryProp"
                  placeholder="请选择关联标识属性"
                  :no-data-text="
                    scope.row.referenceHandle ? '无数据' : '请输入关联标识'
                  "
                  value-key="fieldIndex"
                >
                  <el-option
                    v-for="item in queryFieldList"
                    :key="item.fieldIndex"
                    :value="item"
                    :label="item.field"
                  >
                    <span style="float: left">{{ item.field }}</span>
                    <span style="float: left; margin-left: 5px">{{
                      (item.fieldIndex && item.fieldIndex > 0) || item.remark
                        ? "-"
                        : ""
                    }}</span>
                    <span style="float: left; margin-left: 5px">{{
                      item.fieldIndex && item.fieldIndex > 0
                        ? item.fieldIndex
                        : ""
                    }}</span>
                    <span style="float: left; margin-left: 5px">{{
                      item.remark ? "(" + item.remark + ")" : ""
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                style="margin-left: 12px"
                :prop="'references.' + scope.$index + '.paramProp'"
                :rules="paramPropRules(scope.$index)"
                ><el-select
                  v-model="scope.row.paramProp"
                  placeholder="请选择标识属性"
                  :disabled="form.fixDisabled"
                  value-key="fieldIndex"
                >
                  <el-option
                    v-for="(item, index) in paramFieldList"
                    :key="index"
                    :value="item"
                    :label="item.field"
                  >
                    <span style="float: left">{{ item.field }}</span>
                    <span style="float: left; margin-left: 5px">{{
                      (item.fieldIndex && item.fieldIndex > 0) || item.remark
                        ? "-"
                        : ""
                    }}</span>
                    <span style="float: left; margin-left: 5px">{{
                      item.fieldIndex && item.fieldIndex > 0
                        ? item.fieldIndex
                        : ""
                    }}</span>
                    <span style="float: left; margin-left: 5px">{{
                      item.remark ? "(" + item.remark + ")" : ""
                    }}</span>
                  </el-option>
                </el-select></el-form-item
              >
            </el-row>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100px">
          <template #default="scope">
            <el-form style="display: inline; margin-left: 12px">
              <el-button
                type="primary"
                text
                @click="resolveHandle(scope.row.referenceHandle)"
                >解析</el-button
              >
            </el-form>
          </template>
        </el-table-column>
      </el-table>
    </el-form-item>

    <el-form-item
      v-if="form.fieldType === FIELD_TYPE_MAP.handleWithAttr"
      label="属性值"
      prop="handleValue"
    >
      <el-table :data="form.referencesAttr" border>
        <el-table-column label="关联标识">
          <template #default="scope">
            <el-row style="flex-wrap: nowrap"
              ><el-form-item
                :prop="'referencesAttr.' + scope.$index + '.referenceHandle'"
                :rules="[
                  {
                    required: true,
                    validator: referenceHandleValidate,
                    trigger: 'blur',
                  },
                ]"
              >
                <el-input
                  v-model.trim="scope.row.referenceHandle"
                  @input="
                    scope.row.queryProp = {};
                    scope.row.referenceHandleProp = {};
                  "
                  placeholder="输入关联的标识"
                  clearable
                ></el-input>
              </el-form-item>
              <el-form-item
                style="margin-left: 12px"
                :prop="
                  'referencesAttr.' + scope.$index + '.referenceHandleProp'
                "
                :rules="propHandleRules(scope.$index)"
              >
                <el-select
                  :loading="loading"
                  :popper-append-to-body="false"
                  v-model="scope.row.referenceHandleProp"
                  placeholder="请选择属性"
                  :no-data-text="
                    scope.row.referenceHandle ? '无数据' : '请输入关联标识'
                  "
                  value-key="fieldIndex"
                >
                  <el-option
                    v-for="item in allFieldList"
                    :key="item.fieldIndex"
                    :value="item"
                    :label="item.field"
                  >
                    <span style="float: left">{{ item.field }}</span>
                    <span style="float: left; margin-left: 5px">{{
                      (item.fieldIndex && item.fieldIndex > 0) || item.remark
                        ? "-"
                        : ""
                    }}</span>
                    <span style="float: left; margin-left: 5px">{{
                      item.fieldIndex && item.fieldIndex > 0
                        ? item.fieldIndex
                        : ""
                    }}</span>
                    <span style="float: left; margin-left: 5px">{{
                      item.remark ? "(" + item.remark + ")" : ""
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item></el-row
            >
          </template>
        </el-table-column>
        <el-table-column label="映射关系" width="450">
          <template #header>
            <span>映射关系</span>
            <el-tooltip effect="light" placement="top-start">
              <template #content>
                <img
                  style="width: 650px; height: 300px"
                  fit="contain"
                  src="@/assets/images/handle/handleReferanceDemo.jpg"
              /></template>
              <span
                ><el-icon size="18" style="vertical-align: text-bottom"
                  ><Warning /></el-icon
              ></span>
            </el-tooltip>
          </template>
          <template #default="scope">
            <el-row>
              <el-form-item
                :prop="'referencesAttr.' + scope.$index + '.queryProp'"
                :rules="queryPropRules(scope.$index)"
              >
                <el-select
                  :disabled="form.fixDisabled"
                  :loading="loading"
                  :popper-append-to-body="false"
                  v-model="scope.row.queryProp"
                  placeholder="请选择关联标识属性"
                  :no-data-text="
                    scope.row.referenceHandle ? '无数据' : '请输入关联标识'
                  "
                  value-key="fieldIndex"
                >
                  <el-option
                    v-for="item in queryFieldList"
                    :key="item.fieldIndex"
                    :value="item"
                    :label="item.field"
                  >
                    <span style="float: left">{{ item.field }}</span>
                    <span style="float: left; margin-left: 5px">{{
                      (item.fieldIndex && item.fieldIndex > 0) || item.remark
                        ? "-"
                        : ""
                    }}</span>
                    <span style="float: left; margin-left: 5px">{{
                      item.fieldIndex && item.fieldIndex > 0
                        ? item.fieldIndex
                        : ""
                    }}</span>
                    <span style="float: left; margin-left: 5px">{{
                      item.remark ? "(" + item.remark + ")" : ""
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item
                style="margin-left: 12px"
                :prop="'referencesAttr.' + scope.$index + '.paramProp'"
                :rules="paramPropRules(scope.$index)"
                ><el-select
                  :disabled="form.fixDisabled"
                  v-model="scope.row.paramProp"
                  placeholder="请选择标识属性"
                  value-key="fieldIndex"
                >
                  <el-option
                    v-for="(item, index) in paramFieldList"
                    :key="index"
                    :value="item"
                    :label="item.field"
                  >
                    <span style="float: left">{{ item.field }}</span>
                    <span style="float: left; margin-left: 5px">{{
                      (item.fieldIndex && item.fieldIndex > 0) || item.remark
                        ? "-"
                        : ""
                    }}</span>
                    <span style="float: left; margin-left: 5px">{{
                      item.fieldIndex && item.fieldIndex > 0
                        ? item.fieldIndex
                        : ""
                    }}</span>
                    <span style="float: left; margin-left: 5px">{{
                      item.remark ? "(" + item.remark + ")" : ""
                    }}</span>
                  </el-option>
                </el-select></el-form-item
              >
            </el-row>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100px">
          <template #default="scope">
            <el-form style="display: inline; margin-left: 12px">
              <el-button
                type="primary"
                text
                @click="resolveHandle(scope.row.referenceHandle)"
                >解析</el-button
              >
            </el-form>
          </template>
        </el-table-column>
      </el-table>
    </el-form-item>
  </el-form>
  <el-dialog
    v-model="data.dialogResolve"
    title="解析结果"
    append-to-body
    width="800"
    @close="data.dialogResolve = false"
  >
    <div class="relate-handle-matrix-graph">
      <matrix-graph ref="matrixGraphRef"></matrix-graph>
    </div>
    <template #footer>
      <el-button type="primary" @click="data.dialogResolve = false">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import {
  reactive,
  defineProps,
  watch,
  defineExpose,
  ref,
  nextTick,
  PropType,
} from "vue";
import { ElMessage } from "element-plus";
import { deepClone } from "@/utils/auth";
import { idResolve } from "@/api/idRes/index";
import matrixGraph from "@/views/idRes/components/matrix-graph.vue";
import {
  getDataSourceApi,
  getFieldListApi,
  getServiceListApi,
} from "@/api/objHandle/manager";
import {
  FIELD_TYPE_MAP,
  FIELD_TYPE_LIST,
  DIALOG_TYPE_MAP,
} from "@/utils/constant";

const formRef = ref();
const matrixGraphRef = ref();
const loading = ref(false);
const oldField = ref("");
const oldDescription = ref("");
const oldHandle = ref("");
interface DataService {
  id: number;
  dataServiceName: string;
  serviceAddress: string;
  serviceToken: string;
}

const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
});
const dataService = ref<DataService[]>([]);
// 数据源、数据服务的英文名集合（去除已新增属性英文名）
const optionalFieldList = ref<string[]>([]);
// 父组件传过来的英文名集合
const tableFieldList = ref<string[]>([]);
// 定义全部属性下拉框
const allFieldList = ref<PropField[]>([]);
// 定义查询属性下拉框
const queryFieldList = ref<PropField[]>([]);
// 定义参数属性下拉框
const paramFieldList = ref<PropField[]>([]);
// 已关联的标识集合，用于校验不能关联统一标识
const referenceHandleList = ref<string[]>([]);
interface PropField {
  valueKey?: string;
  remark?: string;
  fieldIndex?: number;
  field?: string;
  fieldType?: number;
}

interface FieldTypeList {
  value: number;
  name: string;
}
interface DataSource {
  id: number;
  name: string;
}
const data: {
  dialogResolve: boolean;
  fieldTypeList: FieldTypeList[];
  dataSource: DataSource[];
} = reactive({
  fieldTypeList: FIELD_TYPE_LIST,
  dialogResolve: false,
  dataSource: [],
});

interface Form {
  fieldIndex: number | null;
  field: string;
  description: string;
  fieldValue: string;
  remark: string;
  fieldType: number | null;
  references: {
    referenceHandle: string;
    queryProp: PropField;
    paramProp: PropField;
  }[];
  referencesAttr: {
    referenceHandle: string;
    referenceHandleProp: PropField;
    queryProp: PropField;
    paramProp: PropField;
  }[];
  auth: number[];
  itemHandle: string;
  dataService: number | null;
  dataSource: number | null;
  dataSourceName: string | null;
  dataServiceName: string | null;
  wildcard: string | null;
  fixDisabled: boolean;
}
const form = reactive<Form>({
  fieldIndex: null,
  field: "",
  description: "",
  remark: "",
  fieldValue: "",
  fieldType: null,
  references: [],
  referencesAttr: [],
  auth: [],
  itemHandle: "",
  dataService: null,
  dataSource: null,
  dataSourceName: "",
  dataServiceName: "",
  wildcard: "",
  // 关联属性为固定值不能修改关联属性、标识属性下拉框
  fixDisabled: false,
});

const fieldValidate = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback("请填写英文名称");
  }
  const reg = /^[a-zA-Z\d_]+$/;
  if (!reg.test(value)) {
    return callback("英文名称格式错误，请重新填写");
  }
  let noOldFieldList = [];
  if (props.item.dialogType === DIALOG_TYPE_MAP.editor) {
    // 去除原字段值
    noOldFieldList = props.item.tableData.filter(
      (item: any) => item.field !== oldField.value
    );
  }

  // 判断是否为编辑模式
  if (
    props.item.dialogType === DIALOG_TYPE_MAP.editor &&
    noOldFieldList &&
    noOldFieldList.some((item: any) => item.field === form.field)
  ) {
    return callback("英文名称已存在，请重新填写");
  }
  // 新增、新增下一个模式
  if (
    props.item.dialogType !== DIALOG_TYPE_MAP.editor &&
    props.item.tableData &&
    props.item.tableData.some((item: any) => item.field === form.field)
  ) {
    return callback("英文名称已存在，请重新填写");
  }

  callback();
};

const handleValueValidate = (rule: any, value: any, callback: any) => {
  callback();
};

const handleValidateDescription = (rule: any, value: any, callback: any) => {
  let noOldDescriptionList = [];
  if (props.item.dialogType === DIALOG_TYPE_MAP.editor) {
    // 去除原字段值
    noOldDescriptionList = props.item.tableData.filter(
      (item: any) => item.description !== oldDescription.value
    );
  }

  // 判断是否为编辑模式
  if (
    props.item.dialogType === DIALOG_TYPE_MAP.editor &&
    noOldDescriptionList &&
    noOldDescriptionList.some(
      (item: any) => item.description === form.description
    )
  ) {
    return callback("中文名称已存在，请重新填写");
  }
  // 新增、新增下一个模式
  if (
    props.item.dialogType !== DIALOG_TYPE_MAP.editor &&
    props.item.tableData &&
    props.item.tableData.some(
      (item: any) => item.description === form.description
    )
  ) {
    return callback("中文名称已存在，请重新填写");
  }

  callback();
};

// 校验关联标识
async function referenceHandleValidate(
  rule: any,
  handleVal: any,
  callback: any
) {
  if (handleVal) {
    // 判断不能关联本身
    if (form.wildcard === handleVal) {
      return callback("关联标识和当前标识重复");
    }
    let noOldHandleList: any[] = [];
    if (props.item.dialogType === DIALOG_TYPE_MAP.editor) {
      // 去除原字段值
      noOldHandleList = referenceHandleList.value.filter(
        (item: any) => item !== oldHandle.value
      );
    }
    // 若输入的关联标识属性都为固定值时，清空关联属性
    if (await getFixedFilter(handleVal)) {
      if (form.fieldType === FIELD_TYPE_MAP.handleValue) {
        form.references[0].paramProp = {};
        form.references[0].queryProp = {};
      } else if (form.fieldType === FIELD_TYPE_MAP.handleWithAttr) {
        form.referencesAttr[0].paramProp = {};
        form.referencesAttr[0].queryProp = {};
      }
      callback();
      return;
    }
  } else {
    return callback("请选择关联标识");
  }
  callback();
}

// 校验标识下属性是否都为固定值
async function getFixedFilter(handleVal: any) {
  // 查询
  await getIdResolve(handleVal);
  form.fixDisabled = false;
  if (allFieldList.value.length > 0) {
    // 过滤类型为固定值
    const filterArray = allFieldList.value.filter(
      (item: { fieldType?: number }) => item.fieldType === FIELD_TYPE_MAP.fixed
    );
    // 若输入的关联标识属性都为固定值时,返回true;参数、查询下拉框置灰，可保存，完成关联；
    if (filterArray.length === allFieldList.value.length) {
      form.fixDisabled = true;
      return true;
    }
  }
}

// 根据标识查询属性
function getIdResolve(handleVal: any) {
  if (handleVal) {
    allFieldList.value = [];
    return idResolve({ handle: handleVal })
      .then((response: any) => {
        // 遍历标识解析结果，获取关联属性下拉框数据
        response.values.forEach(
          (
            item: { fieldType: any; remark: any; fieldIndex: any; field: any },
            index: any
          ) => {
            // 只关联固定值和数据源
            if (
              item.fieldType === FIELD_TYPE_MAP.fixed ||
              item.fieldType === FIELD_TYPE_MAP.source
            ) {
              allFieldList.value.push({
                fieldType: item.fieldType,
                remark: item.remark ? item.remark : "",
                fieldIndex: item.fieldIndex,
                field: item.field,
              });
            }
          }
        );
      })
      .finally(() => {
        // 初始化数据源下拉框
        getProperty([FIELD_TYPE_MAP.source]);
      });
  }
}

// 标识-属性 选择属性校验
function propHandleRules(index: number) {
  return [
    {
      required: true,
      validator: propHandleRulesValidate,
      trigger: "blur",
    },
  ];
}

// 标识-属性校验规则生成
async function propHandleRulesValidate(rule: any, val: any, callback: any) {
  if (!val.field) {
    callback("请选择属性");
    return;
  }
  callback();
}

// 关联标识属性校验（当关联标识下都为固定值时，可以为空，否则不能为空）
function queryPropRules(index: number) {
  return [
    {
      required: true,
      validator: queryPropRulesValidate,
      trigger: "blur",
    },
  ];
}

// 关联标识属性校验规则生成
async function queryPropRulesValidate(rule: any, val: any, callback: any) {
  if (form.fixDisabled && !val.field) {
    callback();
    return;
  }
  if (!val.field) {
    callback("请选择关联标识属性");
    return;
  }
  callback();
}

// 标识属性校验（当关联标识下都为固定值时，可以为空，否则不能为空）
function paramPropRules(index: number) {
  return [
    {
      required: true,
      validator: paramPropRulesValidate,
      trigger: "blur",
    },
  ];
}

// 关联标识属性校验规则生成
async function paramPropRulesValidate(rule: any, val: any, callback: any) {
  if (form.fixDisabled && !val.field) {
    callback();
    return;
  }
  if (!val.field) {
    callback("请选择标识属性");
    return;
  }
  callback();
}

const rules = reactive({
  field: [
    { required: true, validator: fieldValidate },
    { max: 30, message: "最大长度为30字符" },
  ],
  remark: [
    {
      required: false,
      max: 100,
      message: "最大长度为100字符",
      trigger: "blur",
    },
  ],
  dataService: [{ required: true, message: "请选择数据服务", trigger: "blur" }],
  dataSource: [{ required: true, message: "请选择数据源", trigger: "blur" }],
  auth: [
    {
      required: true,
      type: "array",
      message: "请选择权限",
      trigger: "change",
    },
  ],
  description: [
    { required: true, message: "请填写中文名称", trigger: "blur" },
    { max: 30, message: "最大长度为30字符", trigger: "blur" },
    { required: true, validator: handleValidateDescription, trigger: "blur" },
  ],
  fieldValue: [
    { required: true, message: "请填写属性值", trigger: "blur" },
    { max: 255, message: "最大长度为255字符", trigger: "blur" },
  ],
  fieldType: [{ required: true, message: "请选择属性类型", trigger: "blur" }],
  itemHandle: { required: true, message: "请输入标识", trigger: "change" },
  handleValue: {
    required: true,
    validator: handleValueValidate,
    trigger: "blur",
  },
});

watch(
  props,
  async (val) => {
    const tempData: any = deepClone(val);
    // 获取父组件表单数据
    const parentForm = tempData.item.form;
    // 获取父组件基础属性列表数据
    const parentTableData = tempData.item.tableData;
    // 获取父组件扩展属性列表数据
    const extendTableData = tempData.item.extendTableData;
    // 判断3种处理模式，新增、新增下一个、编辑、模式
    if (tempData.item.dialogType === DIALOG_TYPE_MAP.add) {
      // 新增模式，设置属性类型为固定值
      form.fieldType = FIELD_TYPE_MAP.fixed;
    } else if (tempData.item.dialogType === DIALOG_TYPE_MAP.addNext) {
      // 新增下一个模式，停留在当前属性类型，重置form
      fieldTypeChange();
      // 置空标识属性下拉框，下方必要处理中会重新赋值
      paramFieldList.value = [];
    } else if (tempData.item.dialogType === DIALOG_TYPE_MAP.editor) {
      // 编辑模式，获取父组件选中行数据
      let parentReferenceData = {
        field: "",
        description: "",
        remark: "",
        fieldValue: "",
        fieldType: null,
        references: [],
        referencesAttr: [],
        itemHandle: "",
        dataServiceId: null,
        dataSourceId: null,
        dataSourceName: "",
        dataServiceName: "",
        fieldIndex: null,
      };
      parentReferenceData = parentTableData[tempData.item.referenceIndex];
      form.fieldType = parentReferenceData.fieldType;
      form.field = parentReferenceData.field;
      form.description = parentReferenceData.description;
      form.remark = parentReferenceData.remark;
      form.itemHandle = parentReferenceData.itemHandle;
      form.fieldValue = parentReferenceData.fieldValue;
      form.fieldIndex = parentReferenceData.fieldIndex;
      // 编辑模式，获取原英文名、中文名
      oldField.value = parentReferenceData.field;
      oldDescription.value = parentReferenceData.description;
      // 获取选中行/初始化，标识值、标识-属性的关联关系
      if (form.fieldType === FIELD_TYPE_MAP.handleValue) {
        // 初始化全部下拉框
        await getIdResolve(form.fieldValue);
        form.references = deepClone(parentReferenceData.references);
        oldHandle.value = form.references[0]?.referenceHandle;
        getFixedFilter(form.references[0]?.referenceHandle || "");
      } else if (form.fieldType === FIELD_TYPE_MAP.handleWithAttr) {
        // 初始化全部下拉框
        await getIdResolve(form.fieldValue);
        form.referencesAttr = deepClone(parentReferenceData.references);
        oldHandle.value = form.referencesAttr[0]?.referenceHandle;
        getFixedFilter(form.referencesAttr[0].referenceHandle || "");
      }

      // 初始化数据源、数据服务
      form.dataService = parentReferenceData.dataServiceId;
      form.dataSource = parentReferenceData.dataSourceId;
      if (parentReferenceData.dataSourceId) {
        getFieldList(
          parentReferenceData.dataServiceId,
          parentReferenceData.dataSourceId
        );
      }
      form.dataSourceName = parentReferenceData.dataSourceName;
      form.dataServiceName = parentReferenceData.dataServiceName;
    }
    // **
    // ********以下部分为必要处理，不分处理模式**********
    // **
    // 获取当前标识属性编码，用于校验不能关联本身标识
    form.wildcard = parentForm.entPrefix + "/" + parentForm.handle;
    // 筛选基础属性字段类型为固定值、标识解析数据源的行，获取英文名作为关联属性下拉框内容
    const tableData = parentTableData?.filter(
      (item: { fieldType: number }) =>
        item.fieldType === FIELD_TYPE_MAP.source ||
        item.fieldType === FIELD_TYPE_MAP.fixed
    );
    tableData.forEach(
      (
        item: {
          remark: string;
          fieldIndex: number | undefined;
          field: string;
        },
        index: any
      ) => {
        paramFieldList.value.push({
          remark: item.remark,
          fieldIndex: item.fieldIndex,
          field: item.field,
        });
      }
    );
    // 获取所有已关联标识，用于判断不能关联同一标识
    const handleList = parentTableData
      .concat(extendTableData)
      ?.filter(
        (item: { references: any }) =>
          item.references?.length > 0 && item.references[0].referenceHandle
      );
    referenceHandleList.value = handleList?.map(
      (item: any) => item.references[0].referenceHandle
    );
    // 获取已新增的属性字段，用于数据源英文名去重
    tableFieldList.value =
      parentTableData?.map((item: any) => item.field) || [];
  },
  {
    immediate: true,
    deep: true,
  }
);
watch(
  data.dataSource,
  async () => {
    await getDataService();
    await getDataSource();
    resetDataSource();
  },
  {
    immediate: true,
    deep: true,
  }
);

// 切换属性类型，置空已填写的值
function fieldTypeChange(val?: any) {
  const type = form.fieldType;
  formRef.value.resetFields();
  // 置空form已填写的值
  form.field = "";
  form.description = "";
  form.remark = "";
  form.fieldValue = "";
  form.references = [];
  form.referencesAttr = [];
  form.dataSourceName = "";
  form.dataServiceName = "";
  form.dataService = null;
  form.dataSource = null;
  if (props.item.dialogType !== DIALOG_TYPE_MAP.editor) {
    form.fieldIndex = null;
  }

  // 置空下拉框
  data.dataSource = [];
  allFieldList.value = [];
  queryFieldList.value = [];
  optionalFieldList.value = [];

  form.fieldType = type;
  // 标识值，标识属性初始化关联表格
  handleReferenceAdd();
}

function handleReferenceAdd() {
  form.fixDisabled = false;
  // 判断当前标识属性类型
  if (form.fieldType === FIELD_TYPE_MAP.handleValue) {
    form.references.push({
      referenceHandle: "",
      queryProp: {},
      paramProp: {},
    });
  } else if (form.fieldType === FIELD_TYPE_MAP.handleWithAttr) {
    form.referencesAttr.push({
      referenceHandle: "",
      referenceHandleProp: {},
      queryProp: {},
      paramProp: {},
    });
  }
}

function selectChange() {
  getFieldList(form.dataService, form.dataSource);
  form.field = "";
  resetDataSource();
}

function resetDataSource() {
  let dataSourceName = "";
  let dataServiceName = "";
  dataService.value.forEach((item) => {
    if (item.id === form.dataService) {
      dataServiceName = item.dataServiceName;
    }
  });
  data.dataSource.forEach((item) => {
    if (item.id === form.dataSource) {
      dataSourceName = item.name;
    }
  });
  form.dataSourceName = dataSourceName;
  form.dataServiceName = dataServiceName;
}
// 获取数据源英文名并去重
function getFieldList(
  dataServiceId: number | null,
  dataSourceId: number | null
) {
  getFieldListApi({
    dataServiceId,
    dataSourceId,
  }).then((res: any) => {
    optionalFieldList.value = res.filter(
      (item: any) => !tableFieldList.value.includes(item)
    );
  });
}

// 过滤出指定类型的属性
function getProperty(fieldType: number[]) {
  queryFieldList.value = allFieldList.value.filter((item: any) =>
    fieldType.includes(item.fieldType)
  );
}

function resolveHandle(id: any) {
  if (id) {
    idResolve({ handle: id }).then((response: any) => {
      data.dialogResolve = true;
      nextTick(() => {
        matrixGraphRef.value.refresh(response);
      });
    });
  } else {
    ElMessage({
      message: "请输入标识",
      type: "error",
    });
  }
}

async function validateData() {
  let isError = false;
  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  return isError;
}

function getData() {
  return {
    ...form,
    field: form.field,
    fieldValue: form.fieldValue,
    fieldIndex: form.fieldIndex,
    remark: form.remark,
    description: form.description,
    itemHandle: form.itemHandle,
    fieldType: form.fieldType,
    references: form.references,
    dataServiceId: form.dataService,
    dataSourceId: form.dataSource,
    dataSourceName: form.dataSourceName,
  };
}

// 切换数据源下拉框
async function getDataSourceChange() {
  form.dataSource = null;
  form.field = "";
  getDataSource();
}

async function getDataService() {
  dataService.value = [];
  return getServiceListApi().then((res: any) => {
    if (res.code) {
      dataService.value = [];
    } else {
      dataService.value = res;
    }
  });
}

async function getDataSource() {
  optionalFieldList.value = [];
  data.dataSource = [];
  if (form.dataService) {
    return getDataSourceApi({ id: form.dataService }).then((res: any) => {
      data.dataSource = res || [];
    });
  }
}

// onMounted(() => {
//   if (form.dataService) {
//     getDataSource();
//   }
// });

defineExpose({
  form,
  validateData,
  getData,
});
</script>
<style lang="scss" scoped>
.el-table {
  .is-error {
    height: 70px;
    :deep(.el-form-item__error) {
      top: 50px;
    }
  }
}

.mb10 {
  margin-bottom: 10px;
}
.mb11 {
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 5px;
  color: #929292;
}
.mb12 {
  float: right;
}
.mb13 {
  margin-top: 5px;
  letter-spacing: 0.5px;
  // text-align: center;
  margin: 0 30px;
  > div {
    display: flex;
    align-items: baseline;
    span {
      flex: 1;
      &:first-child {
        text-align: right;
      }
    }
  }
}
.mb14 {
  margin-left: 10px;
}
.mb15 {
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 8px;
  letter-spacing: 0.5px;
  // text-align: center;
  margin: 40px 30px 10px;
  > div {
    display: flex;
    align-items: baseline;
    span {
      flex: 1;
      &:first-child {
        text-align: right;
      }
    }
  }
}
:deep(.el-select-dropdown__empty) {
  padding: 10px 10px !important;
}

:deep(.add-item-btn) {
  width: 100%;
  border: 1px solid #dfe4e3;
  border-top: 0;
  height: 44px;
  border-radius: 0;
  color: #1664ff;
  font-size: 12px;
  line-height: 20px;
  &:hover {
    color: #4086ff !important;
    background-color: transparent !important;
  }
}

.relate-handle-matrix-graph {
  height: 600px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
