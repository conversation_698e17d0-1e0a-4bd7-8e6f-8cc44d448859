<template>
  <div class="dataService">
    <div class="btn-position" v-if="!isFirstAdd">
      <el-select
        v-model="dataService.dataService"
        multiple
        collapse-tags
        collapse-tags-tooltip
        size="small"
        placeholder="选择现有服务"
        @change="selectDataService"
      >
        <el-option
          v-for="item in dataServiceList"
          :key="item.id"
          :value="item.id"
          :label="item.dataServiceName"
          :disabled="deleteDisabled(item.id)"
        ></el-option>
      </el-select>
      <el-button type="primary" style="margin-left: 12px" @click="addServive"
        >添加新服务</el-button
      >
    </div>
    <div class="section-title">
      <div class="section-title-line"></div>
      <span class="section-title-text"> 数据服务 </span>
    </div>
    <div v-loading="serviceLoading">
      <div class="service-list" v-if="!isFirstAdd">
        <div
          class="service-item"
          v-for="(item, index) in dataService.choiseDataServiceList"
          :key="index"
        >
          <div class="service-title">
            <span class="num">{{ index + 1 }}</span>
            <span class="name">{{ item.dataServiceName }}</span>
          </div>
          <div class="service-body">
            <div class="body-item">
              <span>地址：</span>
              <span>{{ item.serviceAddress }}</span>
            </div>
            <div class="body-item">
              <span>Token：</span>
              <span>{{ item.serviceToken }}</span>
            </div>
          </div>
          <div class="service-btn">
            <el-tooltip
              effect="dark"
              content="此服务正在使用中"
              placement="top"
              v-if="deleteDisabled(item.id)"
            >
              <div>
                <el-button
                  type="primary"
                  text
                  :disabled="deleteDisabled(item.id)"
                  @click="editDataService(item.id)"
                  >编辑</el-button
                >
              </div>
            </el-tooltip>
            <div v-show="!deleteDisabled(item.id)">
              <div>
                <el-button
                  type="primary"
                  text
                  :disabled="deleteDisabled(item.id)"
                  @click="editDataService(item.id)"
                  >编辑</el-button
                >
              </div>
            </div>

            <el-tooltip
              effect="dark"
              content="此服务正在使用中"
              placement="top"
              v-if="deleteDisabled(item.id)"
            >
              <div>
                <el-button
                  type="primary"
                  text
                  @click="deleteDataService(item.id)"
                  :disabled="deleteDisabled(item.id)"
                  >删除</el-button
                >
              </div>
            </el-tooltip>
            <div v-show="!deleteDisabled(item.id)">
              <el-popconfirm
                :width="200"
                confirm-button-text="确定"
                cancel-button-text="取消"
                title="确定删除该数据服务吗？"
                @confirm="deleteDataService(item.id)"
              >
                <template #reference>
                  <el-button type="primary" text>删除</el-button>
                </template>
              </el-popconfirm>
            </div>
          </div>
        </div>
      </div>
      <div class="no-service-data" v-if="isFirstAdd">
        <img
          src="@/assets/images/nodata.png"
          alt=""
          style="width: 120px; height: 124px"
        />
        <div>
          <el-button type="primary" @click="addServive">添加数据服务</el-button>
        </div>
      </div>
    </div>
  </div>
  <addOrEditService
    v-if="addServiceVisible"
    @update-service="addServiceList"
    @close-dialog="addServiceVisible = false"
    @addSuccess="addSuccess"
    :selectDataServiceId="selectDataServiceId"
    :isAddService="isAddService"
  ></addOrEditService>
</template>
<script setup lang="ts">
import { ElMessage } from "element-plus";
import { computed, ref, onMounted, watch } from "vue";
import { deepClone } from "@/utils/auth";
import addOrEditService from "./addOrEditService.vue";
import { DataServiceList } from "@/types/handle";
import { getServiceListApi, deleteServiceApi } from "@/api/objHandle/manager";

const props = defineProps({
  dataService: {
    type: Object,
    default: () => ({}),
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});
const addServiceVisible = ref(false);

const selectDataServiceId = ref<number>();
const isAddService = ref(true);

const choiseDataServiceList = ref<DataServiceList[]>([]);
const preFormDataService = ref<any[]>([]);
const dataServiceList = ref<DataServiceList[]>([]);
const isFirstAdd = ref(true);
const serviceLoading = ref(false);

const tableDataSource = computed(() => {
  return props.data.tableData.map((item: any) => item.dataServiceId);
});

// 设置不可删除方法
function deleteDisabled(id: number) {
  return tableDataSource.value.includes(id);
}

function addSuccess(service: any, isAdd: boolean) {
  if (isAdd) {
    props.dataService.choiseDataServiceList.push(service);
    props.dataService.dataService.push(service.id);
    preFormDataService.value = deepClone(props.dataService.dataService);
  } else {
    props.dataService.choiseDataServiceList.find(
      (item: { id: any; dataServiceName: any }) => {
        if (item.id === service.id) {
          item.dataServiceName = service.dataServiceName;
        }
      }
    );
  }
}

function addServiceList() {
  getServiceList();
}

// 获取数据服务列表
async function getServiceList() {
  serviceLoading.value = true;
  return getServiceListApi()
    .then((res: any) => {
      if (res.code) {
        dataServiceList.value = [];
      } else {
        dataServiceList.value = res;
      }
      if (dataServiceList.value.length === 0) {
        isFirstAdd.value = true;
      } else {
        isFirstAdd.value = false;
      }
    })
    .finally(() => {
      serviceLoading.value = false;
    });
}

// 选择现有服务器preFormDataService dataService dataServiceList
function selectDataService() {
  if (
    !preFormDataService.value.length &&
    !props.dataService.dataService.length
  ) {
    return;
  }
  let addItem = "";
  let deleItem = "";
  // 删除数据服务
  if (preFormDataService.value.length > props.dataService.dataService.length) {
    deleItem = preFormDataService.value.filter(
      (item: any) => !props.dataService.dataService.includes(item)
    )[0];
  }
  // 新增数据服务
  if (preFormDataService.value.length < props.dataService.dataService.length) {
    addItem = props.dataService.dataService.filter(
      (item: any) => !preFormDataService.value.includes(item)
    )[0];
  }

  if (deleItem) {
    let deleteItemIndex = -1;
    props.dataService.choiseDataServiceList.forEach(
      (item: { id: any }, index: number) => {
        if (`${item.id}` === `${deleItem}`) {
          deleteItemIndex = index;
        }
      }
    );
    props.dataService.choiseDataServiceList.splice(deleteItemIndex, 1);
  }

  if (addItem) {
    const addItemData = dataServiceList.value.filter(
      (item: { id: any }) => `${item.id}` === `${addItem}`
    );
    props.dataService.choiseDataServiceList.push(addItemData[0]);
  }
  preFormDataService.value = deepClone(props.dataService.dataService);
}

function addServive() {
  addServiceVisible.value = true;
  isAddService.value = true;
}

function editDataService(id: number) {
  addServiceVisible.value = true;
  selectDataServiceId.value = id;
  isAddService.value = false;
}

function deleteDataService(id: number) {
  deleteServiceApi({ id }).then(() => {
    props.dataService.dataService = props.dataService.dataService.filter(
      (item: number) => item !== id
    );
    props.dataService.choiseDataServiceList =
      props.dataService.choiseDataServiceList.filter(
        (item: DataServiceList) => item.id !== id
      );
    getServiceList();
    ElMessage({
      message: "删除数据服务成功",
      type: "success",
    });
  });
}

watch(
  props,
  async (val) => {
    const tempData: any = deepClone(val);
    preFormDataService.value = tempData.dataService.dataService || [];
  },
  {
    immediate: true,
    deep: true,
  }
);

onMounted(() => {
  getServiceList();
});
const dataServiceModel = ref<any[]>([]);
</script>
<style lang="scss" scoped>
.dataService {
  padding-bottom: 16px;
  margin-bottom: 24px;
  position: relative;
  .no-service-data {
    text-align: center;
  }
  .btn-position {
    position: absolute;
    top: 0;
    right: 0;
  }
  .service-list {
    height: 220px;
    overflow: auto;
    white-space: nowrap;
    padding: 0 8px;
    .service-item {
      width: 300px;
      height: 200px;
      border: 1px solid #f0f0f0;
      box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
      border-radius: 2px;
      display: inline-block;
      margin-right: 16px;
      &:last-child {
        margin-right: 0px;
      }
      .service-title {
        height: 56px;
        border-bottom: 1px solid #f0f0f0;
        padding: 16px 20px;
        .num {
          width: 22px;
          height: 24px;
          display: inline-block;
          background: #eef2f1;
          border-radius: 2px;
          vertical-align: middle;
          text-align: center;
          line-height: 24px;
          color: #7b9790;
          font-weight: bold;
          margin-right: 10px;
        }
        .name {
          color: #1a2233;
          font-weight: bold;
          vertical-align: middle;
        }
      }
      .service-body {
        height: 98px;
        border-bottom: 1px solid #f0f0f0;
        padding: 10px 20px;
        .body-item {
          display: flex;
          height: 20px;
          margin-bottom: 8px;
          span {
            align-self: center;
            font-size: 12px;
            color: #535f5c;
            &:first-child {
              width: 50px;
            }
            &:last-child {
              flex: 1;
            }
          }
        }
      }
      .service-btn {
        height: 44px;
        display: flex;
        > div {
          flex: 1;
          text-align: center;
          align-self: center;
          span {
            cursor: pointer;
            color: #0057fe;
          }
        }
      }
    }
  }
}
</style>
