<template>
  <div class="yc-description data-service-wrap">
    <div class="yc-description-title">
      <div class="yc-description-title-tip"></div>
      <div class="yc-description-title-text">数据服务</div>
    </div>
    <div class="btn-position" v-if="serviceList.length && !props.isView">
      <el-select
        v-model="selectedService"
        multiple
        collapse-tags
        collapse-tags-tooltip
        size="small"
        placeholder="选择现有服务"
        @change="handleChangeSelectedService"
      >
        <el-option
          v-for="item in serviceList"
          :key="item.id"
          :value="item.id"
          :label="item.dataServiceName"
          :disabled="deleteDisabled(item.id)"
        ></el-option>
      </el-select>
      <el-button
        type="primary"
        size="small"
        style="margin-left: 12px"
        @click="addService"
        >添加新服务</el-button
      >
    </div>
    <div class="data-service-card">
      <div v-loading="loading">
        <div class="service-list" v-if="serviceList.length">
          <div
            class="service-item"
            v-for="(item, index) in serviceCardList"
            :key="index"
          >
            <div class="service-title">
              <span class="num">{{ index + 1 }}</span>
              <span class="name">{{ item.dataServiceName }}</span>
            </div>
            <div class="service-body">
              <div class="body-item">
                <span>地址：</span>
                <span>{{ item.serviceAddress }}</span>
              </div>
              <div class="body-item">
                <span>Token：</span>
                <span>{{ item.serviceToken }}</span>
              </div>
            </div>
            <div v-if="!props.isView" class="service-btn">
              <div>
                <el-button type="primary" text @click="handleEdit(item.id)"
                  >编辑</el-button
                >
              </div>
              <el-tooltip
                effect="dark"
                content="此服务正在使用中"
                placement="top"
                v-if="deleteDisabled(item.id)"
              >
                <div>
                  <el-button
                    type="primary"
                    text
                    @click="deleteDataService(item.id)"
                    :disabled="deleteDisabled(item.id)"
                    >删除</el-button
                  >
                </div>
              </el-tooltip>
              <div v-show="!deleteDisabled(item.id)">
                <el-button
                  type="primary"
                  text
                  @click="deleteDataService(item.id)"
                  >删除</el-button
                >
              </div>
            </div>
          </div>
        </div>
        <div class="no-service-data" v-if="!serviceList.length">
          <img
            src="@/assets/images/nodata.png"
            alt=""
            style="width: 120px; height: 124px"
          />
          <div v-if="!isProvince">
            <el-button type="primary" @click="addService"
              >添加数据服务</el-button
            >
          </div>
        </div>
      </div>
    </div>
  </div>
  <addOrEditService v-if="addServiceVisible"></addOrEditService>
</template>
<script setup lang="ts">
import { ref, defineProps, watch, computed } from "vue";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
import { LEVEL_TYPE } from "@/utils/constant";
import { IDataServiceItem } from "@/types/handle";
import { getServiceListApi, deleteServiceApi } from "@/api/objHandle/manager";
import { deepClone } from "@/utils/auth";
import addOrEditService from "./addOrEditService.vue";

const store = useStore();
const userInfo = computed(() => store.getters.userInfo);
const isProvince = computed(
  () => userInfo.value.levelType === LEVEL_TYPE.PROVINCE
);

const props = defineProps({
  ids: {
    type: Array,
    default: () => [],
  },
  isView: {
    type: Boolean,
    default: false,
  },
});

const loading = ref(false);
const serviceList = ref<IDataServiceItem[]>([]);
const serviceCardList = ref<IDataServiceItem[]>([]);
const preSelectedService = ref<number[]>([]);
const selectedService = ref<number[]>([]);
const addServiceVisible = ref(false);

// 获取数据服务列表
function getServiceList() {
  return getServiceListApi().then((res: any) => {
    serviceList.value = res;
  });
}

// 编辑按钮
function handleEdit(id: number) {
  console.log("编辑");
}

// 设置不可删除方法
function deleteDisabled(id: number) {
  return props.ids.includes(id);
}

// 删除数据服务
function deleteDataService(id: number) {
  deleteServiceApi({ id }).then(() => {
    // form.dataService = form.dataService.filter((item: number) => item !== id);
    // choiseDataServiceList.value = choiseDataServiceList.value.filter(
    //   (item: DataServiceList) => item.id !== id
    // );
    getServiceList();
    ElMessage({
      message: "删除数据服务成功",
      type: "success",
    });
  });
}

// 选择现有数据服务
function handleChangeSelectedService() {
  // 选择现有服务器preFormDataService form.dataService dataServiceList
  if (!preSelectedService.value.length && !selectedService.value.length) {
    return;
  }
  let addItem = 0;
  let deleItem = 0;
  // 删除数据服务
  if (preSelectedService.value.length > selectedService.value.length) {
    deleItem = preSelectedService.value.filter(
      (item) => !selectedService.value.includes(item)
    )[0];
  }

  // 新增数据服务
  if (preSelectedService.value.length < selectedService.value.length) {
    addItem = selectedService.value.filter(
      (item: any) => !preSelectedService.value.includes(item)
    )[0];
  }

  if (deleItem) {
    let deleteItemIndex = -1;
    serviceCardList.value.forEach((item, index) => {
      if (`${item.id}` === `${deleItem}`) {
        deleteItemIndex = index;
      }
    });
    serviceCardList.value.splice(deleteItemIndex, 1);
  }

  if (addItem) {
    const addItemData = serviceCardList.value.filter(
      (item) => `${item.id}` === `${addItem}`
    );
    serviceCardList.value.push(addItemData[0]);
  }
  preSelectedService.value = deepClone(selectedService.value);
}

// 新增数据服务按钮
function addService() {
  addServiceVisible.value = true;
}

watch(
  () => props.ids,
  async () => {
    await getServiceList();
    serviceCardList.value = serviceList.value.filter((item) => {
      return props.ids && props.ids.includes(item.id);
    });
  },
  {
    deep: true,
    immediate: true,
  }
);
</script>
<style lang="scss" scoped>
.data-service-card {
  padding-bottom: 16px;
  margin-bottom: 24px;
  position: relative;
  .no-service-data {
    text-align: center;
  }
  .btn-position {
    position: absolute;
    top: 0;
    right: 0;
  }
  .service-list {
    overflow: auto;
    white-space: nowrap;
    padding: 0 8px;
    .service-item {
      width: 300px;
      border: 1px solid #f0f0f0;
      box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
      border-radius: 2px;
      display: inline-block;
      margin-right: 16px;
      &:last-child {
        margin-right: 0px;
      }
      .service-title {
        height: 56px;
        border-bottom: 1px solid #f0f0f0;
        padding: 16px 20px;
        .num {
          width: 22px;
          height: 24px;
          display: inline-block;
          background: #eef2f1;
          border-radius: 2px;
          vertical-align: middle;
          text-align: center;
          line-height: 24px;
          color: #7b9790;
          font-weight: bold;
          margin-right: 10px;
        }
        .name {
          color: #1a2233;
          font-weight: bold;
          vertical-align: middle;
        }
      }
      .service-body {
        height: 98px;
        border-bottom: 1px solid #f0f0f0;
        padding: 10px 20px;
        .body-item {
          display: flex;
          height: 20px;
          margin-bottom: 8px;
          span {
            align-self: center;
            font-size: 12px;
            color: #535f5c;
            &:first-child {
              width: 50px;
            }
            &:last-child {
              flex: 1;
            }
          }
        }
      }
      .service-btn {
        height: 44px;
        display: flex;
        > div {
          flex: 1;
          text-align: center;
          align-self: center;
          span {
            cursor: pointer;
            color: #0057fe;
          }
        }
      }
    }
  }
}
</style>
