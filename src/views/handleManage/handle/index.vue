<template>
  <div class="infoAlert" v-if="infoAlertShow">
    <svg-icon icon-class="handleUploadInfo" class="handleUploadInfo" />
    <span style="margin-left: 10px">批量新增标识正在上传校验中，您可在</span>
    <span class="handleRecordClass" @click="handleRecord">上传记录</span>
    <span>中查看进度</span>
    <div class="svgClose">
      <svg-icon
        icon-class="handleUploadClose"
        @click="infoAlertShow = false"
        class="handleUploadClose"
      />
    </div>
  </div>
  <div class="page-search" v-if="!recordVisible">
    <search-layout>
      <template #left v-if="isAppRole">
        <el-button
          type="primary"
          @click="handleAdd"
          v-permission="AUTH_CODE.HANDLE_REGISTER_ADD"
          >新增</el-button
        >
        <el-button
          type="primary"
          plain
          @click="handleBatch"
          v-permission="AUTH_CODE.HANDLE_REGISTER_BATCH_UPLOAD"
          >批量上传</el-button
        >
        <el-button
          type="primary"
          plain
          @click="handleRecord"
          v-permission="AUTH_CODE.HANDLE_REGISTER_BATCH_UPLOAD_RECORD"
          >上传记录</el-button
        >
      </template>
      <template #right>
        <el-form
          v-permission="[
            AUTH_CODE.HANDLE_REGISTER_PAGE,
            AUTH_CODE.HANDLE_WATCH_PAGE,
          ]"
          :inline="true"
          :model="searchForm"
          @submit.prevent
        >
          <el-form-item>
            <el-input
              v-model.trim="searchForm.searchKey"
              clearable
              @clear="handleSearch"
              placeholder="请输入标识名称"
            >
              <template #prefix>标识名称：</template></el-input
            >
          </el-form-item>
          <el-form-item
            ><el-input
              v-model.trim="searchForm.handle"
              clearable
              @clear="handleSearch"
              placeholder="请输入标识"
            >
              <template #prefix>标识：</template></el-input
            ></el-form-item
          >
          <el-form-item v-if="isProvince"
            ><el-input
              v-model="searchForm.orgName"
              placeholder="请输入企业"
              clearable
              @clear="handleSearch"
            >
              <template #prefix>企业名称：</template>
            </el-input></el-form-item
          >
          <el-form-item v-if="isEnterprise && !isAppRole"
            ><el-input
              v-model="searchForm.appName"
              placeholder="请输入应用"
              clearable
              @clear="handleSearch"
            >
              <template #prefix>应用名称：</template>
            </el-input></el-form-item
          >
          <el-form-item
            ><el-date-picker
              v-model="searchForm.maintainTime"
              type="daterange"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled-date="disabledDate"
              clearable
              @change="handleTimeClear()"
          /></el-form-item>
          <el-form-item style="width: 60px; max-width: 60px"
            ><el-button
              type="primary"
              :loading="data.searchLoading"
              @click="handleSearch"
              >搜索</el-button
            ></el-form-item
          >
        </el-form>
      </template>
    </search-layout>
  </div>
  <div class="page-search-body" v-if="!recordVisible">
    <el-table
      :data="data.tableData"
      v-loading="data.tableLoading"
      border
      size="small"
    >
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column property="name" label="标识名称" show-overflow-tooltip>
        <template #default="scope">
          <span v-copy="scope.row.name">{{ scope.row.name || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        property="handle"
        label="标识"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <span v-copy="scope.row.handle">{{ scope.row.handle || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        property="appName"
        label="所属应用"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span v-copy="scope.row.appName">{{ scope.row.appName || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        property="orgName"
        label="企业名称"
        show-overflow-tooltip
        v-if="isProvince"
      >
        <template #default="scope">
          <span v-copy="scope.row.orgName">{{ scope.row.orgName || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column property="updatedTime" label="操作时间" />
      <el-table-column property="reportName" label="操作人" v-if="isProvince">
        <template #default="scope">
          {{ scope.row.reportName || "-" }}
        </template>
      </el-table-column>
      <el-table-column property="updatedBy" label="操作人" v-if="!isProvince">
        <template #default="scope">
          {{ scope.row.updatedBy || "-" }}
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="isAppRole ? 200 : 100">
        <template #default="scope">
          <el-button
            type="primary"
            text
            @click="handleView(scope.row)"
            v-permission="[
              AUTH_CODE.HANDLE_REGISTER_DETAIL,
              AUTH_CODE.HANDLE_WATCH_DETAIL,
            ]"
            >详情</el-button
          >
          <el-button
            v-if="isAppRole"
            type="primary"
            text
            @click="handleEdit(scope.$index, scope.row)"
            v-permission="AUTH_CODE.HANDLE_REGISTER_EDIT"
            >编辑</el-button
          >
          <el-popconfirm
            v-if="isAppRole"
            :width="200"
            confirm-button-text="确定"
            cancel-button-text="取消"
            title="删除标识会导致标识解析失败，请谨慎删除，是否删除该标识？"
            @confirm="handleDeleteConfirm(scope.row.handle)"
          >
            <template #reference v-if="isAppRole">
              <el-button
                text
                type="primary"
                v-permission="AUTH_CODE.HANDLE_REGISTER_DELETE"
                >删除</el-button
              >
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      v-model:currentPage="data.page"
      v-model:page-size="data.size"
      :page-sizes="[10, 20, 30, 40]"
      small
      layout="total, sizes, prev, pager, next, jumper"
      :total="data.totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>

  <el-dialog
    class="handle-detail-dialog"
    v-model="data.dialogVisible"
    :title="data.dialogTitle"
    width="30%"
    fullscreen
    @close="handleCancel"
  >
    <handleDetail
      v-if="data.dialogVisible"
      ref="handleDetailRef"
      :handleData="data.selectHandleData"
      :disabled="data.dialogDisabled"
      :appList="data.appList"
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="data.dialogVisible = false">{{
          data.dialogDisabled ? "关闭" : "取消"
        }}</el-button>
        <el-button
          v-if="!data.dialogDisabled"
          type="primary"
          :disabled="data.dialogDisabled"
          :loading="data.confirmLoading"
          @click="handleConfirm"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
  <detailDrawer
    v-if="detailDrawerVisible"
    :id="data.selectHandleData.handle"
    @close="detailDrawerVisible = false"
  ></detailDrawer>
  <!-- 批量上传 -->
  <batchForm
    v-if="batchDialogFormVisible"
    :formData="batchFormData"
    :sucessThenQuery="sucessThenQuery"
  ></batchForm>
  <!-- 上传记录 -->
  <record v-if="recordVisible" @backToList="backToList"></record>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, formContextKey } from "element-plus";
import { useStore } from "vuex";
import { LEVEL_TYPE, ROLE_TYPE } from "@/utils/constant";
import {
  ApiGetHandleEnterprise,
  ApiGetHandlePageApp,
  ApiAddHandle,
  ApiDeleteHandle,
  ApiUpdateHandle,
  ApiGetHandleProvince,
  appList,
} from "@/api/objHandle/manager";
import handleDetail from "./components/handleDetail.vue";
import detailDrawer from "./components/detailDrawer.vue";
import { AUTH_CODE } from "@/utils/authCode";
import batchForm from "./components/batchForm.vue";
import record from "./components/record.vue";
import SearchLayout from "@/components/searchLayout/index.vue";

const store = useStore();
const userInfo = computed(() => store.getters.userInfo);

const infoAlertShow = ref(false);
const detailDrawerVisible = ref(false);
const batchDialogFormVisible = ref(false);
const batchFormData = reactive<any>({
  dialogVisible: batchDialogFormVisible,
});
const recordVisible = ref(false);

const isProvince = computed(
  () => userInfo.value.levelType === LEVEL_TYPE.PROVINCE
);

const isEnterprise = computed(
  () => userInfo.value.levelType === LEVEL_TYPE.PROXY
);

// id=3,为应用账号
const isAppRole = computed(() =>
  userInfo.value.roleInfos.some((item: any) => item.roleType === ROLE_TYPE.APP)
);

const disabledDate = (time: Date) => {
  return time.getTime() > new Date().getTime();
};

const handleDetailRef = ref();

const searchForm = reactive({
  searchKey: "",
  handle: "",
  maintainTime: [] as any,
  orgName: "",
  appId: "",
  appName: "",
});

interface Data {
  tableData: {
    appName: string;
    createdBy: string;
    createdTime: string;
    entityType: number;
    handle: string;
    id: number;
    name: string;
    type: number;
    reportName: string;
    updatedTime: string;
    uploadState: number;
  }[];
  appList: {
    id: number;
    appName: string;
  }[];
  page: number;
  size: number;
  totalCount: number;
  dialogTitle: string;
  dialogVisible: boolean;
  selectHandleData: any;
  dialogDisabled: boolean;
  deleteLoading: boolean;
  tableLoading: boolean;
  searchLoading: boolean;
  confirmLoading: boolean;
}
const data = reactive<Data>({
  tableData: [],
  appList: [],
  page: 1,
  size: 10,
  totalCount: 1,
  dialogTitle: "",
  dialogVisible: false,
  selectHandleData: null,
  dialogDisabled: false,
  deleteLoading: false,
  tableLoading: false,
  searchLoading: false,
  confirmLoading: false,
});

function getTableData() {
  data.tableLoading = true;
  data.tableData = [];
  const params = {
    name: searchForm.searchKey,
    handle: searchForm.handle,
    page: data.page - 1,
    size: data.size,
    startTime: "",
    endTime: "",
    orgName: searchForm.orgName,
    appId: searchForm.appId,
    appName: searchForm.appName,
  };
  if (searchForm.maintainTime && searchForm.maintainTime.length) {
    const [startTime, endTime] = searchForm.maintainTime;
    params.startTime = startTime;
    params.endTime = endTime.replace("00:00:00", "23:59:59");
  }

  if (isProvince.value) {
    ApiGetHandleProvince(params)
      .then((response: any) => {
        // const result = isProvince.value ? response : response?.pageVO;
        const result = response;
        data.tableData = result?.content || [];
        data.page = result?.pageNumber || 1;
        data.totalCount = result?.totalCount || 0;
      })
      .finally(() => {
        data.tableLoading = false;
        data.searchLoading = false;
      });
  } else if (isAppRole.value) {
    ApiGetHandlePageApp(params)
      .then((response: any) => {
        // const result = isProvince.value ? response : response?.pageVO;
        const result = response;
        data.tableData = result?.content || [];
        data.page = result?.pageNumber || 1;
        data.totalCount = result?.totalCount || 0;
      })
      .finally(() => {
        data.tableLoading = false;
        data.searchLoading = false;
      });
  } else if (isEnterprise.value) {
    ApiGetHandleEnterprise(params)
      .then((response: any) => {
        // const result = isProvince.value ? response : response?.pageVO;
        const result = response;
        data.tableData = result?.content || [];
        data.page = result?.pageNumber || 1;
        data.totalCount = result?.totalCount || 0;
      })
      .finally(() => {
        data.tableLoading = false;
        data.searchLoading = false;
      });
  }
}

// 详情按钮
function handleView(item: any) {
  data.selectHandleData = item;
  detailDrawerVisible.value = true;
}

// 清空操作时间，查询数据
function handleTimeClear() {
  if (!searchForm.maintainTime || searchForm.maintainTime.length === 0) {
    handleSearch();
  }
}

function handleSearch() {
  data.searchLoading = true;
  data.page = 1;
  getTableData();
}

function handleSizeChange(num: number) {
  data.size = num;
  data.page = 1;
  getTableData();
}

function handleCurrentChange(num: number) {
  data.page = num;
  getTableData();
}

function handleAdd() {
  data.dialogVisible = true;
  data.dialogTitle = "新增标识";
  data.selectHandleData = null;
  data.dialogDisabled = false;
}

async function handleConfirm() {
  const validateError = await handleDetailRef.value.validateData();
  if (validateError) return;

  data.confirmLoading = true;
  if (!data.selectHandleData) {
    const addParams = handleDetailRef.value.getAddData();
    ApiAddHandle(addParams)
      .then(() => {
        ElMessage.success("新增成功");
        data.dialogVisible = false;
        data.page = 1;
        getTableData();
      })
      .finally(() => {
        data.confirmLoading = false;
      });
    return;
  }
  const requestParams = handleDetailRef.value.getData();
  ApiUpdateHandle(requestParams)
    .then(() => {
      ElMessage.success("更新成功");
      data.dialogVisible = false;
      data.page = 1;
      getTableData();
    })
    .finally(() => {
      data.confirmLoading = false;
    });
}

function handleDeleteConfirm(handle: any) {
  data.deleteLoading = true;
  ApiDeleteHandle(handle)
    .then(() => {
      ElMessage.success("删除成功");
      data.page = 1;
      getTableData();
    })
    .finally(() => {
      data.deleteLoading = false;
    });
}

// 编辑按钮
function handleEdit(index: any, item: any) {
  data.selectHandleData = item;
  data.dialogVisible = true;
  data.dialogTitle = "编辑标识";
  data.dialogDisabled = false;
}

// 取消编辑
function handleCancel() {
  data.selectHandleData = {};
  data.dialogVisible = false;
}

// 获取应用列表下拉框
function getAppList() {
  appList().then((response: any) => {
    data.appList = Array.isArray(response) ? response : [];
  });
}

// 批量上传按钮
function handleBatch() {
  batchDialogFormVisible.value = true;
}

// 子组件操作成功查询列表
function sucessThenQuery() {
  data.page = 1;
  getTableData();
  infoAlertShow.value = true;
  batchDialogFormVisible.value = false;
  // 延时关闭提示
  setTimeout(function a() {
    infoAlertShow.value = false;
  }, 10000);
}

function handleRecord() {
  recordVisible.value = true;
}

function backToList() {
  recordVisible.value = false;
  handleSearch();
}
onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (
    AllPermission.includes(AUTH_CODE.HANDLE_REGISTER_PAGE) ||
    AllPermission.includes(AUTH_CODE.HANDLE_WATCH_PAGE)
  ) {
    getTableData();
    getAppList();
  }
});
</script>
<style lang="scss" scoped>
.popover-delete-tips {
  margin-bottom: 20px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .popover-delete-tips-text {
    margin-left: 10px;
    font-size: 14px;
  }
}
.popover-delete-wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
.infoAlert {
  display: flex;
  align-items: center;
  height: 30px;
  margin: -44px -44px 14px;
  background-color: rgb(232, 244, 255);
}
.handleUploadInfo {
  width: 20px;
  height: 20px;
  margin-left: 20px;
}

.handleRecordClass {
  margin-left: 4px;
  margin-right: 4px;
  color: rgb(0, 183, 255);
  cursor: pointer;
}
.svgClose {
  height: 100%;
  width: 25px;
  margin-left: auto;
  display: flex;
  align-items: center;
}
.handleUploadClose {
  width: 15px;
  height: 15px;
  cursor: pointer;
}
</style>
