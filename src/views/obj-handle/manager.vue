<template>
  <div class="page-search">
    <div class="page-search-header">
      <el-form :inline="true" :model="searchForm">
        <el-form-item>
          <el-input
            class="width200"
            v-model.trim="searchForm.searchKey"
            clearable
            placeholder="请输入对象标识名称"
          ></el-input>
        </el-form-item>
        <el-form-item v-if="!isProvince">
          <el-select
            class="width200"
            v-model="searchForm.appId"
            clearable
            placeholder="请选择应用"
            ><el-option
              v-for="item in data.appList"
              :key="item.id"
              :label="item.appName"
              :value="item.id"
          /></el-select>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            :loading="data.searchLoading"
            @click="handleSearch"
            >搜索</el-button
          >
          <el-button v-if="!isProvince" type="primary" @click="handleAdd"
            >新增</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <div class="page-search-body">
      <el-table
        :data="data.tableData"
        v-loading="data.tableLoading"
        border
        size="small"
      >
        <el-table-column label="序号" type="index" width="55" />
        <el-table-column
          property="name"
          label="对象标识名称"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span v-copy="scope.row.name">{{ scope.row.name || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          property="handle"
          label="对象标识"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span v-copy="scope.row.handle">{{ scope.row.handle || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          property="appName"
          label="所属应用"
          show-overflow-tooltip
          v-if="!isProvince"
        >
          <template #default="scope">
            <span v-copy="scope.row.appName">{{
              scope.row.appName || "-"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column property="updatedTime" label="修改时间" />
        <el-table-column property="updatedBy" label="修改人" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button type="primary" text @click="handleView(scope.row)"
              >查看</el-button
            >
            <el-button
              v-if="!isProvince"
              type="primary"
              text
              @click="handleEdit(scope.$index, scope.row)"
              >编辑</el-button
            >
            <el-popconfirm
              v-if="!isProvince"
              :width="200"
              confirm-button-text="确定"
              cancel-button-text="取消"
              title="该对象标识下可能已维护标识，删除后将同步删除标识信息，是否删除该对象标识？"
              @confirm="handleDeleteConfirm(scope.row.id)"
            >
              <template #reference>
                <el-button text type="primary">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        v-model:currentPage="data.page"
        v-model:page-size="data.size"
        :page-sizes="[10, 20, 30, 40]"
        small
        layout="total, sizes, prev, pager, next, jumper"
        :total="data.totalCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
  <el-dialog
    class="handle-detail-dialog"
    v-model="data.dialogVisible"
    :title="data.dialogTitle"
    width="30%"
    fullscreen
    @close="handleCancel"
  >
    <handleDetail
      v-if="data.dialogVisible"
      ref="handleDetailRef"
      :isProvince="isProvince"
      :handleData="data.selectHandleData"
      :disabled="data.dialogDisabled"
      :appList="data.appList"
    />
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="data.dialogVisible = false">{{
          data.dialogDisabled ? "关闭" : "取消"
        }}</el-button>
        <el-button
          v-if="!data.dialogDisabled"
          type="primary"
          :disabled="data.dialogDisabled"
          :loading="data.confirmLoading"
          @click="handleConfirm"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { ref, reactive, defineProps, watch, onMounted, computed } from "vue";
import { ElMessage } from "element-plus";
import {
  ApiGetHandlePage,
  ApiAddObjectHandle,
  ApiDeleteObjectHandle,
  ApiUpdateObjectHandle,
  ApiGetProvinceObjHandle,
  appList,
} from "@/api/objHandle/manager";
import handleDetail from "./components/handleDetail.vue";

const props = defineProps({
  type: String,
});

const isProvince = computed(() => props.type === "province");

const handleDetailRef = ref();

const searchForm = reactive({
  searchKey: "",
  appId: "",
});

interface Data {
  tableData: {
    appName: string;
    createdBy: string;
    createdTime: string;
    entityType: number;
    handle: string;
    id: number;
    name: string;
    type: number;
    updatedBy: string;
    updatedTime: string;
    uploadState: number;
  }[];
  appList: {
    id: number;
    appName: string;
  }[];
  page: number;
  size: number;
  totalCount: number;
  dialogTitle: string;
  dialogVisible: boolean;
  selectHandleData: any;
  dialogDisabled: boolean;
  deleteLoading: boolean;
  tableLoading: boolean;
  searchLoading: boolean;
  confirmLoading: boolean;
}
const data = reactive<Data>({
  tableData: [],
  appList: [],
  page: 1,
  size: 10,
  totalCount: 1,
  dialogTitle: "",
  dialogVisible: false,
  selectHandleData: null,
  dialogDisabled: false,
  deleteLoading: false,
  tableLoading: false,
  searchLoading: false,
  confirmLoading: false,
});

function getTableData() {
  data.tableLoading = true;
  data.tableData = [];
  const params = {
    name: searchForm.searchKey,
    appId: searchForm.appId,
    page: data.page - 1,
    size: data.size,
  };
  const promiseFn = isProvince.value
    ? ApiGetProvinceObjHandle(params)
    : ApiGetHandlePage(params);
  promiseFn
    .then((response: any) => {
      // const result = isProvince.value ? response : response?.pageVO;
      const result = response;
      data.tableData = result?.content || [];
      data.page = result?.pageNumber || 1;
      data.totalCount = result?.totalCount || 0;
    })
    .finally(() => {
      data.tableLoading = false;
      data.searchLoading = false;
    });
}

function handleSearch() {
  data.searchLoading = true;
  data.page = 1;
  getTableData();
}

function handleSizeChange(num: number) {
  data.size = num;
  data.page = 1;
  getTableData();
}

function handleCurrentChange(num: number) {
  data.page = num;
  getTableData();
}

function handleView(item: any) {
  data.selectHandleData = item;
  data.dialogVisible = true;
  data.dialogTitle = "查看对象标识";
  data.dialogDisabled = true;
}

function handleAdd() {
  data.dialogVisible = true;
  data.dialogTitle = "新增对象标识";
  data.selectHandleData = null;
  data.dialogDisabled = false;
}

async function handleConfirm() {
  const validateError = await handleDetailRef.value.validateData();
  if (validateError) return;
  const requestParams = handleDetailRef.value.getData();
  data.confirmLoading = true;
  if (!data.selectHandleData) {
    ApiAddObjectHandle(requestParams)
      .then(() => {
        ElMessage.success("新增成功");
        data.dialogVisible = false;
        data.page = 1;
        getTableData();
      })
      .finally(() => {
        data.confirmLoading = false;
      });
    return;
  }
  ApiUpdateObjectHandle(requestParams)
    .then(() => {
      ElMessage.success("更新成功");
      data.dialogVisible = false;
      data.page = 1;
      getTableData();
    })
    .finally(() => {
      data.confirmLoading = false;
    });
}

function handleDeleteConfirm(id: any) {
  data.deleteLoading = true;
  ApiDeleteObjectHandle(id)
    .then(() => {
      ElMessage.success("删除成功");
      data.page = 1;
      getTableData();
    })
    .finally(() => {
      data.deleteLoading = false;
    });
}

// 编辑按钮
function handleEdit(index: any, item: any) {
  data.selectHandleData = item;
  data.dialogVisible = true;
  data.dialogTitle = "编辑对象标识";
  data.dialogDisabled = false;
}

// 取消编辑
function handleCancel() {
  data.selectHandleData = {};
  data.dialogVisible = false;
}

// 获取应用列表下拉框
function getAppList() {
  appList().then((response: any) => {
    data.appList = Array.isArray(response) ? response : [];
  });
}

onMounted(() => {
  getTableData();
  getAppList();
});
</script>
<style lang="scss" scoped>
.popover-delete-tips {
  margin-bottom: 20px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .popover-delete-tips-text {
    margin-left: 10px;
    font-size: 14px;
  }
}
.popover-delete-wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
</style>
