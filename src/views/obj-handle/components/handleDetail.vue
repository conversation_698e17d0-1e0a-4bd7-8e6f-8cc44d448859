<template>
  <div class="handle-detail-wrap">
    <div class="section-title">
      <div class="section-title-line"></div>
      <span class="section-title-text"> 基本信息 </span>
    </div>
    <el-form
      :model="form"
      ref="formRef"
      label-width="120px"
      :disabled="disabled"
      :rules="rules"
      label-position="left"
    >
      <el-form-item
        class="form-item-handle"
        required
        label="对象标识TID"
        prop="handle"
      >
        <el-input
          :disabled="isEdit"
          v-model.trim="form.handle"
          placeholder="请输入"
        >
          <template #prepend>
            <el-select
              v-model="form.entPrefix"
              placeholder="请选择"
              :disabled="isEdit"
            >
              <el-option
                v-for="item in data.entPrefixList"
                :key="item"
                :value="item"
                :label="item"
              ></el-option>
            </el-select>
            <div class="form-item-handle-line">/</div>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="对象标识名称" prop="name">
        <el-input :disabled="isEdit" v-model.trim="form.name" />
      </el-form-item>
      <el-row>
        <el-form-item label="实体类型" prop="entityType">
          <el-select
            :disabled="isEdit"
            v-model="form.entityType"
            placeholder="请选择"
          >
            <el-option
              v-for="item in data.entityTypeList"
              :key="item.value"
              :value="item.value"
              :label="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="所属应用"
          prop="appId"
          label-width="80px"
          style="margin-left: 30px"
          v-if="!isProvince"
        >
          <el-select v-model="form.appId" placeholder="请选择">
            <el-option
              v-for="item in props.appList"
              :key="item.id"
              :value="item.id"
              :label="item.appName"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-row>
    </el-form>

    <div class="components-handle-detail-line"></div>
    <div class="dataService">
      <div class="btn-position" v-if="!isFirstAdd">
        <el-select
          v-model="form.dataService"
          multiple
          collapse-tags
          collapse-tags-tooltip
          size="small"
          placeholder="选择现有服务"
          @change="selectDataService"
        >
          <el-option
            v-for="item in dataServiceList"
            :key="item.id"
            :value="item.id"
            :label="item.dataServiceName"
            :disabled="deleteDisabled(item.id)"
          ></el-option>
        </el-select>
        <el-button
          type="primary"
          size="small"
          style="margin-left: 12px"
          @click="addServive"
          >添加新服务</el-button
        >
      </div>
      <div class="section-title">
        <div class="section-title-line"></div>
        <span class="section-title-text"> 数据服务 </span>
      </div>
      <div v-loading="serviceLoading">
        <div class="service-list" v-if="!isFirstAdd">
          <div
            class="service-item"
            v-for="(item, index) in choiseDataServiceList"
            :key="index"
          >
            <div class="service-title">
              <span class="num">{{ index + 1 }}</span>
              <span class="name">{{ item.dataServiceName }}</span>
            </div>
            <div class="service-body">
              <div class="body-item">
                <span>地址：</span>
                <span>{{ item.serviceAddress }}</span>
              </div>
              <div class="body-item">
                <span>Token：</span>
                <span>{{ item.serviceToken }}</span>
              </div>
            </div>
            <div class="service-btn">
              <div>
                <el-button type="primary" text @click="editDataService(item.id)"
                  >编辑</el-button
                >
              </div>
              <el-tooltip
                effect="dark"
                content="此服务正在使用中"
                placement="top"
                v-if="deleteDisabled(item.id)"
              >
                <div>
                  <el-button
                    type="primary"
                    text
                    @click="deleteDataService(item.id)"
                    :disabled="deleteDisabled(item.id)"
                    >删除</el-button
                  >
                </div>
              </el-tooltip>
              <div v-show="!deleteDisabled(item.id)">
                <el-button
                  type="primary"
                  text
                  @click="deleteDataService(item.id)"
                  >删除</el-button
                >
              </div>
            </div>
          </div>
        </div>
        <div class="no-service-data" v-if="isFirstAdd">
          <img
            src="../../../assets/images/nodata.png"
            alt=""
            style="width: 120px; height: 124px"
          />
          <div>
            <el-button type="primary" @click="addServive"
              >添加数据服务</el-button
            >
          </div>
        </div>
      </div>
    </div>

    <div class="components-handle-detail-line"></div>
    <div class="section-title">
      <div class="section-title-line"></div>
      <span class="section-title-text"> 基础属性 </span>
    </div>
    <el-button
      v-if="!disabled"
      class="mb10"
      type="primary"
      :disabled="disabled"
      @click="handleAdd"
      >新增基础属性</el-button
    >
    <el-table :data="data.tableData" border size="small">
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column
        label="中文名称"
        property="description"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span v-copy="scope.row.description">{{
            scope.row.description || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="英文名称" property="field" show-overflow-tooltip>
        <template #default="scope">
          <span v-copy="scope.row.field">{{ scope.row.field || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="数据源"
        property="dataSourceName"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span v-copy="scope.row.dataSourceName">{{
            scope.row.dataSourceName || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="字段类型" property="length">
        <template #default="scope">
          <div>{{ fieldTypeNameMap[scope.row.fieldType] }}</div>
        </template>
      </el-table-column>
      <el-table-column label="关联/对象标识" :show-overflow-tooltip="true">
        <template #default="scope">
          <span v-copy="getReferenceDis(scope.row)">{{
            getReferenceDis(scope.row)
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="140">
        <template #default="scope">
          <el-button
            size="small"
            type="primary"
            text
            @click="handleEdit(scope.$index, scope.row)"
            >{{ disabled ? "查看" : "编辑" }}</el-button
          >
          <el-popconfirm
            confirm-button-text="删除"
            cancel-button-text="取消"
            :icon="InfoFilled"
            title="是否删除该基础属性?"
            @confirm="handleDelete(scope.$index)"
          >
            <template #reference>
              <el-button
                v-if="!disabled"
                size="small"
                type="primary"
                text
                :disabled="disabled"
                >删除</el-button
              >
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog
      v-model="data.dialogVisible"
      :title="dialogTitle"
      append-to-body
      @close="handleDialogCancel"
      width="70%"
    >
      <relate-handle-form
        v-if="data.dialogVisible"
        ref="relateHandleFormRef"
        :disabled="disabled"
        :item="data.referenceData"
        :fields="fields"
        :tableFieldList="resetFieldList"
        :type="form.type"
        :dataService="choiseDataServiceList"
      ></relate-handle-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button
            v-if="data.dialogType !== dialogTypeMap.editor"
            type="primary"
            :disabled="disabled"
            @click="handleDialogConfirm('addNext')"
          >
            新增下一个
          </el-button>
          <el-button @click="data.dialogVisible = false">{{
            disabled ? "关闭" : "取消"
          }}</el-button>
          <el-button
            v-if="!disabled"
            type="primary"
            :disabled="disabled"
            @click="handleDialogConfirm"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
    <addOrEditService
      v-if="addServiceVisible"
      @update-service="addServiceList"
      @close-dialog="addServiceVisible = false"
      @addSuccess="addSuccess"
      :selectDataServiceId="selectDataServiceId"
      :isAddService="isAddService"
    ></addOrEditService>
  </div>
</template>
<script setup lang="ts">
import {
  reactive,
  computed,
  defineProps,
  ref,
  watch,
  defineExpose,
  PropType,
  onMounted,
} from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
import { InfoFilled } from "@element-plus/icons-vue";
import {
  ApiGetHandlePrefixDropDownList,
  ApiGetHandleDetail,
  ApiGetProvinceObjHandleDetail,
  getServiceListApi,
  deleteServiceApi,
} from "@/api/objHandle/manager";
import relateHandleForm from "./relateHandleForm.vue";
import addOrEditService from "./addOrEditService.vue";
import { deepClone } from "@/utils/auth";

const store = useStore();

const globalConfig = computed(() => store.getters.globalConfig);

const serviceLoading = ref(false);

const formRef = ref();

const relateHandleFormRef = ref();

const addServiceVisible = ref(false);

const isFirstAdd = ref(true);

interface AppList {
  id: number;
  appName: string;
}
const props = defineProps({
  isProvince: {
    type: Boolean,
    default: true,
  },
  handleData: {
    type: Object,
    default: () => null,
  },
  disabled: {
    type: Boolean,
    default: true,
  },
  appList: {
    type: Array as PropType<AppList[]>,
    default: () => [],
  },
});

const isEdit = computed(() => !!props.handleData);

const handleValidate = (rule: any, value: any, callback: any) => {
  if (!form.entPrefix || !form.handle) {
    return callback("请填写正确的对象标识TID");
  }
  callback();
};

const rules = reactive({
  handle: [{ validator: handleValidate, trigger: ["change", "blur"] }],
  name: [{ required: true, message: "请输入对象标识名称", trigger: "blur" }],
  type: [{ required: true, message: "请选择数据源类型", trigger: "blur" }],
  entityType: [{ required: true, message: "请选择实体类型", trigger: "blur" }],
  appId: [{ required: true, message: "请选择所属应用", trigger: "blur" }],
});

const dialogTypeMap = {
  add: 1,
  editor: 2,
};

const dialogTypeNameMap = {
  [dialogTypeMap.add]: "新增",
  [dialogTypeMap.editor]: "编辑",
};

const fieldTypeMap = {
  basic: 1,
  handle: 2,
  relate: 3,
};
const fieldTypeNameMap = {
  [fieldTypeMap.basic]: "字符串",
  [fieldTypeMap.handle]: "标识属性",
  [fieldTypeMap.relate]: "关联属性",
};

const typeMap = reactive({
  local: 1,
  business: 2,
  database: 3,
});

const data = reactive<any>({
  entPrefixList: [],
  entityTypeList: [
    { value: 1, name: "业务实体" },
    { value: 2, name: "资源实体" },
  ],
  tableData: [{}],
  tableFieldList: [],
  optionalFieldList: [],
  referenceIndex: 0,
  referenceData: {},
  dialogVisible: false,
  dataConnect: false,
  dialogType: dialogTypeMap.add,
});

const form = reactive<any>({
  entPrefix: "", // 企业前缀
  handle: "",
  name: "",
  type: 1, // 1:本地资源实体 2:业务资源实体
  entityType: 1,
  appId: "",
  dataService: [],
});

interface DataServiceList {
  disabled?: boolean;
  id: number;
  dataServiceName: string;
  serviceAddress: string;
  serviceToken: string;
}
const dataServiceList = ref<DataServiceList[]>([]);
const selectDataServiceId = ref<number>();
const isAddService = ref(true);

const choiseDataServiceList = ref<DataServiceList[]>([]);
const preFormDataService = ref<any[]>([]);

const dialogTitle = computed(() => {
  if (props.disabled) return "查看";
  return dialogTypeNameMap[data.dialogType];
});

const fields = computed(() => {
  return data.tableData.map((item: any) => item.field);
});

const tableDataSource = computed(() => {
  return data.tableData.map((item: any) => item.dataServiceId);
});

const resetFieldList = computed(() => {
  data.tableData.forEach((tableItem: any) => {
    data.tableFieldList.push(tableItem.field);
  });
  return data.tableFieldList;
});
// 增加基础属性
function handleAdd() {
  data.referenceData = {};
  data.dialogType = dialogTypeMap.add;
  data.dialogVisible = true;
}

// 编辑基础属性
function handleEdit(index: any, reference: any) {
  data.dialogType = dialogTypeMap.editor;
  data.referenceIndex = index;
  data.referenceData = deepClone(reference);
  data.dialogVisible = true;
}

// 删除基础属性
function handleDelete(index: number) {
  data.tableData.splice(index, 1);
}

// 取消新增/编辑关联属性
function handleDialogCancel() {
  data.dialogVisible = false;
  data.referenceData = {};
  data.tableFieldList = [];
}

async function handleDialogConfirm(addNext: string) {
  const validateError = await relateHandleFormRef.value.validateData();
  if (validateError) return;
  const formData = relateHandleFormRef.value.getData();
  data.tableFieldList = [];
  // 关联
  if (formData.fieldType === 3) {
    formData.references.forEach((item: any) => {
      if (item.paramProp === "实例标识") {
        item.paramProp = "";
      }
    });
  }
  data.dialogVisible = false;
  if (addNext === "addNext") {
    // 新增下一个
    data.referenceData = {};
    data.dialogType = dialogTypeMap.add;
    data.dialogVisible = true;
  }
  if (data.dialogType === dialogTypeMap.add) {
    data.tableData.push(formData);
    return;
  }
  data.tableData[data.referenceIndex] = deepClone(formData);
}

function getEntPrefixList() {
  ApiGetHandlePrefixDropDownList().then((response: any) => {
    data.entPrefixList = Array.isArray(response) ? response : [];
  });
}
onMounted(() => {
  getEntPrefixList();
});

function getHandleData(str: string, entPrefix: any) {
  const handleStr = str.split(entPrefix);
  return handleStr.length > 1
    ? handleStr[handleStr.length - 1].substring(1)
    : "";
}

function getHandleDetail() {
  const requestParams = {
    id: props && props.handleData && props.handleData.id,
    type: 1, // 前端写死1，获取所有详情
  };
  const promiseFn = props.isProvince
    ? ApiGetProvinceObjHandleDetail(requestParams)
    : ApiGetHandleDetail(requestParams);
  promiseFn.then(async (response: any) => {
    const constFields = ["entPrefix", "name", "type", "entityType", "appId"];
    constFields.forEach((field) => {
      form[field] = response[field];
    });
    form.handle = getHandleData(response.handle, response.entPrefix);
    data.tableData = response.items;
    form.dataService = response.services; // 服务数据
    await getServiceList();
    // dataServiceList 所有的数据服务
    choiseDataServiceList.value = [];
    choiseDataServiceList.value = dataServiceList.value.filter((item) =>
      form.dataService.includes(item.id)
    );
    preFormDataService.value = deepClone(form.dataService);
  });
}

function initData() {
  const constFields = ["entPrefix", "name", "handle"];
  constFields.forEach((field) => {
    form[field] = "";
  });
  form.type = 1;
  data.tableData = [];
  dataServiceList.value = [];
}

// 设置不可删除方法
function deleteDisabled(id: number) {
  if (tableDataSource.value.includes(id)) {
    return true;
  }
  return false;
}

// 获取数据服务列表
async function getServiceList() {
  serviceLoading.value = true;
  return getServiceListApi()
    .then((res: any) => {
      dataServiceList.value = res;
      if (dataServiceList.value.length === 0) {
        isFirstAdd.value = true;
      } else {
        isFirstAdd.value = false;
      }
    })
    .finally(() => {
      serviceLoading.value = false;
    });
}

function addSuccess(service: any, isAdd: boolean) {
  if (isAdd) {
    choiseDataServiceList.value.push(service);
    form.dataService.push(service.id);
  } else {
    choiseDataServiceList.value.find((item) => {
      if (item.id === service.id) {
        item.dataServiceName = service.dataServiceName;
      }
    });
  }
}

function addServiceList() {
  getServiceList();
}

// 选择现有服务器preFormDataService form.dataService dataServiceList
function selectDataService() {
  if (!preFormDataService.value.length && !form.dataService.length) {
    return;
  }
  let addItem = "";
  let deleItem = "";
  // 删除数据服务
  if (preFormDataService.value.length > form.dataService.length) {
    deleItem = preFormDataService.value.filter(
      (item) => !form.dataService.includes(item)
    )[0];
  }

  // 新增数据服务
  if (preFormDataService.value.length < form.dataService.length) {
    addItem = form.dataService.filter(
      (item: any) => !preFormDataService.value.includes(item)
    )[0];
  }

  if (deleItem) {
    let deleteItemIndex = -1;
    choiseDataServiceList.value.forEach((item, index) => {
      if (`${item.id}` === `${deleItem}`) {
        deleteItemIndex = index;
      }
    });
    choiseDataServiceList.value.splice(deleteItemIndex, 1);
  }

  if (addItem) {
    const addItemData = dataServiceList.value.filter(
      (item) => `${item.id}` === `${addItem}`
    );
    choiseDataServiceList.value.push(addItemData[0]);
  }
  preFormDataService.value = deepClone(form.dataService);
}

function addServive() {
  addServiceVisible.value = true;
  isAddService.value = true;
}

function editDataService(id: number) {
  addServiceVisible.value = true;
  selectDataServiceId.value = id;
  isAddService.value = false;
}

function deleteDataService(id: number) {
  deleteServiceApi({ id }).then(() => {
    form.dataService = form.dataService.filter((item: number) => item !== id);
    choiseDataServiceList.value = choiseDataServiceList.value.filter(
      (item: DataServiceList) => item.id !== id
    );
    getServiceList();
    ElMessage({
      message: "删除数据服务成功",
      type: "success",
    });
  });
}

watch(
  props,
  (val) => {
    if (val.handleData) {
      getHandleDetail();
      return;
    }
    initData();
  },
  {
    immediate: true,
    deep: true,
  }
);

function getData() {
  return {
    id: props.handleData?.id,
    entPrefix: form.entPrefix, // 企业前缀
    handle: `${form.entPrefix}/${form.handle}`,
    name: form.name,
    type: form.type, // 1:本地资源实体 2:业务资源实体
    items: data.tableData,
    entityType: form.entityType,
    appId: form.appId,
    services: form.dataService,
  };
}

async function validateData() {
  let isError = false;
  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  if (!data.tableData.length) {
    ElMessage.error("基础属性不能为空");
    isError = true;
  }
  return isError;
}

function getReferenceDis(data: any) {
  if (!data.references || !data.references.length) {
    return "-";
  }
  return data.references
    .map((reference: any) => reference.referenceObjectHandle)
    .join(",");
}

defineExpose({
  getData,
  validateData,
});
</script>
<style lang="scss" scoped>
.handle-detail-wrap {
  padding: 0 40px;
}
.mb10 {
  margin-bottom: 10px;
}
.components-handle-detail-line {
  border-top: 1px solid #e5e8ef;
  margin-bottom: 24px;
}

.handle-detail-line {
  padding: 20px 0 0 0;
  border-top: 1px solid #e5e8ef;
  // border-bottom: 1px solid #e5e8ef;
  margin-bottom: 20px;
}

.form-item-handle {
  :deep(.el-input-group__prepend) {
    padding-right: 0;
  }
  .form-item-handle-line {
    border-left: 1px solid #dcdfe6;
    padding: 0 10px;
    margin-left: 20px;
  }
}

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  background-color: #f5f5f5;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
  border-radius: 10px;
  background-color: #f5f5f5;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 10px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.3);
  background-color: #ccc;
}
.dataService {
  padding-bottom: 16px;
  margin-bottom: 24px;
  position: relative;
  .no-service-data {
    text-align: center;
  }
  .btn-position {
    position: absolute;
    top: 0;
    right: 0;
  }
  .service-list {
    height: 220px;
    overflow: auto;
    white-space: nowrap;
    padding: 0 8px;
    .service-item {
      width: 300px;
      height: 200px;
      border: 1px solid #f0f0f0;
      box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
      border-radius: 2px;
      display: inline-block;
      margin-right: 16px;
      &:last-child {
        margin-right: 0px;
      }
      .service-title {
        height: 56px;
        border-bottom: 1px solid #f0f0f0;
        padding: 16px 20px;
        .num {
          width: 22px;
          height: 24px;
          display: inline-block;
          background: #eef2f1;
          border-radius: 2px;
          vertical-align: middle;
          text-align: center;
          line-height: 24px;
          color: #7b9790;
          font-weight: bold;
          margin-right: 10px;
        }
        .name {
          color: #1a2233;
          font-weight: bold;
          vertical-align: middle;
        }
      }
      .service-body {
        height: 98px;
        border-bottom: 1px solid #f0f0f0;
        padding: 10px 20px;
        .body-item {
          display: flex;
          height: 20px;
          margin-bottom: 8px;
          span {
            align-self: center;
            font-size: 12px;
            color: #535f5c;
            &:first-child {
              width: 50px;
            }
            &:last-child {
              flex: 1;
            }
          }
        }
      }
      .service-btn {
        height: 44px;
        display: flex;
        > div {
          flex: 1;
          text-align: center;
          align-self: center;
          span {
            cursor: pointer;
            color: #0057fe;
          }
        }
      }
    }
  }
}
</style>
