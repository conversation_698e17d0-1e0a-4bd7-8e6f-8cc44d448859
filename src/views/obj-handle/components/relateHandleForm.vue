<template>
  <el-form
    ref="formRef"
    label-position="left"
    :model="form"
    label-width="100px"
    :disabled="disabled"
    :rules="rules"
  >
    <!-- <el-form-item label="是否唯一" prop="uniqueness">
      <el-radio-group v-model="form.uniqueness">
        <el-radio :label="uniquenessMap.Yes">是</el-radio>
        <el-radio :label="uniquenessMap.No">否</el-radio>
      </el-radio-group>
    </el-form-item> -->
    <el-form-item label="字段类型" prop="fieldType">
      <el-select v-model="form.fieldType" placeholder="请选择">
        <el-option
          v-for="item in data.fieldTypeList"
          :key="item.value"
          :value="item.value"
          :label="item.name"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-row
      v-if="
        form.fieldType === fieldTypeMap.basic ||
        form.fieldType === fieldTypeMap.handle
      "
    >
      <el-form-item label="数据源" prop="dataService">
        <el-select
          v-model="form.dataService"
          placeholder="请选择"
          @change="getDataSource"
        >
          <el-option
            v-for="item in props.dataService"
            :key="item.id"
            :value="item.id"
            :label="item.dataServiceName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="dataSource" label-width="12px">
        <el-select
          v-model="form.dataSource"
          placeholder="请选择"
          @change="selectChange"
        >
          <el-option
            v-for="item in data.dataSource"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-row>
    <el-form-item label="英文名称" prop="field">
      <el-select
        v-model="form.field"
        allow-create
        clearable
        filterable
        placeholder="请选择"
      >
        <el-option
          v-for="item in optionalFieldList"
          :key="item"
          :value="item"
          :label="item"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="中文名称" prop="description">
      <el-input v-model.trim="form.description" />
    </el-form-item>
    <el-row v-if="form.fieldType === fieldTypeMap.handle">
      <el-form-item label="对象标识" prop="itemHandle">
        <el-input style="width: 220px" v-model.trim="form.itemHandle" />
      </el-form-item>
      <el-button
        type="primary"
        style="margin-left: 20px"
        @click="resolveHandle(form.itemHandle)"
        :disabled="form.itemHandle == '' ? true : false"
        >解析</el-button
      >
    </el-row>
    <el-form-item
      v-if="form.fieldType === fieldTypeMap.relate"
      label="关联对象"
      prop="references"
    >
      <el-button class="mb10" type="primary" @click="handleReferenceAdd"
        >新增关联对象</el-button
      >
      <el-table :data="form.references" border size="small">
        <el-table-column label="关联对象标识">
          <template #default="scope">
            <el-input
              v-model.trim="scope.row.referenceObjectHandle"
              @input="scope.row.queryProp = ''"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column label="映射关系" width="500">
          <template #header>
            <span>映射关系</span>
            <el-tooltip
              style="width: 182px"
              effect="dark"
              placement="top-start"
            >
              <template #content>
                通过建立关联对象标识属性和<br />被关联对象标识属性间的映射<br />关系，实现关联标识和被关联
                <br />标识的解析、查询</template
              >
              <span
                ><el-icon size="18" style="vertical-align: text-bottom"
                  ><Warning /></el-icon
              ></span>
            </el-tooltip>
          </template>
          <template #default="scope">
            <el-row>
              <el-select
                v-loading="loading"
                @click="getProperty(scope.row.referenceObjectHandle)"
                :popper-append-to-body="false"
                v-model="scope.row.queryProp"
                placeholder="请选择"
                :no-data-text="
                  scope.row.referenceObjectHandle
                    ? '无数据'
                    : '请输入关联对象标识'
                "
              >
                <el-option
                  v-for="item in data.propertyList"
                  :key="item.fieldIndex"
                  :value="item.field"
                  :label="item.field"
                ></el-option>
              </el-select>
              <el-select
                v-model="scope.row.paramProp"
                placeholder="请选择"
                style="margin-left: 12px"
              >
                <el-option
                  v-for="(item, index) in fieldList"
                  :key="index"
                  :value="item"
                  :label="item"
                ></el-option>
              </el-select>
            </el-row>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100px">
          <template #default="scope">
            <el-popconfirm
              confirm-button-text="删除"
              cancel-button-text="取消"
              :icon="InfoFilled"
              title="是否执行删除操作?"
              @confirm="handleReferenceDelete(scope.$index)"
            >
              <template #reference>
                <el-button type="primary" text>删除</el-button>
              </template>
            </el-popconfirm>
            <el-form style="display: inline; margin-left: 12px">
              <el-button
                type="primary"
                text
                @click="resolveHandle(scope.row.referenceObjectHandle)"
                >解析</el-button
              >
            </el-form>
          </template>
        </el-table-column>
      </el-table>
    </el-form-item>
  </el-form>
  <el-dialog
    v-model="data.dialogResolve"
    title="解析结果"
    append-to-body
    width="800"
    @close="data.dialogResolve = false"
  >
    <div class="relate-handle-matrix-graph">
      <matrix-graph ref="matrixGraphRef"></matrix-graph>
    </div>
    <template #footer>
      <el-button type="primary" @click="data.dialogResolve = false">
        确定
      </el-button>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import {
  reactive,
  defineProps,
  watch,
  defineExpose,
  ref,
  computed,
  nextTick,
  PropType,
  onMounted,
} from "vue";
import { ElMessage } from "element-plus";
import { InfoFilled } from "@element-plus/icons-vue";
import { deepClone } from "@/utils/auth";
import { idResolve } from "@/api/idRes/index";
import matrixGraph from "@/views/idRes/components/matrix-graph.vue";
import { getDataSourceApi, getFieldListApi } from "@/api/objHandle/manager";

const formRef = ref();
const matrixGraphRef = ref();
const loading = ref(false);

interface DataService {
  id: number;
  dataServiceName: string;
  serviceAddress: string;
  serviceToken: string;
}
const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
  fields: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  disabled: Boolean,
  type: Number,
  tableFieldList: {
    type: Array as PropType<{ value: string }[]>,
    default: () => [],
  },
  dataService: {
    type: Array as PropType<DataService[]>,
    default: () => [],
  },
});

const fieldList = computed(() => {
  return ["实例标识"].concat([...props.fields]);
});

const fieldTypeMap = {
  basic: 1,
  handle: 2,
  relate: 3,
};

interface PropertyList {
  fieldIndex: number;
  field: string;
}
interface FieldTypeList {
  value: number;
  name: string;
}
interface DataSource {
  id: number;
  name: string;
}
const data: {
  propertyList: PropertyList[];
  dialogResolve: boolean;
  fieldTypeList: FieldTypeList[];
  dataSource: DataSource[];
} = reactive({
  fieldTypeList: [
    { value: 1, name: "字符串" },
    { value: 2, name: "标识属性" },
    { value: 3, name: "关联属性" },
  ],
  dialogResolve: false,
  propertyList: [],
  dataSource: [],
});

interface Form {
  field: string;
  description: string;
  fieldType: number | null;
  references: {
    referenceObjectHandle: string;
    queryProp: string;
    paramProp: string;
  }[];
  auth: number[];
  itemHandle: string;
  dataService: number | null;
  dataSource: number | null;
  dataSourceName: string | null;
}
const form = reactive<Form>({
  field: "",
  description: "",
  fieldType: null,
  references: [],
  auth: [],
  itemHandle: "",
  dataService: null,
  dataSource: null,
  dataSourceName: "",
});
const formFieldList = ref<string[]>([]);
const optionalFieldList = ref<string[]>([]);
const preData: any = computed(() => deepClone(props.item));

const handleValidate = (rule: any, value: any, callback: any) => {
  if (!form.references.length) {
    return callback("内容需填写完整");
  }
  callback();
};

const fieldValidate = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback("请填写英文名称");
  }
  const reg = /^[a-zA-Z\d_]+$/;
  if (!reg.test(value)) {
    return callback("英文名称格式错误，请重新填写");
  }
  if (
    form.field !== preData.value.field &&
    fieldList.value.includes(form.field)
  ) {
    return callback("英文名称已存在，请重新填写");
  }
  callback();
};

const rules = reactive({
  field: [
    { required: true, validator: fieldValidate, trigger: "blur" },
    { max: 30, message: "最大长度为30字符", trigger: "blur" },
  ],
  dataService: [
    { required: true, message: "请选择数据服务", trigger: "change" },
  ],
  dataSource: [{ required: true, message: "请选择数据源", trigger: "change" }],
  auth: [
    {
      required: true,
      type: "array",
      message: "请选择权限",
      trigger: "change",
    },
  ],
  description: [
    { required: true, message: "请填写中文名称", trigger: "blur" },
    { max: 30, message: "最大长度为30字符", trigger: "blur" },
  ],
  fieldType: [{ required: true, message: "请选择字段类型", trigger: "blur" }],
  references: { required: true, validator: handleValidate },
  itemHandle: { required: true, message: "请输入对象标识", trigger: "change" },
});

watch(
  props,
  (val) => {
    const tempData: any = deepClone(val);
    form.field = tempData.item?.field || "";
    form.description = tempData.item?.description || "";
    form.itemHandle = tempData.item?.itemHandle || "";
    form.fieldType = tempData.item?.fieldType || 1;
    form.references =
      (tempData.item?.references && deepClone(tempData.item.references)) || [];
    form.references.forEach((referenceTemp: any) => {
      if (referenceTemp.paramProp === "") {
        referenceTemp.paramProp = "实例标识";
      }
    });
    form.dataService = tempData.item?.dataServiceId || null;
    form.dataSource = tempData.item?.dataSourceId || null;
    if (tempData.item?.dataSourceId) {
      getFieldList(tempData.item?.dataServiceId, tempData.item.dataSourceId);
    }
    form.dataSourceName = tempData.item?.dataServiceName;
  },
  {
    immediate: true,
    deep: true,
  }
);

function handleReferenceAdd() {
  form.references.push({
    referenceObjectHandle: "",
    queryProp: "",
    paramProp: "",
  });
}

function selectChange() {
  getFieldList(form.dataService, form.dataSource);
  resetDataSource();
}

function resetDataSource() {
  let dataSourceName = "";
  let dataServiceName = "";
  props.dataService.forEach((item) => {
    if (item.id === form.dataService) {
      dataServiceName = item.dataServiceName;
    }
  });
  data.dataSource.forEach((item) => {
    if (item.id === form.dataSource) {
      dataSourceName = item.name;
    }
  });
  form.dataSourceName = dataServiceName + " - " + dataSourceName;
}
// 获取英文名并去重
function getFieldList(
  dataServiceId: number | null,
  dataSourceId: number | null
) {
  getFieldListApi({
    dataServiceId,
    dataSourceId,
  }).then((res: any) => {
    formFieldList.value = res;
    const copydata = deepClone(props.tableFieldList);
    optionalFieldList.value = formFieldList.value.filter(
      (item: any) => !copydata.includes(item)
    );
  });
}
// 获取查询属性
function getProperty(handleVal: any) {
  if (handleVal) {
    loading.value = true;
    idResolve({ handle: handleVal })
      .then((response: any) => {
        data.propertyList = response.values;
        loading.value = false;
      })
      .catch(() => {
        data.propertyList = [];
        loading.value = false;
      });
  } else {
    data.propertyList = [];
    loading.value = false;
  }
}

function resolveHandle(id: any) {
  if (id) {
    idResolve({ handle: id }).then((response: any) => {
      data.dialogResolve = true;
      nextTick(() => {
        matrixGraphRef.value.refresh(response);
      });
    });
  } else {
    ElMessage({
      message: "请输入标识",
      type: "error",
    });
  }
}

function handleReferenceDelete(index: any) {
  form.references.splice(index, 1);
}

async function validateData() {
  let isError = false;
  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  return isError;
}

function getData() {
  return {
    ...form,
    field: form.field,
    description: form.description,
    itemHandle: form.itemHandle,
    fieldType: form.fieldType,
    references: form.references,
    dataServiceId: form.dataService,
    dataSourceId: form.dataSource,
    dataSourceName: form.dataSourceName,
  };
}

function getDataSource() {
  data.dataSource = [];
  getDataSourceApi({ id: form.dataService }).then((res: any) => {
    data.dataSource = res;
  });
}

onMounted(() => {
  if (form.dataService) {
    getDataSource();
  }
});

defineExpose({
  form,
  validateData,
  getData,
});
</script>
<style lang="scss" scoped>
.mb10 {
  margin-bottom: 10px;
}
.mb11 {
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 5px;
  color: #929292;
}
.mb12 {
  float: right;
}
.mb13 {
  margin-top: 5px;
  letter-spacing: 0.5px;
  // text-align: center;
  margin: 0 30px;
  > div {
    display: flex;
    align-items: baseline;
    span {
      flex: 1;
      &:first-child {
        text-align: right;
      }
    }
  }
}
.mb14 {
  margin-left: 10px;
}
.mb15 {
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 8px;
  letter-spacing: 0.5px;
  // text-align: center;
  margin: 40px 30px 10px;
  > div {
    display: flex;
    align-items: baseline;
    span {
      flex: 1;
      &:first-child {
        text-align: right;
      }
    }
  }
}
:deep(.el-select-dropdown__empty) {
  padding: 10px 10px !important;
}

.relate-handle-matrix-graph {
  height: 600px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
