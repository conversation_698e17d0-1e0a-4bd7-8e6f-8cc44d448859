<template>
  <el-dialog
    v-model="dialogFormVisible"
    title="新增数据服务"
    width="700px"
    @close="$emit('close-dialog')"
    destroy-on-close
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="110px">
      <el-form-item label="数据服务名称" prop="dataServiceName">
        <el-input
          v-model.trim="formData.dataServiceName"
          placeholder="请输入数据服务名称"
        />
      </el-form-item>
      <el-form-item label="地址">
        <el-input
          v-model.trim="formData.serviceAddress"
          placeholder="请输入数据服务地址"
        >
          <template #prepend>
            <el-select
              v-model="formData.type"
              placeholder="请选择"
              style="width: 98px"
            >
              <el-option
                v-for="item in httpType"
                :key="item"
                :value="item"
                :label="item"
              ></el-option>
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="token" prop="serviceToken">
        <el-input
          v-model.trim="formData.serviceToken"
          placeholder="请输入接口访问令牌"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="validForm(formRef)"
          v-loading="btnLoading"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import {
  addServiceApi,
  editServiceApi,
  getServiceDetailApi,
} from "@/api/objHandle/manager";

const emit = defineEmits(["update-service", "addSuccess"]);
const props = defineProps({
  selectDataServiceId: {
    type: Number,
    default: null,
  },
  isAddService: {
    type: Boolean,
    default: true,
  },
});
const btnLoading = ref(false);
const dialogFormVisible = ref(true);
const httpType = ref(["http://", "https://"]);

const formRef = ref<FormInstance>();
const formData = ref({
  id: null,
  dataServiceName: "",
  serviceAddress: "",
  serviceToken: "",
  type: "http://",
});
const serviceAddressValidate = (rule: any, value: any, callback: any) => {
  const reg =
    /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?):([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])$/;
  if (!value) {
    return callback("请填写服务器地址");
  }
  if (!reg.test(value)) {
    return callback("服务地址格式错误，请重新填写");
  }
  callback();
};
const rules = reactive({
  dataServiceName: [
    { required: true, message: "请输入数据服务名称", trigger: "blur" },
    { max: 20, message: "最大长度不超过20字符", trigger: "blur" },
  ],
  serviceAddress: [{ validator: serviceAddressValidate, trigger: "blur" }],
  serviceToken: [
    { required: true, message: "请输入数据服务token", trigger: "blur" },
    { max: 50, message: "最大长度不超过50字符", trigger: "blur" },
  ],
});

const validForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid) => {
    if (valid) {
      addOrEditService();
    }
  });
};

const addOrEditService = () => {
  const api = props.isAddService ? addServiceApi : editServiceApi;
  btnLoading.value = true;
  api({
    id: formData.value.id,
    dataServiceName: formData.value.dataServiceName,
    serviceAddress: formData.value.serviceAddress
      ? formData.value.type + formData.value.serviceAddress
      : "",
    serviceToken: formData.value.serviceToken,
  })
    .then((res) => {
      dialogFormVisible.value = false;
      emit("update-service");
      emit("addSuccess", res, props.isAddService);
      ElMessage({
        message: `${props.isAddService ? "新增" : "编辑"}数据服务成功!`,
        type: "success",
      });
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

const getServiceDetail = () => {
  getServiceDetailApi({ id: props.selectDataServiceId }).then((res: any) => {
    formData.value = res || {};
  });
};

onMounted(() => {
  if (!props.isAddService) {
    getServiceDetail();
  }
});
</script>

<style lang="scss"></style>
