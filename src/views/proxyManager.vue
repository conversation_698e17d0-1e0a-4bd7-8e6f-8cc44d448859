<template>
  <SearchLayout>
    <template #left
      ><el-button
        type="primary"
        @click="handleManager(true)"
        v-permission="[
          AUTH_CODE.HOSTING_CONFIG_ADD,
          AUTH_CODE.CONFIG_MANAGER_ADD,
        ]"
        >新增</el-button
      ></template
    >
    <template #right>
      <el-form
        v-permission="[
          AUTH_CODE.HOSTING_CONFIG_PAGE,
          AUTH_CODE.CONFIG_MANAGER_PAGE,
        ]"
        ref="queryRef"
        :model="queryForm"
        :inline="true"
        @submit.prevent
      >
        <el-form-item style="width: 240px; max-width: 240px">
          <el-input
            v-model.trim="queryForm.srvName"
            placeholder="请输入"
            clearable
            @clear="getTableData(true)"
          >
            <template #prefix>{{ titleName }}：</template>
          </el-input>
        </el-form-item>
        <el-form-item style="width: 60px; max-width: 60px">
          <el-button type="primary" @click="() => getTableData(true)"
            >搜索</el-button
          >
        </el-form-item>
      </el-form></template
    >
  </SearchLayout>
  <div class="manegerTable">
    <el-table
      :data="tableData"
      style="width: 100%"
      size="small"
      border
      v-loading="pageLoading"
    >
      <el-table-column
        :label="titleName"
        class-name="table-column-style"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span v-copy="scope.row.srvName">{{ scope.row.srvName || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="内网" width="400px" align="center">
        <el-table-column label="IP类型">
          <template #default="scope">
            <span v-if="scope.row.ipType">{{
              scope.row.ipType === 4
                ? "IPv4"
                : scope.row.ipType === 6
                ? "IPv6"
                : "-"
            }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="IP地址" show-overflow-tooltip>
          <template #default="scope">
            <span v-copy="scope.row.ip">{{ scope.row.ip || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="http端口">
          <template #default="scope">
            <span>{{ scope.row.httpPort ? scope.row.httpPort : "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="tcp端口">
          <template #default="scope">
            <span>{{ scope.row.tcpPort ? scope.row.tcpPort : "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="udp端口" class-name="table-column-style">
          <template #default="scope">
            <span>{{ scope.row.udpPort ? scope.row.udpPort : "-" }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="外网" width="400px" align="center">
        <el-table-column prop="ipTypeReslove" label="IP类型">
          <template #default="scope">
            <span v-if="scope.row.ipTypeReslove">{{
              scope.row.ipTypeReslove === 4 ? "IPv4" : "IPv6"
            }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="IP地址" show-overflow-tooltip>
          <template #default="scope">
            <span v-copy="scope.row.ipReslove">{{
              scope.row.ipReslove || "-"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="http端口">
          <template #default="scope">
            <span>{{
              scope.row.httpPortReslove ? scope.row.httpPortReslove : "-"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="tcp端口">
          <template #default="scope">
            <span>{{
              scope.row.tcpPortReslove ? scope.row.tcpPortReslove : "-"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="udp端口" class-name="table-column-style">
          <template #default="scope">
            <span>{{
              scope.row.udpPortReslove ? scope.row.udpPortReslove : "-"
            }}</span>
          </template>
        </el-table-column>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button
            text
            type="primary"
            @click="handleManager(false, scope.row)"
            v-permission="[
              AUTH_CODE.HOSTING_CONFIG_EDIT,
              AUTH_CODE.CONFIG_MANAGER_EDIT,
            ]"
            >编辑</el-button
          >
          <el-popconfirm
            confirm-button-text="确定"
            cancel-button-text="取消"
            title="确定删除吗?"
            @confirm="cancelManager(scope.row.id)"
          >
            <template #reference>
              <el-button
                text
                type="primary"
                v-permission="[
                  AUTH_CODE.HOSTING_CONFIG_DELETE,
                  AUTH_CODE.CONFIG_MANAGER_DELETE,
                ]"
                >删除</el-button
              >
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <el-pagination
      v-model:currentPage="pagination.currentPage"
      v-model:page-size="pagination.pageSize"
      :page-sizes="[10, 20, 30, 40]"
      small
      background
      layout="total,  prev, pager, next, sizes,jumper"
      :total="pagination.total"
    />
  </div>
  <!-- 查看详情弹窗 -->
  <el-dialog
    v-model="dialogShow"
    :title="dialogTitle"
    :before-close="dialogCancel"
    destroy-on-close
    align-center
  >
    <el-form
      ref="managerFormRef"
      :model="managerForm"
      :rules="managerRules"
      label-position="left"
      label-width="76px"
    >
      <el-form-item :label="titleName" prop="srvName" label-width="82px">
        <el-input
          v-model.trim="managerForm.srvName"
          placeholder="请输入"
        ></el-input>
      </el-form-item>
      <div class="form-line"></div>
      <div class="box-flex">
        <div class="intranet">
          <div class="title">内网</div>
          <div class="content">
            <el-form-item label="IP类型" prop="ipType">
              <el-radio-group v-model="managerForm.ipType">
                <el-radio :label="4">V4</el-radio>
                <el-radio :label="6">V6</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="IP地址" prop="ip">
              <el-input
                v-model.trim="managerForm.ip"
                placeholder="请输入托管企业注册解析的内网IP"
              ></el-input>
            </el-form-item>
            <el-form-item label="TCP端口" prop="tcpPort">
              <el-input
                v-model.trim="managerForm.tcpPort"
                placeholder="请输入托管企业注册解析的内网TCP端口"
              ></el-input>
            </el-form-item>
            <el-form-item label="HTTP端口" prop="httpPort">
              <el-input
                v-model.trim="managerForm.httpPort"
                placeholder="请输入托管企业注册解析的内网HTTP端口"
              ></el-input>
            </el-form-item>
            <el-form-item label="UDP端口" prop="udpPort">
              <el-input
                v-model.trim="managerForm.udpPort"
                placeholder="请输入托管企业注册解析的内网UDP端口"
              ></el-input>
            </el-form-item>
          </div>
        </div>
        <div class="extranet">
          <div class="title">外网</div>
          <div class="content">
            <el-form-item label="IP类型" prop="ipTypeReslove">
              <el-radio-group v-model="managerForm.ipTypeReslove">
                <el-radio :label="4">V4</el-radio>
                <el-radio :label="6">V6</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="IP地址" prop="ipReslove">
              <el-input
                v-model.trim="managerForm.ipReslove"
                placeholder="请输入托管企业注册解析的外网IP"
              ></el-input>
            </el-form-item>
            <el-form-item label="TCP端口" prop="tcpPortReslove">
              <el-input
                v-model.trim="managerForm.tcpPortReslove"
                placeholder="请输入托管企业注册解析的外网TCP端口"
              ></el-input>
            </el-form-item>
            <el-form-item label="HTTP端口" prop="httpPortReslove">
              <el-input
                v-model.trim="managerForm.httpPortReslove"
                placeholder="请输入托管企业注册解析的外网HTTP端口"
              ></el-input>
            </el-form-item>
            <el-form-item label="UDP端口" prop="udpPortReslove">
              <el-input
                v-model.trim="managerForm.udpPortReslove"
                placeholder="请输入托管企业注册解析的外网UDP端口"
              ></el-input>
            </el-form-item>
          </div>
        </div>
      </div>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogCancel">取消</el-button>
        <el-button type="primary" @click="submitForm(managerFormRef)">
          保存
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { onMounted, reactive, ref, watch, computed } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import {
  getManagerList,
  addManagerData,
  deleteManagerData,
  editManagerData,
} from "@/api/proxyManager/index";
import { TableData, ManagerForm } from "@/types/proxyManage";
import { deepClone } from "@/utils/auth";
import { AUTH_CODE } from "@/utils/authCode";
import store from "@/store";
import SearchLayout from "@/components/searchLayout/index.vue";
import { SYSTEM_TYPE } from "@/utils/constant";

const isEntSystem = computed(
  () => store.getters.globalConfig.systemType === SYSTEM_TYPE.SYSTEM_PROXY
);

const titleName = computed(() => {
  return isEntSystem.value ? "解析服务器" : "托管服务器";
});

// 页面加载loading
const pageLoading = ref(true);
const queryRef = ref<FormInstance>();
const managerFormRef = ref<FormInstance>();

const queryForm = reactive({
  srvName: "",
});
const managerForm = ref<ManagerForm>({
  srvName: "",
  ip: "",
  ipType: 4,
  tcpPort: null,
  udpPort: null,
  httpPort: null,
  ipTypeReslove: 4,
  tcpPortReslove: null,
  udpPortReslove: null,
  httpPortReslove: null,
  ipReslove: "",
});
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

const tableData = ref<TableData[]>([]);

const dialogShow = ref(false);
const dialogTitle = ref("");
const isAddDialog = ref(true);

const ipv4 =
  /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/;
const ipv6 =
  /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/;
const validateIp = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error("IP不能为空"));
  } else {
    if (
      (managerForm.value.ipType === 4 && ipv4.test(value)) ||
      (managerForm.value.ipType === 6 && ipv6.test(value))
    ) {
      callback();
    } else {
      callback(new Error("IP格式不正确"));
    }
  }
};
const validateIpReslove = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error("IP不能为空"));
  } else {
    if (
      (managerForm.value.ipTypeReslove === 4 && ipv4.test(value)) ||
      (managerForm.value.ipTypeReslove === 6 && ipv6.test(value))
    ) {
      callback();
    } else {
      callback(new Error("IP格式不正确"));
    }
  }
};
const validateTCP = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error("端口不能为空"));
  } else {
    if (/^([1-9][0-9]*)$/.test(value)) {
      if (value > 0 && value < 65535) {
        callback();
      } else {
        callback(new Error("请确认端口范围"));
      }
    } else {
      callback(new Error("请确认端口格式"));
    }
  }
};

const validatePort = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback();
  } else {
    if (/^([1-9][0-9]*)$/.test(value)) {
      if (value > 0 && value < 65535) {
        callback();
      } else {
        callback(new Error("请确认端口范围"));
      }
    } else {
      callback(new Error("请确认端口格式"));
    }
  }
};
const managerRules = reactive<FormRules>({
  srvName: [
    { required: true, message: `请输入${titleName.value}`, trigger: "blur" },
    { max: 30, message: "最大长度不超过30", trigger: "blur" },
  ],
  ip: [{ required: true, validator: validateIp, trigger: "blur" }],
  ipType: [{ required: true, message: "请选择IP类型", trigger: "change" }],
  tcpPort: [{ required: true, validator: validateTCP, trigger: "blur" }],
  udpPort: [{ required: false, validator: validatePort, trigger: "blur" }],
  httpPort: [{ required: true, validator: validateTCP, trigger: "blur" }],
  ipTypeReslove: [
    { required: true, message: "请选择IP类型", trigger: "change" },
  ],
  tcpPortReslove: [{ required: true, validator: validateTCP, trigger: "blur" }],
  udpPortReslove: [
    { required: false, validator: validatePort, trigger: "blur" },
  ],
  httpPortReslove: [
    { required: false, validator: validatePort, trigger: "blur" },
  ],
  ipReslove: [
    { required: true, validator: validateIpReslove, trigger: "blur" },
  ],
});

watch([() => pagination.currentPage, () => pagination.pageSize], () => {
  getTableData(false);
});

// 查询托管列表
const getTableData = (isReset: boolean) => {
  if (isReset) {
    pagination.currentPage = 1;
    pagination.pageSize = 10;
    pagination.total = 0;
  }
  pageLoading.value = true;
  const { currentPage, pageSize } = pagination;
  getManagerList({
    srvName: queryForm.srvName,
    page: currentPage - 1,
    size: pageSize,
  }).then((res) => {
    pageLoading.value = false;
    tableData.value = res.content;
    pagination.total = res.totalElements;
  });
};
// 添加/编辑托管前缀
function handleManager(isAdd: boolean, row?: any) {
  dialogShow.value = true;
  isAddDialog.value = isAdd;
  if (isAdd) {
    dialogTitle.value = `新增${titleName.value}`;
  } else {
    dialogTitle.value = `编辑${titleName.value}`;
    managerForm.value = deepClone(row);
  }
}
const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid) => {
    if (valid) {
      managerConfirm();
    }
  });
};
function managerConfirm() {
  const api = isAddDialog.value ? addManagerData : editManagerData;
  api(managerForm.value).then(() => {
    ElMessage({
      message: `${isAddDialog.value ? "新增" : "编辑"}托管服务器成功!`,
      type: "success",
    });
    getTableData(true);
    dialogCancel();
    dialogShow.value = false;
  });
}
function cancelManager(id: number) {
  deleteManagerData({ id }).then(() => {
    ElMessage({
      message: "操作成功",
      type: "success",
    });
    getTableData(true);
  });
}

function dialogCancel() {
  managerForm.value = {
    srvName: "",
    ip: "",
    ipType: 4,
    tcpPort: null,
    udpPort: null,
    httpPort: null,
    ipTypeReslove: 4,
    tcpPortReslove: null,
    udpPortReslove: null,
    httpPortReslove: null,
    ipReslove: "",
  };
  dialogShow.value = false;
}
onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (
    AllPermission.includes(AUTH_CODE.HOSTING_CONFIG_PAGE) ||
    AllPermission.includes(AUTH_CODE.CONFIG_MANAGER_PAGE)
  ) {
    getTableData(true);
  }
});
</script>
<style lang="scss" scoped>
:deep(.el-table--border th.el-table__cell) {
  border-bottom: 1px solid #ddd;
  border-right: 1px solid #ddd;
}

:deep(.el-table) {
  .cell {
    font-weight: unset;
  }
  thead.is-group th.el-table__cell {
    background: #eef2f1;
  }
  .cell {
    font-size: 12px;
  }
}
.summary-card {
  .el-form {
    text-align: right;
  }
}
.form-line {
  border-bottom: 1px solid #dfe4e3;
  margin: 0 -20px;
}
.box-flex {
  display: flex;
  justify-content: space-between;
  .intranet,
  .extranet {
    width: 47%;
    margin-top: 24px;
    .title {
      font-weight: bold;
      font-size: 12px;
      color: #1d2129;
      margin-bottom: 10px;
    }
  }
}
:deep(th.el-table__cell.is-leaf) {
  border-right: none;
}
:deep(.table-column-style) {
  border-right: 1px solid #dfe4e3 !important;
}
</style>
