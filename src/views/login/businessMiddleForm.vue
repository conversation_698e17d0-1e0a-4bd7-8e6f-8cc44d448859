<template>
  <div class="login-box">
    <img src="../../assets/logo/logo-gradient.png" alt="" class="icon" />
    <div class="login-box-title">
      <h2>
        {{ SYSTEM_NAME.SYSTEM_NAME_PROVINCE }}
      </h2>
      <h3>省级标识业务管理系统</h3>
      <p>欢迎登录</p>
    </div>
    <div class="login-box-tips">业务中台登录</div>
    <!-- 登录框 输入用户名密码-->
    <div class="login-form">
      <el-form
        ref="loginRef"
        :model="loginForm"
        :rules="loginRules"
        key="ruleForm"
      >
        <el-form-item prop="username" class="form-item">
          <el-input
            v-model="loginForm.username"
            type="text"
            auto-complete="off"
            placeholder="请输入业务中台账号"
            icon="User"
            clearable
            :readonly="readonlyInput"
            @focus="cancelReadOnly()"
          >
            <template #prefix
              ><svg-icon icon-class="user" class="el-input__icon input-icon"
            /></template>
          </el-input>
        </el-form-item>
        <el-form-item prop="password" class="form-item">
          <el-input
            v-model.trim="loginForm.password"
            type="password"
            auto-complete="off"
            placeholder="请输入业务中台密码"
            show-password
            clearable
            @keyup.enter="handleLogin"
          >
            <template #prefix
              ><svg-icon
                icon-class="password"
                class="el-input__icon input-icon"
            /></template>
          </el-input>
        </el-form-item>
        <div class="find-password">
          <el-button text type="primary"></el-button>
        </div>
        <el-form-item class="form-item">
          <el-button
            :loading="loading"
            type="primary"
            size="large"
            class="submit-btn"
            @click.prevent="handleLogin"
          >
            {{ !loading ? "登 录" : "登 录 中..." }}
          </el-button>
        </el-form-item>
        <!-- <el-form-item class="form-item">
          <el-button
            type="info"
            size="large"
            class="cancle-btn"
            @click.prevent="handleRecoverCancle"
          >
            返回
          </el-button>
        </el-form-item> -->
      </el-form>
    </div>
    <verify
      v-if="showVerify"
      :captcha-type="'blockPuzzle'"
      :img-size="{ width: '400px', height: '200px' }"
      @cancel="handleCancel"
      @success="handleSuccess"
    ></verify>
    <BindDialog
      v-if="bindDialog"
      @close-dialog="cancelBind"
      :data="props.data"
    />
  </div>
</template>
<script setup lang="ts">
import { computed, getCurrentInstance, ref } from "vue";
import { useStore } from "vuex";
import { useRouter } from "vue-router";
import BindDialog from "./bindDialog.vue";
import { getPublicKey, encrypt } from "@/utils/forgeEncrypt";
import { SYSTEM_NAME, CURRENT_PAGE } from "@/utils/constant";
import Verify from "@/components/verifition/Verify.vue";

const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
const { proxy }: any = getCurrentInstance();
const router = useRouter();
const store = useStore();
const globalConfig = computed(() => store.getters.globalConfig);
interface LoginForm {
  username: string;
  password: string;
  rememberMe?: boolean;
  code?: string;
  uuid?: string;
}
const loginForm = ref<LoginForm>({
  username: "",
  password: "",
  rememberMe: false,
  code: "",
  uuid: "",
});

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "blur", message: "请输入验证码" }],
};

const loading = ref(false);
const bindDialog = ref(false);
const redirect = ref(undefined);
// 滑块验证码是否显示
const showVerify = ref(false);

const readonlyInput = ref(true);
const cancelReadOnly = () => {
  readonlyInput.value = false;
};
function handleLogin() {
  proxy.$refs.loginRef.validate(async (valid: any) => {
    if (valid) {
      loading.value = true;
      const publicKey = await getPublicKey().catch(() => {
        loading.value = false;
      });
      const loginFormTemp = {
        username: loginForm.value.username,
        password: globalConfig.value?.login.passwordEncrypted
          ? encrypt(loginForm.value.password, publicKey)
          : loginForm.value.password,
      };
      await store.dispatch("SystemBindExist", loginFormTemp).catch(() => {
        loading.value = false;
      });
      if (store.getters.binding) {
        if (globalConfig.value.login.captchaEnable) {
          showVerify.value = true;
          return;
        }
        store
          .dispatch("LoginWithMiddle", loginFormTemp)
          .then(() => {
            router.push({ path: redirect.value || "/" });
          })
          .catch(() => {
            loading.value = false;
          });
      } else if (store.getters.binding === false) {
        bindDialog.value = true;
      }
    }
  });
}
// 滑块-取消
function handleCancel() {
  loading.value = false;
  showVerify.value = false;
}

// 滑块-成功
async function handleSuccess(data: any) {
  const publicKey = await getPublicKey().catch(() => {
    loading.value = false;
  });
  const { captchaVerification } = data;
  const loginFormTemp = {
    username: loginForm.value.username,
    password: globalConfig.value?.login.passwordEncrypted
      ? encrypt(loginForm.value.password, publicKey)
      : loginForm.value.password,
    captcha: captchaVerification,
  };
  store
    .dispatch("LoginWithMiddle", loginFormTemp)
    .then(() => {
      router.push({ path: redirect.value || "/" });
    })
    .finally(() => {
      loading.value = false;
    });
}
function handleRecoverCancle() {
  props.data.currentPage = CURRENT_PAGE.LOGIN;
}

function cancelBind() {
  store.state.user.binding = null;
  bindDialog.value = false;
  loading.value = false;
}
</script>
<style lang="scss" scoped>
.login-box {
  width: 3.7rem;
  height: 4.5rem;
  position: absolute;
  left: 0.9rem;
  top: calc(50% - 3.5rem);
  .icon {
    display: block;
    width: 0.98rem;
    height: 1.11rem;
    margin: 0 auto 0.35rem;
  }
  .login-box-title {
    text-align: center;
    h2 {
      font-size: 0.33rem;
      font-family: PingFang SC-中黑体, PingFang SC;
      color: rgba(0, 0, 0, 0.85);
      line-height: 0.46rem;
      margin: 0;
    }
    h3 {
      margin-top: 0.05rem;
    }
    p {
      font-size: 0.14rem;
      color: rgba(0, 0, 0, 0.45);
      line-height: 0.2rem;
      margin: 0.2rem 0 0 0;
    }
  }
  .login-box-tips {
    width: calc(100% - 0.28rem);
    margin: 0.05rem auto 0;
    line-height: 0.5rem;
    font-size: 0.16rem;
    color: #00a57c;
    font-weight: 600;
    border-bottom: 1px solid #bdc2cf;
    position: relative;
    &::after {
      content: "";
      width: 1.37rem;
      height: 2px;
      background-color: #00a57c;
      position: absolute;
      bottom: 0;
      left: 0;
    }
  }
  .login-form {
    padding-top: 0.24rem;
    .form-item {
      margin: 0 0 0.26rem;
      .el-form-item__content {
        line-height: 0.4rem;
        display: flex;
        justify-content: space-between;
      }
      .el-input {
        height: 0.4rem;
        line-height: 0.4rem;
        input {
          height: 0.4rem;
          line-height: 0.4rem;
        }
      }
      &.code {
        .el-input {
          width: 58%;
        }
        .login-code {
          width: 40%;
          height: 0.38rem;
          line-height: 0.38rem;
          img {
            width: 100%;
            height: inherit;
            // vertical-align: middle;
            border-radius: 3px;
          }
        }
      }
      .submit-btn {
        width: 100%;
        height: 0.4rem;
        line-height: 0.4rem;
        padding: 0;
        border-radius: 0.2rem;
        background-color: #00a57c;
      }
      .cancle-btn {
        width: 100%;
        height: 0.4rem;
        line-height: 0.4rem;
        padding: 0;
        border-radius: 0.2rem;
      }
    }
    .find-password {
      text-align: right;
      width: 100%;
      margin-top: -0.3rem;
      margin-bottom: 0.3rem;
      .el-button {
        font-size: 12px;
        padding: 0;
      }
    }
  }
}
</style>
