<template>
  <el-dialog
    v-model="dialogVisible"
    width="30%"
    @close="$emit('close-dialog')"
    align-center
  >
    <div style="text-align: center">
      <el-icon :size="20" color="#E6A23C"><InfoFilled /></el-icon>
      <p>当前账号未绑定关联系统账号，是否绑定？</p>
    </div>

    <template #footer>
      <span>
        <el-button @click="dialogCancel">取消</el-button>
        <el-button type="primary" @click="validForm"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useStore } from "vuex";
import { CURRENT_PAGE } from "@/utils/constant";

const store = useStore();
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
});
const dialogVisible = ref(true);

function validForm() {
  dialogVisible.value = false;
  props.data.currentPage = CURRENT_PAGE.BIND_ACCOUNT;
}
function dialogCancel() {
  dialogVisible.value = false;
  store.state.user.binding = null;
}
</script>

<style lang="scss"></style>
