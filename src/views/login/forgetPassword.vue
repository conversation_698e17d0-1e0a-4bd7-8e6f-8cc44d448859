<template>
  <!-- 找回密码-->
  <div class="login-box">
    <div class="login-box-tips">找回密码</div>
    <!-- 登录框 输入用户名密码-->
    <div class="login-form">
      <el-form
        ref="recoverRef"
        :model="recoverForm"
        :rules="recoverRules"
        key="ruleForm"
      >
        <el-form-item class="form-item">
          <span>请输入账号绑定邮箱来重置你的账号密码</span>
        </el-form-item>
        <el-form-item
          prop="provinceId"
          class="form-item"
          v-if="globalConfig.isTenant === IS_TENANT.TENANT_Y"
        >
          <el-select
            v-model="recoverForm.provinceId"
            placeholder="请选择节点"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="item in tenantOptions"
              :key="item.provinceId"
              :label="item.name"
              :value="item.provinceId"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="email" class="form-item" ref="emailRef">
          <el-input
            v-model="recoverForm.email"
            :readonly="emailReadonly"
            :onfocus="emailOnfocus"
            type="text"
            auto-complete="off"
            placeholder="请输入邮箱"
            clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item prop="code" class="form-item">
          <el-input
            v-model="recoverForm.code"
            type="text"
            auto-complete="off"
            placeholder="请输入邮箱验证码"
            clearable
          >
            <template #suffix>
              <el-link
                type="primary"
                v-show="show"
                :underline="false"
                @click="handleCheckCode()"
                >发送验证码</el-link
              >
              <span v-show="!show">{{ count }} s</span>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="newPassword" class="form-item">
          <el-input
            v-model.trim="recoverForm.newPassword"
            placeholder="请输入新密码"
            type="password"
            auto-complete="off"
            show-password
            clearable
          />
        </el-form-item>
        <el-form-item prop="rePassword" class="form-item">
          <el-input
            v-model.trim="recoverForm.rePassword"
            placeholder="请再次输入新密码"
            type="password"
            auto-complete="off"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item class="form-item" style="margin-top: 1.13rem">
          <el-button
            type="info"
            size="large"
            class="cancle-btn"
            @click.prevent="handleRecoverCancle"
          >
            返回
          </el-button>
        </el-form-item>
        <el-form-item class="form-item">
          <el-button
            :loading="recoverLoading"
            type="primary"
            size="large"
            class="submit-btn"
            @click.prevent="handleRecoverPassword"
          >
            确定
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
  <verify
    v-if="showVerify"
    :captcha-type="'blockPuzzle'"
    :img-size="{ width: '400px', height: '200px' }"
    @cancel="handleCancel"
    @success="handleSuccess"
  ></verify>
</template>
<script setup lang="ts">
import { computed, getCurrentInstance, ref, reactive, onMounted } from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
import Verify from "@/components/verifition/Verify.vue";
import { sendEmail, forget } from "@/api/login";
import { CURRENT_PAGE, IS_TENANT } from "@/utils/constant";
import { getPublicKey, encrypt } from "@/utils/forgeEncrypt";

const store = useStore();
const globalConfig = computed(() => store.getters.globalConfig);
// 滑块验证码是否显示
const showVerify = ref(false);
const { proxy }: any = getCurrentInstance();
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  tenantOptions: {
    type: Object,
    default: () => ({}),
  },
});
// 找回密码-发送验证码是否显示
const show = ref(true);
// 找回密码-倒计时
const count = ref("60");
// 找回密码-确定loadind
const recoverLoading = ref(false);
// 找回密码-返回
function handleRecoverCancle() {
  props.data.currentPage = CURRENT_PAGE.LOGIN;
}
// email防止自动填充
const emailReadonly = ref(true);

// 移入事件，防止自动填充
function emailOnfocus() {
  emailReadonly.value = false;
}

// 校验两次密码不一致
const checkRePWD = (rule: any, value: string, callback: any) => {
  if (recoverForm.value.newPassword !== recoverForm.value.rePassword) {
    callback(new Error("两次新密码输入不一致"));
  } else {
    callback();
  }
};

interface RecoverForm {
  provinceId: string;
  email: string;
  code: string;
  newPassword: string;
  rePassword: string;
  captcha: any;
}

const recoverForm = ref<RecoverForm>({
  provinceId: "",
  email: "",
  code: "",
  newPassword: "",
  rePassword: "",
  captcha: {},
});

/**
 * 校验密码强度
 */
const checkPassWord = (rule: any, value: any, callback: any) => {
  // ⾄8-16位，必须包含大写字母、小写字母 、数字、 特殊字符（四种里至少三种，8-16位）
  const reg =
    /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,20}$/;
  if (!reg.test(value)) {
    callback(new Error("8-20位，包含大小写字母、数字、特殊符号至少三种"));
  } else {
    callback();
  }
};

/**
 * 校验邮箱正则
 */
const checkEmail = (rule: any, value: any, callback: any) => {
  const reg = /^$|^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
  if (!reg.test(value)) {
    return callback(new Error("邮箱格式不正确"));
  }
  callback();
};

// 校验租户id
const checkProvinceId = (rule: any, value: string, callback: any) => {
  const checkVal = recoverForm.value.provinceId + "";
  if (globalConfig.value.isTenant === IS_TENANT.TENANT_Y && !checkVal) {
    return callback(new Error("请选择节点"));
  }
  callback();
};

const recoverRules = {
  provinceId: [{ required: true, validator: checkProvinceId, trigger: "blur" }],
  email: [
    { required: true, trigger: "blur", message: "请输入您的邮箱" },
    { max: 30, message: "邮箱最大长度不超过30", trigger: "blur" },
    { required: true, validator: checkEmail, trigger: "blur" },
  ],
  code: [{ required: true, trigger: "blur", message: "请输入验证码" }],
  newPassword: [
    { required: true, trigger: "blur", message: "请输入新密码" },
    { required: true, validator: checkPassWord, trigger: "blur" },
  ],
  rePassword: [
    { required: true, trigger: "blur", message: "请再次输入新密码" },
    { required: true, validator: checkRePWD, trigger: "blur" },
    { required: true, validator: checkPassWord, trigger: "blur" },
  ],
};

// 找回密码 - 获取验证码
function handleCheckCode() {
  proxy.$refs.recoverRef.validateField(
    ["provinceId", "email"],
    async (valid: any) => {
      if (valid) {
        showVerify.value = true;
      }
    }
  );
}

// 找回密码-确定
async function handleRecoverPassword() {
  proxy.$refs.recoverRef.validate(async (valid: any) => {
    if (valid) {
      recoverLoading.value = true;
      const publicKey = await getPublicKey();
      const formTemp = {
        provinceId: recoverForm.value.provinceId,
        email: recoverForm.value.email,
        code: recoverForm.value.code,
        newPassword: globalConfig.value?.login.passwordEncrypted
          ? encrypt(recoverForm.value.newPassword, publicKey)
          : recoverForm.value.newPassword,
        rePassword: globalConfig.value?.login.passwordEncrypted
          ? encrypt(recoverForm.value.rePassword, publicKey)
          : recoverForm.value.rePassword,
        captcha: recoverForm.value.captcha,
      };
      forget(formTemp)
        .then(() => {
          ElMessage.success("修改密码成功，请用新密码登录");
          props.data.currentPage = CURRENT_PAGE.LOGIN;
        })
        .finally(() => {
          recoverLoading.value = false;
        });
    }
  });
}

// 滑块-成功
function handleSuccess(data: any) {
  const { captchaVerification } = data;
  // 发送验证码
  const formTemp = {
    email: recoverForm.value.email,
    captcha: captchaVerification,
    provinceId: recoverForm.value.provinceId,
  };
  sendEmail(formTemp).then(() => {
    ElMessage.success("验证码已发送");
    let duration = 60;
    // 倒计时期间按钮不能单击
    show.value = false;
    // 开启定时器
    const timer = setInterval(() => {
      const tmp = duration--;
      count.value = `${tmp}`;
      if (tmp <= 0) {
        // 清除掉定时器
        clearInterval(timer);
        // 设置按钮可以单击
        show.value = true;
        count.value = "60";
      }
    }, 1000);
  });
}

// 滑块-取消
function handleCancel() {
  showVerify.value = false;
}
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.login-right {
  width: 5.5rem;
  height: 100%;
  background: url(../assets/images/login/login_bg_right.png) no-repeat;
  background-size: 100% 100%;
  position: relative;
  .login-box {
    width: 3.7rem;
    height: 4.5rem;
    position: absolute;
    left: 0.9rem;
    top: calc(50% - 3.5rem);
    .icon {
      display: block;
      width: 0.98rem;
      height: 1.11rem;
      margin: 0 auto 0.35rem;
    }
    .login-box-title {
      text-align: center;
      h2 {
        font-size: 0.33rem;
        font-family: PingFang SC-中黑体, PingFang SC;
        color: rgba(0, 0, 0, 0.85);
        line-height: 0.46rem;
        margin: 0;
      }
      h3 {
        margin-top: 0.05rem;
      }
      p {
        font-size: 0.14rem;
        color: rgba(0, 0, 0, 0.45);
        line-height: 0.2rem;
        margin: 0.2rem 0 0 0;
      }
    }
    .login-box-tips {
      width: calc(100% - 0.28rem);
      margin: 0.05rem auto 0;
      line-height: 0.5rem;
      font-size: 0.16rem;
      color: #00a57c;
      font-weight: 600;
      border-bottom: 1px solid #bdc2cf;
      position: relative;
      &::after {
        content: "";
        width: 1.37rem;
        height: 2px;
        background-color: #00a57c;
        position: absolute;
        bottom: 0;
        left: 0;
      }
    }
    .login-form {
      padding-top: 0.24rem;
      .form-item {
        margin: 0 0 0.26rem;
        .el-form-item__content {
          line-height: 0.4rem;
          display: flex;
          justify-content: space-between;
        }
        .el-input {
          height: 0.4rem;
          line-height: 0.4rem;
          input {
            height: 0.4rem;
            line-height: 0.4rem;
          }
        }
        &.code {
          .el-input {
            width: 58%;
          }
          .login-code {
            width: 40%;
            height: 0.38rem;
            line-height: 0.38rem;
            img {
              width: 100%;
              height: inherit;
              // vertical-align: middle;
              border-radius: 3px;
            }
          }
        }
        .submit-btn {
          width: 100%;
          height: 0.4rem;
          line-height: 0.4rem;
          padding: 0;
          border-radius: 0.2rem;
          background-color: #00a57c;
        }
        .cancle-btn {
          width: 100%;
          height: 0.4rem;
          line-height: 0.4rem;
          padding: 0;
          margin-top: 0.2rem;
          border-radius: 0.2rem;
        }
      }
    }
  }
}
</style>
