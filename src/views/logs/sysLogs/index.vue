<template>
  <div class="box">
    <SearchLayout>
      <template #right>
        <el-form
          v-permission="AUTH_CODE.SYSTEM_OPERATION_LOG_PAGE"
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          @submit.prevent
        >
          <el-form-item
            label=""
            prop="userName"
            style="width: 240px; max-width: 240px"
          >
            <el-input
              v-model.trim="queryParams.userName"
              placeholder="请输入"
              clearable
              @keyup.enter="handleQuery"
            >
              <template #prefix>登录名：</template>
            </el-input>
          </el-form-item>
          <el-form-item style="width: 60px; max-width: 60px">
            <el-button
              type="primary"
              :loading="searchLoading"
              @click="handleQuery"
              >搜索</el-button
            >
          </el-form-item>
        </el-form>
      </template>
    </SearchLayout>

    <el-table
      border
      loading="loading"
      size="small"
      :data="sysLogs"
      show-overflow-tooltip
      :tooltip-options="toolTipOption"
    >
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column label="登录名" prop="userName">
        <template #default="scope">
          <span v-copy="scope.row.userName">{{
            scope.row.userName || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="请求IP" width="110" prop="host">
        <template #default="scope">
          {{ scope.row.host || "-" }}
        </template>
      </el-table-column>
      <el-table-column
        label="请求路径"
        prop="path"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <span v-copy="scope.row.path">{{ scope.row.path || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="请求方式" width="80" prop="method">
        <template #default="scope">
          {{ scope.row.method || "-" }}
        </template>
      </el-table-column>
      <el-table-column label="描述" prop="description">
        <template #default="scope">
          {{ scope.row.description || "-" }}
        </template>
      </el-table-column>
      <el-table-column label="请求参数" prop="param">
        <template #default="scope">
          {{ scope.row.param || "-" }}
        </template>
      </el-table-column>
      <el-table-column label="操作结果" prop="responseCode">
        <template #default="scope">
          {{ scope.row.responseCode || "-" }}
        </template>
      </el-table-column>
      <el-table-column label="操作日期" width="130" prop="operateTime">
        <template #default="scope">
          {{ scope.row.operateTime || "-" }}
        </template>
      </el-table-column>
      <!-- <el-table-column
          label="操作"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              type="text"
              icon="View"
              @click="handleView(scope.row, scope.index)"
              v-hasPermi="['monitor:operlog:query']"
              >删除</el-button
            >
          </template>
        </el-table-column> -->
    </el-table>

    <el-pagination
      v-model:currentPage="queryParams.pageNo"
      v-model:page-size="queryParams.pageSize"
      :page-sizes="[10, 20, 30, 40]"
      small
      background
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup name="SysLog" lang="ts">
import { reactive, ref, toRefs } from "vue";
import { sysLogInfo } from "@/api/logs/index";
import { SysLogs } from "@/types/logs";
import { AUTH_CODE } from "@/utils/authCode";
import SearchLayout from "@/components/searchLayout/index.vue";

const sysLogs = ref<SysLogs[]>([]);
const loading = ref(true);
const total = ref(0);
const searchLoading = ref(false);
const tableLoading = ref(false);
const data = reactive({
  queryParams: {
    pageNo: 1,
    pageSize: 10,
    userName: "",
  },
});
const toolTipOption = {
  trigger: "click",
  hideAfter: 200000,
};
const { queryParams } = toRefs(data);

/** 查询系统日志 */
function getList() {
  loading.value = true;
  tableLoading.value = true;
  sysLogInfo({
    pageNo: queryParams.value.pageNo - 1,
    pageSize: queryParams.value.pageSize,
    userName: queryParams.value.userName,
  })
    .then((response) => {
      sysLogs.value = response.content;
      total.value = response.totalCount;
      loading.value = false;
    })
    .finally(() => {
      searchLoading.value = false;
      tableLoading.value = false;
    });
}
function handleCurrentChange(val: number) {
  queryParams.value.pageNo = val;
  getList();
}
function handleSizeChange(val: number) {
  queryParams.value.pageSize = val;
  getList();
}
/** 搜索按钮操作 */
function handleQuery() {
  searchLoading.value = true;
  queryParams.value.pageNo = 1;
  queryParams.value.pageSize = 10;
  getList();
}

getList();
</script>
<style lang="scss" scoped>
.box {
  :deep(.el-popper) {
    &.is-dark {
      max-width: 800px !important; //宽度可根据自己需要进行设置
    }
  }
}
</style>
