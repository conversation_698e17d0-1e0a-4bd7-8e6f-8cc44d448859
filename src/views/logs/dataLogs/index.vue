<template>
  <div>
    <el-table
      ref="operlogRef"
      border
      size="small"
      v-loading="loading"
      :data="dataLogList"
    >
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column label="企业前缀" prop="prefix" show-overflow-tooltip>
        <template #default="scope">
          <span v-copy="scope.row.prefix">{{ scope.row.prefix || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="标识" prop="handleName" show-overflow-tooltip>
        <template #default="scope">
          <span v-copy="scope.row.handleName">{{
            scope.row.handleName || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作类型" prop="opName">
        <template #default="scope">
          {{ scope.row.opName || "-" }}
        </template>
      </el-table-column>
      <el-table-column label="日志产生时间" prop="operatorTime">
        <template #default="scope">
          {{ scope.row.operatorTime || "-" }}
        </template>
      </el-table-column>
      <el-table-column label="递归节点IP" prop="userIp">
        <template #default="scope">
          {{ scope.row.userIp || "-" }}
        </template>
      </el-table-column>
    </el-table>
  </div>
  <div class="bottombtn">
    <el-button
      type="primary"
      @click="toHomePage"
      v-permission="AUTH_CODE.DATA_OPERATION_LOG_PAGE"
      >首页</el-button
    >
    <el-button
      :disabled="queryParams.pageNo === 1"
      @click="prePage"
      v-permission="AUTH_CODE.DATA_OPERATION_LOG_PAGE"
      >上一页</el-button
    >
    <el-button
      @click="nextPage"
      v-permission="AUTH_CODE.DATA_OPERATION_LOG_PAGE"
      >下一页</el-button
    >
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, toRefs } from "vue";
import { dataLogInfo } from "@/api/logs/index";
import { DataLogList } from "@/types/logs";
import { AUTH_CODE } from "@/utils/authCode";
import store from "@/store";

const dataLogList = ref<DataLogList[]>([]);
const loading = ref(true);
const data = reactive({
  form: {},
  queryParams: {
    pageNo: 1,
    pageSize: 10,
  },
});

const { queryParams } = toRefs(data);

/** 查询登录日志 */
function getList() {
  loading.value = true;
  dataLogInfo({
    pageNo: queryParams.value.pageNo - 1,
    pageSize: queryParams.value.pageSize,
  })
    .then((response) => {
      dataLogList.value = Array.isArray(response) ? response : [];
    })
    .finally(() => {
      loading.value = false;
    });
}
function toHomePage() {
  queryParams.value.pageNo = 1;
  getList();
}
function prePage() {
  if (queryParams.value.pageNo > 1) {
    queryParams.value.pageNo--;
    getList();
  } else {
    console.log("error");
  }
}
function nextPage() {
  queryParams.value.pageNo++;
  getList();
}

onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (AllPermission.includes(AUTH_CODE.DATA_OPERATION_LOG_PAGE)) {
    getList();
  }
});
</script>
<style lang="scss" scoped>
.topTitle {
  color: #606266;
  font-weight: bold;
  margin-bottom: 20px;
}

.bottombtn {
  float: right;
  margin-top: 15px;
}
</style>
