<template>
  <el-dialog
    v-model="dialogFormVisible"
    title="新增应用"
    width="700px"
    @close="$emit('close-dialog')"
    destroy-on-close
    align-center
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="类型" prop="appType" v-if="canCreateDMM">
        <el-radio-group
          v-model="formData.appType"
          size="small"
          @click="handleAppTypeChange"
        >
          <el-radio :label="APP_TYPE.DMM">{{
            APP_TYPE_DESC[APP_TYPE.DMM]
          }}</el-radio>
          <el-radio :label="APP_TYPE.NORMAL">{{
            APP_TYPE_DESC[APP_TYPE.NORMAL]
          }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="formData.appType === APP_TYPE.DMM"
        label="应用名称"
        prop="mpDmmAppId"
      >
        <el-cascader
          v-model="formData.mpDmmAppId"
          :options="appTreeList"
          @change="onCascaderChange"
          style="width: 100%"
          :props="cascaderProps"
        />
      </el-form-item>
      <el-form-item
        v-if="formData.appType === APP_TYPE.NORMAL"
        label="应用名称"
        prop="appName"
      >
        <el-input
          v-model.trim="formData.appName"
          placeholder="请输入应用名称"
          clearable
        />
      </el-form-item>
      <el-form-item
        label="主数据范围"
        prop="masterDataIdList"
        v-if="formData.appType === APP_TYPE.DMM"
      >
        <el-select
          v-model="formData.masterDataIdList"
          multiple
          placeholder="请选择主数据范围"
        >
          <el-option
            v-for="item in MASTER_DATA_ENUM"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="企业前缀" prop="prefixId" style="width: 100%">
        <el-select v-model="formData.prefixId" placeholder="请选择前缀">
          <el-option
            v-for="item in prefixData"
            :key="item.id"
            :label="item.entPrefix"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        class="form-item-pl10"
        label="部署地址"
        prop="deployAddress"
      >
        <el-input
          v-model.trim="formData.deployAddress"
          placeholder="请输入部署地址"
          clearable
        />
      </el-form-item>
      <el-form-item class="form-item-pl10" label="系统版本" prop="sysVersion">
        <el-input
          v-model.trim="formData.sysVersion"
          placeholder="请输入系统版本"
          clearable
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="validForm" :loading="btnLoading">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref, toRefs } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { useStore } from "vuex";
import {
  addAppInfoApi,
  getPrefix,
  getMpDmmAppTree,
} from "@/api/appManege/index";
import { Form } from "@/types/appManage";
import { MASTER_DATA_ENUM } from "./constant";
import { APP_TYPE, APP_TYPE_DESC } from "@/utils/dataPlatform";

const store = useStore();
const { canCreateDMM } = toRefs(store.getters);

const emit = defineEmits(["dialog-visible", "update-table"]);

const btnLoading = ref(false);
const formRef = ref<FormInstance>();
const dialogFormVisible = ref(true);
const appTreeList = ref<any>([]);
const cascaderProps = reactive({ value: "id", label: "name" });
const formData = reactive<Form & { masterDataIdList: number[] }>({
  appName: "",
  appType: APP_TYPE.DMM,
  mpDmmAppId: null, // 中台应用的应用id
  masterDataIdList: [], // 主数据列表，作为接口入参时，要转为[].join(',')
  deployAddress: "",
  entId: null,
  prefixId: null,
  sysVersion: "",
});
const rules = reactive({
  appType: [
    {
      required: true,
      trigger: "blur",
    },
  ],
  mpDmmAppId: [
    {
      required: true,
      message: "请输入应用名称",
      trigger: "blur",
    },
  ],
  appName: [
    {
      required: true,
      message: "请输入应用名称",
      trigger: "blur",
    },
    {
      max: 30,
      message: "应用名称最大长度不超过30",
      trigger: "blur",
    },
  ],
  masterDataIdList: [
    {
      required: true,
      message: "请选择主数据范围",
      trigger: "blur",
    },
    {
      validator: (rule: any, value: any, callback: any) => {
        if (formData.appType !== APP_TYPE.DMM) return callback();

        if (!value || (Array.isArray(value) && !value.length))
          return callback(new Error("请选择主数据范围"));

        callback();
      },
      trigger: "blur",
    },
  ],
  prefixId: [
    {
      required: true,
      message: "请选择标识编码",
      trigger: "blur",
    },
  ],
  deployAddress: [
    {
      max: 30,
      message: "部署地址最大长度不超过30",
      trigger: "blur",
    },
  ],
  sysVersion: [
    {
      max: 30,
      message: "系统版本最大长度不超过30",
      trigger: "blur",
    },
  ],
});

interface PrefixItem {
  entId: number;
  entPrefix: string;
  id: number;
  state: number;
}
const prefixData = ref<PrefixItem[]>([]);

// 获取前缀下拉
const getPrefixData = () => {
  getPrefix().then((res) => {
    prefixData.value = Array.isArray(res) ? res : [];
  });
};

// 提交
const validForm = () => {
  if (!formRef.value) return;
  formRef.value.validate((valid) => {
    if (valid) {
      addAppInfo();
    } else {
      ElMessage.error("校验不通过!");
      return false;
    }
  });
};

const handleAppTypeChange = () => {
  formData.mpDmmAppId = null;
  formData.appName = "";
  formData.masterDataIdList = [];
  formData.prefixId = null;
  formData.deployAddress = "";
  formData.sysVersion = "";
};

const addAppInfo = () => {
  btnLoading.value = true;
  prefixData.value.forEach((item) => {
    if (item.id === formData.prefixId) {
      formData.entId = item.entId;
    }
  });
  const { masterDataIdList, ...otherFormData } = formData;
  if (formData.appType === APP_TYPE.DMM) {
    otherFormData.masterDataScope = masterDataIdList?.join(",");
    otherFormData.mpDmmAppId = (otherFormData.mpDmmAppId as any).join(",");
  } else {
    Reflect.deleteProperty(otherFormData, "mpDmmAppId"); // 非中台应用不需要改参数
  }

  addAppInfoApi(otherFormData)
    .then(() => {
      dialogFormVisible.value = false;
      emit("update-table", true);
      ElMessage({
        message: "新增应用成功",
        type: "success",
      });
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

const onCascaderChange = (value: any) => {
  // console.log("===应用名称级联选中change获取的值====", value);
  // formData.appName = xxx // 这里根据选择的值，把id赋值给mpDmmAppId，name赋值给appName
  const appName = getAppName(appTreeList.value, 0);
  function getAppName(
    data: { name: string; id: number; children?: any }[],
    index: number
  ) {
    const appItem = data.filter((item) => item.id === value[index])[0];
    let name = appItem.name;
    if (appItem.children && appItem.children.length) {
      name = getAppName(appItem.children, index + 1);
    }
    return name;
  }
  formData.appName = appName;
};

// 获取中台应用树
const getMpDmmAppTreeData = () => {
  getMpDmmAppTree().then((res) => {
    appTreeList.value = res || [];
  });
};

onMounted(() => {
  // 应用类型默认是非中台，如果不能进行中台配置，则设置为非中台应用
  formData.appType = canCreateDMM.value ? APP_TYPE.DMM : APP_TYPE.NORMAL;
  getPrefixData();
  getMpDmmAppTreeData(); // 只在初始化的时候请求一次吧，后面如果变更频繁可监听radio变化再次调用
});
</script>

<style lang="scss" scoped>
.el-select {
  width: 100%;
}
</style>
