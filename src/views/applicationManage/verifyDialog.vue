<template>
  <div class="verify-dialog">
    <el-dialog
      v-model="dialogFormVisible"
      title="安全认证"
      width="800px"
      @close="handleClose"
      append-to-body
      destroy-on-close
      align-center
    >
      <el-form
        ref="formRef"
        :model="data"
        :rules="rules"
        label-position="left"
        label-width="100px"
      >
        <el-form-item label="应用身份标识" prop="appName" required>
          <el-input v-model.trim="data.appName" disabled />
        </el-form-item>
        <el-form-item label="公钥" prop="publicKey" required>
          <el-input
            v-model="data.publicKey"
            type="textarea"
            readonly
            @click="copy(data.publicKey)"
          />
        </el-form-item>
        <el-form-item label="私钥" prop="privateKey" required>
          <el-input
            v-model="data.privateKey"
            type="textarea"
            readonly
            @click="copy(data.privateKey)"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span>
          <el-button @click="handleClose">取消</el-button>
          <el-button @click="refreshKey">更新密钥</el-button>
          <el-button type="primary" @click="validForm" :loading="btnLoading">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import useClipboard from "vue-clipboard3";
import { ElMessage } from "element-plus";
import { getAppDetailApi, getKeyPair, updateKey } from "@/api/appManege/index";

const { toClipboard } = useClipboard();
const emit = defineEmits(["close"]);
const formRef = ref();
const props = defineProps<{
  rowData: any;
}>();

const data = ref({
  id: null,
  appName: "",
  publicKey: "",
  privateKey: "",
});
const handleClose = () => {
  console.log(111);
  emit("close");
};
const btnLoading = ref(false);
const dialogFormVisible = ref(true);
const refreshKey = () => {
  console.log("更新秘钥");
  getKeyPair().then((res) => {
    const r = res as any;
    data.value.publicKey = r.publicKey;
    data.value.privateKey = r.privateKey;
  });
};

const rules = ref({
  appName: [{ required: true, message: "应用名称不能为空", trigger: "blur" }],
  publicKey: [{ required: true, message: "请更新密钥", trigger: "blur" }],
  privateKey: [{ required: true, message: "请更新密钥", trigger: "blur" }],
  // privateKey: [
  //   { required: true, validator: privateKeyValidator, trigger: "blur" },
  // ],
});

// async function privateKeyValidator(rule: any, value: any, callback: any) {
//   callback();
// }

const validForm = async () => {
  console.log("保存");
  const valid = await formRef.value.validate();
  if (valid) {
    updateKey({ id: data.value.id, publicKey: data.value.publicKey }).then(
      (res) => {
        if (res) {
          ElMessage.success("保存成功");
          emit("close");
        }
      }
    );
  } else {
    return false;
  }
};
onMounted(() => {
  console.log("加载秘钥");
  console.log(props.rowData);
  getAppDetailApi(props.rowData.id).then((res) => {
    data.value = res as any;
  });
});
const copy = async (text: string) => {
  console.log(text);
  try {
    await toClipboard(text);
    ElMessage.success("复制成功");
  } catch (e) {
    ElMessage.success("复制失败");
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-textarea__inner) {
  color: #272e2c;
}
</style>
