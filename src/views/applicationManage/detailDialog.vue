<template>
  <el-drawer
    v-model="dialogFormVisible"
    title="应用详情"
    @close="$emit('close-dialog')"
    destroy-on-close
  >
    <el-descriptions :column="1" direction="horizontal">
      <el-descriptions-item label="应用类型" v-if="canCreateDMM">{{
        APP_TYPE_DESC[rowData.appType]
      }}</el-descriptions-item>
      <el-descriptions-item label="应用名称">{{
        rowData?.appName || "-"
      }}</el-descriptions-item>
      <el-descriptions-item
        v-if="rowData?.appType === APP_TYPE.DMM"
        label="主数据范围"
        >{{
          getScopeName(rowData?.masterDataScope) || "-"
        }}</el-descriptions-item
      >
      <el-descriptions-item label="应用前缀">{{
        rowData?.prefixName || "-"
      }}</el-descriptions-item>
      <el-descriptions-item label="部署地址">{{
        rowData?.deployAddress || "-"
      }}</el-descriptions-item>
      <el-descriptions-item label="系统版本">{{
        rowData?.sysVersion || "-"
      }}</el-descriptions-item>
      <el-descriptions-item label="维护时间">{{
        rowData?.updatedTime || "-"
      }}</el-descriptions-item>
    </el-descriptions>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, toRefs } from "vue";
import { useStore } from "vuex";
import { TableData } from "@/types/appManage";
import { APP_TYPE, APP_TYPE_DESC } from "@/utils/dataPlatform";
import { MASTER_DATA_ENUM } from "./constant";

const store = useStore();
const { canCreateDMM } = toRefs(store.getters);
const props = defineProps<{
  rowData: TableData;
}>();

const dialogFormVisible = ref(true);
const getScopeName = (dataScope: any) => {
  let scopeName = "";
  if (dataScope) {
    const scopeNameList = dataScope.split(",").map((item: any) => {
      const data = MASTER_DATA_ENUM.filter(
        (enumItem) => enumItem.key === Number(item)
      )[0];
      return data.value || "";
    });
    if (scopeNameList.length) {
      scopeName = scopeNameList.join(",");
    }
  }
  return scopeName;
};
</script>

<style lang="scss" scoped>
.el-select {
  width: 100%;
}
:deep(.el-descriptions__label) {
  width: 70px;
  min-width: 70px;
  display: inline-block;
}
</style>
