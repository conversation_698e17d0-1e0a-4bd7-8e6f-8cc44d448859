<template>
  <SearchLayout>
    <template #left
      ><el-button
        type="primary"
        @click="addApp"
        v-permission="AUTH_CODE.APP_MANAGER_ADD"
        >新增应用</el-button
      ></template
    >
    <template #right>
      <el-form
        v-permission="AUTH_CODE.APP_MANAGER_PAGE"
        ref="queryRef"
        :model="queryForm"
        :inline="true"
        @submit.prevent
      >
        <el-form-item style="width: 240px; max-width: 240px">
          <el-input
            v-model.trim="queryForm.appName"
            placeholder="请输入"
            @clear="getTableData(true)"
            clearable
          >
            <template #prefix>应用名称：</template>
          </el-input>
        </el-form-item>
        <el-form-item
          v-if="canCreateDMM"
          style="width: 240px; max-width: 240px"
        >
          <el-select
            v-model="queryForm.appType"
            placeholder="请选择"
            clearable
            @clear="getTableData(true)"
          >
            <template #prefix>应用类型：</template>
            <el-option
              v-for="item in Object.keys(APP_TYPE_DESC)"
              :key="item"
              :label="APP_TYPE_DESC[item]"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="width: 60px; max-width: 60px">
          <el-button type="primary" @click="() => getTableData(true)"
            >搜索</el-button
          >
        </el-form-item>
      </el-form>
    </template>
  </SearchLayout>
  <el-table :data="tableData" border size="small" v-loading="pageLoading">
    <el-table-column label="序号" type="index" width="55" />
    <el-table-column label="应用名称" show-overflow-tooltip>
      <template #default="scope">
        <span v-copy="scope.row.appName">{{ scope.row.appName || "-" }}</span>
      </template>
    </el-table-column>
    <el-table-column label="应用类型" v-if="canCreateDMM">
      <template #default="scope">
        {{ APP_TYPE_DESC[scope.row.appType] || "-" }}
      </template>
    </el-table-column>
    <el-table-column label="应用前缀" show-overflow-tooltip>
      <template #default="scope">
        <span v-copy="scope.row.prefixName">{{
          scope.row.prefixName || "-"
        }}</span>
      </template>
    </el-table-column>
    <el-table-column label="应用身份标识" show-overflow-tooltip>
      <template #default="scope">
        <span v-copy="scope.row.handleCode">{{
          scope.row.handleCode || "-"
        }}</span>
      </template>
    </el-table-column>
    <el-table-column label="部署地址" show-overflow-tooltip>
      <template #default="scope">
        <span v-copy="scope.row.deployAddress">{{
          scope.row.deployAddress || "-"
        }}</span>
      </template>
    </el-table-column>
    <el-table-column label="系统版本" show-overflow-tooltip>
      <template #default="scope">
        <span v-copy="scope.row.sysVersion">{{
          scope.row.sysVersion || "-"
        }}</span>
      </template>
    </el-table-column>
    <el-table-column label="维护时间" width="130">
      <template #default="scope">
        {{ scope.row.updatedTime || "-" }}
      </template>
    </el-table-column>
    <el-table-column label="操作" width="150">
      <template #default="scope">
        <el-button
          text
          type="primary"
          @click="handleDetail(scope.row)"
          v-permission="AUTH_CODE.APP_MANAGER_DETAIL"
          >详情</el-button
        >
        <el-button
          text
          type="primary"
          @click="handleVerify(scope.row)"
          v-permission="AUTH_CODE.APP_MANAGER_EDIT"
          >安全认证</el-button
        >
        <el-dropdown trigger="click" class="drop-btn" :hide-on-click="false">
          <el-button type="primary" text>
            <img src="@/assets/icons/svg/more.svg" />
          </el-button>
          <span></span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>
                <el-button
                  text
                  type="primary"
                  @click="handleEdit(scope.row)"
                  v-permission="AUTH_CODE.APP_MANAGER_EDIT"
                  >编辑</el-button
                >
              </el-dropdown-item>
              <el-dropdown-item>
                <el-popconfirm
                  confirm-button-text="确定"
                  cancel-button-text="取消"
                  title="删除后应用内的标识数据将被清空，确认删除吗?"
                  @confirm="clickDelBtn(scope.row.id)"
                >
                  <template #reference>
                    <el-button
                      text
                      type="primary"
                      v-permission="AUTH_CODE.APP_MANAGER_DELETE"
                      >删除</el-button
                    >
                  </template>
                </el-popconfirm>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
    </el-table-column>
  </el-table>
  <!-- 分页 -->
  <el-pagination
    v-model:currentPage="pagination.currentPage"
    v-model:page-size="pagination.pageSize"
    :page-sizes="[10, 20, 30, 40]"
    small
    background
    layout="total,  prev, pager, next, sizes,jumper"
    :total="pagination.total"
  />
  <AddDialog
    v-if="addVisible"
    @update-table="getTableData"
    @close-dialog="addVisible = false"
  />
  <EditDialog
    v-if="editVisible"
    @update-table="getTableData"
    @close-dialog="editVisible = false"
    :rowData="selectRow"
  />
  <DetailDialog
    v-if="detailVisible"
    @close-dialog="detailVisible = false"
    :rowData="selectRow"
  />
  <VerifyDialog
    v-if="verifyVisible"
    @close="handleCloseVerify"
    :rowData="selectRow"
  />
</template>
<script setup lang="ts">
import { onMounted, reactive, ref, watch, toRefs } from "vue";
import { useStore } from "vuex";
import { ElMessage, FormInstance } from "element-plus";
import { getAppListApi, deleteAppApi } from "@/api/appManege/index";
import AddDialog from "./addDialog.vue";
import EditDialog from "./editDialog.vue";
import DetailDialog from "./detailDialog.vue";
import VerifyDialog from "./verifyDialog.vue";
import { TableData } from "@/types/appManage";
import { AUTH_CODE } from "@/utils/authCode";
import SearchLayout from "@/components/searchLayout/index.vue";
import { APP_TYPE_DESC } from "@/utils/dataPlatform";

const store = useStore();
const { canCreateDMM } = toRefs(store.getters);

// 页面加载loading
const pageLoading = ref(false);
const queryRef = ref<FormInstance>();
const managerFormRef = ref<FormInstance>();
const queryForm = reactive({
  appName: "",
  appType: "",
});

const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

const tableData = ref<TableData[]>([]);

const addVisible = ref(false);
const editVisible = ref(false);
const detailVisible = ref(false);
const verifyVisible = ref(false);

const selectRow = ref<TableData>({
  appName: "",
  createdTime: "",
  deployAddress: "",
  handleCode: "",
  id: 0,
  prefixName: "",
  sysVersion: "",
  updatedTime: "",

  appType: 0,
});

watch([() => pagination.currentPage, () => pagination.pageSize], () => {
  getTableData(false);
});

// 查询托管列表
const getTableData = (isReset: boolean) => {
  if (isReset) {
    pagination.currentPage = 1;
    pagination.pageSize = 10;
    pagination.total = 0;
  }
  pageLoading.value = true;
  tableData.value = [];
  const { currentPage, pageSize } = pagination;
  getAppListApi({
    appName: queryForm.appName,
    appType: queryForm.appType,
    page: currentPage - 1,
    size: pageSize,
  }).then((res) => {
    pageLoading.value = false;
    tableData.value = res.content;
    pagination.total = res.totalCount;
  });
};

// 新增应用
const addApp = () => {
  addVisible.value = true;
};

// 查看
const handleDetail = (row: TableData) => {
  detailVisible.value = true;
  selectRow.value = row;
};

// 编辑
const handleEdit = (row: TableData) => {
  editVisible.value = true;
  selectRow.value = row;
};

const handleVerify = (row: TableData) => {
  selectRow.value = row;
  verifyVisible.value = true;
};
const handleCloseVerify = () => {
  verifyVisible.value = false;
};

// 删除
const clickDelBtn = (id: number) => {
  pageLoading.value = true;
  deleteAppApi({ id })
    .then(() => {
      ElMessage.success("应用删除成功!");
      getTableData(true);
    })
    .finally(() => {
      pageLoading.value = false;
    });
};
onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (AllPermission.includes(AUTH_CODE.APP_MANAGER_PAGE)) {
    getTableData(true);
  }
});
</script>
<style lang="scss" scoped>
.drop-btn {
  margin-left: 16px;
}
</style>
