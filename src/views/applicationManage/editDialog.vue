<template>
  <el-dialog
    v-model="dialogFormVisible"
    title="编辑应用"
    width="700px"
    @close="$emit('close-dialog')"
    destroy-on-close
    align-center
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="类型" prop="appType" v-if="canCreateDMM">
        <el-radio-group
          v-model="formData.appType"
          size="small"
          class="relate-type"
          disabled
        >
          <el-radio :label="APP_TYPE.DMM">{{
            APP_TYPE_DESC[APP_TYPE.DMM]
          }}</el-radio>
          <el-radio :label="APP_TYPE.NORMAL">{{
            APP_TYPE_DESC[APP_TYPE.NORMAL]
          }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="formData.appType === APP_TYPE.DMM"
        label="应用名称"
        prop="mpDmmAppId"
      >
        <el-cascader
          v-model="formData.mpDmmAppId"
          :options="appTreeList"
          style="width: 100%"
          :props="cascaderProps"
          disabled
        />
      </el-form-item>
      <el-form-item
        v-if="formData.appType === APP_TYPE.NORMAL"
        label="应用名称"
        prop="appName"
      >
        <el-input
          v-model.trim="formData.appName"
          placeholder="请输入应用名称"
          clearable
          disabled
        />
      </el-form-item>
      <el-form-item
        v-if="formData.appType === APP_TYPE.DMM"
        label="主数据范围"
        prop="masterDataIdList"
      >
        <el-select
          v-model="formData.masterDataIdList"
          multiple
          placeholder="请选择主数据范围"
          disabled
        >
          <el-option
            v-for="item in MASTER_DATA_ENUM"
            :key="item.key"
            :label="item.value"
            :value="item.key"
          />
        </el-select>
      </el-form-item>
      <el-form-item
        label="企业前缀"
        prop="prefixName"
        style="width: 100%"
        disabled
      >
        <el-input
          v-model.trim="formData.prefixName"
          placeholder="请选择企业前缀"
          disabled
        />
      </el-form-item>
      <el-form-item
        class="form-item-pl10"
        label="部署地址"
        prop="deployAddress"
      >
        <el-input
          v-model.trim="formData.deployAddress"
          placeholder="请输入部署地址"
          clearable
        />
      </el-form-item>
      <el-form-item class="form-item-pl10" label="系统版本" prop="sysVersion">
        <el-input
          v-model.trim="formData.sysVersion"
          placeholder="请输入系统版本"
          clearable
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="validForm" :loading="btnLoading">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref, computed, toRefs } from "vue";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { useStore } from "vuex";
import { editAppInfoApi, getMpDmmAppTree } from "@/api/appManege/index";
import { deepClone } from "@/utils/auth";
import { TableData } from "@/types/appManage";
import { MASTER_DATA_ENUM } from "./constant";
import { APP_TYPE, APP_TYPE_DESC } from "@/utils/dataPlatform";

const store = useStore();
const { canCreateDMM } = toRefs(store.getters);
const emit = defineEmits(["dialog-visible", "update-table"]);
// const props = defineProps(["rowData"]);
const props = defineProps<{
  rowData: TableData;
}>();

const cascaderProps = reactive({ value: "id", label: "name" });
const btnLoading = ref(false);
const formRef = ref<FormInstance>();
const dialogFormVisible = ref(true);

const appTreeList = ref([]);

const formData = ref<Record<string, any>>({
  appName: "",
  appType: APP_TYPE.DMM,
  masterDataIdList: [],
  masterDataScope: "", // masterDataIdList的join(',')
  deployAddress: "",
  sysVersion: "",
  handleCode: "",
  prefixName: "",
});
const rules = reactive<FormRules>({
  appName: [
    {
      required: true,
      message: "请输入应用名称",
      trigger: "blur",
    },
    {
      max: 30,
      message: "应用名称最大长度不超过30",
      trigger: "blur",
    },
  ],
  prefixName: [
    {
      required: true,
      message: "请选择标识编码",
      trigger: "change",
    },
  ],
  deployAddress: [
    {
      max: 30,
      message: "部署地址最大长度不超过30",
      trigger: "blur",
    },
  ],
  sysVersion: [
    {
      max: 30,
      message: "系统版本最大长度不超过30",
      trigger: "blur",
    },
  ],
});

// 提交
const validForm = () => {
  if (!formRef.value) return;
  formRef.value.validate((valid: any) => {
    if (valid) {
      editAppInfo();
    } else {
      ElMessage.error("校验不通过!");
      return false;
    }
  });
};

const editAppInfo = () => {
  btnLoading.value = true;
  editAppInfoApi({
    ...formData.value,
    mpDmmAppId:
      formData.value.appType === APP_TYPE.DMM
        ? formData.value.mpDmmAppId.join(",")
        : "",
  })
    .then(() => {
      dialogFormVisible.value = false;
      emit("update-table", true);
      ElMessage({
        message: "编辑应用成功",
        type: "success",
      });
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

// 获取中台应用树
const getMpDmmAppTreeData = () => {
  getMpDmmAppTree().then((res) => {
    appTreeList.value = res || [];
  });
};

const onCascaderChange = (value: any) => {
  // console.log("===应用名称级联选中change获取的值====", value);
  // formData.appName = xxx // 这里根据选择的值，把id赋值给mpDmmAppId，name赋值给appName
  const appName = getAppName(appTreeList.value, 0);
  function getAppName(
    data: { name: string; id: number; children?: any }[],
    index: number
  ) {
    const appItem = data.filter((item) => item.id === value[index])[0];
    let name = appItem.name;
    if (appItem.children && appItem.children.length) {
      name = getAppName(appItem.children, index + 1);
    }
    return name;
  }
  formData.value.appName = appName;
};

onMounted(() => {
  formData.value = deepClone(props.rowData);
  getMpDmmAppTreeData();

  if (formData.value.appType === APP_TYPE.DMM) {
    formData.value.mpDmmAppId = formData.value.mpDmmAppId.split(",");
    formData.value.masterDataIdList = formData.value.masterDataScope
      .split(",")
      .map((item: any) => item * 1);
  }
});
</script>

<style lang="scss" scoped>
.el-select {
  width: 100%;
}
</style>
