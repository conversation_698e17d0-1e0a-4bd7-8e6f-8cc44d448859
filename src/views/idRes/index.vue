<template>
  <div class="handle-query">
    <div class="handle-query-header">
      <div class="navbar-logo">
        <img src="@/assets/logo/logo_middle.png" alt="" class="logo" />
      </div>
      <div>
        <el-button text type="primary" @click="handleGoBack">返回</el-button>
      </div>
    </div>
    <div class="handle-query-body">
      <div class="header">
        <div class="header-left">标识解析</div>
        <div class="header-right">
          <el-input
            v-model.trim="data.ids"
            placeholder="请输入标识"
            style="width: 396px"
            clearable
          >
            <template #prepend> 标识: </template>
            <template #suffix
              ><el-icon @click="startScan = true"><Camera /></el-icon
            ></template>
          </el-input>
          <el-input
            v-if="isAppUser"
            v-model.trim="data.secretKey"
            placeholder="请输入密钥"
            style="width: 396px; margin-left: 12px"
            clearable
          >
            <template #prepend> 密钥: </template>
          </el-input>
          <el-button @click="showScan = true" style="margin-left: 10px"
            >扫码</el-button
          >
          <el-button
            type="primary"
            :disabled="!data.ids"
            @click="handleSearch"
            class="handle-query-search-form-btn"
            >查询</el-button
          >
        </div>
      </div>
      <div class="handle-query-result" v-loading="loading">
        <div v-if="!data.searchData" class="handle-query-result-blank">
          <img src="@/assets/blank.svg" />
          <div class="handle-query-result-blank-text">暂无数据</div>
        </div>
        <matrix-graph
          :secret-key="data.secretKey"
          v-else
          ref="matrixGraphRef"
        ></matrix-graph>
      </div>
    </div>
    <div class="handle-query-footer">
      <span
        >2017-{{
          new Date().getFullYear()
        }}技术支持：中国信息通信研究院(CAICT)</span
      >
    </div>
    <ScanQrcode v-if="startScan" @success="scanQrSuccess" @fail="scanQrFail">
    </ScanQrcode>
    <scan
      v-if="showScan"
      @close="showScan = false"
      @success="scanSuccess"
    ></scan>
  </div>
</template>

<script setup lang="ts">
import { nextTick, toRefs, ref, reactive } from "vue";
import { useStore } from "vuex";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { idResolve } from "@/api/idRes/index";
import { getPublicKey, encrypt } from "@/utils/forgeEncrypt";
import MatrixGraph from "./components/matrix-graph.vue";
import ScanQrcode from "./components/scan-qrcode.vue";
import scan from "./scan.vue";

const store = useStore();
const { isAppUser } = toRefs(store.getters);

const router = useRouter();

const loading = ref(false);

const startScan = ref(false);

const showScan = ref(false);

const matrixGraphRef = ref();
const data = reactive({
  ids: "",
  secretKey: "",
  searchData: null,
});

const scanSuccess = (scanResult: any) => {
  if (scanResult && scanResult.length) {
    data.ids = scanResult[0];
    ElMessage.success("识别成功");
  }
  showScan.value = false;
};

async function handleSearch() {
  loading.value = true;
  let key = data.secretKey;
  if (key) {
    const publicKey = await getPublicKey().catch(() => {
      return "";
    });
    key = encrypt(key, publicKey);
  }
  const p = isAppUser.value
    ? { handle: data.ids, secretKey: key }
    : { handle: data.ids };
  idResolve(p)
    .then((response: any) => {
      data.searchData = response;
      nextTick(() => {
        matrixGraphRef.value.refresh(data.searchData);
      });
    })
    .catch(() => {
      data.searchData = null;
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleGoBack() {
  router.back();
}

const scanQrSuccess = (info: any) => {
  startScan.value = false;
  data.ids = info;
};
const scanQrFail = (msg: string) => {
  startScan.value = false;
  ElMessage({
    message: msg,
    type: "error",
  });
};
</script>
<style lang="scss" scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 0 20px;

  .header-left {
    font-weight: 500;
    line-height: 12px;
    padding-left: 8px;
    border-left: 3px solid #007457;
  }

  .header-right {
    display: flex;
    align-items: center;
  }

  :deep(.el-input__wrapper) {
    box-shadow: none;
    border-radius: 2px;
    background-color: #eef2f1;
  }
  :deep(.el-input-group__prepend) {
    box-shadow: none;
    background-color: #eef2f1;
    padding-right: 0;
  }
}
.handle-query-search-wrap {
  min-width: 1440px;
  height: 168px;
  background: url("@/assets/images/handle-search.png") no-repeat center;
  background-size: 100% 100%;
}
.handle-query-search-form {
  width: 600px;
  margin: 60px auto;
  .el-input {
    height: 48px;
    line-height: 48px;
  }
  :deep(.el-input-group__append) {
    color: #fff;
    background-color: #00a57c;
    box-shadow: none;
  }
}

.handle-query-search-form-btn {
  // width: 100px;
  // font-size: 16px;
  // height: 100%;
  // border-top-left-radius: 0;
  // border-bottom-left-radius: 0;
  margin-left: 8px;
}

.handle-query-result {
  flex: 1;
  background: #f5f7f7;
  display: flex;
  flex-direction: column;
  position: relative;
  // overflow: auto;
  margin: 20px 0 0 0;
}

.handle-query {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.handle-query-body {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.handle-query-header {
  height: 58px;
  position: relative;
  background-color: #d8e7e3;
  box-shadow: 0 1px 4px #00152914;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  .navbar-logo {
    width: 276px;
    min-width: 276px;
    padding-left: 20px;
    img {
      width: 164px;
      height: 50px;
    }
  }
}
.handle-query-footer {
  line-height: 35px;
  height: 36px;
  min-height: 36px;
  text-align: center;
  color: #657180;
  font-size: 12px;
  border-top: 1px solid #e7e9f0;
  background-color: #fff;
  a {
    margin-left: 0.15rem;
  }
}

.handle-query-result-blank {
  position: absolute;
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  img {
    width: 120px;
    height: auto;
  }
  .handle-query-result-blank-text {
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    margin-top: 4px;
    color: #7b9790;
  }
}
</style>
