<template>
  <div class="matrix-graph-box">
    <div class="matrix-graph">
      <div
        ref="matrixGraphRef"
        class="matrix-handle-node"
        id="handleNode"
      ></div>
    </div>

    <TeleportContainer />
  </div>
</template>
<script setup lang="ts">
import {
  ref,
  createVNode,
  defineExpose,
  reactive,
  computed,
  nextTick,
  provide,
  defineProps,
} from "vue";
import { Graph } from "@antv/x6";
import { useTeleport } from "@antv/x6-vue-shape";

import { deepClone } from "@/utils/auth";
import HandleNode from "./handle-node.vue";
import { GRAPH_TYPE, OBJECT_ENTITY_TYPE_NAME } from "@/utils/constant";

const props = defineProps({
  secretKey: {
    type: String,
    default: "",
  },
});

const itemMiddleY = 13;

const UNIQ_GRAPH_ID = "UNIQ_GRAPH_ID";

const TeleportContainer = useTeleport(UNIQ_GRAPH_ID);

const matrixGraphRef = ref(null);

const currentData = reactive<any>({
  data: {},
});

interface Nodes {
  id: any;
  shape: string;
  width: number;
  height: number;
  view: string;
  component: string;
  parentId: any;
  data: any;
  ports: any;
}
interface Edges {
  source: any;
  target: any;
}
interface GraphData {
  nodes: Nodes[];
  edges: Edges[];
}
const graphData = reactive<GraphData>({
  nodes: [],
  edges: [],
});

const mouseenterEdgeSourceId = ref(""); // 存储鼠标hover edge上的源port id
provide("mouseenterEdgeSourceId", mouseenterEdgeSourceId);

// 成环节点不执行添加
const isCircle = (sourceNodeId: string, targetNodeId: string) => {
  const cell = graph.value.getCellById(sourceNodeId);
  const predNodeList = graph.value.getPredecessors(cell);
  const preNodeIdList = predNodeList.map(
    (preNode: Record<string, any>) => preNode.id
  );

  // 目标节点在先驱路径上，不连线，避免出现环
  if (preNodeIdList.includes(targetNodeId)) return true;
  return false;
};
const addNode = (params: {
  handle: string;
  item: { label: string; value: string; field: number }; // label:引出下一个节点的属性名称 field: 引出下一个节点对应的属性所有  value: 就是下一个node的handle
  nextNode: any;
  isExist?: false;
}) => {
  if (isCircle(params.handle, params.item.value)) return;
  // 已存在的节点，不会再次addnode，只连线
  if (params.isExist) {
    const edge = setEdge(params.handle, params.item);
    graph.value.addEdge(edge);
    return;
  }
  const newData: Record<string, any> = formatData(params.nextNode);
  newData.attrGroup = `${params.handle}-${params.item.field}`;
  setNode(newData, params.handle, params.item.field);

  const edge = setEdge(params.handle, params.item);
  graph.value.addEdge(edge);
};

// 删除标识
const deleteNode = (handle: string) => {
  const nodes = graph.value.getNodes();
  const matchNode = nodes.find((node: any) => {
    if (node.id === `${handle}`) return true;
  });

  if (matchNode) {
    const cell = graph.value.getCellById(matchNode.id);

    const successorList = graph.value.getSuccessors(cell);

    const toDelList = [matchNode.id];
    successorList.forEach((sNode: Record<string, any>) =>
      toDelList.push(sNode.id)
    );

    toDelList.forEach((id: string) => {
      graph.value.removeNode(id);
    });
  }
};

const graph = ref();

Graph.registerVueComponent(
  "handle-node",
  {
    render: () => {
      return createVNode(HandleNode, {
        add: addNode, // 查看下一个标识
        delete: deleteNode, // 删除节点
        onHeightChange,
        secretKey: props.secretKey,
      });
    },
  },
  true
);

// 获取节点的高度
function getNodeHeight(data: any) {
  const extendData = data.values.filter((item: any) => {
    return item.fieldSourceType === 1;
  });
  return extendData.length > 0
    ? data.values.length * 36 + 140 + 72
    : data.values.length * 36 + 140 + 36; // TODO 最后一个36需要替换为查询+header的高度
}

// 获取ports
const getNodePorts = (handle: string, basicValues: any, extendValues: any) => {
  const items: any = [
    {
      id: `${handle}-out`, // 头部的桩
      group: "right",
      args: {
        x: 298,
        y: 50,
      },
    },
    {
      id: `${handle}-in`, // 头部的桩
      group: "left",
      args: {
        x: 0,
        y: 50,
      },
    },
    {
      id: `${handle}-basic-out`, // 头部的桩
      group: "right",
      args: {
        x: 298,
        y: 140 + itemMiddleY,
      },
      field: "FEBASIC",
    },
    {
      id: `${handle}-basic-in`, // 头部的桩
      group: "left",
      args: {
        x: 0,
        y: 140 + itemMiddleY,
      },
      field: "FEBASIC",
    },
  ];

  basicValues.forEach((item: any, index: number) => {
    items.push({
      id: `${handle}-${item.field}-out`, // portId: 当前标识-属性field
      group: "right",
      args: {
        x: 298,
        y: (index + 1) * 36 + 140 + itemMiddleY,
      },
      field: item.field, // 链接装所在的属性索引
      fieldSourceType: item.fieldSourceType,
    });
    items.push({
      id: `${handle}-${item.field}-in`, // portId: 当前标识-属性field
      group: "left",
      args: {
        x: 0,
        y: (index + 1) * 36 + 140 + itemMiddleY,
      },
      field: item.field, // 链接装所在的属性索引
      fieldSourceType: item.fieldSourceType,
    });
  });
  if (extendValues.length) {
    items.push({
      id: `${handle}-extend-out`,
      group: "right",
      args: {
        x: 298,
        y: (basicValues.length + 1) * 36 + 140 + itemMiddleY,
      },
      field: "FEEXTEND",
    });
    items.push({
      id: `${handle}-extend-in`,
      group: "left",
      args: {
        x: 0,
        y: (basicValues.length + 1) * 36 + 140 + itemMiddleY,
      },
      field: "FEEXTEND",
    });
    extendValues.forEach((item: any, index: number) => {
      items.push({
        id: `${handle}-${item.field}-out`, // portId: 当前标识-属性field
        group: "right",
        args: {
          x: 298,
          y: (index + basicValues.length + 2) * 36 + 140 + itemMiddleY,
        },
        field: item.field, // 链接装所在的属性索引
        fieldSourceType: item.fieldSourceType,
      });
      items.push({
        id: `${handle}-${item.field}-in`, // portId: 当前标识-属性field
        group: "left",
        args: {
          x: 0,
          y: (index + basicValues.length + 2) * 36 + 140 + itemMiddleY,
        },
        field: item.field, // 链接装所在的属性索引
        fieldSourceType: item.fieldSourceType,
      });
    });
  }

  return items;
};

const portGroupConfig = {
  // 链接桩位置-矩形节点右侧均匀分布
  right: {
    // 链接桩DOM结构定义
    markup: [
      {
        tagName: "rect", // svg html 元素
        selector: "body", // 该元素的选择器，通过选择器来定位该元素或为该元素指定样式
      },
    ],
    position: {
      name: "absolute",
      args: { x: 298, y: 0 },
    },
    attrs: {
      // 设置桩的样式
      body: {
        width: 2,
        height: 10,
        fill: "transparent", // TODO test 明确观察桩的位置，去掉注释
      },
    },
  },
  left: {
    // 链接桩DOM结构定义
    markup: [
      {
        tagName: "rect", // svg html 元素
        selector: "body", // 该元素的选择器，通过选择器来定位该元素或为该元素指定样式
      },
    ],
    position: {
      name: "absolute",
      args: { x: 0, y: 0 },
    },
    attrs: {
      // 设置桩的样式
      body: {
        width: 2,
        height: 10,
        fill: "transparent", // TODO test 明确观察桩的位置，去掉注释
      },
    },
  },
};

// 设置节点
function setNode(data: any, parentId: string | null, field?: number) {
  const basicValues = data.values.filter((item: any) => {
    return item.fieldSourceType === 0;
  });
  const extendValues = data.values.filter((item: any) => {
    return item.fieldSourceType === 1;
  });
  const items = getNodePorts(data.handle, basicValues, extendValues);

  const nodes = graph.value.getNodes();

  // 再添加前，删除同一属性组的节点
  const matchNode = nodes.find((node: any) => {
    if (node.data.attrGroup === `${parentId}-${field}`) return true;
  });

  if (matchNode) {
    const cell = graph.value.getCellById(matchNode.id);

    const successorList = graph.value.getSuccessors(cell);

    const toDelList = [matchNode.id];
    successorList.forEach((sNode: Record<string, any>) =>
      toDelList.push(sNode.id)
    );

    toDelList.forEach((id: string) => {
      graph.value.removeNode(id);
    });
  }

  data.isRootNode = parentId === null; // 只有第一个Node的parentId:null的为root
  data.isCollapsed = !(parentId === null);

  const node = {
    id: data.handle,
    shape: "vue-shape",
    width: 300,
    height: getNodeHeight(data),
    view: UNIQ_GRAPH_ID,
    component: "handle-node",
    parentId, // 用途：1.在handle-node中识别是否是root节点
    data,
    ports: {
      groups: portGroupConfig,
      items, // 链接桩ID
    },
    attrGroup: data.attrGroup, // 属于的属性组，属性组id: 所在handle-属性的field
    options: true,
  };

  if (!parentId) {
    // 渲染根节点
    graph.value.addNode(node);
    const cell = graph.value.getCell(node.data.handle);
    cell.position(30, 30);

    return;
  }

  let translateX = 0;
  let translateY = 0;
  const parentNode = graph.value.getCell(parentId);
  const { width, height } = parentNode.size();
  const parentChildren = parentNode.children;
  if (!parentChildren) {
    // 是第一个子节点
    translateX = width + 30; // x坐标偏移30
    translateY = 0; // y坐标不偏移
  } else {
    let tempY = 0; // 获取y最大的节点
    let tempHeight = 0;
    parentChildren.forEach((children: any) => {
      if (children.view) {
        const { height } = children.size();
        const { y } = children.position();
        if (tempY < y) {
          tempY = y;
          tempHeight = height;
        }
      }
    });
    translateX = width + 30;
    translateY = tempHeight + 30;
  }

  const childNode = graph.value.addNode(node);
  parentNode.addChild(childNode);

  childNode.position(translateX, translateY, { relative: true });
}

// 设置连线
function setEdge(
  sourceId: string,
  targetData: { label: string; value: string; field: number }
) {
  return {
    source: {
      cell: sourceId, // 源节点
      port: `${sourceId}-${targetData.field}-out`,
      realPortId: `${sourceId}-${targetData.field}`, // 用来高亮
    },
    target: {
      cell: targetData.value,
      port: `${targetData.value}-in`,
      // port: `${targetData.value}-${targetData.value}--`,
    },
    connector: {
      name: "rounded",
    },
  };
}

// 获取对象标识固定字段
function wrapHeaderInfo(data: any) {
  const { objectValues, appValues, handle } = data;
  const { entName, appName, handleCode } = appValues;
  const { name, entityType } = objectValues;

  return {
    handle,
    handleName: name || "-",
    entityTypeDesc: OBJECT_ENTITY_TYPE_NAME[entityType] || "-",
    appInfo: `${entName || "-"}-${appName || "-"}`,
  };
}

// 格式化data,references里面有多个值，需要并列展示
function formatData(data: any) {
  const headerInfo = wrapHeaderInfo(data); // 固定的几个值
  const newValues: Record<string, any>[] = [];
  data.values?.forEach((val: any) => {
    const { description } = val;

    newValues.push({
      label: description,
      canCollapse: false,

      ...val,
    });
  });

  return {
    isRootNode: false, // 标记是否是第一个节点
    type: data.type, // type: 1：实例标识解析， 2：对象标识解析
    handle: data.handle || "-", // 当前节点的handle
    headerInfo, // 封装的节点头部显示的基础信息
    searchContent: "", // 搜索内容
    isCollapsed: true, // 标识整个node是否收起
    nodeHeight: 0,
    values: [...newValues], // 节点需要的和原始value的融合
  };
}

// 重新渲染
function refresh(data: any) {
  // showHandleCommon.value = data.type === HANDLE_TYPE.OBJECT;
  currentData.graphType = GRAPH_TYPE.COMPLEX;
  currentData.data = deepClone(data);
  // if (!showHandleCommon.value) {
  if (graph.value) {
    graph.value.dispose();
  }
  nextTick(() => {
    graph.value = new Graph({
      container: document.getElementById("handleNode")!,
      autoResize: true,
      grid: false,
      panning: {
        enabled: true,
        modifiers: "shift",
      },
      connecting: {
        router: {
          name: "er",
          args: {
            offset: 25,
            direction: "H",
          },
        },
      },
      scroller: {
        enabled: true,
        pannable: true,
      },
      mousewheel: {
        enabled: true,
        modifiers: ["ctrl", "meta"], // ctrl键 + 滚轮，缩放
        minScale: 0.1,
        maxScale: 4,
      },
    });

    graph.value.on("edge:mouseenter", ({ edge }: any) => {
      mouseenterEdgeSourceId.value = edge.source.realPortId;
    });

    graph.value.on("edge:mouseleave", () => {
      mouseenterEdgeSourceId.value = "";
    });

    const newData: Record<string, any> = formatData(currentData.data);
    newData.attrGroup = null;
    graphData.nodes = [];
    graphData.edges = [];
    setNode(newData, null);
  });
  // }
}

const onHeightChange = (node: any, heightList: Record<string, any>[]) => {
  // node节点的高度变化时，重新计算链接桩的位置

  const len = node.port.ports.length;
  const basicY = 153; // 基础属性位置
  let extendY = 189; // 扩展属性收起状态位置

  // 有属性列表时，才去动态计算
  if (len > 1) {
    const headerH = 140; // TODO 头部的高度， 待设置常量
    let reduceH = 0;

    const newPortPos: Record<string, number> = {};
    heightList.forEach(({ field, height, type }) => {
      const posY = headerH + reduceH + (height / 2 - 5);
      if (field === "FEEXTEND") {
        extendY = posY;
      }
      newPortPos[field] = posY;
      reduceH += height;
    });

    node.port.ports.forEach((port: any) => {
      const { field, fieldSourceType } = port; // 头部没有field
      if (field && field !== "FEBASIC") {
        const newY =
          newPortPos[field] || (fieldSourceType === 0 ? basicY : extendY);
        // 头部和基础属性不需要修改位置
        node.setPortProp(port.id, "args", {
          x: port.args.x,
          y: newY,
        });
      }
    });
  }
};

defineExpose({
  refresh,
});
</script>
<style lang="scss" scoped>
:deep(.x6-graph-grid) {
  cursor: move;
}
:deep(.matrix-handle-node) {
  cursor: move;
}
.matrix-graph-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f6f7fb;
  // height: 560px;
  width: 100%;
}

.matrix-graph-type {
  margin: 12px;
}

.matrix-graph {
  flex: 1;
  width: 100%;
  height: 100%;
  display: flex;
  .matrix-handle-node {
    flex: 1;
  }
}
</style>
