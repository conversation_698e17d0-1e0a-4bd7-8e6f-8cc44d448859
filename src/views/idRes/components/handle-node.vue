<template>
  <div
    class="handle-node-box"
    ref="nodeContainer"
    v-loading="loading"
    :class="{ 'not-root-node': !nodeInfo.isRootNode }"
  >
    <div class="header">
      <div class="name">
        <ellipsisText :value="nodeInfo.headerInfo.handleName" />
        <div class="name-right-wrap">
          <span class="name-right">
            {{ nodeInfo.headerInfo.entityTypeDesc }}
          </span>
          <el-popconfirm
            v-if="!nodeInfo.isRootNode"
            title="确认取消展示当前及其下属所有标识解析结果吗?"
            :icon="InfoFilled"
            @confirm="handleDelete(nodeInfo.headerInfo.handle)"
          >
            <template #reference>
              <div class="name-delete">
                <el-icon><CloseBold icon-color="#7B9790" /></el-icon>
              </div>
            </template>
          </el-popconfirm>
        </div>
      </div>
      <ellipsisText :value="nodeInfo.headerInfo.handle" />
      <ellipsisText class="app" :value="nodeInfo.headerInfo.appInfo" />
      <div class="search-input">
        <el-input
          v-model="nodeInfo.searchContent"
          size="small"
          clearable
          placeholder="请输入属性名称"
          :prefix-icon="Search"
          @input="onSearchChange"
        />
      </div>
    </div>

    <div
      class="attr-box handle-node-body-item cursor-pointer"
      data-key="FEBASIC"
      @click="basicCollapsed = !basicCollapsed"
    >
      <div class="attr-box-text">基础属性</div>
      <div class="search-expand-collapse" style="cursor: pointer">
        <el-icon>
          <ArrowDownBold v-if="basicCollapsed" /><ArrowUpBold v-else />
        </el-icon>
        <span>{{ basicCollapsed ? "详情" : "收起" }}</span>
      </div>
    </div>
    <div
      class="handle-node-body-null handle-node-body-item"
      v-if="!basicSearchResult.length && !basicCollapsed"
    >
      <el-empty :image-size="48" description="暂无搜索结果" />
    </div>
    <div class="handle-node-body" v-if="!basicCollapsed">
      <div v-for="item in basicSearchResult" :key="item.field">
        <div
          class="handle-node-body-item"
          :class="{
            'align-top': isRightList(item),
            'item-hover':
              `${nodeInfo.handle}-${item.field}` === mouseenterEdgeSourceId,
          }"
          :data-key="item.field"
          :data-type="item.fieldSourceType"
        >
          <div class="handle-node-body-item-left">
            <ellipsisText :value="item.label" :copy="false"></ellipsisText>
            <div class="database" v-if="showDatabase(item)">
              <ellipsisText :value="`数据库：${item?.databaseName || '-'}`">
              </ellipsisText>
            </div>
          </div>
          <!--1 右侧内容 -->
          <div v-if="!item.hasAuth" class="handle-node-body-item-right-red">
            无权限
          </div>
          <div v-else-if="!isRightList(item)" class="right-container">
            <div class="database" v-if="showDatabase(item)">
              <ellipsisText value="<实例数据源>"></ellipsisText>
              <ellipsisText :value="`数据库IP：${item?.databaseIp || '-'}`">
              </ellipsisText>
            </div>
            <div>
              <!-- 1.1.1 是可点击的链接形式 -->
              <div
                class="handle-node-body-item-right"
                v-if="isClickHandle(formatRightValue(item))"
              >
                <el-button
                  link
                  type="primary"
                  @click="handleClick(formatRightValue(item), item)"
                >
                  <ellipsisText
                    :value="formatRightValue(item)"
                    :copy="false"
                  ></ellipsisText>
                </el-button>
              </div>
              <!-- 1.1.2 只显示文案 -->
              <div class="handle-node-body-item-right" v-else>
                <ellipsisText
                  :value="formatRightValue(item)"
                  :copy="false"
                ></ellipsisText>
              </div>
            </div>
          </div>
          <div v-else class="right-container">
            <div class="database" v-if="showDatabase(item)">
              <ellipsisText value="<实例数据源>"></ellipsisText>
              <ellipsisText :value="`数据库IP：${item?.databaseIp || '-'}`">
              </ellipsisText>
            </div>
            <PageAttr
              :resolve-type="nodeInfo.type"
              :attr-item="item"
              :secret-key="secretKey"
              :node-handle="nodeInfo.handle"
              :node-values="nodeInfo.values"
              @click-handle="handleClick"
            ></PageAttr>
          </div>
        </div>
      </div>
      <div class="no-data" v-if="!nodeInfo.values.length">暂无搜索数据</div>
    </div>

    <!-- 扩展属性 -->
    <div
      v-if="extendItems.length"
      class="attr-box is-extend handle-node-body-item cursor-pointer"
      data-key="FEEXTEND"
      @click="extendCollapsed = !extendCollapsed"
    >
      <div class="attr-box-title">扩展属性</div>
      <div class="search-expand-collapse" style="cursor: pointer">
        <el-icon>
          <ArrowDownBold v-if="extendCollapsed" /><ArrowUpBold v-else />
        </el-icon>
        <span>{{ extendCollapsed ? "详情" : "收起" }}</span>
      </div>
    </div>
    <div
      class="handle-node-body-null handle-node-body-item"
      v-if="
        extendItems.length && !extendSearchResult.length && !extendCollapsed
      "
    >
      <el-empty :image-size="48" description="暂无搜索结果" />
    </div>
    <div class="handle-node-body is-extend" v-if="!extendCollapsed">
      <div v-for="item in extendSearchResult" :key="item.field">
        <div
          class="handle-node-body-item"
          :class="{
            'align-top': isRightList(item),
            'item-hover':
              `${nodeInfo.handle}-${item.field}` === mouseenterEdgeSourceId,
          }"
          :data-key="item.field"
        >
          <div class="handle-node-body-item-left">
            <div class="extend-word-wrap">
              <div class="extend-word-text">
                <ellipsisText :value="item.label" :copy="false"></ellipsisText>
              </div>

              <div class="extend-word">扩</div>
            </div>

            <div class="database" v-if="showDatabase(item)">
              <ellipsisText :value="`数据库：${item?.databaseName || '-'}`">
              </ellipsisText>
            </div>
          </div>
          <!--1 右侧内容 -->
          <div v-if="!item.hasAuth" class="handle-node-body-item-right-red">
            无权限
          </div>
          <div v-else-if="!isRightList(item)" class="right-container">
            <div class="database" v-if="showDatabase(item)">
              <ellipsisText value="<实例数据源>"></ellipsisText>
              <ellipsisText :value="`数据库IP：${item?.databaseIp || '-'}`">
              </ellipsisText>
            </div>
            <div>
              <!-- 1.1.1 是可点击的链接形式 -->
              <div
                class="handle-node-body-item-right"
                v-if="isClickHandle(formatRightValue(item))"
              >
                <el-button
                  link
                  type="primary"
                  @click="handleClick(formatRightValue(item), item)"
                >
                  <ellipsisText
                    :value="formatRightValue(item)"
                    :copy="false"
                  ></ellipsisText>
                </el-button>
              </div>
              <!-- 1.1.2 只显示文案 -->
              <div class="handle-node-body-item-right" v-else>
                <ellipsisText
                  :value="formatRightValue(item)"
                  :copy="false"
                ></ellipsisText>
              </div>
            </div>
          </div>
          <div v-else class="right-container">
            <div class="database" v-if="showDatabase(item)">
              <ellipsisText value="<实例数据源>"></ellipsisText>
              <ellipsisText :value="`数据库IP：${item?.databaseIp || '-'}`">
              </ellipsisText>
            </div>
            <PageAttr
              :resolve-type="nodeInfo.type"
              :attr-item="item"
              :secret-key="secretKey"
              :node-handle="nodeInfo.handle"
              :node-values="nodeInfo.values"
              @click-handle="handleClick"
            ></PageAttr>
          </div>
        </div>
      </div>
      <div class="no-data" v-if="!nodeInfo.values.length">暂无搜索数据</div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { inject, onMounted, onUnmounted, defineProps, ref } from "vue";
import { useMutationObserver } from "@vueuse/core";
import { Search, InfoFilled } from "@element-plus/icons-vue";
import { getPublicKey, encrypt } from "@/utils/forgeEncrypt";
import { idResolve } from "@/api/idRes/index";
import { FIELD_TYPE_MAP, HANDLE_TYPE } from "@/utils/constant";
import ellipsisText from "@/components/ellipsisText/index.vue";
import PageAttr from "./page-attr.vue";

const getGraph: any = inject("getGraph");
const getNode: any = inject("getNode");
const mouseenterEdgeSourceId = inject("mouseenterEdgeSourceId");
const graph = ref();
const node = ref();
const loading = ref(false);
let oldNodeHeight = 0;

const handleRegex = /^\d{2}\.\d{4}\.\d{4}\/.*$/;

const props = defineProps({
  add: {
    type: Function,
  },
  delete: {
    type: Function,
  },
  updateGraphData: {
    type: Function,
  },
  onHeightChange: {
    type: Function,
  },
  secretKey: {
    type: String,
    default: "",
  },
});

const nodeInfo = ref<any>({
  type: "", // 解析类型，实例 or 对象
  handle: "", // 节点标识
  headerInfo: {}, // 头部标识相关信息展示
  values: [], // 属性列表
  isRootNode: true,
  searchContent: "",
  isCollapsed: true,
});

const nodeContainer = ref();

const searchResult = ref<Record<string, any>>([]);

const basicItems = ref<Record<string, any>>([]);
const extendItems = ref<Record<string, any>>([]);
const basicSearchResult = ref<Record<string, any>>([]); // 基础属性
const extendSearchResult = ref<Record<string, any>>([]); // 扩展属性
const basicCollapsed = ref(false);
const extendCollapsed = ref(false);

const handleDelete = (handle: string) => {
  props && props.delete && props.delete(handle);
};

const onSearchChange = () => {
  basicSearchResult.value = basicItems.value.filter(
    (item: { label: string; value: any }) =>
      item.label
        .toLowerCase()
        .indexOf(nodeInfo.value.searchContent.toLowerCase()) > -1
  );
  extendSearchResult.value = extendItems.value.filter(
    (item: { label: string; value: any }) =>
      item.label
        .toLowerCase()
        .indexOf(nodeInfo.value.searchContent.toLowerCase()) > -1
  );
  basicCollapsed.value = false; // 搜索后，默认展开
  extendCollapsed.value = false; // 搜索后，默认展开
};

// 对象标识展示数据库
const showDatabase = (item: Record<string, any>) => {
  const { fieldType, databaseName } = item;
  return (
    nodeInfo.value.type !== HANDLE_TYPE.HANDLE &&
    fieldType === FIELD_TYPE_MAP.source &&
    databaseName
  );
};

const isClickHandle = (handle: string) => {
  if (handle === nodeInfo.value.headerInfo.handle) {
    // 标识是本标识
    return false;
  }
  const httpsReg = /^(HTTPS|https):\/\/.*/; // 支持https开头
  // 统一用正则判断是否可点
  return (
    handleRegex.test(handle) ||
    httpsReg.test(handle) ||
    `${handle}`.includes("/99") ||
    `${handle}`.includes("99.1000.1")
  );
};

/*
item: {
      label: description,
      show: true,
      canCollapse: false,
      isCollapsed: false,
      ...val, // val是解析接口返回的values中的单个值
    }
 */
const formatRightValue = (item: Record<string, any>) => {
  const { fieldType, value, reference, hasAuth } = item;
  // 对象解析类型，右侧显示内容封装
  if (nodeInfo.value.type === HANDLE_TYPE.OBJECT) {
    const contentMap = {
      [FIELD_TYPE_MAP.fixed]: value,
      [FIELD_TYPE_MAP.source]: value, // value是数组时，不走这
      [FIELD_TYPE_MAP.handleValue]: reference?.referenceHandle,
      [FIELD_TYPE_MAP.handleWithAttr]: `${reference?.referenceHandle || "-"}`, // 实际显示时，需要添加不可点击的_${reference?.referenceHandleProp}
    };
    return contentMap[fieldType];
  }
  return item.value;
};

// 右侧显示的是否是关联的多个可解析的列表
const isRightList = (item: Record<string, any>) => {
  // 对象解析类型，右侧显示内容封装
  if (nodeInfo.value.type === HANDLE_TYPE.OBJECT) {
    const { fieldType, value } = item;

    // 数据源类型且value是数组形式就分页列表展示
    if (fieldType === FIELD_TYPE_MAP.source) {
      if (Array.isArray(value)) {
        return true;
      }
      const tempValue = value.split(",");
      if (tempValue.length) {
        return true;
      }
      return true;
    }
    return false;
  }

  // 实例解析类型
  if (nodeInfo.value.type === HANDLE_TYPE.HANDLE) {
    if (nodeInfo.value.type === HANDLE_TYPE.OBJECT) return false;

    const { fieldType, format, reference, field } = item;

    // 1. 正向:fieldType=2数据源&&format=2数组
    if (fieldType === FIELD_TYPE_MAP.source && format === 2) {
      return true;
    }

    /*
    2.2 反向:fieldType=3
      2.2.1 根据兼容(待确定) paramProp.field是空,反向tid关联
        handle=关联对象标识(B)              
        referenceHandlefield = 关联对象属性(a_tid)          
        queryProp.fieldfieldValue = 当前标识tid(A.tid)      
        handlerequestType =2
      2.2.2 根据兼容是兼容模式(待确定),反向属性关联
        handle=关联对象标识(B)                     
        referenceHandlefield = 关联对象属性(a_code)             
        queryProp.fieldfieldValue = 当前标识属性(A.code)    
        当前标识的paramProp.field指定属性的值requestType= 2
    */
    if (fieldType === FIELD_TYPE_MAP.handleValue) {
      if (Object.keys(reference || {}).length) {
        return true;
      }
    }
    return false;
  }
};

onMounted(() => {
  node.value = getNode();
  graph.value = getGraph();
  nodeInfo.value = node.value.data || {};
  searchResult.value = nodeInfo.value.values || [];
  basicItems.value = searchResult.value.filter((item: any) => {
    return item.fieldSourceType === 0;
  });
  extendItems.value = searchResult.value.filter((item: any) => {
    return item.fieldSourceType === 1;
  });
  basicSearchResult.value = searchResult.value.filter((item: any) => {
    return item.fieldSourceType === 0;
  });
  extendSearchResult.value = searchResult.value.filter((item: any) => {
    return item.fieldSourceType === 1;
  });
  if (nodeInfo.value.isCollapsed) {
    basicCollapsed.value = true;
    extendCollapsed.value = true;
  }
  oldNodeHeight = node.value.size().height;
});

// 对象类型的handle有可能不是取value，
const getRealHandle = (item: Record<string, any>) => {
  const { fieldType, value, reference } = item;

  // 针对page-attr里封装的不做下面的handle转换
  if (!fieldType) return item.value;
  // 对象解析类型，右侧显示内容封装
  if (nodeInfo.value.type === HANDLE_TYPE.OBJECT) {
    const contentMap = {
      [FIELD_TYPE_MAP.fixed]: value,
      [FIELD_TYPE_MAP.source]: value, // value是数组时，不走这
      [FIELD_TYPE_MAP.handleValue]: reference?.referenceHandle,
      [FIELD_TYPE_MAP.handleWithAttr]: reference?.referenceHandle, // 实际显示时，需要添加不可点击的_${reference?.referenceHandleProp}
    };

    return contentMap[fieldType];
  }
  return item.value;
};

async function handleClick(handle: string, item: Record<string, any>) {
  // if (!handleRegex.test(handle) || handle === nodeInfo.value.handle) return;
  if (!isClickHandle(handle) || handle === nodeInfo.value.handle) return;

  item.value = getRealHandle(item);
  // 已存在节点，不再次解析，直接连线
  if (graph.value.hasCell(handle)) {
    props &&
      props.add &&
      props.add({
        handle: nodeInfo.value.handle,
        item,
        nextNode: null,
        isExist: true,
      });
    return;
  }
  loading.value = true;

  let key = props.secretKey;
  if (key) {
    const publicKey = await getPublicKey().catch(() => {
      return "";
    });
    key = encrypt(key, publicKey);
  }
  idResolve({ handle, secretKey: key })
    .then((response: any) => {
      const tempItem = { ...item };
      tempItem.value = response.handle;
      props &&
        props.add &&
        props.add({
          handle: nodeInfo.value.handle,
          item: tempItem,
          nextNode: response,
        });
    })
    .finally(() => {
      loading.value = false;
    });
}

const observer = useMutationObserver(
  nodeContainer,
  () => {
    const boxSize = nodeContainer.value?.getBoundingClientRect();
    const newHeight = Math.trunc(boxSize.height);
    if (Math.abs(oldNodeHeight - newHeight) > 30) {
      const heightList: Record<string, number | string>[] = [];
      nodeContainer.value
        .querySelectorAll(".handle-node-body-item")
        .forEach((el: any) => {
          const boxSize = el.getBoundingClientRect();
          heightList.push({
            field: el.dataset.key,
            height: Math.trunc(boxSize.height),
          });
        });

      oldNodeHeight = newHeight;

      // 设置node的高度为渲染后的新高度，不设置的话，firefox中显示异常
      node.value.size(300, newHeight);

      props.onHeightChange && props.onHeightChange(node.value, heightList);
    }
  },
  {
    childList: true,
    subtree: true,
  }
);

onUnmounted(() => {
  observer && observer?.stop(); // 销毁observer
});
</script>
<style lang="scss" scoped>
.ellipsis-text-wrap {
  height: 20px;
  line-height: 20px;
}
.item-extend-attr {
  padding: 0 4px;
  margin: 4px 0;
  border-radius: 2px;
  background: #dafff6;
  color: #00a57c;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  display: inline-block;
}
.not-root-node {
  &.handle-node-box {
    border-color: #d8e7e3;
  }

  .header {
    background-color: #c6dbd6;
    color: #000;

    .name .name-right {
      display: inline-block;
      padding: 3px;
      background-color: #d8e7e3;
    }
  }
  .search-input {
    :deep(.el-input__wrapper) {
      background: rgba(255, 255, 255, 0.5) !important;

      .el-input__icon {
        color: #535f5c !important;
      }

      .el-input__inner {
        color: #272e2c !important;
        &::placeholder {
          color: #7b9790 !important;
        }
      }
    }
  }
}
.handle-node-box {
  background-color: #fff;
  font-size: 12px;
  // background: #0ac8d2;
  border: 1px solid #00bf8f;
  cursor: grab;
}
.handle-node-title {
  // background: #0ac8d2;
  // height: 36px;
  color: #fff;
  padding: 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .handle-node-title-tip {
    height: 6px;
    width: 6px;
    background-color: #fff;
    border-radius: 50%;
    margin-right: 8px;
  }
  .handle-node-title-text {
    flex: 1;
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 12px;
  }
  .el-icon {
    cursor: pointer;
  }
}

.header {
  background-color: #00a57c;
  display: flex;
  flex-direction: column;
  padding: 12px 8px;
  color: #fff;

  & > span {
    margin-bottom: 3px;
  }

  .name {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .ellipsis-text-wrap {
      flex: 1;
      min-width: 0;
      font-weight: 500;
    }

    .name-right-wrap {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      flex-wrap: nowrap;
    }

    .name-right {
      display: inline-block;
      padding: 3px;
      height: 24px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 2px;
      font-weight: 400;
      font-size: 12px;
      line-height: 20px;
      white-space: nowrap;
      margin-left: 2px;
    }

    .name-delete {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      margin-left: 8px;
      height: 24px;
      width: 24px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.2);
      cursor: pointer;

      :deep(.el-icon) {
        color: #7b9790;
      }
    }
  }

  .app {
    margin-top: 8px;
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
  }
}
.search-input {
  width: 282px;
  padding-top: 12px;
  .el-input {
    height: 24px !important;
  }

  :deep(.el-input__wrapper) {
    box-shadow: none !important;
    border: none !important;
    background: rgba(255, 255, 255, 0.33) !important;
    border-radius: 2px !important;
    color: #fff !important;
    .el-input__prefix {
      color: #fff !important;
    }
    .el-input__inner {
      color: #fff !important;
      height: 24px !important;
      line-height: 24px !important;
      &::placeholder {
        color: #d8e7e3 !important;
      }
    }
  }
}
.handle-node-body {
  .handle-node-body-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: start;
    min-height: 36px;
    padding: 7px 12px 8px;
    color: #4e5969;
    border-bottom: 1px solid #e5e8ef;
    white-space: nowrap;
    overflow: hidden;
    font-weight: 400;
    font-size: 12px;

    .database {
      width: 146px;
      color: #535f5c;
    }

    &.item-hover {
      background-color: #f1f7f5;
    }

    &.align-top {
      align-items: start;
      padding-top: 8px;
      padding-bottom: 8px;
    }
    .handle-node-body-item-left {
      margin-right: 20px;
      max-width: 120px;
      color: #272e2c;
    }
  }
}

.right-container {
  width: 100%;
  flex: 1;
  min-width: 0;
  text-align: right;
}
.handle-node-body-item-right {
  color: #4e5969;
  :deep(.el-button) {
    width: 100%;
    > span {
      display: inline-block;
      width: 100%;
    }
  }

  .el-icon {
    cursor: pointer;
  }

  :deep(.el-button.is-link) {
    font-size: 12px;
    text-align: right;
    padding: 0;
    border: none;
  }
}

.handle-node-body-item-right-red {
  color: #e63f3f;
}

.handle-item-tooltip {
  max-width: 500px;
  white-space: normal;
  word-break: break-all;
}

.attr-box {
  height: 36px;
  padding: 8px 12px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  background: #d8e7e3;
  box-shadow: 0px -1px 0px 0px #e5e8ef inset;

  .search-expand-collapse {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    .el-icon {
      margin-right: 4px;
    }
  }
  &.is-extend {
    background: #eef2f1;
  }
  .attr-box-text {
    color: #4e5969;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
  }
}
.handle-node-body-null {
  .el-empty {
    height: 110px;
  }
  :deep(.el-empty__description) {
    p {
      color: #1d2129;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
  }
}
.extend-word-wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .extend-word-text {
    flex: 1;
    min-width: 0;
  }
  .extend-word {
    color: #00a57c;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    border-radius: 2px;
    border: 1px solid #00a57c;
    margin-left: 8px;
  }
}

.cursor-pointer {
  cursor: pointer;
}
</style>
