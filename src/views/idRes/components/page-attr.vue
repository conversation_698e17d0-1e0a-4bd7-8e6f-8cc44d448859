<template>
  <div v-if="pageInfo.pagination.totalCount === 0" class="container">
    <span>-</span>
  </div>
  <div v-else-if="pageInfo.pagination.totalCount === 1" class="container">
    <ellipsisText
      v-if="!isClickHandle(handleList[0])"
      :value="handleList[0]"
      :copy="false"
    />
    <el-button
      v-if="isClickHandle(handleList[0])"
      link
      type="primary"
      class="handle-list-btn"
      @click="handleClick(handleList[0])"
    >
      <ellipsisText :value="handleList[0]" :copy="false" />
    </el-button>
  </div>
  <div v-else class="container">
    <div class="handle-count">
      <el-button link type="primary" @click="handleCollapsed" class="result">
        <span>共{{ pageInfo.pagination.totalCount }}条结果</span>
        <el-icon>
          <ArrowDownBold v-if="pageInfo.listCollapsed" /><ArrowUpBold v-else />
        </el-icon>
      </el-button>
    </div>
    <div class="handle-list" :class="{ collapsed: pageInfo.listCollapsed }">
      <div style="width: 100%" v-for="handle in handleList" :key="handle">
        <ellipsisText
          v-if="!isClickHandle(handle)"
          :value="handle"
          :copy="false"
        />
        <el-button
          v-if="isClickHandle(handle)"
          class="handle-list-btn"
          :class="{ hightlight: pageInfo.highlightHandle === handle }"
          link
          type="primary"
          @click="handleClick(handle)"
        >
          <ellipsisText
            :value="handle"
            :copy="false"
            v-if="!pageInfo.listCollapsed"
          />
        </el-button>
      </div>

      <el-pagination
        v-model:page="pageInfo.pagination.page"
        v-model:page-size="pageInfo.pagination.size"
        small
        layout="prev, pager, next"
        :total="pageInfo.pagination.totalCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
<script setup lang="ts">
import {
  inject,
  onMounted,
  onUpdated,
  defineProps,
  defineEmits,
  ref,
  watch,
} from "vue";
import { useStore } from "vuex";
import { ApiGetRelateList } from "@/api/idRes/index";
import ellipsisText from "@/components/ellipsisText/index.vue";
import { HANDLE_TYPE, FIELD_TYPE_MAP } from "@/utils/constant";
import { getPublicKey, encrypt } from "@/utils/forgeEncrypt";

const emit = defineEmits(["clickHandle"]);
const props = defineProps({
  resolveType: {
    type: Number,
  },
  attrItem: {
    type: Object,
    default: () => {
      return {};
    },
  },
  secretKey: {
    type: String,
    default: "",
  },
  nodeHandle: {
    type: String,
  },
  nodeValues: {
    type: Array,
    default: () => {
      return [];
    },
  },
});

const handleRegex = /^\d{2}\.\d{4}\.\d{4}\/.*$/;
const httpsReg = /^(HTTPS|https):\/\/.*/; // 支持https开头

const PAGINATION_SIZE = 5;
const defaultPageInfo = {
  highlightHandle: null, // 点击后高亮handle
  listCollapsed: true,
  pagination: {
    page: 1,
    size: PAGINATION_SIZE,
    totalCount: 0,
  },
};

const handleList = ref([]);

const pageInfo = ref<Record<string, any>>({ ...defaultPageInfo });

const handleCollapsed = () => {
  pageInfo.value.listCollapsed = !pageInfo.value.listCollapsed;
};

const getTableData = () => {
  // 对象解析，前端分页
  if (props.resolveType === HANDLE_TYPE.OBJECT) {
    return customPagination();
  }

  // 后端分页
  if (props.resolveType === HANDLE_TYPE.HANDLE) {
    return getRelateList();
  }
};

const getResolveParams = () => {
  // 实例解析类型
  if (props.resolveType === HANDLE_TYPE.HANDLE) {
    if (props.resolveType === HANDLE_TYPE.OBJECT) return false;

    const { fieldType, format, reference, field } = props.attrItem;

    // 1. 正向:fieldType=2数据源&&format=2数组
    if (fieldType === FIELD_TYPE_MAP.source && format === 2) {
      return {
        requestType: 1,
        field,
        handle: props.nodeHandle,
        fieldValue: null,
      };
    }

    /*
    2.2 反向:fieldType=3
      2.2.1 根据兼容(待确定) paramProp.field是空,反向tid关联
        handle=关联对象标识(B)
        referenceHandlefield = 关联对象属性(a_tid)
        queryProp.fieldfieldValue = 当前标识tid(A.tid)
        handlerequestType =2
      2.2.2 根据兼容是兼容模式(待确定),反向属性关联
        handle=关联对象标识(B)
        referenceHandlefield = 关联对象属性(a_code)
        queryProp.fieldfieldValue = 当前标识属性(A.code)
        当前标识的paramProp.field指定属性的值requestType= 2
    */
    if (fieldType === FIELD_TYPE_MAP.handleValue) {
      if (Object.keys(reference || {}).length) {
        const { queryProp, paramProp, referenceHandle } = reference;
        let fieldValue = props.nodeHandle; // 当前节点
        // 兼容模式有paramProp字段，需要取paramProp字段对应的value值
        if (paramProp && props.nodeValues?.length) {
          const matchParamPropItem: any = props.nodeValues.filter(
            (item: any) => item.field === paramProp
          );
          if (matchParamPropItem?.length && matchParamPropItem[0].value) {
            fieldValue = matchParamPropItem[0].value;
          }
        }
        return {
          requestType: 2,
          handle: referenceHandle,
          fieldValue,
          field: queryProp,
        };
      }
    }
  }
  return null;
};

const getRelateList = async () => {
  const { page, size } = pageInfo.value.pagination;
  const resolveAttrParams = getResolveParams();

  if (!resolveAttrParams) return;
  let key = props.secretKey;
  if (key) {
    const publicKey = await getPublicKey().catch(() => {
      return "";
    });
    key = encrypt(key, publicKey);
  }
  ApiGetRelateList({
    ...resolveAttrParams,
    page: page - 1,
    size,
    secretKey: key,
  })
    .then((res: any) => {
      const { content, totalCount } = res || {};
      pageInfo.value.pagination.totalCount = totalCount;
      handleList.value = content;
    })
    .catch(() => {
      pageInfo.value.pagination.totalCount = 0;
      handleList.value = [];
    });
};

const customPagination = () => {
  const { page, size } = pageInfo.value.pagination;
  const { value, fieldType } = props.attrItem || [];
  try {
    let tempValue = JSON.parse(JSON.stringify(value));
    if (fieldType === FIELD_TYPE_MAP.source) {
      tempValue = value.split(",");
    }
    const startIndex = (page - 1) * size;
    pageInfo.value.pagination.totalCount = tempValue?.length || 0;
    handleList.value = tempValue.slice(startIndex, startIndex + size) || [];
  } catch (error) {
    console.log("customPagination error:", error);
  }
};

const handleSizeChange = (num: number) => {
  pageInfo.value.pagination.size = num;
  pageInfo.value.pagination.page = 1;
  getTableData();
};

const handleCurrentChange = (num: number) => {
  pageInfo.value.pagination.page = num;
  getTableData();
};

const isClickHandle = (handle: string) => {
  if (handle === props.nodeHandle) {
    // 标识是本标识
    return false;
  }
  // 统一用正则判断是否可点
  return (
    handleRegex.test(handle) ||
    httpsReg.test(handle) ||
    `${handle}`.includes("/99") ||
    `${handle}`.includes("99.1000.1")
  );
};

const handleClick = (handle: string) => {
  pageInfo.value.highlightHandle = handle;

  emit("clickHandle", handle, {
    label: props.attrItem.label,
    value: handle,
    field: props.attrItem.field,
  });
};

onMounted(() => {
  getTableData();
});
</script>
<style lang="scss" scoped>
.ellipsis-text-wrap {
  height: 20px;
  line-height: 20px;
}
.container {
  width: 100%;
  .handle-count {
    text-align: right;
    color: #1664ff !important;
    height: 20px;
    line-height: 20px;

    .result {
      font-size: 12px;
      font-weight: 400;
    }

    .el-icon {
      margin-left: 4px;
      vertical-align: text-top;
    }
  }
  .handle-list {
    display: flex;
    flex-direction: column;
    align-items: end;

    // :deep(.handle-list-btn) {

    :deep(.el-button + .el-button) {
      margin-left: 0;
    }

    :deep(.el-pagination) {
      button,
      li {
        background: #f5f7f7;
      }
    }
  }

  :deep(.handle-list-btn) {
    width: 100%;
    font-size: 12px;
    height: 20px;

    .ellipsis-text-wrap {
      text-align: right;
    }

    &.hightlight {
      .ellipsis-text-wrap {
        span {
          border-radius: 2px;
          color: #0e49d2;
          background-color: #e5e8ef !important;
          padding: 0 2px;
          display: inline-block;
          height: 20px;
          line-height: 20px;
        }
      }
    }
  }

  .collapsed {
    display: none;
  }

  :deep(.el-button) {
    & > span {
      width: 100%;
      display: inline-block;
    }
  }
}
</style>
