<template>
  <div style="visibility: hidden; height: 0">
    <input ref="fileInput" type="file" @change="onInputChange" />
    <canvas ref="qrcanvas"></canvas>
  </div>
</template>
<script setup lang="ts">
import { defineEmits, ref, onMounted } from "vue";
import jsQR from "jsqr";

const emit = defineEmits(["success", "fail"]);

const fileInput = ref();
const qrcanvas = ref();

const onInputChange = (e: any) => {
  const file = e.target.files[0];
  if (
    !["image/jpeg", "image/png", "image/bmp", "image/gif"].includes(file.type)
  ) {
    emit("fail", "上传格式不正确");
    return;
  }
  if (window.FileReader) {
    const fr = new FileReader();
    fr.readAsDataURL(file);
    fr.onloadend = (e) => {
      const base64Data = e.target?.result;
      base64ToqR(base64Data);
    };
  }
};

const base64ToqR = (data: any) => {
  const ctx = qrcanvas.value.getContext("2d");

  const img = new Image();
  img.src = data;
  img.onload = () => {
    qrcanvas.value.setAttribute("width", img.width);
    qrcanvas.value.setAttribute("height", img.height);

    ctx.drawImage(img, 0, 0, img.width, img.height);
    const imageData = ctx.getImageData(0, 0, img.width, img.height);
    const code = jsQR(imageData.data, imageData.width, imageData.height, {
      inversionAttempts: "dontInvert",
    });

    if (code) {
      emit("success", code.data);
    } else {
      emit("fail", "识别异常，请上传清晰的图片重试");
    }
  };
};

onMounted(() => {
  fileInput.value.click(); // 调用上传文件
});
</script>
<!-- <style lang="scss" scoped></style> -->
