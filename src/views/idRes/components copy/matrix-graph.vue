<template>
  <div class="section-title">
    <div class="section-title-line"></div>
    <span class="section-title-text">
      {{ currentData.data.handle || "-" }}
    </span>
  </div>
  <handle-common
    v-if="showHandleCommon"
    :data="currentData.data.values"
  ></handle-common>
  <div v-else class="matrix-graph-box">
    <el-radio-group
      class="matrix-graph-type"
      v-model="currentData.graphType"
      @change="handleGraphTypeChange"
    >
      <el-radio-button :label="SEARCH_TYPE.COMPLEX">查询结果</el-radio-button>
      <el-radio-button :label="SEARCH_TYPE.SIMPLE">矩阵图</el-radio-button>
    </el-radio-group>
    <div class="matrix-graph">
      <div
        ref="matrixGraphRef"
        class="matrix-handle-node"
        id="handleNode"
      ></div>
    </div>

    <TeleportContainer />
  </div>
</template>
<script setup lang="ts">
import {
  ref,
  createVNode,
  defineExpose,
  reactive,
  computed,
  nextTick,
} from "vue";
import { Graph } from "@antv/x6";
import { useTeleport } from "@antv/x6-vue-shape";
import { DagreLayout, Model } from "@antv/layout";
import { deepClone } from "@/utils/auth";
import HandleNode from "./handle-node.vue";
import HandleCommon from "./handle-common.vue";
import {
  HANDLE_FIELD_TYPE,
  HANDLE_TYPE,
  GRAPH_TYPE,
  OBJECT_ENTITY_TYPE_NAME,
} from "@/utils/constant";

const UNIQ_GRAPH_ID = "UNIQ_GRAPH_ID";

const TeleportContainer = useTeleport(UNIQ_GRAPH_ID);

const matrixGraphRef = ref(null);

const matrixGrapWidth = ref(0);

const SEARCH_TYPE = GRAPH_TYPE;

const currentData = reactive<any>({
  graphType: GRAPH_TYPE.COMPLEX,
  data: {},
});

interface Nodes {
  id: any;
  shape: string;
  width: number;
  height: number;
  view: string;
  component: string;
  parentId: any;
  data: any;
  ports: any;
}
interface Edges {
  source: any;
  target: any;
}
interface GraphData {
  nodes: Nodes[];
  edges: Edges[];
}
const graphData = reactive<GraphData>({
  nodes: [],
  edges: [],
});

const showHandleCommon = ref(false);

// 获取所有节点集合
const nodesMap = computed(() => {
  const newObj: { [x: string]: Nodes } = {};
  graphData.nodes.forEach((node) => {
    newObj[node.id] = node;
  });
  return newObj;
});

// 获取所有节点的id
const nodeIds = computed(() => {
  return graphData.nodes.map((node) => node.id);
});

// 获取所有线的id，组成形式为sourceId-targetId
const edgeIds = computed(() => {
  return graphData.edges.map((edge) => `${edge.source}-${edge.target}}`);
});

const nodesepFunc = (data: { height: number }) => {
  return Math.trunc(data.height / 3);
};
const ranksepFunc = (data: { parentId: any }) => {
  const { parentId } = data;
  if (!parentId) return 0;
  const parentWidth = nodesMap.value[parentId].width;
  return Math.trunc(parentWidth - 40);
};

// 设置布局
const dagreLayout = new DagreLayout({
  type: "dagre",
  rankdir: "LR", // 布局的方向'TB' | 'BT' | 'LR' | 'RL'
  align: "UL", // 节点对齐方式。U：upper（上）；D：down（下）；L：left（左）；R：right（右）；undefined (居中)
  // ranksep: 150, // 节点间距，在 rankdir 为 TB 或 BT 时是节点的水平间距；在 rankdir 为 LR 或 RL 时代表节点的竖直方向间距
  // nodesep: 10, // 层间距（px）。在 rankdir 为 TB 或 BT 时是竖直方向相邻层间距；在 rankdir 为 LR 或 RL 时代表水平方向相邻层间距
  nodesepFunc, // 层间距（px）
  ranksepFunc, // 节点间距
  controlPoints: true,
});

const addNode = (params: {
  handle: string;
  item: { label: string; value: string; fieldIndex: number; belongsTo: string };
  nextNode: any;
}) => {
  const newData = formatData(params.nextNode);
  const node = setNode(newData, params.handle, NODE_COLOR.CHILD_NODE_COLOR);
  const edge = setEdge(params.handle, params.item.belongsTo, params.item);
  if (
    nodeIds.value.includes(params.item.value) &&
    edgeIds.value.includes(`${params.handle}}-${params.item.value}`)
  ) {
    return;
  }
  if (!nodeIds.value.includes(params.item.value)) {
    graphData.nodes.push(node);
  }

  if (!edgeIds.value.includes(`${params.handle}}-${params.item.value}`)) {
    graphData.edges.push(edge);
  }

  handleGraphTypeChange(currentData.graphType);
};

const handleCollapse = (params: {
  handle: string;
  isCollapsed: boolean;
  item: any;
}) => {
  graphData.nodes.forEach((tempNode) => {
    if (tempNode.id === params.handle) {
      tempNode.data.values.forEach((valueItem: any) => {
        if (valueItem.label === params.item.label) {
          valueItem.isCollapsed = params.isCollapsed;
        }
      });
    }
  });
  formatNewData();
};

// 判断是否是头部折叠
const isHeadCollapsed = (dataValues: any) => {
  return dataValues[0].isCollapsed;
};

// 判断是否是标识信息折叠
const isHandleInfoCollapsed = (dataValues: any) => {
  return !!dataValues.filter(
    (item: any) => item.label === "标识信息" && item.isCollapsed
  ).length;
};

// 判断是否是属性信息折叠
const isAttrInfoCollapsed = (dataValues: any) => {
  return !!dataValues.filter(
    (item: any) => item.label === "属性信息" && item.isCollapsed
  ).length;
};

// 获取头部、标识信息、属性信息protId
const getPortIds = (handle: string, dataValues: any) => {
  const newMap = { title: "", handle: "", attr: "" };
  dataValues.forEach((dataValue: any) => {
    if (dataValue.label === handle) {
      newMap.title = `${handle}-${dataValue.label}-${dataValue.value}-${dataValue.fieldIndex}`;
    }
    if (dataValue.label === "标识信息") {
      newMap.handle = `${handle}-${dataValue.label}-${dataValue.value}-${dataValue.fieldIndex}`;
    }
    if (dataValue.label === "属性信息") {
      newMap.attr = `${handle}-${dataValue.label}-${dataValue.value}-${dataValue.fieldIndex}`;
    }
  });
  return newMap;
};

const formatNewData = () => {
  const nodePortMap: any = {};
  const nodeCollapsedMap: any = {};
  graphData.nodes.forEach((nodeItem) => {
    nodePortMap[nodeItem.data.handle] = getPortIds(
      nodeItem.data.handle,
      nodeItem.data.values
    );

    const isHeadCollapsedResult = isHeadCollapsed(nodeItem.data.values);
    const isHandleInfoCollapsedResult = isHandleInfoCollapsed(
      nodeItem.data.values
    );
    const isAttrInfoCollapsedResult = isAttrInfoCollapsed(nodeItem.data.values);
    nodeCollapsedMap[nodeItem.data.handle] = {
      isHeadCollapsed: isHeadCollapsedResult,
      isHandleInfoCollapsed: isHandleInfoCollapsedResult,
      isAttrInfoCollapsed: isAttrInfoCollapsedResult,
    };

    nodeItem.data.values.forEach((nodeValueItem: any, index: number) => {
      // 头部折叠，所有的都要折叠
      if (isHeadCollapsedResult) {
        if (index !== 0) {
          nodeValueItem.show = false;
        }
        if (nodeValueItem.label === "标识信息") {
          nodeValueItem.isCollapsed = !isHeadCollapsedResult;
        }
        if (nodeValueItem.label === "属性信息") {
          nodeValueItem.isCollapsed = !isHeadCollapsedResult;
        }
      } else {
        if (["标识信息", "属性信息"].includes(nodeValueItem.label)) {
          nodeValueItem.show = true;
        }
        if (
          nodeValueItem.belongsTo === "标识信息" &&
          nodeValueItem.label !== "标识信息"
        ) {
          nodeValueItem.show = !isHandleInfoCollapsedResult;
        }
        if (
          nodeValueItem.belongsTo === "属性信息" &&
          nodeValueItem.label !== "属性信息"
        ) {
          nodeValueItem.show = !isAttrInfoCollapsedResult;
        }
        if (index === 0) {
          nodeValueItem.show = true;
        }
      }
    });
    // 控制线初始位置
    Object.keys(nodeCollapsedMap).forEach((nodeItem) => {
      graphData.edges.forEach((edgeItem) => {
        if (edgeItem.source.cell === nodeItem) {
          // 头部收起
          if (nodeCollapsedMap[nodeItem].isHeadCollapsed) {
            edgeItem.source.port = nodePortMap[nodeItem].title;
          }
          if (!nodeCollapsedMap[nodeItem].isHeadCollapsed) {
            if (
              nodeCollapsedMap[nodeItem].isHandleInfoCollapsed &&
              edgeItem.source.belongsTo === "标识属性"
            ) {
              edgeItem.source.port = nodePortMap[nodeItem].handle;
            } else if (
              nodeCollapsedMap[nodeItem].isAttrInfoCollapsed &&
              edgeItem.source.belongsTo === "属性信息"
            ) {
              edgeItem.source.port = nodePortMap[nodeItem].attr;
            } else {
              edgeItem.source.port = edgeItem.source.originPort;
            }
          }
        }
      });
      if (nodeCollapsedMap[nodeItem].isHeadCollapsed) {
        graphData.edges.forEach((edgeItem) => {
          if (edgeItem.source.cell === nodeItem) {
            edgeItem.source.port = nodePortMap[nodeItem].title;
          }
        });
      }
    });
    const showValue = nodeItem.data.values.filter((item: any) => !!item.show);
    nodeItem.height = showValue.length * 36 + 36; // 高度会发生变化，所以需要改变一下高度
    nodeItem.ports.items = getNodePorts(nodeItem.id, nodeItem.data.values);
  });
  showGraph(graphData);
};

const graph = ref();

Graph.registerVueComponent(
  "handle-node",
  {
    render: () => {
      return createVNode(HandleNode, {
        add: addNode, // 查看下一个标识
        collapse: handleCollapse, // 展开收起
      });
    },
  },
  true
);

// 设置链接桩位置
Graph.registerPortLayout(
  "erPortPosition",
  (portsPositionArgs) => {
    return portsPositionArgs.map((_, index) => {
      return {
        position: {
          x: 0,
          y: index * 36 + 18,
        },
        angle: 0,
      };
    });
  },
  true
);

// 获取节点的高度
function getNodeHeight(data: any) {
  return data.values.length * 36 + 36;
}

// 计算文本宽度
function countTextWidth(text: string) {
  const font = "normal 14px arial";
  const canvas = document.createElement("canvas");
  const context = canvas.getContext("2d");
  context!.font = font;
  const { width } = context!.measureText(text);
  return Math.ceil(width);
}

// 获取节点的长度长度
function getNodeWidth(data: any) {
  const widths = [];
  widths.push(countTextWidth(data.handle));
  data.values.forEach((item: { value: any; label: any; show?: boolean }) => {
    if (item.show) {
      widths.push(countTextWidth(`${item.value || ""}：${item.label || ""}`));
    }
  });

  // 22-左右间距, 24-button左右间距
  let result =
    Math.max(...widths) + 22 + 24 > 280 ? Math.max(...widths) + 22 + 24 : 280;

  if (result >= 500) result = 500;

  return result;
}

// 获取ports
const getNodePorts = (handle: string, dataValues: any) => {
  const items: any = [];
  dataValues.forEach((item: any) => {
    if (item.show) {
      items.push({
        id: `${handle}-${item.label}-${item.value}-${item.fieldIndex}`,
        group: "right",
      });
    }
  });
  return items;
};

// 设置节点
function setNode(data: any, parentId: string | null, color: string) {
  const items = getNodePorts(data.handle, data.values);
  data.values.forEach((item: any) => {
    item.showTooltip =
      countTextWidth(`${item.value || ""}：${item.label || ""}`) > 500;
  });
  const maxNodeWidth = getNodeWidth(data);
  return {
    id: data.handle,
    shape: "vue-shape",
    width: maxNodeWidth,
    height: getNodeHeight(data),
    view: UNIQ_GRAPH_ID,
    component: "handle-node",
    parentId,
    backgroundColor: color, // 设置节点颜色
    data,
    ports: {
      groups: {
        right: {
          markup: [
            {
              tagName: "rect",
              selector: "portBody",
            },
          ],
          position: "erPortPosition",
          attrs: {
            portBody: {
              width: maxNodeWidth,
              height: 0.1,
              fill: "transparent",
              magnet: false,
            },
          },
        },
      },
      items, // 链接桩ID
    },
  };
}

// 设置连线
function setEdge(
  sourceId: string,
  belongsTo: string,
  targetData: { label: string; value: string; fieldIndex: number }
) {
  return {
    source: {
      cell: sourceId, // 源节点
      belongsTo,
      originPort: `${sourceId}-${targetData.label}-${targetData.value}-${targetData.fieldIndex}`,
      port: `${sourceId}-${targetData.label}-${targetData.value}-${targetData.fieldIndex}`, // 链接桩 ID
    },
    target: {
      cell: targetData.value,
      port: `${targetData.value}-${targetData.value}--`,
    },
    connector: {
      name: "rounded",
    },
  };
}

// 绘制图形
function showGraph(graphData: GraphData) {
  const model: any = dagreLayout.layout(graphData);
  // model.nodes[0].x =
  //   currentData.data.type === HANDLE_TYPE.INSTANCE
  //     ? matrixGrapWidth.value / 2 - model.nodes[0].width / 2
  //     : 12;
  model.nodes[0].x = 30;
  model.nodes[0].y = 20;
  graph.value.fromJSON(model);
}

// 获取对象标识固定字段
function getFixItems(data: any) {
  const { type, objectValues, appValues, handle } = data;
  const newArr = []; // 固定的几个值
  newArr.push({
    label: `${handle || "-"}`,
    value: "",
    type: HANDLE_FIELD_TYPE.Title,
    fieldIndex: "",
    show: true,
    canCollapse: true,
    isCollapsed: false,
    belongsTo: "标识信息",
  });
  newArr.push({
    label: "标识信息",
    value: "",
    type: HANDLE_FIELD_TYPE.Title,
    fieldIndex: "",
    show: true,
    canCollapse: true,
    isCollapsed: false,
    belongsTo: "标识信息",
  });
  if (appValues) {
    const { entName, appName, handleCode } = appValues;
    newArr.push({
      label: `${entName || "-"}-${appName || "-"}`,
      value: "",
      type: HANDLE_FIELD_TYPE.TEXT,
      fieldIndex: "",
      show: true,
      belongsTo: "标识信息",
    });
  }
  if (objectValues) {
    const { name, type, entityType } = objectValues;
    newArr.push({
      label: "标识名称",
      value: name || "-",
      type: HANDLE_FIELD_TYPE.TEXT,
      fieldIndex: "",
      show: true,
      belongsTo: "标识信息",
    });
    newArr.push({
      label: "实体类型",
      value: OBJECT_ENTITY_TYPE_NAME[entityType] || "-",
      type: HANDLE_FIELD_TYPE.TEXT,
      fieldIndex: "",
      show: true,
      belongsTo: "标识信息",
    });
  }
  return newArr;
}

// 格式化data,references里面有多个值，需要并列展示
function formatData(data: any) {
  const fixItems = getFixItems(data); // 固定的几个值
  const newValues: {
    label: any;
    value: any;
    type: any;
    fieldIndex: any;
    canCollapse?: boolean;
    isCollapsed?: boolean;
    show?: boolean;
    belongsTo?: string;
  }[] = [];
  data.values?.forEach((val: any) => {
    const { description, fieldIndex, fieldType, value } = val;
    if (Array.isArray(value)) {
      value.forEach((valueItem, index) => {
        newValues.push({
          label: description,
          value: valueItem || "-",
          type: fieldType,
          fieldIndex,
          show: true,
          canCollapse: false,
          isCollapsed: false,
          belongsTo: "属性信息",
        });
      });
    } else {
      newValues.push({
        label: description,
        value: value || "-",
        type: fieldType,
        fieldIndex,
        show: true,
        canCollapse: false,
        isCollapsed: false,
        belongsTo: "属性信息",
      });
    }
  });
  if (newValues.length) {
    newValues.unshift({
      label: "属性信息",
      value: "",
      type: HANDLE_FIELD_TYPE.Title,
      fieldIndex: "",
      canCollapse: true,
      isCollapsed: false,
      show: true,
      belongsTo: "属性信息",
    });
  }
  return {
    handle: data.handle || "-",
    values: [...fixItems, ...newValues],
  };
}

// 选择’查询结果‘|'矩阵图'
function handleGraphTypeChange(data: any) {
  const newData: GraphData = deepClone(graphData);
  const tempNodes: Nodes[] = deepClone(graphData.nodes);
  if (data === GRAPH_TYPE.SIMPLE) {
    tempNodes.forEach((tempNode) => {
      const tempValues = deepClone(tempNode.data.values);
      tempNode.data.values = tempValues.filter(
        (node: any) => node.type !== HANDLE_FIELD_TYPE.TEXT
      );
      const dataValues = tempNode.data.values.map(
        (item: any) =>
          `${tempNode.id}-${item.label}-${item.value}-${item.fieldIndex}`
      );
      const tempPorts = deepClone(tempNode.ports.items);
      tempNode.ports.items = tempPorts.filter((item: any) =>
        dataValues.includes(`${item.id}`)
      );
      tempNode.height = tempNode.data.values.length * 36 + 36; // 高度会发生变化，所以需要改变一下高度
    });
    newData.nodes = deepClone(tempNodes);
  }
  showGraph(newData);
}

// 重新渲染
function refresh(data: any) {
  nextTick(() => {
    matrixGrapWidth.value = (
      matrixGraphRef.value as any
    ).getBoundingClientRect().width;
  });
  showHandleCommon.value = data.type === HANDLE_TYPE.OBJECT;
  currentData.graphType = GRAPH_TYPE.COMPLEX;
  currentData.data = deepClone(data);
  if (!showHandleCommon.value) {
    if (graph.value) {
      graph.value.dispose();
    }
    nextTick(() => {
      graph.value = new Graph({
        container: document.getElementById("handleNode")!,
        autoResize: true,
        scroller: true,
        grid: false,
        panning: {
          enabled: true,
          modifiers: "shift",
        },
        connecting: {
          router: {
            name: "er",
            args: {
              offset: 25,
              direction: "H",
            },
          },
        },
      });

      const newData = formatData(currentData.data);
      graphData.nodes = [];
      graphData.edges = [];
      const node = setNode(newData, null, NODE_COLOR.DEFAULT_COLOR);
      graphData.nodes.push(node);
      console.log("graphData:", graphData);
      showGraph(graphData);
    });
  }
}

defineExpose({
  refresh,
});
</script>
<style lang="scss" scoped>
.matrix-graph-box {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #f6f7fb;
  // height: 560px;
  width: 100%;
}

.matrix-graph-type {
  margin: 12px;
}

.matrix-graph {
  flex: 1;
  width: 100%;
  height: 100%;
  display: flex;
  .matrix-handle-node {
    flex: 1;
  }
}
</style>
