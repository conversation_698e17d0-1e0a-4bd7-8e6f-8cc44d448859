<template>
  <div
    class="handle-node-box"
    v-loading="loading"
    :style="{ border: '1px solid ' + color }"
  >
    <div class="handle-node-body">
      <div v-for="(item, index) in data.values" :key="index">
        <div
          v-if="index === 0"
          class="handle-node-title"
          :style="{ backgroundColor: color }"
        >
          <div class="handle-node-title-tip"></div>
          <div class="handle-node-title-text">{{ item.label }}</div>
          <el-icon
            v-if="item.canCollapse && !item.isCollapsed"
            @click="handleCollapsed(item, true)"
            ><ArrowUpBold
          /></el-icon>
          <el-icon
            v-if="item.canCollapse && item.isCollapsed"
            @click="handleCollapsed(item, false)"
            ><ArrowDownBold
          /></el-icon>
        </div>
        <div v-if="index !== 0 && item.show" class="handle-node-body-item">
          <div
            class="handle-node-body-item-left"
            :class="{ 'is-bold': item.canCollapse }"
          >
            {{ item.label }}
          </div>
          <el-tooltip v-if="item.showTooltip" effect="light" placement="top">
            <template #content>
              <div class="handle-item-tooltip">{{ item.value }}</div>
            </template>
            <div
              class="handle-node-body-item-right"
              v-if="!handleRegex.test(item.value) || item.value === data.title"
            >
              {{ item.value }}
            </div>
            <div class="handle-node-body-item-right" v-else>
              <el-button link type="primary" @click="handleClick(item)">{{
                item.value
              }}</el-button>
            </div></el-tooltip
          >
          <div v-if="!item.showTooltip">
            <div
              class="handle-node-body-item-right"
              v-if="!handleRegex.test(item.value) || item.value === data.title"
            >
              {{ item.value }}
              <el-icon
                v-if="item.canCollapse && !item.isCollapsed"
                @click="handleCollapsed(item, true)"
                ><ArrowUpBold
              /></el-icon>
              <el-icon
                v-if="item.canCollapse && item.isCollapsed"
                @click="handleCollapsed(item, false)"
                ><ArrowDownBold
              /></el-icon>
            </div>
            <div class="handle-node-body-item-right" v-else>
              <el-button link type="primary" @click="handleClick(item)">{{
                item.value
              }}</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { inject, onMounted, reactive, defineProps, ref } from "vue";
import { idResolve } from "@/api/idRes/index";
import { HANDLE_FIELD_TYPE } from "@/utils/constant";

const getGraph: any = inject("getGraph");
const getNode: any = inject("getNode");
const graph = ref();
const node = ref();
const loading = ref(false);

// const handleRegex = /^(\d+\.)*\d+\/.*$/;
const handleRegex = /^\d{2}\.\d{4}\.\d{4}\/.*$/;
const color = ref("");

const props = defineProps({
  add: {
    type: Function,
  },
  collapse: {
    type: Function,
  },
});

const FieldTypeMap = HANDLE_FIELD_TYPE;

const data = reactive<any>({
  title: "",
  values: [],
});

onMounted(() => {
  node.value = getNode();
  data.title = node.value.data.handle;
  data.values = node.value.data.values;
  graph.value = getGraph();
  color.value = node.value.store.data.backgroundColor;
});

function handleClick(handle: { label: string; value: string }) {
  if (handle.value === data.title) return;
  loading.value = true;
  idResolve({ handle: handle.value })
    .then((response: any) => {
      props &&
        props.add &&
        props.add({
          handle: data.title,
          item: handle,
          nextNode: response,
        });
    })
    .finally(() => {
      loading.value = false;
    });
}

function handleCollapsed(item: any, isCollapsed: boolean) {
  props &&
    props.collapse &&
    props.collapse({ handle: data.title, isCollapsed, item });
}
</script>
<style lang="scss" scoped>
.handle-node-box {
  font-size: 12px;
  background: #0ac8d2;
  border: 1px solid #0ac8d2;
}
.handle-node-title {
  background: #0ac8d2;
  height: 36px;
  color: #fff;
  padding: 4px 8px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .handle-node-title-tip {
    height: 6px;
    width: 6px;
    background-color: #fff;
    border-radius: 50%;
    margin-right: 8px;
  }
  .handle-node-title-text {
    flex: 1;
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    font-size: 12px;
  }
  .el-icon {
    cursor: pointer;
  }
}
.handle-node-body {
  background: #f5f7f7;
  .handle-node-body-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 8px 0 14px;
    height: 36px;
    color: #4e5969;
    border-bottom: 1px solid #e5e8ef;
    white-space: nowrap;
    font-size: 12px;
    max-width: 500px;
    overflow: hidden;
    .handle-node-body-item-left {
      padding-right: 10px;
      &.is-bold {
        font-weight: 500;
        color: #272e2c;
      }
    }
  }
}
.handle-node-body-item-right {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;

  .el-icon {
    cursor: pointer;
  }
}

.handle-item-tooltip {
  max-width: 500px;
  white-space: normal;
  word-break: break-all;
}
</style>
