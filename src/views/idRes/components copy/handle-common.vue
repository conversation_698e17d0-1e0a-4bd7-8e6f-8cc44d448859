<template>
  <div class="handle-common-box">
    <el-table size="small" :data="props.data" border>
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column property="fieldIndex" label="Index" width="100" />
      <el-table-column property="field" label="Type" />
      <el-table-column property="timestamp" label="Timestamp" />
      <el-table-column property="value" label="Data" />
    </el-table>
  </div>
</template>
<script setup lang="ts">
import { defineProps, watch } from "vue";

const props = defineProps({
  data: {
    type: Object,
  },
});
</script>
