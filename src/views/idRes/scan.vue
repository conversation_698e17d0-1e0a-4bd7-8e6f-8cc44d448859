<template>
  <el-dialog
    v-model="dialogVisible"
    title="扫码"
    width="30%"
    @close="handleClose"
  >
    <div v-loading="loading">
      <qrcode-stream
        :paused="paused"
        :track="paintBoundingBox"
        @init="onInit"
        @detect="onDetect"
        @error="onError"
      >
      </qrcode-stream>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { ElMessage } from "element-plus";
import { QrcodeStream } from "@/utils/vue-qrcode-reader";

const emit = defineEmits(["close", "success"]);

const dialogVisible = ref(true);

const result = ref();

const paused = ref(false);

const loading = ref(false);

const handleClose = () => {
  console.log("关闭");
  emit("close");
};

// 展示识别框-可以提供精确度
const paintBoundingBox = (detectedCodes: any, ctx: any) => {
  for (const detectedCode of detectedCodes) {
    const {
      boundingBox: { x, y, width, height },
    } = detectedCode;
    ctx.lineWidth = 2;
    ctx.strokeStyle = "#00BF8F";
    ctx.strokeRect(x, y, width, height);
  }
};

const onError = (error: any) => {
  ElMessage.error(error);
  emit("close");
};

const onDetect = async (detectedCodes: any) => {
  result.value = detectedCodes.map((code: any) => code.rawValue);
  emit("success", result.value);

  paused.value = true;
};

// 检查是否调用摄像头
const onInit = async (promise: any) => {
  try {
    loading.value = true;
    await promise;
    loading.value = false;
  } catch (error: any) {
    let errorText = "";
    if (error.name === "NotAllowedError") {
      errorText = "ERROR: 您需要授予相机访问权限";
    } else if (error.name === "NotFoundError") {
      errorText = "ERROR: 这个设备上没有摄像头";
    } else if (error.name === "NotSupportedError") {
      errorText = "ERROR: 所需的安全上下文(HTTPS、本地主机)";
    } else if (error.name === "NotReadableError") {
      errorText = "ERROR: 相机被占用";
    } else if (error.name === "OverconstrainedError") {
      errorText = "ERROR: 安装摄像头不合适";
    } else if (error.name === "StreamApiNotSupportedError") {
      errorText = "ERROR: 此浏览器不支持流API";
    }
    ElMessage.error(errorText);
    emit("close");
  }
};
</script>

<style scoped lang="scss">
.scan-wrap {
  width: 500px;
  height: 500px;
}
</style>
