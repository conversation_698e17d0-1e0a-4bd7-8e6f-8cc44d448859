@charset "UTF-8";

/*导入配置文件*/
@import "config";

/*导入重置样式*/
@import "overall";
/*========================================
    **重置基本样式
=========================================*/

/*html，body样式重置*/
* {
  box-sizing: border-box;
}
ul,
li {
  // margin: 0;
  // padding: 0;
  list-style: none;
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  min-height: 100%;
  min-width: 1200px;
  color: #444;
  background-color: $bg-color;
  font-size: 12px;
  font-weight: $font-weight;
  font-family: $font-family;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow: hidden;
}

/*重置a标签样式和交互*/
a {
  color: inherit;
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

/*========================================
    **定义公用样式
=========================================*/

/*清除浮动*/
.clearfix {
  zoom: 1;
  &:after {
    content: "";
    overflow: hidden;
    display: block;
    visibility: hidden;
    clear: both;
    height: 0;
  }
}

.color {
  color: $colorBlue;
}
.colorYellow {
  color: $colorYellow;
}
.colorOrg {
  color: $colorOrg;
}
.colorGreen {
  color: $colorGreen;
}

// 文字、行内元素水平居中
.text-center {
  text-align: center;
}
.text-left {
  text-align: left;
}
.text-right {
  text-align: right;
}

// 文字超出隐藏
.overflow-text {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

//面包屑导航
.el-breadcrumb {
  height: 40px;
  padding: 16px 20px;
  margin-top: 20px;
  background-color: #fafafa;
  font-size: 12px;
}
