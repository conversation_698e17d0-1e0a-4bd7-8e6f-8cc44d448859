@charset "UTF-8";

/*============================================================
	**颜色属性, 如果想换新的一套颜色，直接更改这里的颜色
==============================================================*/

/* 主题色 */
$colorBlue: #007457; //基础色
$colorBlue-1: #eaf0ff; //浅蓝，左侧菜单栏选中项目背景色
$colorBlue-2: #3370ff; //证书开关

$colorYellow: #e6a23c;
$colorOrg: #ff7630;
$colorGreen: #67c23a;
$colorGreen1: #8ccc84;
$colorRed: #f56c6c;

$colorWhite: #fff !default;
$colorBlack: #000 !default;

/* 线条颜色 */
$border-color-primary: #e2e2e2; //默认的线条色
$border-color-1: #e5e5e5; //搜索框边框
$border-color-2: #c8c9cd; //重置按钮边框

/* 背景填充色 */
$bg-color-primary: #f7f7f7; //全局背景色
$bg-color-1: #f2f2f2; //大块的填充背景色
$bg-color-2: #1c2650; //顶部导航栏背景色
$bg-color-3: #1c2650; //全屏dialog的header背景色
$bg-color-4: #edf1fc; //分页的左右按钮

/*字体颜色*/
$font-color-primary: #444; //默认的字体色
$font-color-1: #bfc2cd; //导航栏-未选中-文字
$font-color-2: #999; //上传插件的提示文字
$font-color-3: #333; //表头

/*============================================================
	**全局样式属性
==============================================================*/
/*全局背景*/
$bg-color: $bg-color-primary;

/*字体*/
$text-color: $font-color-primary !default;
$font-family: "PingFang SC", "Helvetica Neue", Helvetica, "Hiragino Sans GB",
  "Microsoft YaHei", Arial, SimSun, sans-serif;
$font-size: 12px !default;
$font-weight: 400;

/*============================================================
	**布局属性
==============================================================*/

/*顶部导航条*/
$header-height: 56px;
$topNav-bg: $bg-color-2;

/*侧边导航栏*/
$sidebar-width: 160px;
$sidebar-collapse-width: 40px;
$sidebar-item-height: 40px;
$sidebar-bg: $bg-color-primary;

/*右侧内容区*/
$content-crumbs-height: 60px; //面包屑标题高度

/*页脚*/
$footer-height: 36px;

/*============================================================
	**组件属性
==============================================================*/
/*tab面板*/
$tab-header-height: 52px; //通用tab的标题高度

/*全屏对话框*/
$fullDig-header-height: 56px; //通用全屏对话框标题高度

/*========================================
    **公用配置宏（@mixin）
=========================================*/
//圆角样式
@mixin border-radius($amount: 2px) {
  -webkit-border-radius: $amount;
  -moz-border-radius: $amount;
  border-radius: $amount;
}

//阴影样式
@mixin box-shadow($amount: 0 0 5px rgba(215, 215, 215, 0.9)) {
  -webkit-box-shadow: $amount;
  -moz-box-shadow: $amount;
  box-shadow: $amount;
}

//动画切换
@mixin transition-process($args: all 0.4s cubic-bezier(0.4, 0, 0.2, 1)) {
  -webkit-transition: $args;
  -moz-transition: $args;
  -o-transition: $args;
  transition: $args;
}

//文字超出省略号显示
@mixin words-more($block: block) {
  display: $block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
