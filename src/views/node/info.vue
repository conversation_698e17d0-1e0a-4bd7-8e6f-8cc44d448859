<!--
 * @Author: <PERSON>@gmail.com
 * @Date: 2022-11-08 14:09:44
 * @LastEditors: <PERSON> q<PERSON>@gmail.com
 * @LastEditTime: 2022-11-08 16:54:54
 * @FilePath: /id-yc-province-web/src/views/province/node/info.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <div class="home-section">
      <div class="section-wrap">
        <div class="section-title">
          <div class="section-title-line"></div>
          <span class="title"> 节点详情 </span>
        </div>
        <div class="info-content">
          <div class="info-line">
            <span class="info-item"
              >企业名称：{{ infoData.entName ?? "-" }}</span
            >
            <span class="info-item"
              >节点名称：{{ infoData.nodeName ?? "-" }}</span
            >
          </div>
          <div class="info-line">
            <span class="info-item">
              <span class="info-item-title">节点前缀：</span>
              <div class="info-item-content">
                <el-tooltip
                  popper-class="info-item-content-tooltip"
                  effect="dark"
                  placement="top-start"
                  :content="`${infoData.nodePrefix || '-'}`"
                >
                  <div class="info-item-content-text">
                    {{ infoData.nodePrefix || "-" }}
                  </div>
                </el-tooltip>
              </div>
            </span>
            <span class="info-item">
              <span class="info-item-title">节点地址：</span>
              <div class="info-item-content">
                <el-tooltip
                  popper-class="info-item-content-tooltip"
                  effect="dark"
                  placement="top-start"
                  :content="`${infoData.nodeAddress || '-'}`"
                >
                  <div class="info-item-content-text">
                    {{ infoData.nodeAddress || "-" }}
                  </div>
                </el-tooltip>
              </div>
            </span>
          </div>
        </div>
        <div class="edit-content">
          <el-button
            @click="showEdit"
            type="primary"
            v-permission="AUTH_CODE.NODE_DETAIL_EDIT"
            >修改节点信息</el-button
          >
        </div>
      </div>
    </div>
  </div>
  <EditNode
    v-if="bindHdlVisible"
    @close-bindHdl-dialog="bindHdlVisible = false"
    @update-table="fetchData"
    :infoData="infoData"
  />
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { nodeInfo } from "@/api/province/node";
import EditNode from "./editNode.vue";
import { AUTH_CODE } from "@/utils/authCode";
import store from "@/store";

const bindHdlVisible = ref(false);
const showEdit = () => {
  bindHdlVisible.value = true;
};

const infoData = ref({
  entName: "",
  nodeAddress: "",
  nodeName: "",
  nodePrefix: "",
});
const fetchData = () => {
  nodeInfo().then((data: any) => {
    console.log(data);
    infoData.value = data;
  });
};
onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (AllPermission.includes(AUTH_CODE.NODE_DETAIL_DETAIL)) {
    fetchData();
  }
});
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import "./style/common";

// 数据汇总
.data-content {
  width: 100%;
  padding-bottom: 16px;

  .data-item {
    padding-right: 35px;
    cursor: pointer;
    transition: all 0.2s linear;

    .data-title {
      height: 32px;
      line-height: 32px;
      padding: 0 20px;
      background-color: #007457;
      border-radius: 5px 5px 0 0;
      color: #fff;
      font-size: 16px;
      transition: all 0.2s linear;

      .icon {
        float: left;
        width: 5px;
        height: 16px;
        background-color: #fff;
        margin: 8px 8px 0 0;
      }
    }

    .data-num {
      height: 108px;
      line-height: 108px;
      border: 1px solid #007457;
      border-radius: 0 0 5px 5px;
      text-align: center;
      transition: all 0.2s linear;

      .num {
        display: inline-block;
        line-height: 40px;
        height: 40px;
        font-size: 36px;
        color: #007457;
        border-bottom: 1px solid #007457;
      }
    }

    .data-num1 {
      line-height: 1;

      .num {
        margin-top: 15px;
      }

      .line2 {
        margin-top: 15px;
        font-size: 14px;

        .num2 {
          margin-left: 15px;
          border-bottom: 1px solid #444;
        }
      }
    }

    &:hover {
      box-shadow: 0 3px 7px 0 rgba(191, 195, 205, 0.19);

      .data-title {
        background-color: #007457;
      }

      .data-num {
        border-color: #007457;

        .num {
          color: #007457;
          border-bottom-color: #007457;
        }
      }
    }
  }

  .item1 {
    border-color: #007457;

    .data-title {
      background-color: #007457;
    }

    .data-num {
      border: 1px solid #007457;

      .num {
        color: $colorGreen;
        border-bottom: 1px solid #007457;
      }
    }

    &:hover {
      box-shadow: 0 3px 7px 0 rgba(191, 195, 205, 0.19);

      .data-title {
        background-color: #007457;
      }

      .data-num {
        border-color: #007457;

        .num {
          color: #007457;
          border-bottom-color: #007457;
        }
      }
    }
  }

  .data-item:last-child {
    margin-right: 0;
  }
}
.echarts-container {
  // background: #00f;
  width: 45%;
  height: 528px;
  padding: 10px 0 0px 0;
  display: inline-block;

  .my-chat-title {
    margin: 0 0 0 40px;
    font-size: medium;
    font-weight: 900;
  }
}
.edit-content {
  display: flex;
  flex-direction: row;
  justify-content: right;
  padding-bottom: 10px;
}
</style>
