<template>
  <div>
    <div class="home-section">
      <div class="section-wrap">
        <div class="section-title">
          <div class="section-title-line"></div>
          <span class="section-title-text"> 节点信息 </span>
        </div>
        <div class="info-content">
          <div class="info-line">
            <span class="info-item"
              >节点名称：{{ infoData.nodeName ?? "-" }}</span
            >
            <span class="info-item"
              >企业名称：{{ infoData.entName ?? "-" }}</span
            >
          </div>
          <div class="info-line">
            <span class="info-item">
              <span class="info-item-title">节点前缀：</span>
              <div class="info-item-content">
                <EllipsisText
                  :value="infoData.nodePrefix || '-'"
                ></EllipsisText>
              </div>
            </span>
            <span class="info-item"
              >节点地址：{{ infoData.nodeAddress ?? "-" }}</span
            >
          </div>
        </div>
        <div class="section-title">
          <div class="section-title-line"></div>
          <span class="section-title-text"> 标识统计 </span>
        </div>
        <div class="section-content">
          <el-row class="data-content" type="flex-direction: column;">
            <el-col :span="8" class="data-item">
              <div class="data-title">
                <i class="icon"></i>
                标识注册量
              </div>
              <div class="data-num">
                <span class="num">
                  {{ summaryData.totalStatisticsVO?.handleCreate ?? "-" }}
                </span>
              </div>
            </el-col>
            <el-col :span="8" class="data-item">
              <div class="data-title">
                <i class="icon"></i>
                标识解析量
              </div>
              <div class="data-num">
                <span class="num">
                  {{ summaryData.totalStatisticsVO?.handleQuery ?? "-" }}
                </span>
              </div>
            </el-col>
            <el-col :span="8" class="data-item">
              <div class="data-title">
                <i class="icon"></i>
                对接应用数量
              </div>
              <div class="data-num">
                <span class="num">
                  {{ summaryData.totalStatisticsVO?.appNum ?? "-" }}
                </span>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="section-content">
          <span class="echarts-container">
            <p class="my-chat-title">月度注册量统计</p>
            <stastic-line :lineData="lineData1" :id="'1'" />
          </span>
          <span class="echarts-container">
            <p class="my-chat-title">月度解析量统计</p>
            <stastic-line :lineData="lineData2" :id="'2'" />
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { reactive } from "vue";
import { nodeInfo, nodeSummary } from "@/api/province/node";
import StasticLine from "./echarts/StasticLine.vue";
import store from "@/store";
import { AUTH_CODE } from "@/utils/authCode";
import EllipsisText from "@/components/ellipsisText/index.vue";

interface InfoData {
  entName?: string;
  nodeAddress?: string;
  nodeName?: string;
  nodePrefix?: string;
}
interface SummaryData {
  totalStatisticsVO?: {
    handleCreate: number;
    handleQuery: number;
    appNum: number;
  };
}
export default {
  components: {
    StasticLine,
    EllipsisText,
  },
  data() {
    return {
      infoData: reactive<InfoData>({}),
      summaryData: reactive<SummaryData>({}),
      lineData1: reactive({}),
      lineData2: reactive({}),
    };
  },

  created() {
    // 判断用户是否有初始化查询权限
    const AllPermission = store.getters.auths;
    if (AllPermission.includes(AUTH_CODE.NODE_STATISTICS_DETAIL)) {
      this.fetchData();
    }
  },
  methods: {
    fetchData() {
      nodeSummary().then((data: any) => {
        this.summaryData = data;
        this.lineData1 = {
          xData: data.graphStatisticsVO.date,
          yData: data.graphStatisticsVO.handleCreateNum, // [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], //
        };
        this.lineData2 = {
          xData: data.graphStatisticsVO.date,
          yData: data.graphStatisticsVO.handleQueryNum, // [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], //
        };
      });
      nodeInfo({}).then((data: any) => {
        this.infoData = data;
      });
    },
  },
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import "./style/common";

.section-content {
  padding-left: 20px;
}

// 数据汇总
.data-content {
  width: 100%;
  padding-bottom: 16px;

  .data-item {
    padding-right: 35px;
    cursor: pointer;
    transition: all 0.2s linear;

    .data-title {
      height: 32px;
      line-height: 32px;
      padding: 0 20px;
      background-color: #007457;
      border-radius: 5px 5px 0 0;
      color: #fff;
      font-size: 16px;
      transition: all 0.2s linear;

      .icon {
        float: left;
        width: 5px;
        height: 16px;
        background-color: #fff;
        margin: 8px 8px 0 0;
      }
    }

    .data-num {
      height: 108px;
      line-height: 108px;
      border: 1px solid #007457;
      border-radius: 0 0 5px 5px;
      text-align: center;
      transition: all 0.2s linear;

      .num {
        display: inline-block;
        line-height: 40px;
        height: 40px;
        font-size: 36px;
        color: #007457;
        border-bottom: 1px solid #007457;
      }
    }

    .data-num1 {
      line-height: 1;

      .num {
        margin-top: 15px;
      }

      .line2 {
        margin-top: 15px;
        font-size: 14px;

        .num2 {
          margin-left: 15px;
          border-bottom: 1px solid #444;
        }
      }
    }

    &:hover {
      box-shadow: 0 3px 7px 0 rgba(191, 195, 205, 0.19);

      .data-title {
        background-color: #007457;
      }

      .data-num {
        border-color: #007457;

        .num {
          color: #007457;
          border-bottom-color: #007457;
        }
      }
    }
  }

  .item1 {
    border-color: #007457;

    .data-title {
      background-color: #007457;
    }

    .data-num {
      border: 1px solid #007457;

      .num {
        color: $colorGreen;
        border-bottom: 1px solid #007457;
      }
    }

    &:hover {
      box-shadow: 0 3px 7px 0 rgba(191, 195, 205, 0.19);

      .data-title {
        background-color: #007457;
      }

      .data-num {
        border-color: #007457;

        .num {
          color: #007457;
          border-bottom-color: #007457;
        }
      }
    }
  }

  .data-item:last-child {
    margin-right: 0;
  }
}
.echarts-container {
  // background: #00f;
  width: 45%;
  height: 528px;
  padding: 10px 0 0px 0;
  display: inline-block;

  .my-chat-title {
    margin: 0 0 0 40px;
    font-size: medium;
    font-weight: 900;
  }
}
</style>
