<!--
 * @Author: <PERSON>@gmail.com
 * @Date: 2022-11-08 14:09:44
 * @LastEditors: <PERSON> q<PERSON>@gmail.com
 * @LastEditTime: 2022-11-08 16:54:46
 * @FilePath: /id-yc-province-web/src/views/province/node/account.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <div class="home-section">
      <div v-if="isProvince || isEnterprise" class="section-wrap">
        <div class="section-title">
          <div class="section-title-line"></div>
          <span class="section-title-text"> 账号信息 </span>
        </div>
        <div class="info-content">
          <div class="info-line">
            <span class="info-item">
              <span class="info-item-title">登录名：</span>
              <div class="info-item-content">
                <ellipsisText :value="infoData.username"></ellipsisText>
              </div>
            </span>
            <span class="info-item">
              <span class="info-item-title">角色：</span>
              <div class="info-item-content">
                {{ roleName }}
              </div>
            </span>
          </div>
        </div>
      </div>
      <div v-else class="section-wrap">
        <div class="section-title">
          <div class="section-title-line"></div>
          <span class="section-title-text"> 账号信息 </span>
        </div>
        <div class="info-content">
          <div class="info-line">
            <span class="info-item">
              <span class="info-item-title">手机：</span>
              <div class="info-item-content">
                {{ phoneDesensitize(infoData.phone) }}
              </div>
            </span>
            <span class="info-item">
              <span class="info-item-title">地址：</span>
              <div class="info-item-content">
                <ellipsisText :value="infoData.address"></ellipsisText>
              </div>
            </span>
          </div>
          <div class="info-line">
            <span class="info-item">
              <span class="info-item-title">登录名：</span>
              <div class="info-item-content">
                <ellipsisText :value="infoData.username"></ellipsisText>
              </div>
            </span>
            <span class="info-item">
              <span class="info-item-title">邮箱：</span>
              <div class="info-item-content">
                <ellipsisText
                  :value="emailDesensitize(infoData.email)"
                ></ellipsisText>
              </div>
            </span>
          </div>
          <div class="info-line">
            <span class="info-item">
              <span class="info-item-title">备注：</span>
              <div class="info-item-content">
                <ellipsisText :value="infoData.remark"></ellipsisText>
              </div>
            </span>
            <span class="info-item">
              <span class="info-item-title">绑定标识：</span>
              <div class="info-item-content">
                <ellipsisText :value="infoData.handleUser"></ellipsisText>
              </div>
            </span>
          </div>
        </div>
        <div class="edit-content">
          <el-button
            @click="showEdit"
            type="primary"
            v-permission="AUTH_CODE.NODE_USER_EDIT"
            >修改账号信息</el-button
          >
          <el-button
            @click="showEditPwd"
            type="primary"
            v-permission="AUTH_CODE.NODE_USER_PWDEDIT"
            >修改密码</el-button
          >
        </div>
      </div>
    </div>
  </div>
  <EditAccount
    v-if="bindHdlVisible"
    @close-bindHdl-dialog="bindHdlVisible = false"
    @update-table="fetchData"
    :infoData="infoData"
  />
  <EditPwd
    v-if="editPwdVisible"
    @close-bindHdl-dialog="editPwdVisible = false"
    @update-table="fetchData"
  />
</template>

<script setup lang="ts">
import { onMounted, computed, ref } from "vue";
import { useStore } from "vuex";
import { accountInfo } from "@/api/province/node";
import EditAccount from "./editAccount.vue";
import EditPwd from "./editPwd.vue";
import { AUTH_CODE } from "@/utils/authCode";
import store from "@/store";
import ellipsisText from "@/components/ellipsisText/index.vue";
import { ROLE_TYPE } from "@/utils/constant";

const s = useStore();
const userInfo = computed(() => s.getters.userInfo);

const isProvince = computed(() =>
  userInfo.value.roleInfos.some(
    (item: any) => item.roleType === ROLE_TYPE.SYSTEM_ADMIN
  )
);

// 省级账号
const roleName = computed(() => userInfo.value.roleInfos[0].roleName || "");

const isEnterprise = computed(() =>
  userInfo.value.roleInfos.some(
    (item: any) => item.roleType === ROLE_TYPE.ENT_SYSTEM_ADMIN
  )
);

const bindHdlVisible = ref(false);
const editPwdVisible = ref(false);
const infoData = ref({
  phone: "",
  address: "",
  username: "",
  email: "",
  remark: "",
  handleUser: "",
});

// 手机号脱敏
const phoneDesensitize = (str: string) => {
  if (!str) return "-";
  return str.replace(/^(.{3}).*(.{4})$/, "$1****$2");
};

// 邮箱脱敏
const emailDesensitize = (str: string) => {
  if (!str) return "-";
  return str.split("@")[0].substring(0, 3) + "****@" + str.split("@")[1];
};

const fetchData = () => {
  accountInfo().then((data: any) => {
    infoData.value = data;
  });
};

const showEdit = () => {
  bindHdlVisible.value = true;
};
const showEditPwd = () => {
  editPwdVisible.value = true;
};
onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (AllPermission.includes(AUTH_CODE.NODE_USER_DETAIL)) {
    fetchData();
  }
});
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import "./style/common";

// 数据汇总
.data-content {
  width: 100%;
  padding-bottom: 16px;

  .data-item {
    padding-right: 35px;
    cursor: pointer;
    transition: all 0.2s linear;

    .data-title {
      height: 32px;
      line-height: 32px;
      padding: 0 20px;
      background-color: #007457;
      border-radius: 5px 5px 0 0;
      color: #fff;
      font-size: 16px;
      transition: all 0.2s linear;

      .icon {
        float: left;
        width: 5px;
        height: 16px;
        background-color: #fff;
        margin: 8px 8px 0 0;
      }
    }

    .data-num {
      height: 108px;
      line-height: 108px;
      border: 1px solid #007457;
      border-radius: 0 0 5px 5px;
      text-align: center;
      transition: all 0.2s linear;

      .num {
        display: inline-block;
        line-height: 40px;
        height: 40px;
        font-size: 36px;
        color: #007457;
        border-bottom: 1px solid #007457;
      }
    }

    .data-num1 {
      line-height: 1;

      .num {
        margin-top: 15px;
      }

      .line2 {
        margin-top: 15px;
        font-size: 14px;

        .num2 {
          margin-left: 15px;
          border-bottom: 1px solid #444;
        }
      }
    }

    &:hover {
      box-shadow: 0 3px 7px 0 rgba(191, 195, 205, 0.19);

      .data-title {
        background-color: #007457;
      }

      .data-num {
        border-color: #007457;

        .num {
          color: #007457;
          border-bottom-color: #007457;
        }
      }
    }
  }

  .item1 {
    border-color: #007457;

    .data-title {
      background-color: #007457;
    }

    .data-num {
      border: 1px solid #007457;

      .num {
        color: $colorGreen;
        border-bottom: 1px solid #007457;
      }
    }

    &:hover {
      box-shadow: 0 3px 7px 0 rgba(191, 195, 205, 0.19);

      .data-title {
        background-color: #007457;
      }

      .data-num {
        border-color: #007457;

        .num {
          color: #007457;
          border-bottom-color: #007457;
        }
      }
    }
  }

  .data-item:last-child {
    margin-right: 0;
  }
}
.echarts-container {
  // background: #00f;
  width: 45%;
  height: 528px;
  padding: 10px 0 0px 0;
  display: inline-block;

  .my-chat-title {
    margin: 0 0 0 40px;
    font-size: medium;
    font-weight: 900;
  }
}
.edit-content {
  display: flex;
  flex-direction: row;
  justify-content: right;
  padding-bottom: 10px;
}
</style>
