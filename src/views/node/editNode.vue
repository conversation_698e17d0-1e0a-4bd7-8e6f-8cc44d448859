<template>
  <el-dialog
    v-model="dialogFormVisible"
    title="修改节点信息"
    width="500px"
    @close="$emit('close-bindHdl-dialog')"
    destroy-on-close
    align-center
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="企业名称" prop="entName">
        <el-input v-model.trim="formData.entName" clearable />
      </el-form-item>
      <el-form-item label="节点名称" prop="nodeName">
        <el-input v-model.trim="formData.nodeName" clearable />
      </el-form-item>
      <el-form-item label="节点前缀" prop="nodePrefix">
        <el-input v-model.trim="formData.nodePrefix" clearable disabled />
      </el-form-item>
      <el-form-item label="节点地址" prop="nodeAddress">
        <el-input v-model.trim="formData.nodeAddress" clearable />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="bindHdl"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { getCurrentInstance, reactive, ref } from "vue";
import { ElMessage } from "element-plus";
import { editNode } from "@/api/province/node";

const { proxy }: any = getCurrentInstance();
const emit = defineEmits(["dialog-visible", "update-table"]);
const props = defineProps({
  infoData: {
    type: Object as () => {
      entName: string;
      nodeAddress: string;
      nodeName: string;
      nodePrefix: string;
    },
    default: () => ({}),
  },
});

const dialogFormVisible = ref(true);
const formData = ref({
  entName: props.infoData.entName,
  nodeName: props.infoData.nodeName,
  nodePrefix: props.infoData.nodePrefix,
  nodeAddress: props.infoData.nodeAddress,
});
const rules = reactive({
  entName: [
    {
      required: true,
      message: "请输入企业名称",
      trigger: "blur",
    },
  ],
  nodeName: [
    {
      required: true,
      message: "请输入节点名称",
      trigger: "blur",
    },
  ],
  nodePrefix: [
    {
      required: true,
      message: "请输入节点前缀",
      trigger: "blur",
    },
  ],
  nodeAddress: [
    {
      required: true,
      message: "请输入节点地址",
      trigger: "blur",
    },
  ],
});

function bindHdl() {
  proxy.$refs.formRef.validate(async (valid: any) => {
    if (valid) {
      editNode(formData.value).then((res: any) => {
        dialogFormVisible.value = false;
        emit("update-table", true);
        ElMessage({
          message: "修改节点信息成功!",
          type: "success",
        });
      });
    }
  });
}
</script>

<style lang="scss"></style>
