<template>
  <el-dialog
    v-model="dialogFormVisible"
    title="修改账号信息"
    width="600px"
    @close="$emit('close-bindHdl-dialog')"
    destroy-on-close
    align-center
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="地址" prop="address">
        <el-input v-model.trim="formData.address" clearable />
      </el-form-item>
      <el-form-item label="手机" prop="phone">
        <el-input v-model.trim="formData.phone" clearable />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input v-model.trim="formData.email" clearable />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model.trim="formData.remark" clearable />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="bindHdl"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { getCurrentInstance, reactive, ref } from "vue";
import { ElMessage } from "element-plus";
import { editAccount } from "@/api/province/node";

const emit = defineEmits(["dialog-visible", "update-table"]);
const { proxy }: any = getCurrentInstance();
// const props = defineProps(["infoData"]);
const props = defineProps({
  infoData: {
    type: Object as () => {
      phone: string;
      address: string;
      username: string;
      email: string;
      remark: string;
      handleUser: string;
    },
    default: () => ({}),
  },
});

const dialogFormVisible = ref(true);
const formData = ref({
  remark: props.infoData.remark,
  address: props.infoData.address,
  phone: props.infoData.phone,
  email: props.infoData.email,
});
const rules = reactive({
  // nickName: [
  //   {
  //     required: true,
  //     message: "请输入账户名称",
  //     trigger: "blur",
  //   },
  // ],
  address: [
    {
      required: true,
      message: "请输入地址",
      trigger: "blur",
    },
  ],
  phone: [
    {
      required: true,
      message: "请输入手机号",
      trigger: "blur",
    },
  ],
  email: [
    {
      required: true,
      message: "请输入邮箱",
      trigger: "blur",
    },
  ],
});

function bindHdl() {
  proxy.$refs.formRef.validate(async (valid: any) => {
    if (valid) {
      editAccount(formData.value).then((res: any) => {
        dialogFormVisible.value = false;
        emit("update-table", true);
        ElMessage({
          message: "修改账户信息成功!",
          type: "success",
        });
      });
    }
  });
}
</script>

<style lang="scss"></style>
