<template>
  <el-dialog
    v-model="dialogFormVisible"
    :title="dialogTitle"
    width="500px"
    @close="$emit('close-bindHdl-dialog')"
    destroy-on-close
    align-center
    :show-close="false"
    :close-on-press-escape="false"
    :close-on-click-modal="false"
    modal="true"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="原始密码" prop="oldPwd">
        <el-input
          v-model.trim="formData.oldPwd"
          type="password"
          show-password
          clearable
        />
      </el-form-item>
      <el-form-item label="新密码" prop="newPwd">
        <el-input
          v-model.trim="formData.newPwd"
          type="password"
          show-password
          clearable
        />
      </el-form-item>
      <el-form-item label="确认密码" prop="rePwd" type="password">
        <el-input v-model.trim="formData.rePwd" show-password clearable />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button
          @click="dialogFormVisible = false"
          v-if="
            props.titleCode !== NEED_UPDATE_PASSWORD.EXPIRE &&
            props.titleCode !== NEED_UPDATE_PASSWORD.INIT
          "
          >取消</el-button
        >
        <el-button type="primary" :loading="loading" @click="bindHdl">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, getCurrentInstance, reactive, ref } from "vue";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
import { editPwd } from "@/api/province/node";
import { getPublicKey, encrypt } from "@/utils/forgeEncrypt";
import { NEED_UPDATE_PASSWORD } from "@/utils/constant";

const props = defineProps({
  titleCode: {
    type: Number,
    default: 9,
  },
});
const emit = defineEmits(["dialog-visible", "update-table"]);
const { proxy }: any = getCurrentInstance();

const dialogFormVisible = ref(true);
const loading = ref(false);
const formData = ref({
  oldPwd: "",
  newPwd: "",
  rePwd: "",
});

const dialogTitle = computed(() => {
  let title = "修改密码";
  if (props.titleCode === NEED_UPDATE_PASSWORD.EXPIRE) {
    title = "密码已过期，请修改密码";
  }

  if (props.titleCode === NEED_UPDATE_PASSWORD.INIT) {
    title = "首次登录，请修改密码";
  }
  return title;
});

const checkPassWord = (rule: any, value: string, callback: any) => {
  // ⾄8-16位，必须包含大写字母、小写字母 、数字、 特殊字符（四种里至少三种，8-20位）
  const reg =
    /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,20}$/;
  if (!reg.test(value)) {
    callback(new Error("8-20位，包含大小写字母、数字、特殊符号至少三种"));
  } else {
    callback();
  }
};
const checkRePWD = (rule: any, value: string, callback: any) => {
  if (formData.value.newPwd !== formData.value.rePwd) {
    callback(new Error("两次新密码输入不一致"));
  } else {
    callback();
  }
};
const rules = reactive({
  oldPwd: [{ required: true, message: "请输入旧密码", trigger: "blur" }],
  newPwd: [
    { required: true, trigger: "blur", message: "请输入您的密码" },
    { required: true, validator: checkPassWord, trigger: "blur" },
  ],
  rePwd: [
    { required: true, trigger: "blur", message: "请再次输入新密码" },
    { required: true, validator: checkRePWD, trigger: "blur" },
  ],
});

const store = useStore();
const globalConfig = computed(() => store.getters.globalConfig);

function bindHdl() {
  proxy.$refs.formRef.validate(async (valid: any) => {
    if (valid) {
      const publicKey = await getPublicKey();
      const formTemp = {
        oldPwd: globalConfig.value?.login.passwordEncrypted
          ? encrypt(formData.value.oldPwd, publicKey)
          : formData.value.oldPwd,
        newPwd: globalConfig.value?.login.passwordEncrypted
          ? encrypt(formData.value.newPwd, publicKey)
          : formData.value.newPwd,
      };
      loading.value = true;
      editPwd(formTemp)
        .then(() => {
          dialogFormVisible.value = false;
          emit("update-table", true);
          ElMessage({
            message: "修改密码成功!",
            type: "success",
          });
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}
</script>

<style lang="scss"></style>
