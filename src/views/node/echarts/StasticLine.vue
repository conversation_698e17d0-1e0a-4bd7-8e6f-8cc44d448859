<template>
  <div class="charts-box" :id="id" />
</template>

<script lang="ts">
import { defineComponent, toRefs, inject, watch } from "vue";

export default defineComponent({
  props: {
    id: {
      type: String,
      required: true,
    },
    lineData: {
      type: Object,
      required: true,
    },
  },
  setup(props) {
    const echarts: any = inject("echarts");
    const { id, lineData } = toRefs(props);
    const change = () => {
      const chartBox = echarts.init(document.getElementById(id.value));
      const option = {
        color: ["#007457"],
        tooltip: {
          trigger: "axis",
        },
        xAxis: {
          data: lineData.value.xData,
          axisTick: {
            alignWithLabel: true,
          },
          type: "category",
          axisLabel: {
            show: true,
            // interval: 0,
          },
        },
        yAxis: {},
        series: [
          {
            data: lineData.value.yData,
            type: "bar",
          },
        ],
      };
      chartBox.setOption(option);
    };
    watch(lineData, () => {
      change();
    });
  },
});
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import "../style/common";
.charts-box {
  width: 100%;
  height: 500px;
}
</style>
