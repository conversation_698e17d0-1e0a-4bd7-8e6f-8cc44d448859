<template>
  <el-drawer
    class="tool-dialog"
    append-to-body
    v-model="dialogVisible"
    size="880px"
    :title="title"
    @close="handleClose"
  >
    <div class="data-channel-wrap" v-loading="loading">
      <el-form
        :model="form"
        ref="formRef"
        label-width="100px"
        :rules="rules"
        label-position="left"
      >
        <el-form-item
          label="数据通道名称"
          prop="dataChannelName"
          class="form-item-custom"
        >
          <el-input
            v-model.trim="form.dataChannelName"
            placeholder="请输入"
            clearable
          />
        </el-form-item>
        <el-form-item
          :disabled="isEdit"
          class="form-item-custom"
          required
          label="所属对象标识"
          prop="objectHandleId"
        >
          <div class="object-handle-wrap">
            <el-select
              v-if="appType === APP_TYPE.DMM"
              v-model="form.objectHandleType"
              class="select-object-handle-type"
              @change="(val: string | number) => handleObjectHandleChange({ handleType: val })"
            >
              <el-option
                v-for="item in OBJECT_HANDLE_TYPE_LIST"
                :key="item.id"
                :value="item.id"
                :label="item.name"
              ></el-option>
            </el-select>
            <el-input
              v-model="identifyName"
              placeholder="请选择"
              class="select-object-handle"
              disabled
            >
            </el-input>
            <el-button
              class="cb"
              type="primary"
              @click="() => (showChooseIdenfity = true)"
            >
              选择对象标识
            </el-button>
          </div>
        </el-form-item>
        <el-form-item
          label="所属数据服务"
          class="form-item-custom"
          prop="dataServiceId"
        >
          <el-select
            v-model="form.dataServiceId"
            placeholder="请选择"
            @change="handleServiceIdChange"
          >
            <el-option
              v-for="item in serviceList"
              :key="item.id"
              :value="item.id"
              :label="item.dataServiceName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数据库" class="form-item-custom" prop="databaseId">
          <el-select v-model="form.databaseId" placeholder="请选择">
            <el-option
              v-for="item in databaseList"
              :key="item.databaseId"
              :value="item.databaseId"
              :label="item.databaseName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="数据通道ID"
          prop="dataChannelId"
          class="form-item-custom form-item-pl10"
        >
          <el-input
            v-model.trim="form.dataChannelId"
            :disabled="isEdit"
            maxlength="30"
            placeholder="输入正确的历史通道ID或自动生成"
            clearable
            @blur="handleGetInfo"
          >
          </el-input>
        </el-form-item>
        <el-form-item
          label="数据通道类型"
          prop="dataType"
          class="form-item-custom"
        >
          <el-select v-model="form.dataType" placeholder="请选择">
            <el-option
              v-for="item in DATA_TYPE_LIST"
              :key="item.id"
              :value="item.id"
              :label="item.name"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="解析SQL"
          prop="resolveSql"
          class="form-item-custom"
        >
          <div class="sql-wrap">
            <el-input
              v-model="form.resolveSql"
              :rows="2"
              type="textarea"
              placeholder="请输入"
            />
            <el-button
              type="primary"
              text
              @click="handleCreateParsingStatements"
            >
              创建解析语句
            </el-button>
          </div>
        </el-form-item>
        <div class="query-sql-switch">
          <div
            class="switch-open"
            v-if="!showQuerySQL"
            @click="showQuerySQL = true"
          >
            <el-icon><CaretBottom /></el-icon>
            <span>更多</span>
          </div>
          <div class="switch-open" v-else @click="showQuerySQL = false">
            <el-icon><CaretTop /></el-icon>
            <span>收起</span>
          </div>
          <div class="switch-open-line"></div>
        </div>
        <el-form-item
          v-if="showQuerySQL"
          label="查询SQL"
          class="form-item-custom"
        >
          <div class="sql-wrap">
            <el-input
              v-model="form.querySql"
              :rows="2"
              type="textarea"
              placeholder="请输入"
            />
          </div>
        </el-form-item>
      </el-form>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="confirmLoading"
          @click="handleConfirm"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-drawer>
  <ParsingStatementsDialog
    :data="parsingStatementData"
    @close="showParsing = false"
    @confirm="buildConfirm"
    v-if="showParsing"
  ></ParsingStatementsDialog>
  <ChooseIdentifyDrawer
    v-if="showChooseIdenfity"
    :data-type="appType"
    :handle-type="form.objectHandleType"
    @success="chooseIdentifyDone"
    @cancel="chooseIdentifyCancel"
  />
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, toRefs } from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
import { encryptData, decryptData } from "@/utils/gm4";
import {
  OBJECT_HANDLE_TYPE_LIST,
  DATA_TYPE_LIST,
  APP_TYPE,
} from "@/utils/dataPlatform";
import {
  ApiDataChannelDetail,
  ApiDataChannelCreate,
  ApiServiceList,
  ApiDataChannelDatabaseList,
  ApiObjectHandle,
  ApiDataChannelUpdate,
  ApiGetChannelInfo,
} from "@/api/tool/index";
import type { IServiceList, IChannelDatabase } from "@/types/tool";
import ParsingStatementsDialog from "@/components/ParsingStatementsDialog/index.vue";
import ChooseIdentifyDrawer from "./chooseIdentifyDrawer.vue";

interface Iparam {
  handleType?: string | number;
  dataType?: string | number | undefined;
}

const props = defineProps({
  id: String,
});
const store = useStore();
// const userInfo = computed(() => store.getters.userInfo);
const { appType } = toRefs(store.getters);

const emit = defineEmits(["close", "success"]);

const showParsing = ref(false);

const showChooseIdenfity = ref(false);

const title = ref("新增通道");

const dialogVisible = true;

const showQuerySQL = ref(false);

const confirmLoading = ref(false);

const identifyName = ref("");
const form = reactive<any>({
  objectHandleType: "", // 所属对象标识-类型
  dataChannelName: "", // 数据通道名称
  objectHandleId: "", // 所属对象标识
  dataServiceId: "", // 所属数据服务
  databaseId: "", // 数据库
  dataChannelId: "", // 数据通道ID
  dataType: "", // 数据通道类型
  resolveSql: "", // 解析SQL
  querySql: "", // 查询SQL
});

const isEdit = ref(false);

const loading = ref(false);

const dataChannelIdValidate = (rule: any, value: any, callback: any) => {
  const reg = /^[0-9]+$/;
  if (value && !reg.test(value)) {
    return callback("数据通道ID格式错误，请重新填写");
  }
  callback();
};
const rules = reactive({
  dataChannelName: [
    { required: true, message: "请输入数据通道名称", trigger: "blur" },
    {
      max: 255,
      message: "最大长度为255字符",
      trigger: "blur",
    },
  ],
  dataChannelId: [
    { required: false, message: "请输入数据通道ID", trigger: "blur" },
    { validator: dataChannelIdValidate, trigger: "blur" },
    // {
    //   max: 30,
    //   message: "最大长度为30字符",
    //   trigger: "blur",
    // },
  ],
  objectHandleType: [
    { required: true, message: "请选择所属对象标识", trigger: "change" },
  ],
  objectHandleId: [
    { required: true, message: "请选择所属对象标识", trigger: "change" },
  ],
  dataServiceId: [
    { required: true, message: "请选择所属数据服务", trigger: "change" },
  ],
  databaseId: [{ required: true, message: "请选择数据库", trigger: "change" }],
  dataType: [
    { required: true, message: "请选择数据通道类型", trigger: "change" },
  ],
  resolveSql: [
    { required: true, message: "请输入解析SQL", trigger: ["blur", "change"] },
  ],
});

const formRef = ref();

const handleList = ref<any>([]);

const serviceList = ref<IServiceList[]>([]);

const databaseList = ref<IChannelDatabase[]>([]);

const parsingStatementData = ref();

const handleCreateParsingStatements = () => {
  if (!form.dataChannelName) {
    ElMessage.warning("请填写数据通道名称");
    return;
  }
  if (!form.objectHandleId) {
    ElMessage.warning("请填写所属对象标识");
    return;
  }
  if (!form.dataServiceId) {
    ElMessage.warning("请填写所属数据服务");
    return;
  }
  if (!form.databaseId) {
    ElMessage.warning("请填写数据库");
    return;
  }
  try {
    const objectHandleName = identifyName.value;
    const dataServiceName = serviceList.value.filter(
      (item: any) => item.id === form.dataServiceId
    )[0].dataServiceName;
    const databaseName = databaseList.value.filter(
      (item: any) => item.databaseId === form.databaseId
    )[0].databaseName;
    parsingStatementData.value = {
      dataChannelName: form.dataChannelName, // 数据通道名称
      objectHandleName, // 所属对象标识
      dataServiceName, // 所属数据服务
      dataServiceId: form.dataServiceId,
      databaseName, // 数据库
      databaseId: form.databaseId,
      id: form.objectHandleId || 1,
    };
  } catch (error) {
    parsingStatementData.value = {
      dataChannelName: form.dataChannelName, // 数据通道名称
      objectHandleName: "---", // 所属对象标识
      dataServiceName: "---", // 所属数据服务
      databaseName: "ewm", // 数据库
      id: form.objectHandleId || 1,
    };
    console.log(2);
  }

  showParsing.value = true;
};

const buildConfirm = (data: string) => {
  form.resolveSql = data;
  showParsing.value = false;
};

const handleClose = () => {
  emit("close");
};

async function formValidate() {
  let isError = false;
  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  return isError;
}

const handleObjectHandleChange = async (params: Iparam) => {
  form.objectHandleId = "";
  await ApiObjectHandle({ ...params, dataType: appType.value }).then((res) => {
    handleList.value = res.content;
  });
};

// 获取数据服务列表
const getServiceList = () => {
  ApiServiceList().then((res) => {
    serviceList.value = Array.isArray(res) ? res : [];
  });
};

// 获取数据库列表
const getDatabaseList = () => {
  ApiDataChannelDatabaseList(form.dataServiceId).then((res) => {
    if (!res) {
      databaseList.value = [];
      return;
    }
    databaseList.value = Array.isArray(res) ? res : [];
  });
};

const handleServiceIdChange = (val: any) => {
  if (val) {
    databaseList.value = [];
    form.databaseId = "";
    getDatabaseList();
  }
};

// 获取通道详情
const getDetail = async () => {
  const result = await ApiDataChannelDetail(props.id || "").then((res) => {
    const data = Object.assign(form, { ...res });
    if (form.querySql !== null && form.querySql !== "") {
      form.querySql = decryptData(form.querySql);
    }
    if (form.resolveSql !== null && form.resolveSql !== "") {
      form.resolveSql = decryptData(form.resolveSql);
    }
    return data;
  });
  return result;
};

const handleConfirm = () => {
  formRef.value.validate((valid: any) => {
    if (valid) {
      confirmLoading.value = true;
      const params = { ...form };
      if (form.querySql !== null && form.querySql !== "") {
        params.querySql = encryptData(form.querySql);
      }
      if (form.resolveSql !== null && form.resolveSql !== "") {
        params.resolveSql = encryptData(form.resolveSql);
      }
      if (!isEdit.value) {
        ApiDataChannelCreate(params)
          .then(() => {
            ElMessage.success("创建成功");
            emit("success");
          })
          .finally(() => {
            confirmLoading.value = false;
          });
      } else {
        ApiDataChannelUpdate({ ...params, id: props.id })
          .then(() => {
            ElMessage.success("更新成功");
            emit("success");
          })
          .finally(() => {
            confirmLoading.value = false;
          });
      }
    }
  });
};

const chooseIdentifyCancel = () => {
  showChooseIdenfity.value = false;
};

const chooseIdentifyDone = (item: any) => {
  showChooseIdenfity.value = false;
  identifyName.value =
    form.objectHandleType === 2 || appType.value === 2
      ? item.name + "-" + item.handle
      : item.name;
  form.objectHandleId = item.id;
};

const handleGetInfo = () => {
  if (!form.dataServiceId) {
    ElMessage.warning("请先填写所属数据服务");
    form.dataChannelId = "";
    return;
  }
  if (!form.dataChannelId) return;
  // 格式校验
  const reg = /^[0-9]+$/;
  if (!reg.test(form.dataChannelId)) {
    // 格式错误
    return;
  }

  ApiGetChannelInfo(form.dataChannelId, form.dataServiceId).then((res) => {
    Object.assign(form, {
      resolveSql: res.resolveSql,
      dataType: res.dataChannelType,
    });
  });
};

const editData = ref();

onMounted(async () => {
  if (appType.value === 2 && !isEdit.value) {
    handleObjectHandleChange({ dataType: appType.value });
  }
  getServiceList();
  isEdit.value = !!props.id;
  if (isEdit.value) {
    title.value = "编辑通道";
    getDetail().then((res) => {
      console.log("res:", res);
      editData.value = res;
      getDatabaseList();
      // 中台
      const objectHandleId = res.objectHandleId;
      if (appType.value === 1 && res.objectHandleType) {
        handleObjectHandleChange({ handleType: res.objectHandleType }).then(
          () => {
            form.objectHandleId = objectHandleId;
            identifyName.value = res.objectHandle;
          }
        );
      }
      // 非中台不需要传handleType
      if (appType.value === 2) {
        handleObjectHandleChange({
          dataType: appType.value,
        }).then(() => {
          form.objectHandleId = objectHandleId;
          identifyName.value = res.objectHandle;
        });
      }
    });
  }
});
</script>
<style scoped lang="scss">
.query-sql-switch {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  align-items: flex-end;
  margin: 20px 0;
  .switch-open {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    cursor: pointer;
  }
  .switch-open-line {
    width: 100%;
    height: 1px;
    background-color: #dfe4e3;
    margin-top: 4px;
  }
}
.form-item-custom {
  margin-bottom: 20px !important;
  :deep(.el-input-group__prepend) {
    padding-right: 0;
  }
  .el-select {
    width: 100%;
  }
  .sql-wrap {
    width: 100%;
  }

  :deep(.el-textarea__inner) {
    height: 126px;
  }
  .el-button {
    padding: 8px 0;
  }

  .cb {
    margin-left: 12px;
    padding-left: 16px;
    padding-right: 16px;
  }
}
.object-handle-wrap {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .select-object-handle-type {
    width: 120px;
    min-width: 120px;
    margin-right: 12px;
  }
  .select-object-handle {
    flex: 1;
    min-width: 0;
  }
}

:deep(.tool-dialog) {
  width: 880px !important;
}
:deep(.handle-detail-dialog) {
  margin: initial;
}
.add-or-edit-dialog {
  padding: 0 40px;
}
.components-handle-detail-line {
  border-top: 1px solid #e5e8ef;
  margin-bottom: 24px;
}

.handle-tid-wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  .el-select {
    width: 146px;
    min-width: 146px;
  }
  .handle-tid-line {
    font-size: 12px;
    font-size: 12px;
    line-height: 20px;
    margin: 0 4px;
  }
  .el-form-item {
    flex: 1;
    min-width: 0;
  }
}
</style>
