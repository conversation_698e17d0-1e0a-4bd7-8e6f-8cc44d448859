<template>
  <el-drawer
    class="choose-identify-drawer"
    v-model="dialogVisible"
    size="880"
    title="选择对象标识"
    append-to-body
    @close="handleClose"
  >
    <div class="page-search">
      <search-layout>
        <template #right>
          <el-form :inline="true" :model="searchForm" @submit.prevent>
            <el-form-item style="width: 240px; max-width: 240px">
              <el-input
                v-model.trim="searchForm.name"
                clearable
                @clear="handleSearch"
                placeholder="请输入"
              >
                <template #prefix>对象标识名称：</template></el-input
              >
            </el-form-item>
            <el-form-item style="width: 240px; max-width: 240px">
              <el-input
                v-model.trim="searchForm.handle"
                clearable
                @clear="handleSearch"
                placeholder="请输入"
              >
                <template #prefix>对象标识：</template></el-input
              ></el-form-item
            >
            <el-form-item style="width: 60px; max-width: 60px">
              <el-button
                type="primary"
                :loading="data.searchLoading"
                @click="handleSearch"
                >搜索</el-button
              >
            </el-form-item>
            <el-form-item style="width: 60px; max-width: 60px"
              ><el-button
                type="info"
                :loading="data.searchLoading"
                @click="handleReset"
                color="#EEF2F1"
                >重置</el-button
              ></el-form-item
            >
          </el-form>
        </template>
      </search-layout>
    </div>
    <div class="page-search-body" v-if="!recordVisible">
      <el-table
        :data="data.tableData"
        v-loading="data.tableLoading"
        border
        size="small"
        @current-change="handleCurrentChange"
      >
        <el-table-column width="64" label="">
          <template #default="scope">
            <el-radio
              v-model="currentData"
              :value="scope.$index"
              :label="scope.$index"
              @click.prevent
              >{{
            }}</el-radio>
          </template>
        </el-table-column>
        <el-table-column label="序号" type="index" width="55" />
        <el-table-column property="name" label="对象标识名称">
          <template #default="scope">
            <ellipsisText :value="scope.row.name">{{
              scope.row.name || "-"
            }}</ellipsisText>
          </template>
        </el-table-column>
        <el-table-column property="handle" label="对象标识">
          <template #default="scope">
            <ellipsisText :value="scope.row.handle">{{
              scope.row.handle || "-"
            }}</ellipsisText>
          </template>
        </el-table-column>
        <el-table-column v-if="isAppUser" label="发布状态">
          <template #default="scope">
            {{ getHandleStatusName(scope.row.handleStatus) }}
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        v-model:currentPage="data.page"
        v-model:page-size="data.size"
        :page-sizes="[10, 20, 30, 40]"
        small
        layout="total, sizes, prev, pager, next, jumper"
        :total="data.totalCount"
        @size-change="handleSizeChange"
        @current-change="handlPageChange"
      />
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="confirmLoading"
          @click="handleConfirm"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-drawer>
</template>
<script setup lang="ts">
import {
  ref,
  reactive,
  onMounted,
  toRefs,
  defineEmits,
  defineProps,
} from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
import { ApiObjectHandle } from "@/api/tool/index";
import { AUTH_CODE } from "@/utils/authCode";
import SearchLayout from "@/components/searchLayout/index.vue";
import ellipsisText from "@/components/ellipsisText/index.vue";

const props = defineProps({
  handleType: {
    type: Number,
    default: null,
  },
  dataType: {
    type: Number,
    default: null,
  },
});

const store = useStore();
const { isProvinceUser, isEntUser, isAppUser } = toRefs(store.getters);

const dialogVisible = ref(true);
const recordVisible = ref(false);
const confirmLoading = ref(false);
const currentData = ref();

const emits = defineEmits(["cancel", "success"]);
const handleClose = () => {
  emits("cancel");
};
const handleConfirm = () => {
  if (currentData.value === -1) {
    ElMessage.error("请勾选一条对象标识数据");
    confirmLoading.value = false;
    return;
  }
  const item = data.tableData[currentData.value];
  emits("success", item);
};
const getHandleStatusName = (status: number) => {
  if (status === 1) return "草稿";
  if (status === 2) return "发布";
  return "-";
};

const searchForm = reactive({
  name: "",
  handle: "",
});

interface Data {
  tableData: {
    appName: string;
    createdBy: string;
    createdTime: string;
    entityType: number;
    handle: string;
    id: number;
    name: string;
    type: number;
    reportName: string;
    updatedTime: string;
    uploadState: number;
  }[];
  appList: {
    id: number;
    appName: string;
  }[];
  page: number;
  size: number;
  totalCount: number;
  dialogTitle: string;
  dialogVisible: boolean;
  selectHandleData: any;
  dialogDisabled: boolean;
  deleteLoading: boolean;
  tableLoading: boolean;
  searchLoading: boolean;
  confirmLoading: boolean;
}
const data = reactive<Data>({
  tableData: [],
  appList: [],
  page: 1,
  size: 10,
  totalCount: 1,
  dialogTitle: "",
  dialogVisible: false,
  selectHandleData: null,
  dialogDisabled: false,
  deleteLoading: false,
  tableLoading: false,
  searchLoading: false,
  confirmLoading: false,
});

function getTableData() {
  currentData.value = -1;
  data.tableLoading = true;
  data.tableData = [];
  const params = {
    name: searchForm.name,
    handle: searchForm.handle,
    page: data.page - 1,
    size: data.size,
    dataType: props.dataType,
    handleType: props.handleType,
  };

  ApiObjectHandle(params)
    .then((response: any) => {
      const result = response;
      data.tableData = result?.content || [];
      data.page = result?.pageNumber || 1;
      data.totalCount = result?.totalCount || 0;
    })
    .finally(() => {
      data.tableLoading = false;
      data.searchLoading = false;
    });
}

const handleCurrentChange = (val: any) => {
  if (val == null) {
    currentData.value = -1;
    return;
  }
  var index = data.tableData.indexOf(val);
  currentData.value = index;
};

function handleSearch() {
  data.searchLoading = true;
  data.page = 1;
  getTableData();
}

function handleReset() {
  data.searchLoading = true;
  data.page = 1;
  searchForm.handle = "";
  searchForm.name = "";
  getTableData();
}

function handleSizeChange(num: number) {
  data.size = num;
  data.page = 1;
  getTableData();
}

function handlPageChange(num: number) {
  data.page = num;
  getTableData();
}

onMounted(() => {
  getTableData();
});
</script>
<style lang="scss" scoped>
.page-search {
  .dialogClass {
    background-color: red;

    .mid-box {
      background-color: red;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      :deep(.el-input__inner) {
        width: 500px;
        height: 32px;
        line-height: 32px;
      }
    }
  }
}
.popover-delete-tips {
  margin-bottom: 20px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .popover-delete-tips-text {
    margin-left: 10px;
    font-size: 14px;
  }
}
.popover-delete-wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
.infoAlert {
  display: flex;
  align-items: center;
  height: 30px;
  margin: -44px -44px 14px;
  background-color: rgb(232, 244, 255);
}
.handleUploadInfo {
  width: 20px;
  height: 20px;
  margin-left: 20px;
}

.handleRecordClass {
  margin-left: 4px;
  margin-right: 4px;
  color: rgb(0, 183, 255);
  cursor: pointer;
}
.svgClose {
  height: 100%;
  width: 25px;
  margin-left: auto;
  display: flex;
  align-items: center;
}
.handleUploadClose {
  width: 15px;
  height: 15px;
  cursor: pointer;
}
.clickable-txt-cls {
  color: #1664ff;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
</style>
