<template>
  <el-drawer
    v-model="dialogFormVisible"
    title="通道详情"
    @close="$emit('close-dialog')"
    destroy-on-close
    size="383px"
  >
    <el-descriptions :column="1" direction="horizontal">
      <el-descriptions-item label="数据通道名称">{{
        detailInfo.dataChannelName
      }}</el-descriptions-item>
      <el-descriptions-item label="所属对象标识">{{
        detailInfo.objectHandle
      }}</el-descriptions-item>
      <el-descriptions-item label="所属数据服务">{{
        detailInfo.dataServiceName
      }}</el-descriptions-item>
      <el-descriptions-item label="数据库">{{
        detailInfo.databaseName
      }}</el-descriptions-item>
      <el-descriptions-item label="数据通道ID">{{
        detailInfo.dataChannelId
      }}</el-descriptions-item>
      <el-descriptions-item label="实例数据类型">{{
        DATA_TYPE_LIST.filter((item) => item.id === detailInfo.dataType)[0]
          ?.name
      }}</el-descriptions-item>
      <el-descriptions-item label="解析SQL">
        <div :class="detailInfo.resolveSql ? 'resolve-desc' : ''">
          <span>{{ detailInfo.resolveSql || "-" }}</span>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="查询SQL">
        <div :class="detailInfo.querySql ? 'resolve-desc' : ''">
          <span>{{ detailInfo.querySql || "-" }}</span>
        </div>
      </el-descriptions-item>
    </el-descriptions>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ApiDataChannelDetail } from "@/api/tool/index";
import { decryptData } from "@/utils/gm4";
import { DATA_TYPE_LIST } from "@/utils/dataPlatform";

// const props = defineProps(["detailInfo"]);
const detailInfo = reactive({
  dataChannelName: "",
  objectHandle: "",
  dataServiceName: "",
  databaseName: "",
  dataChannelId: "",
  dataType: 1,
  resolveSql: "",
  querySql: "",
});
const props = defineProps<{
  id: number;
}>();

const dialogFormVisible = ref(true);
const getHandleDetail = () => {
  ApiDataChannelDetail(props.id).then((res) => {
    Object.assign(detailInfo, { ...res });
    if (detailInfo.querySql !== null && detailInfo.querySql !== "") {
      detailInfo.querySql = decryptData(detailInfo.querySql);
    }
    if (detailInfo.resolveSql !== null && detailInfo.resolveSql !== "") {
      detailInfo.resolveSql = decryptData(detailInfo.resolveSql);
    }
  });
};
onMounted(() => {
  getHandleDetail();
});
</script>

<style lang="scss" scoped>
.el-select {
  width: 100%;
}
:deep(.el-descriptions__cell) {
  display: flex;
}
:deep(.el-descriptions__label) {
  width: 80px;
  min-width: 80px;
  display: inline-block;
}
.resolve-desc {
  background-color: #eef2f1;
  display: inline-block;
  padding: 6px 12px;
  max-height: 100px;
  overflow-y: auto;
  span {
    word-break: break-all;
    white-space: pre-wrap;
  }
}
</style>
