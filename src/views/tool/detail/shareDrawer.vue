<template>
  <el-drawer
    v-model="dialogFormVisible"
    title="已关联对象标识"
    @close="$emit('close-dialog')"
    destroy-on-close
    size="720"
  >
    <div class="dialog-dataChanelInfo">
      <el-space class="description-title">
        <el-divider direction="vertical" class="description-title-divider" />
        <div class="description-title-text">{{ item.dataChannelName }}</div>
      </el-space>
    </div>
    <SearchLayout>
      <template #right>
        <el-form :inline="true" :model="data" @submit.prevent ref="formRef">
          <el-form-item style="width: 266px; max-width: 266px"
            ><el-input
              v-model.trim="searchForm.name"
              clearable
              placeholder="请输入"
              @clear="handleSearch()"
            >
              <template #prefix>对象标识名称：</template></el-input
            ></el-form-item
          >
          <el-form-item style="width: 266px; max-width: 266px"
            ><el-input
              v-model.trim="searchForm.handle"
              clearable
              placeholder="请输入"
              @clear="handleSearch()"
            >
              <template #prefix>对象标识：</template></el-input
            ></el-form-item
          >
          <el-form-item style="width: 56px; max-width: 56px"
            ><el-button
              type="primary"
              :loading="data.searchLoading"
              @click="handleSearch"
              >搜索</el-button
            ></el-form-item
          >
          <el-form-item style="width: 56px; max-width: 56px"
            ><el-button
              type="info"
              :loading="data.searchLoading"
              @click="handleReset"
              color="#EEF2F1"
              >重置</el-button
            ></el-form-item
          >
        </el-form>
      </template>
    </SearchLayout>
    <!-- table内容 -->
    <div class="page-search-body">
      <el-table
        :data="tableData"
        v-loading="data.tableLoading"
        border
        size="small"
      >
        <el-table-column label="序号" type="index" width="55" />
        <el-table-column property="id" v-if="false" />
        <el-table-column
          property="name"
          label="对象标识名称"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-copy="scope.row.name">{{ scope.row.name || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          property="handle"
          label="对象标识"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-copy="scope.row.handle">{{ scope.row.handle || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          property="status"
          label="发布状态"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            {{ getStatus(scope.row.handleStatus) }}
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        background
        v-model:page="data.page"
        v-model:page-size="data.size"
        :page-sizes="[10, 20, 30, 40]"
        small
        layout="total, sizes, prev, pager, next, jumper"
        :total="data.totalCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineProps } from "vue";
import { ApiIdentifyOfShareChannel } from "@/api/tool/index";
import SearchLayout from "@/components/searchLayout/index.vue";

const dialogFormVisible = ref(true);
const props = defineProps<{
  item: Record<string, any>;
}>();

const searchForm = reactive({
  handle: "",
  name: "",
});

interface TableItem {
  id: string;
  appName: string;
  appId: number;
  handle: string;
  name: string;
}
interface Data {
  authGroupId: string | number;
  page: number;
  size: number;
  totalCount: number;
  handle: string;
  name: string;
  tableLoading: boolean;
  searchLoading: boolean;
}
const data = reactive<Data>({
  page: 0,
  size: 10,
  totalCount: 0,
  handle: "",
  name: "",
  authGroupId: "",
  // 列表loading
  tableLoading: false,
  searchLoading: false,
});

const tableData = ref<TableItem[]>([]);

const getData = async () => {
  data.tableLoading = true;
  const res = await ApiIdentifyOfShareChannel({
    ...searchForm,
    id: props.item.id,
    page: data.page,
    size: data.size,
  });
  tableData.value = res.content;
  data.totalCount = res.totalCount;
  data.searchLoading = false;
  data.tableLoading = false;
};

onMounted(() => {
  getData();
});

const handleSearch = () => {
  data.searchLoading = true;
  data.page = 0;
  getData();
};

const handleReset = () => {
  searchForm.name = "";
  searchForm.handle = "";
  handleSearch();
};

const handleSizeChange = (num: number) => {
  data.size = num;
  data.page = 0;
  getData();
};
const handleCurrentChange = (num: number) => {
  data.page = num - 1;
  getData();
};
const getStatus = (num: any) => {
  if (num === 1) {
    return "草稿";
  }
  if (num === 2) {
    return "发布";
  }
  return "-";
};
</script>

<style lang="scss" scoped>
.description-title {
  margin-bottom: 16px;
  font-weight: 500;

  .description-title-divider {
    width: 4px;
    height: 12px;
    background-color: #00a57c;

    &.el-divider {
      margin: 0 0;
    }
  }
}
</style>
