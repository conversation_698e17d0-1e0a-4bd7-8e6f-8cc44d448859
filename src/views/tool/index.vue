<template>
  <div>
    <search-layout>
      <template #left>
        <el-button type="primary" @click="handleAdd">新增</el-button>
      </template>
      <template #right>
        <el-form :inline="true" :model="searchForm" @submit.prevent>
          <el-form-item>
            <el-input
              v-model.trim="searchForm.dataChannelName"
              clearable
              @clear="handleSearch"
              placeholder="请输入"
            >
              <template #prefix>通道名称：</template></el-input
            >
          </el-form-item>
          <el-form-item>
            <el-input
              v-model.trim="searchForm.objectHandle"
              clearable
              @clear="handleSearch"
              placeholder="请输入"
            >
              <template #prefix>所属对象标识：</template></el-input
            >
          </el-form-item>
          <el-form-item>
            <el-select
              v-model="searchForm.sendStatus"
              placeholder="请选择"
              clearable
              @clear="handleSearch"
            >
              <template #prefix>下发状态：</template>
              <el-option
                v-for="item in DATA_CHANNEL_SEND_STATUS_LIST"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-date-picker
              v-model="searchForm.maintainTime"
              type="daterange"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled-date="disabledDate"
              clearable
              @change="handleTimeClear()"
            />
          </el-form-item>
          <el-form-item style="width: 60px; max-width: 60px">
            <el-button
              type="primary"
              :loading="data.searchLoading"
              @click="handleSearch"
            >
              搜索
            </el-button>
          </el-form-item>
        </el-form>
      </template>
    </search-layout>
    <el-table
      :data="data.tableData"
      v-loading="data.tableLoading"
      border
      size="small"
    >
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column property="name" label="通道名称">
        <template #default="scope">
          <ellipsisText :value="scope.row.dataChannelName">{{
            scope.row.dataChannelName || "-"
          }}</ellipsisText>
        </template>
      </el-table-column>
      <el-table-column property="handle" label="所属对象标识">
        <template #default="scope">
          <el-button
            type="primary"
            text
            v-if="scope.row.isShare === 1"
            @click="showShare(scope.row)"
          >
            已共享
          </el-button>
          <ellipsisText :value="scope.row.objectHandleName" v-else>{{
            scope.row.objectHandleName || "-"
          }}</ellipsisText>
        </template>
      </el-table-column>
      <el-table-column property="dataChannelId" label="通道ID">
        <template #default="scope">
          <ellipsisText :value="scope.row.dataChannelId">{{
            scope.row.dataChannelId || "-"
          }}</ellipsisText>
        </template>
      </el-table-column>
      <el-table-column label="实例数据类型">
        <template #default="scope">
          <div>{{ (DATA_TYPE_NAME_MAP as any)[`${scope.row.dataType}`] }}</div>
        </template>
      </el-table-column>
      <el-table-column label="数据库">
        <template #default="scope">
          <ellipsisText :value="scope.row.databaseName">{{
            scope.row.databaseName || "-"
          }}</ellipsisText>
        </template>
      </el-table-column>
      <el-table-column label="数据库IP">
        <template #default="scope">
          <ellipsisText :value="scope.row.databaseIp">{{
            scope.row.databaseIp || "-"
          }}</ellipsisText>
        </template>
      </el-table-column>
      <el-table-column label="下发状态">
        <template #default="scope">
          <ellipsisText
            :value="sendStatusName(scope.row.sendStatus)"
          ></ellipsisText>
        </template>
      </el-table-column>

      <el-table-column property="updatedTime" label="操作时间" />
      <el-table-column label="操作">
        <template #default="scope">
          <el-button
            type="primary"
            text
            @click="handleDownload(scope.$index, scope.row)"
            >下载</el-button
          >
          <el-button
            :disabled="scope.row.dataServiceType !== 1"
            type="primary"
            text
            @click="handleDeliveryChannel(scope.$index, scope.row)"
            >下发通道</el-button
          >
          <el-dropdown trigger="click" class="drop-btn" :hide-on-click="false">
            <el-button type="primary" text>
              <img src="@/assets/icons/svg/more.svg" />
            </el-button>
            <span></span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>
                  <el-button type="primary" text @click="handleView(scope.row)"
                    >详情</el-button
                  >
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button
                    type="primary"
                    text
                    @click="handleEdit(scope.$index, scope.row)"
                    >编辑</el-button
                  >
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-popconfirm
                    :width="200"
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    title="删除标识会导致标识解析失败，请谨慎删除，是否删除该标识？"
                    @confirm="handleDeleteConfirm(scope.row.id)"
                  >
                    <template #reference>
                      <el-button text type="primary">删除</el-button>
                    </template>
                  </el-popconfirm>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button
                    type="primary"
                    text
                    @click="toggleShare(scope.row)"
                  >
                    {{
                      scope.row.isShare === 1 ? "取消共享" : "共享"
                    }}</el-button
                  >
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      v-model:currentPage="data.page"
      v-model:page-size="data.size"
      :page-sizes="[10, 20, 30, 40]"
      small
      layout="total, sizes, prev, pager, next, jumper"
      :total="data.totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
  <AddOrEdit
    v-if="data.dialogVisible"
    :id="selectDataId"
    @close="data.dialogVisible = false"
    @success="updateHandleSuccess"
  ></AddOrEdit>
  <DetailDialog
    v-if="detailVisible"
    @close-dialog="detailVisible = false"
    :id="selectDataId"
  />
  <ShareDetail
    v-if="shareDrawerVisible"
    @close-dialog="shareDrawerVisible = false"
    :item="selectData"
  />
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
import { saveAs } from "file-saver";
import {
  ApiDataChannelPage,
  ApiDownload,
  ApiDeliveryChannel,
  ApiDeleteHandle,
  ApiShareChannel,
  ApiCancelShareChannel,
} from "@/api/tool/index";
import SearchLayout from "@/components/searchLayout/index.vue";
import ellipsisText from "@/components/ellipsisText/index.vue";
import ShareDetail from "./detail/shareDrawer.vue";
import DetailDialog from "./detail/detailDialog.vue";
import AddOrEdit from "./addOrEdit/index.vue";
import {
  DATA_CHANNEL_SEND_STATUS_LIST,
  DATA_TYPE_NAME_MAP,
  DATA_CHANNEL_SEND_STATUS_MAP,
} from "@/utils/dataPlatform";

const shareDrawerVisible = ref(false);

const disabledDate = (time: Date) => {
  return time.getTime() > new Date().getTime();
};

const searchForm = reactive({
  dataChannelName: "",
  objectHandle: "",
  maintainTime: [] as any,
  sendStatus: "",
});

const selectDataId = ref();
const selectData = ref();
const detailVisible = ref(false);

const data = reactive<Record<string, any>>({
  tableData: [],
  appList: [],
  page: 1,
  size: 10,
  totalCount: 1,
  dialogTitle: "",
  dialogVisible: false,
  dialogDisabled: false,
  deleteLoading: false,
  tableLoading: false,
  searchLoading: false,
  confirmLoading: false,
});

const sendStatusName = (val: any) => {
  if (!val) return "-";
  return (DATA_CHANNEL_SEND_STATUS_MAP as any)[val];
};

function getTableData() {
  data.tableLoading = true;
  data.tableData = [];
  const params = {
    dataChannelName: searchForm.dataChannelName || undefined,
    objectHandle: searchForm.objectHandle || undefined,
    sendStatus: searchForm.sendStatus || undefined,
    startTime: "" || undefined,
    endTime: "" || undefined,
    page: data.page - 1,
    size: data.size,
  };
  if (searchForm.maintainTime?.length) {
    const [startTime, endTime] = searchForm.maintainTime;
    params.startTime = startTime;
    params.endTime = endTime.replace("00:00:00", "23:59:59");
  }

  ApiDataChannelPage(params)
    .then((response: any) => {
      const result = response;
      data.tableData = result?.content || [];
      data.page = result?.pageNumber || 1;
      data.totalCount = result?.totalCount || 0;
    })
    .finally(() => {
      data.tableLoading = false;
      data.searchLoading = false;
    });
}

const updateHandleSuccess = () => {
  data.page = 1;
  data.dialogVisible = false;
  getTableData();
};

// 详情按钮
function handleView(item: any) {
  selectDataId.value = item.id;
  detailVisible.value = true;
}

// 清空操作时间，查询数据
function handleTimeClear() {
  if (!searchForm.maintainTime || searchForm.maintainTime.length === 0) {
    handleSearch();
  }
}

function handleSearch() {
  data.searchLoading = true;
  data.page = 1;
  getTableData();
}

function handleSizeChange(num: number) {
  data.size = num;
  data.page = 1;
  getTableData();
}

function handleCurrentChange(num: number) {
  data.page = num;
  getTableData();
}

function handleAdd() {
  data.dialogVisible = true;
  data.dialogTitle = "新增标识";
  selectDataId.value = "";
  data.dialogDisabled = false;
}

function handleDeleteConfirm(handle: any) {
  data.deleteLoading = true;
  ApiDeleteHandle(handle)
    .then(() => {
      ElMessage.success("删除成功");
      data.page = 1;
      getTableData();
    })
    .finally(() => {
      data.deleteLoading = false;
    });
}

// 编辑按钮
function handleEdit(index: any, item: any) {
  selectDataId.value = item.id;
  data.dialogVisible = true;
  data.dialogTitle = "编辑标识";
  data.dialogDisabled = false;
}

// 下载
function handleDownload(index: any, item: any) {
  ApiDownload(item.id).then((res: any) => {
    const fileName = "数据通道配置.json";
    const blob = new Blob([res]);
    saveAs(blob, fileName);
  });
}
// 下发通道
function handleDeliveryChannel(index: any, item: any) {
  ApiDeliveryChannel(item.id).then((res: any) => {
    ElMessage.success("下发成功");
    getTableData();
  });
}

const toggleShare = (item: any) => {
  console.log("改变共享", item);
  if (item.isShare === 1) {
    ApiCancelShareChannel(item.id).then((res: any) => {
      ElMessage.success("取消共享成功");
      getTableData();
    });
  } else {
    ApiShareChannel(item.id).then((res: any) => {
      ElMessage.success("共享成功");
      getTableData();
    });
  }
};

const showShare = (item: any) => {
  console.log("查看共享", item);
  selectData.value = item;
  shareDrawerVisible.value = true;
};

onMounted(() => {
  getTableData();
});
</script>
<style lang="scss" scoped>
.drop-btn {
  margin-left: 16px;
}
</style>
