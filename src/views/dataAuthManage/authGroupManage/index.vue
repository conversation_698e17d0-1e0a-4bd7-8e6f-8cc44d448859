<template>
  <div class="content">
    <div class="left">
      <div class="addLimits">
        <span class="add-title">权限组</span>
        <img
          src="@/assets/images/empower/addicon.png"
          class="img"
          @click="addGroup('新增权限组')"
        />
      </div>
      <div class="search-input">
        <el-input placeholder="请输入" v-model="searchInput" class="input">
          <template #suffix>
            <el-icon
              class="el-input__icon"
              @click="updateGroup(searchInput, 'search')"
            >
              <Search />
            </el-icon>
          </template>
        </el-input>
      </div>
      <!-- 展示组列表 -->
      <div class="group-list" v-if="groupVisiable">
        <div
          class="group-item"
          :class="index == backgroundIndex ? 'bg' : ''"
          v-for="(item, index) in groupData"
          :key="index"
          @click="handleCurrentData(item.id, index)"
        >
          <ellipsisText
            :value="item.authGroupName"
            :copy="false"
            class="group-item-ell"
          ></ellipsisText>

          <el-dropdown
            trigger="click"
            @visible-change="(val: any) => dropdownChange(val, index)"
            placement="bottom-end"
          >
            <img
              src="@/assets/icons/svg/more.svg"
              class="group-img"
              v-if="activeIndex != index"
            />
            <img
              src="@/assets/images/empower/icon-more-vertical1.png"
              style="width: 17.143px"
              v-if="activeIndex === index"
            />
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="addGroup('修改权限组名称', item)">
                  修改名称
                </el-dropdown-item>
                <el-dropdown-item @click="delGroup(item)">
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <div class="group-list" v-else>
        <!-- 暂无数据 -->
        <div class="handle-query-result">
          <img src="@/assets/images/empower/empty.png" />
          <div class="handle-query-result-text" v-if="!emptyVisible">
            暂无权限组数据，<span
              style="color: #1664ff"
              @click="addGroup('新增权限组')"
              >立即新增</span
            >
          </div>
          <div class="handle-query-result-text" v-else>暂无数据</div>
        </div>
      </div>
    </div>

    <div class="right">
      <!-- 暂无数据 -->
      <div class="handle-query-result height" v-if="!tableVisiable">
        <img src="@/assets/blank.svg" />
        <div class="handle-query-result-text">暂无数据，请添加权限组</div>
      </div>
      <SearchLayout v-if="tableVisiable">
        <template #left>
          <el-button type="primary" @click="accreditDate(true)"
            >添加授权标识</el-button
          >
        </template>
        <template #right>
          <el-form :inline="true" :model="data" @submit.prevent ref="formRef">
            <el-form-item style="width: 240px; max-width: 240px"
              ><el-input
                v-model.trim="searchForm.name"
                clearable
                placeholder="请输入"
              >
                <template #prefix>对象标识名称：</template></el-input
              ></el-form-item
            >
            <el-form-item style="width: 240px; max-width: 240px"
              ><el-input
                v-model.trim="searchForm.handle"
                clearable
                placeholder="请输入"
              >
                <template #prefix>对象标识：</template></el-input
              ></el-form-item
            >
            <el-form-item style="width: 60px; max-width: 60px"
              ><el-button
                type="primary"
                :loading="data.searchLoading"
                @click="handleSearch"
                >搜索</el-button
              ></el-form-item
            >
            <el-form-item style="width: 60px; max-width: 60px"
              ><el-button
                type="info"
                :loading="data.searchLoading"
                @click="handleReset"
                color="#EEF2F1"
                >重置</el-button
              ></el-form-item
            >
          </el-form>
        </template>
      </SearchLayout>
      <!-- table内容 -->
      <div class="page-search-body" v-if="tableVisiable">
        <el-table
          :data="tableData"
          v-loading="data.tableLoading"
          border
          size="small"
        >
          <el-table-column label="序号" type="index" width="55" />
          <el-table-column property="id" v-if="false" />
          <el-table-column
            property="name"
            label="对象标识名称"
            :show-overflow-tooltip="true"
          >
            <template #default="scope">
              <span v-copy="scope.row.name">{{ scope.row.name || "-" }}</span>
            </template>
          </el-table-column>
          <el-table-column
            property="handle"
            label="对象标识"
            :show-overflow-tooltip="true"
          >
            <template #default="scope">
              <span v-copy="scope.row.handle">{{
                scope.row.handle || "-"
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            property="appName"
            label="所属应用"
            :show-overflow-tooltip="true"
          >
            <template #default="scope">
              <span v-copy="scope.row.appName">{{
                scope.row.appName || "-"
              }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button
                type="primary"
                text
                @click="accreditDate(false, scope.row)"
                >授权</el-button
              >
              <el-popconfirm
                :width="200"
                confirm-button-text="确定"
                cancel-button-text="取消"
                title="确认删除标识授权吗？"
                @confirm="handleDeleteConfirm(scope.row.id)"
              >
                <template #reference>
                  <el-button text type="primary">删除</el-button>
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          background
          v-model:page="data.page"
          v-model:page-size="data.size"
          :page-sizes="[10, 20, 30, 40]"
          small
          layout="total, sizes, prev, pager, next, jumper"
          :total="data.totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <!-- 添加 编辑组 -->
    <groupOperation
      :dialogFormVisible="dialogFormVisible"
      :status="addStatus"
      :editGroupData="editGroupData"
      @update-group="updateGroup"
      @close-dialog="handleClose"
    >
    </groupOperation>
    <!-- 添加授权标识 -->
    <addAuthorization
      v-if="dialogAddAuthorization"
      :accredit="accredit"
      @close-authorization="dialogAddAuthorization = false"
      @update-group="groupSearch(data)"
    >
    </addAuthorization>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, provide } from "vue";
import { Search, InfoFilled } from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import groupOperation from "./components/addGroup.vue";
import addAuthorization from "./components/addAuthorization.vue";
import SearchLayout from "@/components/searchLayout/index.vue";
import {
  ApiAuthGroupList,
  ApiGroupPage,
  ApiDeleteAuthGroup,
  ApiDelHandleList,
} from "@/api/authGroupManage/index";
import ellipsisText from "@/components/ellipsisText/index.vue";

const searchInput = ref("");
const emptyVisible = ref(false);
const activeIndex = ref(-1);
const backgroundIndex = ref(0);
// const mouseVisible = ref(false);
const editGroupData = reactive<any>({
  id: "",
  authGroupName: "",
});
const formRef = ref();
const dialogFormVisible = ref(false);
const dialogAddAuthorization = ref(false);
const searchForm = reactive({
  handle: "",
  name: "",
});
const addStatus = ref("");
const accredit = ref(true);
const groupVisiable = ref(true);
const tableVisiable = ref(true);
const groupData = ref<any>([]);

interface TableItem {
  id: string;
  appName: string;
  appId: number;
  handle: string;
  name: string;
}
interface Data {
  authGroupId: string | number;
  page: number;
  size: number;
  totalCount: number;
  handle: string;
  name: string;
  tableLoading: boolean;
  searchLoading: boolean;
}
const data = reactive<Data>({
  page: 0,
  size: 10,
  totalCount: 1,
  handle: "",
  name: "",
  authGroupId: "",
  // 列表loading
  tableLoading: false,
  searchLoading: false,
});
const groupVal = ref();
const handleId = ref();
provide("groupId", groupVal);
provide("handleId", handleId);

const tableData = ref<TableItem[]>([]);
onMounted(() => {
  updateGroup("");
});
// 查询权限组
const updateGroup = (name: string, data?: string) => {
  groupData.value = [];
  ApiAuthGroupList(name).then((res: any) => {
    if (Array.isArray(res)) {
      groupData.value = res;
      tableVisiable.value = true;
      groupVisiable.value = true;

      handleCurrentData(res[0]?.id);
    } else {
      groupVisiable.value = false;
      if (data) {
        emptyVisible.value = true;
        tableVisiable.value = true;
      } else {
        emptyVisible.value = false;

        tableVisiable.value = false;
      }
    }
  });
};
// 右侧列表
const groupSearch = (params: any) => {
  data.tableLoading = true;

  ApiGroupPage(params)
    .then((res: any) => {
      tableData.value = res.content;
      data.tableLoading = false;
      data.searchLoading = false;
      data.totalCount = res.totalCount || 0;
    })
    .catch((err: any) => {
      data.searchLoading = false;
      data.tableLoading = false;
    });
};
const handleCurrentData = (val: number, index?: number) => {
  data.authGroupId = val;
  groupVal.value = val;
  // console.log(index, "index");

  if (index !== undefined) {
    backgroundIndex.value = index;
    // console.log(backgroundIndex.value, "backgroundIndex.value");
  }

  // mouseVisible.value = true;
  groupSearch(data);
};

const dropdownChange = (val: boolean, index: number) => {
  // console.log(index, val);
  activeIndex.value = index;
  val ? "" : (activeIndex.value = -1);
};
// const backgroundChange = (data: boolean) => {
//   mouseVisible.value = !data;
// };
// 添加权限组弹窗
const addGroup = (data: string, item?: object) => {
  editGroupData.value = item;
  dialogFormVisible.value = true;
  addStatus.value = data;
};
const accreditDate = (data: boolean, row?: any) => {
  accredit.value = data;
  dialogAddAuthorization.value = true;
  handleId.value = row || "";
};
// 删除权限组
const delGroup = (item: any) => {
  !tableData.value.length
    ? ElMessageBox.confirm("确认删除此权限组吗?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          handleDelGroup(item.id);
        })
        .catch(() => {
          console.log("");
        })
    : handleDelGroup(item.id);
};
const handleDelGroup = (id: number) => {
  ApiDeleteAuthGroup({ id }).then(() => {
    ElMessage({
      message: "删除成功",
      type: "success",
    });
    updateGroup("");
  });
};
const handleSearch = () => {
  // const params = {
  //   ...data,
  data.name = searchForm.name;
  data.handle = searchForm.handle;
  data.page = 0;
  // };

  data.searchLoading = true;
  groupSearch(data);
};
const handleReset = () => {
  searchForm.name = "";
  searchForm.handle = "";
  data.name = "";
  data.handle = "";
  data.searchLoading = true;
  groupSearch(data);
};
const handleClose = () => {
  dialogFormVisible.value = false;
};

const handleSizeChange = (num: number) => {
  data.size = num;
  data.page = 0;
  groupSearch(data);
};
const handleCurrentChange = (num: number) => {
  data.page = num - 1;
  groupSearch(data);
};
const handleDeleteConfirm = (num: number) => {
  ApiDelHandleList({ id: num }).then((res: any) => {
    ElMessage({
      message: "删除成功",
      type: "success",
    });
    groupSearch(data);
  });
};
</script>

<style lang="scss" scoped>
.content {
  margin: -24px;
  display: flex;
  align-items: stretch;
  flex-direction: row;
  flex: 1;
}

.left {
  display: flex;
  width: 200px;
  align-items: stretch;
  padding: 21px 0px;
  flex-direction: column;
  border-right: 1px solid #dfe4e3;

  .addLimits {
    width: 100%;
    height: 38px;
    display: flex;
    padding: 8px 24px;
    justify-content: space-between;
    align-items: center;

    .add-title {
      color: var(---F7-272E2C-, #272e2c);
      font-family: "PingFang SC";
      font-size: 14px;
      font-weight: 500;
    }
  }

  .img {
    width: 18px;
    height: 18px;

    &:hover {
      cursor: pointer;
    }
  }

  .bg {
    background: rgba(232, 241, 239, 0.6);
  }
}

.search-input {
  width: 100%;
  height: 52px;
  padding: 12px 16px;

  .input {
    height: 28px;
    border-radius: 2px;
    background: var(--f-2-eef-2-f-1, #eef2f1);
  }

  :deep(.el-input__wrapper) {
    .el-input__icon {
      cursor: pointer;
      color: #535f5c !important;
    }

    .el-input__inner {
      line-height: 28px;
      color: #272e2c !important;

      &::placeholder {
        font-size: 12px;
        color: #86909c !important;
      }
    }
  }
}

.group-list {
  width: 100%;
  // height: 100%;
  height: calc(100vh - 256px);
  overflow-y: auto;
  color: #000;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  cursor: pointer;

  .group-item {
    width: 100%;
    height: 36px;
    display: flex;
    padding: 8px 24px;
    justify-content: space-between;
    align-items: center;
    align-self: stretch;

    &:hover {
      background: var(--s-1-e-8-f-1-ef, rgba(232, 241, 239, 0.6));
    }

    .group-item-ell {
      // width: 100px;
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .group-img {
    width: 16px;
    height: 16px;
    margin-left: 12px;
  }
}

:deep(.el-dropdown-menu__item) {
  font-size: 12px !important;
  font-family: "PingFang SC";
  font-style: normal;
  font-weight: 400;
}

:deep(.el-dropdown-menu__item:hover) {
  background: var(--s-1-e-8-f-1-ef, rgba(232, 241, 239, 0.6));
  color: #1d2129;
}

// :deep(.el-popper__arrow) {
//   display: none !important;
// }

.right {
  width: calc(100% - 200px);
  padding: 24px;

  :deep(.el-button > span) {
    font-size: 12px;
  }
}

// 暂无数据
.handle-query-result {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  // height: 100%;

  .handle-query-result-text {
    color: var(---T2-7B9790-, #7b9790);
    text-align: center;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }

  &.height {
    height: 100%;
  }
}
</style>
