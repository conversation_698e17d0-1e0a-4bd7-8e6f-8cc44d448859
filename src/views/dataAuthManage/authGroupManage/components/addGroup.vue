<template>
  <el-dialog
    append-to-body
    class="dialog-container"
    align-center
    v-model="dialogFormVisible"
    :title="status"
    width="630px"
    @close="$emit('close-dialog')"
    destroy-on-close
  >
    <el-form ref="formRef" :model="formData" :rules="rules">
      <el-form-item label="权限组名称" prop="authGroupName">
        <el-input v-model.trim="formData.authGroupName" placeholder="请输入" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="$emit('close-dialog')">取消</el-button>
        <el-button
          type="primary"
          @click="validForm(formRef)"
          v-loading="btnLoading"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, onBeforeUpdate } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { ApiAddAuthGroup, ApiEditAuthGroup } from "@/api/authGroupManage/index";

const emit = defineEmits(["update-group", "close-dialog"]);
const props = defineProps({
  dialogFormVisible: {
    type: Boolean,
    default: true,
  },
  status: {
    type: String,
    default: "",
  },
  editGroupData: {
    type: Object,
    default: () => {
      return {};
    },
  },
});
const btnLoading = ref(false);

const formRef = ref<FormInstance>();
const formData = ref({
  id: null,
  authGroupName: "",
});

const rules = reactive({
  authGroupName: [
    { required: true, message: "请输入权限组名称", trigger: "blur" },
    { max: 30, message: "最大长度不超过30字符", trigger: "blur" },
  ],
});

const validForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid) => {
    if (valid) {
      addOrEditService();
    }
  });
};

const addOrEditService = () => {
  const api = props.editGroupData.value?.id
    ? ApiEditAuthGroup
    : ApiAddAuthGroup;
  btnLoading.value = true;
  api({ ...formData.value })
    .then((res) => {
      btnLoading.value = false;
      emit("update-group");
      ElMessage({
        message: `${props.editGroupData.value?.id ? "修改" : "新增"}成功!`,
        type: "success",
      });
      emit("close-dialog");
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

onBeforeUpdate(() => {
  // console.log(props.editGroupData.value?.id, 'update');
  if (props.editGroupData?.value?.id) {
    formData.value = { ...props.editGroupData.value };
  } else {
    formData.value.authGroupName = "";
  }
});
</script>
