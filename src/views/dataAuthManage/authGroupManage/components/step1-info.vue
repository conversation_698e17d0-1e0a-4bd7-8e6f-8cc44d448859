<!-- 第一步标识信息 -->
<template>
  <div class="box">
    <SearchLayout>
      <template #right>
        <el-form :inline="true" :model="searchForm" @submit.prevent>
          <el-form-item style="width: 240px; max-width: 240px"
            ><el-input
              v-model.trim="searchForm.name"
              clearable
              placeholder="请输入"
            >
              <template #prefix>对象标识名称：</template></el-input
            ></el-form-item
          >
          <el-form-item style="width: 240px; max-width: 240px"
            ><el-input
              v-model.trim="searchForm.handle"
              clearable
              placeholder="请输入"
            >
              <template #prefix>对象标识：</template></el-input
            ></el-form-item
          >
          <el-form-item style="width: 60px; max-width: 60px"
            ><el-button
              type="primary"
              :loading="data.searchLoading"
              @click="search"
              >搜索</el-button
            ></el-form-item
          >
          <el-form-item style="width: 60px; max-width: 60px"
            ><el-button
              type="info"
              :loading="data.searchLoading"
              @click="resetSearch"
              color="#EEF2F1"
              >重置</el-button
            ></el-form-item
          >
        </el-form>
      </template>
    </SearchLayout>
    <div class="page-search-body">
      <el-table
        :data="tableItem"
        v-loading="data.tableLoading"
        border
        size="small"
        row-key="id"
        default-expand-all
        @row-click="handleCurrentChange"
      >
        <el-table-column width="55" property="radio">
          <template #default="scope">
            <el-radio :label="scope.row.id" v-model="radioValue">{{}}</el-radio>
          </template>
        </el-table-column>
        <el-table-column label="序号" type="index" width="55" />

        <el-table-column
          property="name"
          label="对象标识名称"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-copy="scope.row.name">{{ scope.row.name || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          property="handle"
          label="对象标识"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-copy="scope.row.handle">{{ scope.row.handle || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          property="appName"
          label="所属应用"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-copy="scope.row.appName">{{
              scope.row.appName || "-"
            }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        v-model:page="data.page"
        v-model:page-size="data.size"
        :page-sizes="[10, 20, 30, 40]"
        small
        layout="total, sizes, prev, pager, next, jumper"
        :total="data.totalCount"
        :current-page="data.page + 1"
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from "element-plus";
import {
  defineProps,
  reactive,
  onMounted,
  ref,
  defineExpose,
  toRefs,
  inject,
} from "vue";
import { useStore } from "vuex";
import { ApiHandleList } from "@/api/authGroupManage/index";
import SearchLayout from "@/components/searchLayout/index.vue";

const store = useStore();
const { appType } = toRefs(store.getters);
const groupId: any = inject("groupId");
const radioValue = ref("");
const emit = defineEmits(["change", "updateData"]);

interface TableItem {
  id: string;
  appName: string;
  appId: number;
  handle: string;
  name: string;
}
const tableItem = ref<TableItem[]>([]);
interface Data {
  authGroupId: string | number;
  page: number;
  size: number;
  totalCount: number;
  handle: string;
  name: string;
  tableLoading: boolean;
  searchLoading: boolean;
}
const data = reactive<Data>({
  page: 0,
  size: 10,
  totalCount: 1,
  handle: "",
  name: "",
  authGroupId: "",
  // 列表loading
  tableLoading: false,
  searchLoading: false,
});
const handleData = ref("");

const props = defineProps({
  handleDetail: {
    type: Object,
    default: null,
  },
  step1Page: {
    type: Number,
    default: 1,
  },
});

const searchForm = reactive({
  name: "",
  handle: "",
});

// 搜索框查询
const search = () => {
  data.name = searchForm.name;
  data.handle = searchForm.handle;
  data.page = 0;
  radioValue.value = "";
  data.searchLoading = true;
  handleSearch(data);
};

// 搜索框删除
const resetSearch = () => {
  searchForm.name = "";
  searchForm.handle = "";
  data.name = "";
  data.handle = "";
  radioValue.value = "";
  // data.totalCount = 1;
  handleSearch(data);
};

// 拿到每一行的信息
const handleCurrentChange = (val: any, data: any) => {
  radioValue.value = val?.id;
  handleData.value = val;
};

const handleSearch = (params: any) => {
  ApiHandleList(params)
    .then((res) => {
      tableItem.value = res.content;
      data.totalCount = res.totalCount || 0;
      emit("updateData", data.totalCount);
      data.searchLoading = false;
      data.tableLoading = false;
    })
    .catch((err: any) => {
      data.searchLoading = false;
      data.tableLoading = false;
    });
};
const submitForm = () => {
  // console.log(radioValue.value, "radioValue");

  if (!radioValue.value && Object.keys(tableItem.value).length !== 0) {
    ElMessage({
      message: "请勾选一条标识数据",
      type: "error",
    });
    return;
  }

  emit("change", handleData.value, data.page);
};

const handleSizeChange = (num: number) => {
  data.size = num;
  data.page = 0;
  handleSearch(data);
};
const handlePageChange = (num: number) => {
  radioValue.value = "";
  data.page = num - 1;

  handleSearch(data);
};

onMounted(() => {
  data.authGroupId = groupId.value;
  data.page = props.step1Page;
  data.tableLoading = true;
  handleSearch(data);
  // console.log(props.handleDetail, "props.handleDetail");
  if (props.handleDetail?.id) {
    handleData.value = props.handleDetail;
    radioValue.value = props.handleDetail?.id;
  } else {
    radioValue.value = "";
    handleData.value = "";
  }
});

defineExpose({
  submitForm,
});
</script>
<style scoped lang="scss">
.box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
  padding: 24px 20px;
  width: 100%;

  .page-search-body {
    width: 100%;
  }

  .handle-tid-wrap {
    display: flex;
    align-items: center;
    justify-content: start;
    width: 100%;

    .handle-tid-line {
      margin: 0 0px;
    }

    .handle-tid-suffix {
      flex: 1;
      min-width: 0;
    }
  }
}
</style>
