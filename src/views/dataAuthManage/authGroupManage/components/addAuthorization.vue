<template>
  <el-dialog
    append-to-body
    class="content-box"
    align-center
    v-model="dialogAddAuthorization"
    :title="accredit ? '添加授权标识' : '授权标识：选择属性'"
    width="1068px"
    @close="$emit('close-authorization')"
    destroy-on-close
  >
    <div class="mid-box" v-loading="midBoxLoading">
      <div class="steps-box" v-if="accredit">
        <el-steps :active="step" finish-status="success" simple>
          <el-step title="选择标识">
            <template v-slot:icon>
              <el-icon v-if="step > 0" size="16">
                <SuccessFilled color="#00A57C" />
              </el-icon>
              <el-icon v-if="step == 0" size="16">
                <img src="@/assets/images/empower/Loading.png" alt="" />
                <!-- <Loading color="#00A57C" /> -->
              </el-icon>
            </template>
          </el-step>
          <el-step title="选择属性">
            <template v-slot:icon>
              <el-icon v-if="step > 1" size="16">
                <SuccessFilled color="#00A57C" />
              </el-icon>
              <el-icon v-if="step == 1" size="16">
                <img src="@/assets/images/empower/Loading.png" alt="" />
              </el-icon>
              <el-icon v-if="step < 1" size="16">
                <RemoveFilled color="#C7D4D1" />
              </el-icon>
            </template>
          </el-step>
        </el-steps>
      </div>
      <div
        v-if="showMidBox"
        style="width: 100%"
        :style="[
          { 'margin-top': accredit ? '86px' : '0' },
          { 'min-height': accredit ? '232px' : '318px' },
        ]"
      >
        <HandleInfo
          v-if="step === 0"
          ref="step1"
          :step1Page="step1Page"
          :handle-detail="handleDetail"
          @change="handleInfoChange"
          @updateData="updateData"
        />
        <step1Info
          v-if="step === 1"
          ref="step2"
          :handle-detail="handleDetail"
          :accredit="accredit"
          @change="changeClose"
          @updateData="updateData"
        >
        </step1Info>
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer" v-if="accredit">
        <el-button @click="lastStep">{{
          step > 0 ? "上一步" : "取消"
        }}</el-button>
        <el-button
          type="primary"
          @click="nextStep"
          :loading="confirmLoading"
          :disabled="!step1Data"
        >
          {{ step >= 1 ? "完成" : "下一步" }}
        </el-button>
      </span>
      <span class="dialog-footer" v-else>
        <el-button @click="$emit('close-authorization')"> 取消 </el-button>
        <el-button
          type="primary"
          @click="nextStep"
          :loading="confirmLoading"
          :disabled="!step1Data"
        >
          完成
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {
  defineProps,
  onBeforeUpdate,
  onMounted,
  ref,
  toRefs,
  reactive,
  provide,
  inject,
} from "vue";
import { SuccessFilled, RemoveFilled, Loading } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import HandleInfo from "./step1-info.vue";
import step1Info from "./step2-info.vue";

const emit = defineEmits(["close-authorization", "update-group"]);

const showMidBox = ref(false);
const midBoxLoading = ref(false);
const dialogAddAuthorization = ref(true);
const isButtonDisabled = ref(false);
const props = defineProps({
  // dialogAddAuthorization: {
  //     type: Boolean,
  //     default: true,
  // },
  accredit: {
    type: Boolean,
    default: true,
  },
});
const step1Page = ref(0);
const currentHandleId = ref();
const step = ref(0);
const step1 = ref();
const step2 = ref();
const confirmLoading = ref(false);
const handleDetail = ref();
const handleId: any = inject("handleId");
const step1Data = ref(0);
provide("handleId", handleDetail);

onMounted(() => {
  handleDetail.value = handleId.value;
  if (!props.accredit) {
    step.value = 1;
  } else {
    step.value = 0;
  }
  showMidBox.value = true;
});
// 查询标识列表

const lastStep = async () => {
  if (step.value === 1) {
    step.value--;
  } else {
    emit("close-authorization");
    step.value = 0;
  }
};

const handleInfoChange = (form: any, page: number) => {
  form ? (step.value = 1) : (step.value = 0);
  handleDetail.value = form;
  step1Page.value = page;
};
const updateData = (data: number) => {
  step1Data.value = data;
};
// const step1Method = async () => {
//   const res = await ;
//   return res ? 1 : step.value;
// };

const changeClose = () => {
  emit("close-authorization");
  emit("update-group");
};

const nextStep = async () => {
  switch (step.value) {
    case 0:
      step1.value.submitForm();
      break;
    case 1:
      step2.value.submitForm();
      break;
    default:
      step.value = 0;
      break;
  }
};
</script>
<style scoped lang="scss">
.content-box {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-end;
  padding: 24px 20px;

  .mid-box {
    display: flex;
    position: relative;
    flex-direction: column;
    align-items: center;
    margin: -30px -20px;

    .steps-box {
      position: absolute;
      left: 0px;
      right: 0px;
      height: 86px;
      background-color: #f5f6f6;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 86px;

      :deep(.el-steps) {
        position: relative;
        width: 45%;

        // background-color: #fff;
        .el-step__main {
          // width: 200px;
          .el-step__title {
            font-family: PingFang SC;
            font-size: 14px;
            font-style: normal;
            font-weight: 500;
            line-height: 22px;
            /* 157.143% */
            color: #535f5c;
          }

          .el-step__arrow::before {
            transform: rotate(0deg) translateY(0px);
            transform-origin: 0 0;
            content: "";
            display: inline-block;
            position: absolute;
            height: 1px;
            width: 56%;
            background: #c1c9c7;
          }

          .el-step__arrow::after {
            transform: rotate(0deg) translateY(0px);
            transform-origin: 0 0;
            content: "";
            display: inline-block;
            position: absolute;
            height: 0px;
            width: 0px;
            background: var(--el-text-color-placeholder);
          }
        }
      }
    }
  }
}
</style>
