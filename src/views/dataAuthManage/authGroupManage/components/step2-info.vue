<!-- 第一步标识信息 -->
<template>
  <div class="box">
    <div class="page-search-body">
      <el-table
        :data="data.tableData"
        v-loading="data.tableLoading"
        border
        size="small"
        :row-key="(row) => row.id"
        max-height="450"
        ref="multipleTableRef"
        default-expand-all
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="序号" type="index" width="55" />

        <el-table-column property="id" v-if="false" />
        <el-table-column
          property="description"
          label="中文名称"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-copy="scope.row.description">{{
              scope.row.description || "-"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          property="field"
          label="英文名称"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-copy="scope.row.field">{{ scope.row.field || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          property="fieldType"
          label="属性类型"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-copy="scope.row.fieldType">{{
              findValue(scope.row.fieldType)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          property="fieldSourceType"
          label="属性来源"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-copy="scope.row.fieldSourceType">{{
              scope.row.fieldSourceType === 0 ? "基础属性" : "扩展属性"
            }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ElMessage } from "element-plus";
import {
  defineProps,
  reactive,
  onMounted,
  ref,
  defineExpose,
  toRefs,
  inject,
  nextTick,
} from "vue";
import { useStore } from "vuex";
import {
  ApiItemList,
  ApiEditAuth,
  ApiAddAuth,
} from "@/api/authGroupManage/index";

const store = useStore();
const { appType } = toRefs(store.getters);
const multipleTableRef = ref<any>();
const emit = defineEmits(["change", "updateData"]);

const handleId: any = inject("handleId");

const groupId: any = inject("groupId");
const groupList = ref([]);
const sourceList = reactive<any[]>([
  {
    key: 1,
    value: "固定值",
  },
  {
    key: 2,
    value: "标识解析数据源",
  },
  {
    key: 3,
    value: "标识值",
  },
  {
    key: 4,
    value: "标识-属性",
  },
]);
interface tableData {
  id: string;
  description: string;
  field: string;
  fieldType: number;
  fieldSourceType: number;
  isChecked: boolean;
}
interface Data {
  tableData: tableData[];
  tableLoading: boolean;
}
const data = reactive<Data>({
  tableData: [],

  tableLoading: false,
});
const props = defineProps({
  handleDetail: {
    type: Object,
    default: null,
  },
  accredit: {
    type: Boolean,
    default: true,
  },
});

const toggleSelection = (rows: any) => {
  // console.log(multipleTableRef, "multipleTableRef");

  if (rows) {
    rows.forEach((row: any) => {
      if (row.isChecked) {
        // console.log(row, "row");
        multipleTableRef.value.toggleRowSelection(row, undefined);
      }
    });
  }
};

const handleSelectionChange = (val: any) => {
  groupList.value = val.map((item) => {
    return { id: item.id, field: item.field };
  });
  // console.log("handleSelectionChange", groupList.value);
};

const submitForm = () => {
  // console.log(groupList.value, "groupList");
  if (!groupList.value.length) {
    ElMessage({
      message: "至少勾选一条属性数据",
      type: "error",
    });
    return;
  }
  // console.log(handleId.value, " handleId.value");

  const params = {
    // id: props.accredit?'':handleId.value.id,
    appId: handleId.value.appId,
    authGroupId: groupId.value,
    handleId: props.accredit ? handleId.value.id : handleId.value.handleId,
    items: groupList.value,
  };
  const api = props.accredit ? ApiAddAuth : ApiEditAuth;

  api(params).then((res: any) => {
    ElMessage({
      message: `${props.accredit ? "新增" : "授权"}成功!`,
      type: "success",
    });
    emit("change");
  });
};

const findValue = (num: number) => {
  for (const item of sourceList) {
    if (item.key === num) {
      return item.value;
    }
  }
};
onMounted(async () => {
  const params = {
    handleId: props.accredit ? handleId.value.id : handleId.value.handleId,
    authGroupId: groupId.value,
  };
  data.tableLoading = true;
  ApiItemList(params)
    .then((res: any) => {
      data.tableLoading = false;
      if (Array.isArray(res)) {
        data.tableData = res || [];

        emit("updateData", !!data.tableData.length);
        nextTick(() => {
          toggleSelection(data.tableData);
        });
      }
    })
    .catch((err: any) => {
      data.tableLoading = false;
    });
});

defineExpose({
  submitForm,
});
</script>
<style scoped lang="scss">
.box {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 24px 20px;
  width: 100%;

  .page-search-body {
    width: 100%;
  }

  .handle-tid-wrap {
    display: flex;
    align-items: center;
    justify-content: start;
    width: 100%;

    .handle-tid-line {
      margin: 0 0px;
    }

    .handle-tid-suffix {
      flex: 1;
      min-width: 0;
    }
  }
}
</style>
