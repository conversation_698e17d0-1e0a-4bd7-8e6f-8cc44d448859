<template>
  <div>
    <el-drawer
      v-model="dialogFormVisible"
      title="应用身份详情"
      @close="handleClose"
      size="373"
      append-to-body
      destroy-on-close
    >
      <el-descriptions :column="1" direction="horizontal">
        <el-descriptions-item label="应用名称">{{
          info.appName || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="应用身份标识">{{
          info.handleCode || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="密钥">{{
          info.secretKey || "-"
        }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{
          info.remark || "-"
        }}</el-descriptions-item>
      </el-descriptions>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, onMounted } from "vue";
import { TableData } from "@/types/appManage";
import { apiIdentifyDetail } from "@/api/grantManage/identify";

const props = defineProps<{
  rowData: TableData;
}>();

const info = ref({
  appName: "",
  handleCode: "",
  secretKey: "",
  remark: "",
});

const emits = defineEmits(["close"]);
const handleClose = () => {
  emits("close");
};

const getDetail = () => {
  apiIdentifyDetail(props.rowData.id).then((res) => {
    info.value = res as any;
  });
};

const dialogFormVisible = ref(true);

onMounted(() => {
  getDetail();
});
</script>

<style lang="scss" scoped>
:deep(.el-descriptions__label) {
  width: 76px;
  min-width: 76px;
  display: inline-block;
}

:deep(.el-descriptions__content) {
  word-break: break-all;
  display: inline-block;
  width: 240px;
  vertical-align: top;
}
</style>
