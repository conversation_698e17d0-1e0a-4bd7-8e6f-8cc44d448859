<!-- 中台标识注册 -->
<template>
  <div class="page-search">
    <search-layout>
      <template #left>
        <el-button type="primary" @click="handleAdd">新增身份</el-button>
      </template>
      <template #right>
        <el-form :inline="true" :model="searchForm" @submit.prevent>
          <el-form-item style="width: 240px; max-width: 240px">
            <el-input
              v-model.trim="searchForm.appName"
              clearable
              @clear="handleSearch"
              placeholder="请输入"
            >
              <template #prefix>应用名称：</template></el-input
            >
          </el-form-item>
          <el-form-item style="width: 60px; max-width: 60px">
            <el-button
              type="primary"
              :loading="data.searchLoading"
              @click="handleSearch"
              >搜索</el-button
            >
          </el-form-item>
        </el-form>
      </template>
    </search-layout>
  </div>
  <div class="page-search-body">
    <el-table
      :data="data.tableData"
      v-loading="data.tableLoading"
      border
      size="small"
    >
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column label="应用名称">
        <template #default="scope">
          <ellipsisText :value="scope.row.appName">{{
            scope.row.appName || "-"
          }}</ellipsisText>
        </template>
      </el-table-column>
      <el-table-column label="应用身份标识">
        <template #default="scope">
          <ellipsisText :value="scope.row.handleCode">{{
            scope.row.handleCode || "-"
          }}</ellipsisText>
        </template>
      </el-table-column>
      <el-table-column label="备注">
        <template #default="scope">
          <ellipsisText :value="scope.row.remark">{{
            scope.row.remark || "-"
          }}</ellipsisText>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="200">
        <template #default="scope">
          <el-button type="primary" text @click="showDetailView(scope.row)"
            >详情</el-button
          >
          <el-button type="primary" text @click="handleEdit(scope.row)"
            >编辑</el-button
          >
          <el-popconfirm
            :width="200"
            confirm-button-text="确定"
            cancel-button-text="取消"
            title="确认删除此应用身份吗？"
            @confirm="handleDeleteConfirm(scope.row.id)"
          >
            <template #reference>
              <el-button text type="primary">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      v-model:currentPage="data.page"
      v-model:page-size="data.size"
      :page-sizes="[10, 20, 30, 40]"
      small
      layout="total, sizes, prev, pager, next, jumper"
      :total="data.totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
  <AddDialog
    v-if="addVisible"
    :is-edit="isEdit"
    :row-data="data.selectData"
    @cancel="addVisible = false"
    @success="addDone"
  ></AddDialog>
  <DetailDrawer
    v-if="detailDrawerVisible"
    :row-data="data.selectData"
    @close="detailDrawerVisible = false"
  />
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { apiIdentifyList, apiIdentifyDelete } from "@/api/grantManage/identify";
import SearchLayout from "@/components/searchLayout/index.vue";
import ellipsisText from "@/components/ellipsisText/index.vue";
import AddDialog from "./addDialog.vue";
import DetailDrawer from "./detailDrawer.vue";

const detailDrawerVisible = ref(false);
const addVisible = ref(false);
const isEdit = ref(false);

function addDone() {
  addVisible.value = false;
  getTableData();
}

const searchForm = reactive({
  appName: "",
});

interface Data {
  tableData: {
    id: number;
    appName: string;
    handleCode: string;
    remark: string;
  }[];
  page: number;
  size: number;
  totalCount: number;
  selectData: any;
  deleteLoading: boolean;
  tableLoading: boolean;
  searchLoading: boolean;
  confirmLoading: boolean;
}

const data = reactive<Data>({
  tableData: [],
  page: 1,
  size: 10,
  totalCount: 1,
  selectData: null,
  deleteLoading: false,
  tableLoading: false,
  searchLoading: false,
  confirmLoading: false,
});

function getTableData() {
  data.tableLoading = true;
  data.tableData = [];
  const params = {
    appName: searchForm.appName,
    page: data.page - 1,
    size: data.size,
  };
  apiIdentifyList(params)
    .then((response: any) => {
      const result = response;
      data.tableData = result?.content || [];
      data.page = result?.pageNumber || 1;
      data.totalCount = result?.totalCount || 0;
    })
    .finally(() => {
      data.tableLoading = false;
      data.searchLoading = false;
    });
}

function handleSearch() {
  data.searchLoading = true;
  data.page = 1;
  getTableData();
}

// 详情按钮
function showDetailView(item: any) {
  detailDrawerVisible.value = true;
  data.selectData = item;
}

function handleSizeChange(num: number) {
  data.size = num;
  data.page = 1;
  getTableData();
}

function handleCurrentChange(num: number) {
  data.page = num;
  getTableData();
}

// 新增标识
function handleAdd() {
  data.selectData = null;
  isEdit.value = false;
  addVisible.value = true;
}

function handleDeleteConfirm(id: any) {
  data.deleteLoading = true;
  apiIdentifyDelete(id)
    .then(() => {
      ElMessage.success("删除成功");
      data.page = 1;
      getTableData();
    })
    .finally(() => {
      data.deleteLoading = false;
    });
}

function handleEdit(item: any) {
  isEdit.value = true;
  data.selectData = item;
  addVisible.value = true;
}

onMounted(() => {
  getTableData();
});
</script>
<style lang="scss" scoped>
.page-search {
  .dialogClass {
    background-color: red;

    .mid-box {
      background-color: red;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      :deep(.el-input__inner) {
        width: 500px;
        height: 32px;
        line-height: 32px;
      }
    }
  }
}
.popover-delete-tips {
  margin-bottom: 20px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .popover-delete-tips-text {
    margin-left: 10px;
    font-size: 14px;
  }
}
.popover-delete-wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
.infoAlert {
  display: flex;
  align-items: center;
  height: 30px;
  margin: -44px -44px 14px;
  background-color: rgb(232, 244, 255);
}
.handleUploadInfo {
  width: 20px;
  height: 20px;
  margin-left: 20px;
}

.handleRecordClass {
  margin-left: 4px;
  margin-right: 4px;
  color: rgb(0, 183, 255);
  cursor: pointer;
}
.svgClose {
  height: 100%;
  width: 25px;
  margin-left: auto;
  display: flex;
  align-items: center;
}
.handleUploadClose {
  width: 15px;
  height: 15px;
  cursor: pointer;
}
.clickable-txt-cls {
  color: #1664ff;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
</style>
