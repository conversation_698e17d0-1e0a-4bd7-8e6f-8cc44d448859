<template>
  <div class="my-dialog">
    <el-dialog
      v-model="dialogFormVisible"
      :title="isEdit ? '编辑身份' : '新增身份'"
      width="800px"
      @close="$emit('cancel')"
      destroy-on-close
      align-center
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item label="应用身份标识" prop="handleCode">
          <el-input
            v-model="formData.handleCode"
            class="search-input"
            placeholder="请输入应用身份标识"
            @blur="handleChanged"
            clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item label="应用名称" prop="appName" class="disable-item">
          <el-input
            v-model="formData.appName"
            class="search-input"
            placeholder="通过应用身份标识获取"
            disabled
          >
          </el-input>
        </el-form-item>

        <el-form-item label="密钥" prop="secretKey" class="disable-item">
          <div class="select-btn">
            <el-input
              v-model="formData.secretKey"
              class="search-input"
              placeholder="请生成密钥"
              clearable
              disabled
            >
            </el-input>
            <el-button @click="clickGen">生成密钥</el-button>
          </div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            class="search-input"
            placeholder="请输入备注"
            clearable
          >
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <span>
          <el-button @click="dialogFormVisible = false">取消</el-button>
          <el-button type="primary" @click="validForm" :loading="btnLoading">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, defineProps, defineEmits, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { TableData } from "@/types/appManage";
import {
  apiIdentifyAdd,
  apiIdenfityEdit,
  apiIdentifyDetail,
  apiIdentifyGenKey,
} from "@/api/grantManage/identify";
import { idResolve } from "@/api/idRes";

const props = defineProps<{
  isEdit: boolean;
  rowData: TableData;
}>();

const emit = defineEmits(["cancel", "success"]);
const formRef = ref();

const btnLoading = ref(false);
const dialogFormVisible = ref(true);

const formData = ref({
  id: "",
  handleCode: "",
  appName: "",
  secretKey: "",
  remark: "",
});

const rules = reactive({
  handleCode: [
    { required: true, message: "请输入应用身份标识", trigger: "input" },
  ],
  appName: [{ required: true, message: "应用名称不能为空", trigger: "change" }],
  secretKey: [{ required: true, message: "请生成密钥", trigger: "change" }],
  remark: [
    {
      required: false,
      max: 100,
      message: "最大长度为100字符",
      trigger: "blur",
    },
  ],
});

const handleChanged = () => {
  if (formData.value.handleCode) {
    idResolve({ handle: formData.value.handleCode })
      .then((res) => {
        const r = res as Record<string, any>;
        if (r.type !== 3) {
          ElMessage.error("此应用身份标识不存在，请重新输入");
          formData.value.appName = "";
          return;
        }
        formData.value.appName = r.appValues.appName;
      })
      .catch(() => {
        formData.value.appName = "";
      });
  }
};

const clickGen = () => {
  apiIdentifyGenKey().then((res) => {
    if (res) {
      formData.value.secretKey = res as string;
      ElMessage({
        message: `生成密钥成功`,
        type: "success",
      });
    }
  });
};
const addOrEditAccount = () => {
  btnLoading.value = true;
  const api = props.isEdit ? apiIdenfityEdit : apiIdentifyAdd;
  const params = props.isEdit
    ? {
        id: formData.value.id,
        secretKey: formData.value.secretKey,
        remark: formData.value.remark,
      }
    : {
        appName: formData.value.appName,
        handleCode: formData.value.handleCode,
        secretKey: formData.value.secretKey,
        remark: formData.value.remark,
      };
  api(params)
    .then(() => {
      emit("success", true);
      ElMessage({
        message: `${props.isEdit ? "编辑" : "新增"}身份成功`,
        type: "success",
      });
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

const validForm = async () => {
  const valid = await formRef.value.validate();
  if (valid) {
    addOrEditAccount();
  }
};
onMounted(() => {
  if (props.isEdit) {
    apiIdentifyDetail(props.rowData.id).then((res) => {
      if (res) {
        formData.value = res as any;
      }
    });
  }
});
</script>

<style lang="scss">
.my-dialog {
  .select-btn {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    .el-select {
      flex: 1;
      min-width: 0;
    }
    .el-button {
      margin-left: 10px;
    }
  }
  .disable-item {
    .el-input {
      --el-input-placeholder-color: #7b9790;
      // --el-input-border-color: #f5f6f6;
      .el-input__wrapper {
        background: #f5f6f6;
      }
    }
  }
}
</style>
