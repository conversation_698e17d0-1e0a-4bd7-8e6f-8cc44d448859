<template>
  <div class="my-dialog">
    <el-dialog
      v-model="dialogFormVisible"
      title="分配权限组"
      width="800px"
      @close="$emit('cancel')"
      destroy-on-close
      align-center
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item label="应用名称" prop="appName" class="disable-item">
          <el-input v-model="formData.appName" class="search-input" disabled>
          </el-input>
        </el-form-item>
        <el-form-item
          label="应用身份标识"
          prop="handleCode"
          class="disable-item"
        >
          <el-input v-model="formData.handleCode" class="search-input" disabled>
          </el-input>
        </el-form-item>
        <el-form-item label="权限组" prop="appAuthGroupListVOList">
          <el-select
            v-model="formData.appAuthGroupListVOList"
            placeholder="请选择权限组"
            clearable
            filterable
            multiple
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="3"
            style="width: 100%"
          >
            <el-option
              v-for="item in groupData"
              :key="item.id"
              :label="item.authGroupName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span>
          <el-button @click="$emit('cancel')">取消</el-button>
          <el-button type="primary" @click="validForm" :loading="btnLoading">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted, defineProps } from "vue";
import { ElMessage } from "element-plus";
import {
  apiIdentifyGrant,
  apiIdentifyGrantGroupList,
} from "@/api/grantManage/identify";

const props = defineProps({
  data: Object,
});
const formData = ref({
  appHandleAuthId: null,
  appName: "",
  handleCode: "",
  appAuthGroupListVOList: [] as Array<any>,
});
const groupData = ref<Array<any>>([]);
const btnLoading = ref(false);
const dialogFormVisible = ref(true);
const emit = defineEmits(["cancel", "success"]);
const formRef = ref();

const rules = reactive({
  appAuthGroupListVOList: [
    { required: true, message: "请选择权限组", trigger: "blur" },
  ],
  handleCode: [{ required: true }],
  appName: [{ required: true }],
});

const requestAPI = () => {
  const list = formData.value.appAuthGroupListVOList.map((id) => {
    return { id };
  });
  btnLoading.value = true;
  apiIdentifyGrant({
    appHandleAuthId: formData.value.appHandleAuthId,
    appAuthGroupListVOList: list,
  })
    .then(() => {
      emit("success", true);
      ElMessage({
        message: `分配成功`,
        type: "success",
      });
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

const validForm = async () => {
  const valid = await formRef.value.validate();
  if (valid) {
    requestAPI();
  } else {
    return false;
  }
};

const getGroupList = () => {
  apiIdentifyGrantGroupList(formData.value.appHandleAuthId).then((res) => {
    if (res instanceof Array) {
      groupData.value = res as Array<any>;
      formData.value.appAuthGroupListVOList = groupData.value
        .filter((item) => item.isChecked === 1)
        .map((item) => item.id);
    }
  });
};

onMounted(() => {
  if (props.data) {
    const { id, appName, handleCode } = props.data;
    formData.value = {
      appHandleAuthId: id,
      appName,
      handleCode,
      appAuthGroupListVOList: [],
    };
  }
  getGroupList();
});
</script>

<style lang="scss">
.my-dialog {
  .disable-item {
    .el-input {
      --el-input-placeholder-color: #7b9790;
      // --el-input-border-color: #f5f6f6;
      .el-input__wrapper {
        background: #f5f6f6;
      }
    }
  }
}
</style>
