<!-- 中台标识注册 -->
<template>
  <div class="page-search">
    <search-layout>
      <template #right>
        <el-form :inline="true" :model="searchForm" @submit.prevent>
          <el-form-item style="width: 240px; max-width: 240px">
            <el-input
              v-model.trim="searchForm.appName"
              clearable
              @clear="handleSearch"
              placeholder="请输入"
            >
              <template #prefix>应用名称：</template></el-input
            >
          </el-form-item>
          <el-form-item style="width: 240px; max-width: 240px">
            <el-input
              v-model.trim="searchForm.groupName"
              clearable
              @clear="handleSearch"
              placeholder="请输入"
            >
              <template #prefix>权限组名称：</template></el-input
            >
          </el-form-item>
          <el-form-item style="width: 60px; max-width: 60px">
            <el-button
              type="primary"
              :loading="data.searchLoading"
              @click="handleSearch"
              >搜索</el-button
            >
          </el-form-item>
          <el-form-item style="width: 60px; max-width: 60px">
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </template>
    </search-layout>
  </div>
  <div class="page-search-body">
    <el-table
      :data="tableData"
      v-loading="data.tableLoading"
      border
      size="small"
    >
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column property="appName" label="应用名称">
        <template #default="scope">
          <ellipsisText :value="scope.row.appName">{{
            scope.row.appName || "-"
          }}</ellipsisText>
        </template>
      </el-table-column>
      <el-table-column property="handleCode" label="应用身份标识">
        <template #default="scope">
          <ellipsisText :value="scope.row.handleCode">{{
            scope.row.handleCode || "-"
          }}</ellipsisText>
        </template>
      </el-table-column>
      <el-table-column property="authGroupName" label="权限组">
        <template #default="scope">
          <ellipsisText :value="scope.row.authGroupName"></ellipsisText>
        </template>
      </el-table-column>
      <el-table-column property="remark" label="备注">
        <template #default="scope">
          <ellipsisText :value="scope.row.remark"></ellipsisText>
        </template>
      </el-table-column>
      <el-table-column label="操作" :width="200">
        <template #default="scope">
          <el-button type="primary" text @click="handleGrant(scope.row)"
            >分配权限组</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      v-model:currentPage="data.page"
      v-model:page-size="data.size"
      :page-sizes="[10, 20, 30, 40]"
      small
      layout="total, sizes, prev, pager, next, jumper"
      :total="data.totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
  <GrantDialog
    v-if="grantVisible"
    :data="selectedData"
    @cancel="grantVisible = false"
    @success="grantDone"
  ></GrantDialog>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, toRefs } from "vue";
import { ElMessage } from "element-plus";
import SearchLayout from "@/components/searchLayout/index.vue";
import ellipsisText from "@/components/ellipsisText/index.vue";
import GrantDialog from "./grantDialog.vue";
import { apiGrantIdentifyList } from "@/api/grantManage/identify";

const grantVisible = ref(false);

function grantDone() {
  grantVisible.value = false;
  getTableData();
}

const searchForm = reactive({
  appName: "",
  groupName: "",
});

const tableData = ref<any>([]);
const selectedData = ref();

const data = reactive({
  groupData: [],
  page: 1,
  size: 10,
  totalCount: 1,
  tableLoading: false,
  searchLoading: false,
});

function getTableData() {
  data.tableLoading = true;
  tableData.value = [];
  const params = {
    ...searchForm,
    page: data.page - 1,
    size: data.size,
  };
  apiGrantIdentifyList(params)
    .then((response: any) => {
      const result = response;
      tableData.value = result?.content || [];
      data.page = result?.pageNumber || 1;
      data.totalCount = result?.totalCount || 0;
    })
    .finally(() => {
      data.tableLoading = false;
      data.searchLoading = false;
    });
}
function handleSearch() {
  data.searchLoading = true;
  data.page = 1;
  getTableData();
}

function handleReset() {
  searchForm.appName = "";
  searchForm.groupName = "";
  handleSearch();
}

function handleSizeChange(num: number) {
  data.size = num;
  data.page = 1;
  getTableData();
}

function handleCurrentChange(num: number) {
  data.page = num;
  getTableData();
}

function handleGrant(item: any) {
  selectedData.value = item;
  grantVisible.value = true;
}

const getGroup = () => {
  // ApiGetMidHandleGroup().then((response: any) => {
  //   const result = response;
  //   data.groupData = result?.content || [];
  // });
};

onMounted(() => {
  getGroup();
  getTableData();
});
</script>
<style lang="scss" scoped>
.page-search {
  .dialogClass {
    background-color: red;

    .mid-box {
      background-color: red;
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      align-items: center;
      :deep(.el-input__inner) {
        width: 500px;
        height: 32px;
        line-height: 32px;
      }
    }
  }
}
.popover-delete-tips {
  margin-bottom: 20px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .popover-delete-tips-text {
    margin-left: 10px;
    font-size: 14px;
  }
}
.popover-delete-wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
.infoAlert {
  display: flex;
  align-items: center;
  height: 30px;
  margin: -44px -44px 14px;
  background-color: rgb(232, 244, 255);
}
.handleUploadInfo {
  width: 20px;
  height: 20px;
  margin-left: 20px;
}

.handleRecordClass {
  margin-left: 4px;
  margin-right: 4px;
  color: rgb(0, 183, 255);
  cursor: pointer;
}
.svgClose {
  height: 100%;
  width: 25px;
  margin-left: auto;
  display: flex;
  align-items: center;
}
.handleUploadClose {
  width: 15px;
  height: 15px;
  cursor: pointer;
}
.clickable-txt-cls {
  color: #1664ff;
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
</style>
