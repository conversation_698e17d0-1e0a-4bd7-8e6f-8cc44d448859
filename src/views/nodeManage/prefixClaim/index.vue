<template>
  <SearchLayout>
    <template #right
      ><el-form
        v-permission="AUTH_CODE.PREFIX_CONFIRM_PAGE"
        :inline="true"
        @submit.prevent
      >
        <el-form-item style="width: 240px; max-width: 240px">
          <el-input
            v-model="searchForm.entPrefix"
            placeholder="请输入"
            clearable
            @clear="handleSearch"
          >
            <template #prefix>企业前缀：</template>
          </el-input>
        </el-form-item>
        <el-form-item style="width: 240px; max-width: 240px">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择"
            clearable
            @clear="handleSearch"
          >
            <template #prefix>认领状态：</template>
            <el-option
              v-for="item in STATUS_LIST"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item style="width: 60px; max-width: 60px">
          <el-button
            :loading="searchLoading"
            type="primary"
            @click="handleSearch"
            >搜索</el-button
          >
        </el-form-item>
      </el-form></template
    >
  </SearchLayout>
  <el-table
    :data="tableData"
    v-loading="tableLoading"
    type="selection"
    size="small"
    border
  >
    <el-table-column label="序号" type="index" width="55" />
    <el-table-column label="企业前缀" show-overflow-tooltip>
      <template #default="scope">
        <span v-copy="scope.row.entPrefix">{{
          scope.row.entPrefix || "-"
        }}</span>
      </template>
    </el-table-column>
    <el-table-column label="认领状态">
      <template #default="scope">
        <span>{{ getStatusName(scope.row.status) }}</span>
      </template>
    </el-table-column>
    <el-table-column label="认领信息">
      <template #default="scope">
        <span>{{ scope.row.remark || "-" }}</span>
      </template>
    </el-table-column>
    <el-table-column label="操作" width="100">
      <template #default="scope">
        <el-button
          text
          type="primary"
          @click="claim(scope.row.id)"
          v-if="
            scope.row.status === STATUS_MAP.unclaimed ||
            scope.row.status === STATUS_MAP.rejected
          "
          v-permission="AUTH_CODE.PREFIX_CONFIRM_CLAIM"
        >
          认领
        </el-button>
        <el-popconfirm
          :width="200"
          confirm-button-text="确定"
          cancel-button-text="取消"
          title="确认取消认领？"
          @confirm="cancelclaim(scope.row.id)"
          v-if="scope.row.status === STATUS_MAP.claimed"
        >
          <template #reference>
            <el-button
              text
              type="primary"
              v-permission="AUTH_CODE.PREFIX_CONFIRM_CANCEL"
              >取消认领</el-button
            >
          </template>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
  <el-pagination
    v-model:currentPage="pagination.currentPage"
    v-model:page-size="pagination.pageSize"
    :page-sizes="[10, 20, 30, 40]"
    small
    background
    layout="total,  prev, pager, next, sizes,jumper"
    :total="pagination.total"
  />
  <claimedDialog
    v-if="claimedVisible"
    :prefixData="prefixData"
    @update-table="handleSearch"
    @close-dialog="claimedVisible = false"
  />
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { ElMessage } from "element-plus";
import claimedDialog from "./claimedDialog.vue";
import {
  ApiGetSearchData,
  ApiGetPrefixData,
  ApiCancelClaim,
} from "@/api/bms/prefixClaim";
import { TableData } from "@/types/nodeManage/prefixClaim";
import { AUTH_CODE } from "@/utils/authCode";
import store from "@/store";
import SearchLayout from "@/components/searchLayout/index.vue";

const tableData = ref<TableData[]>([]);
const claimedVisible = ref(false);
const tableLoading = ref(false);

const searchLoading = ref(false);
const STATUS_MAP = {
  unclaimed: 0,
  claimed: 1,
  rejected: 2,
};
const STATUS_LIST = [
  { value: 0, label: "未认领" },
  { value: 1, label: "已认领" },
  { value: 2, label: "被驳回" },
];
const searchForm = reactive({
  entPrefix: "",
  status: null,
});

const prefixData = ref();

const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});
onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (AllPermission.includes(AUTH_CODE.PREFIX_CONFIRM_PAGE)) {
    getTableData(true);
  }
  getTableData(true);
});

watch([() => pagination.currentPage, () => pagination.pageSize], () => {
  getTableData(false);
});

function handleSearch() {
  searchLoading.value = true;
  pagination.currentPage = 1;
  getTableData(false);
}

// 列表
const getTableData = (isGetAll: boolean) => {
  if (isGetAll) {
    pagination.currentPage = 1;
    pagination.pageSize = 10;
    pagination.total = 0;
  }
  const { currentPage, pageSize } = pagination;
  tableLoading.value = true;
  ApiGetSearchData({
    entPrefix: isGetAll ? "" : searchForm.entPrefix,
    status: searchForm.status,
    size: pageSize,
    page: currentPage - 1,
  })
    .then((res) => {
      const { content, totalCount } = res;
      tableData.value = content;
      pagination.total = totalCount;
    })
    .finally(() => {
      searchLoading.value = false;
      tableLoading.value = false;
    });
};

async function claim(id: number) {
  prefixData.value = await ApiGetPrefixData({ id });
  claimedVisible.value = true;
}
function cancelclaim(id: number) {
  ApiCancelClaim({ id }).then(() => {
    ElMessage({
      message: "取消认领成功!",
      type: "success",
    });
    getTableData(true);
  });
}
function getStatusName(type: number | undefined) {
  if (type === 0) return "未认领";
  if (type === 1) return "已认领";
  if (type === 2) return "被驳回";
  return "-";
}
</script>
<style lang="scss" scoped></style>
