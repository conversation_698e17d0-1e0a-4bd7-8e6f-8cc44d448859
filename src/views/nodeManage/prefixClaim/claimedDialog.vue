<template>
  <el-dialog
    v-model="dialogFormVisible"
    title="前缀认领"
    width="700px"
    @close="$emit('close-dialog')"
    align-center
    :close-on-click-modal="false"
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
      <el-form-item label="认领前缀:" prop="entPrefix">
        <el-input
          v-model.trim="formData.entPrefix"
          clearable
          placeholder="请输入"
          disabled
        />
      </el-form-item>
      <el-form-item label="企业编码:" prop="orgCode">
        <el-input v-model.trim="formData.orgCode" clearable />
      </el-form-item>
      <el-form-item label="企业名称:" prop="orgName">
        <el-input
          v-model.trim="formData.orgName"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="上级单位名称:" prop="parentOrgName">
        <el-input
          v-model.trim="formData.parentOrgName"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="所属省市:" prop="selectProvince">
        <ChinaAreaCascader
          v-model="formData.selectProvince"
          @change="areaChange"
          style="width: 100%"
          clearable
        ></ChinaAreaCascader>
      </el-form-item>
      <el-form-item label="地址:" prop="selectAddress">
        <el-input
          v-model.trim="formData.selectAddress"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
      <el-form-item label="创建时间:" prop="createdTime">
        <el-date-picker
          v-model="formData.createdTime"
          type="datetime"
          placeholder="请选择"
        />
      </el-form-item>
      <el-form-item label="认领信息:" prop="remark">
        <el-input
          type="textarea"
          v-model.trim="formData.remark"
          clearable
          placeholder="请输入"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleApply">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage, FormInstance } from "element-plus";
import { computed, reactive, ref } from "vue";
import { ApiPrefixClaim } from "@/api/bms/prefixClaim";

import ChinaAreaCascader from "@/components/ChinaAreaCascader/index.vue";

const emit = defineEmits(["close-dialog", "update-table"]);
const formRef = ref<FormInstance>();

const props = defineProps({
  prefixData: {
    type: Object,
    default: () => ({}),
  },
});

const loading = ref(false);

const selectProvinceValidate = (rule: any, value: any, callback: any) => {
  if (!value || !value.length) {
    return callback("请选择所属省市");
  }
  // if (value.length < 3 || !value[2]) {
  //   return callback("请选择所属省市");
  // }
  callback();
};
const handleRegex = /^[0-9]\d{1}\.[0-9]\d{3}\.[0-9]\d{3}$/;
const entPrefixValidate = (rule: any, value: any, callback: any) => {
  if (!handleRegex.test(value) && value !== "99.1000.1") {
    return callback("格式不正确: 请输入2+4+4格式前缀");
  }
  callback();
};

const rules = reactive({
  orgCode: [
    { required: true, message: "请输入企业编码", trigger: "blur" },
    { max: 100, message: "最大长度不超过100", trigger: "blur" },
  ],
  orgName: [
    { required: true, message: "请输入企业名称", trigger: "blur" },
    { max: 100, message: "最大长度不超过100", trigger: "blur" },
  ],
  parentOrgName: [
    { required: true, message: "请输入上级单位名称", trigger: "blur" },
    { max: 100, message: "最大长度不超过100", trigger: "blur" },
  ],
  selectProvince: [
    { required: true, trigger: "change", validator: selectProvinceValidate },
  ],
  orgAddrCity: [{ required: true, message: "请输入所属市", trigger: "blur" }],
  selectAddress: [
    { required: true, message: "请输入地址", trigger: "blur" },
    { max: 100, message: "最大长度不超过100", trigger: "blur" },
  ],
  entPrefix: [
    { required: true, message: "请输入企业前缀", trigger: "blur" },
    { validator: entPrefixValidate, trigger: "blur" },
  ],
  createdTime: [{ required: true, message: "请选择创建时间", trigger: "blur" }],
  remark: [
    { required: true, message: "请输入认领信息", trigger: "blur" },
    { max: 100, message: "最大长度不超过100", trigger: "blur" },
  ],
});

const dialogFormVisible = ref(true);
const selectProvinceList = computed(() => {
  if (props.prefixData.orgAddrDistrict) {
    return [
      props.prefixData.orgAddrProvince,
      props.prefixData.orgAddrCity,
      props.prefixData.orgAddrDistrict,
    ];
  }
  return [props.prefixData.orgAddrProvince, props.prefixData.orgAddrCity];
});
const formData = ref({
  orgName: props.prefixData.orgName,
  orgCode: props.prefixData.orgCode,
  parentOrgName: props.prefixData.parentOrgName,
  selectAddress: props.prefixData.orgAddress,
  entPrefix: props.prefixData.entPrefix,
  selectProvince: selectProvinceList,
  createdTime: props.prefixData.createdTime,
  remark: "",
});

const areaChange = (areas: any[]) => {
  formData.value.selectProvince[0] = areas[0];
  formData.value.selectProvince[1] = areas[1];
  formData.value.selectProvince[2] = areas[2];
};

function handleApply() {
  if (!formRef.value) return;
  formRef.value.validate((valid) => {
    if (valid) {
      const params = {
        id: props.prefixData.id,
        orgName: formData.value.orgName,
        orgCode: formData.value.orgCode,
        parentOrgName: formData.value.parentOrgName,
        orgAddrProvince: formData.value.selectProvince[0],
        orgAddrCity: formData.value.selectProvince[1],
        orgAddrDistrict: formData.value.selectProvince[2],
        orgAddress: formData.value.selectAddress,
        entPrefix: formData.value.entPrefix,
        createdTime: formData.value.createdTime,
        remark: formData.value.remark,
      };
      loading.value = true;
      ApiPrefixClaim(params)
        .then(() => {
          dialogFormVisible.value = false;
          emit("update-table", true);
          ElMessage({
            message: "前缀认领成功!",
            type: "success",
          });
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}
</script>

<style lang="scss"></style>
