<template>
  <el-dialog
    v-model="dialogFormVisible"
    title="配置"
    width="700px"
    @close="$emit('close-dialog')"
    align-center
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
      <el-form-item label="企业前缀:" prop="entPrefix">
        <el-input
          v-model.trim="formData.entPrefix"
          placeholder="请输入"
          clearable
          disabled
        />
      </el-form-item>
      <el-form-item label="解析服务器:" prop="hostingServerId">
        <el-select
          v-model="formData.hostingServerId"
          placeholder="请选择"
          style="width: 100%"
          clearable
        >
          <el-option
            v-for="item in hostingOptions"
            :key="item.id"
            :label="item.srvName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="企业代码:" prop="orgCode">
        <el-input
          v-model.trim="formData.orgCode"
          placeholder="请输入"
          clearable
        />
      </el-form-item>
      <el-form-item label="企业名称:" prop="orgName">
        <el-input
          v-model.trim="formData.orgName"
          placeholder="请输入"
          clearable
        />
      </el-form-item>
      <el-form-item label="上级单位名称:" prop="parentOrgName">
        <el-input
          v-model.trim="formData.parentOrgName"
          placeholder="请输入"
          clearable
        />
      </el-form-item>
      <el-form-item label="所属省市:" prop="selectProvince">
        <ChinaAreaCascader
          v-model="formData.selectProvince"
          @change="areaChange"
          style="width: 100%"
          clearable
        ></ChinaAreaCascader>
      </el-form-item>
      <el-form-item label="地址:" prop="selectAddress">
        <el-input
          v-model.trim="formData.selectAddress"
          placeholder="请输入"
          clearable
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage, FormInstance } from "element-plus";
import { onMounted, reactive, ref } from "vue";
import {
  entPrefixConfig,
  entGetServerList,
  entPrefixDetail,
} from "@/api/bms/entPrefixManage";
import ChinaAreaCascader from "@/components/ChinaAreaCascader/index.vue";

const emit = defineEmits(["close-dialog", "update-table"]);
const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
});
const formRef = ref<FormInstance>();

const dialogFormVisible = ref(true);
const loading = ref(false);
const hostingOptions = ref<
  {
    id: number;
    srvName: string;
  }[]
>([]);

const formData = reactive<{
  entPrefix: string;
  hostingServerId: number | null;
  orgName: string;
  parentOrgName: string;
  selectProvince: any[];
  selectAddress: string;
  orgCode: string;
}>({
  entPrefix: "",
  hostingServerId: null,
  orgName: "",
  parentOrgName: "",
  selectProvince: [],
  selectAddress: "",
  orgCode: "",
});
const areaChange = (areas: any[]) => {
  // formData.selectProvince = areas;
  formData.selectProvince[0] = areas[0];
  formData.selectProvince[1] = areas[1];
  formData.selectProvince[2] = areas[2];
};
const selectProvinceValidate = (rule: any, value: any, callback: any) => {
  if (!value || !value.length) {
    return callback("请选择所属省市");
  }
  // if (value.length < 3 || !value[2]) {
  //   return callback("请选择所属省市");
  // }
  callback();
};
const rules = reactive({
  entPrefix: [{ required: true, message: "请输入企业前缀", trigger: "blur" }],
  hostingServerId: [
    { required: true, message: "请选择解析服务器", trigger: "change" },
  ],
  orgName: [{ required: true, message: "请输入企业名称", trigger: "blur" }],
  orgCode: [{ required: true, message: "请输入企业代码", trigger: "blur" }],
  parentOrgName: [
    { required: true, message: "请输入上级单位名称", trigger: "blur" },
  ],
  selectProvince: [
    {
      required: true,
      message: "请输入所属省",
      trigger: "change",
      validator: selectProvinceValidate,
    },
  ],
  selectAddress: [{ required: true, message: "请输入地址", trigger: "blur" }],
});

function handleSubmit() {
  if (!formRef.value) return;
  formRef.value.validate((valid) => {
    if (valid) {
      const params = {
        orgName: formData.orgName,
        orgCode: formData.orgCode,
        hostingServerId: formData.hostingServerId,
        parentOrgName: formData.parentOrgName,
        orgAddrProvince: formData.selectProvince[0],
        orgAddrCity: formData.selectProvince[1],
        orgAddrDistrict: formData.selectProvince[2],
        orgAddress: formData.selectAddress,
        entPrefix: formData.entPrefix,
      };
      entPrefixConfig(params).then(() => {
        ElMessage.success("配置成功!");
        emit("update-table");
        dialogFormVisible.value = false;
      });
    }
  });
}

function getServerList() {
  entGetServerList().then((res: any) => {
    hostingOptions.value = res;
  });
}
function getDetail() {
  entPrefixDetail({
    id: props.item.id,
  }).then((res: any) => {
    formData.entPrefix = res.entPrefix;
    formData.hostingServerId = res.hostingServerId;
    formData.orgName = res.orgName;
    formData.orgCode = res.orgCode;
    formData.parentOrgName = res.parentOrgName;
    formData.selectProvince = [
      res.orgAddrProvince,
      res.orgAddrCity,
      res.orgAddrDistrict,
    ];
    formData.selectAddress = res.orgAddress;
    if (!formData.entPrefix) {
      formData.entPrefix = props.item.entPrefix;
    }
  });
}
onMounted(() => {
  getServerList();
  getDetail();
});
</script>

<style lang="scss"></style>
