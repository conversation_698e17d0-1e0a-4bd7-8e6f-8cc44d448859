<template>
  <el-dialog
    v-model="dialogFormVisible"
    title="新增前缀"
    width="700px"
    @close="$emit('close-dialog')"
    align-center
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="120px">
      <el-form-item label="企业前缀:" prop="entPrefix">
        <el-input
          v-model.trim="formData.entPrefix"
          placeholder="请输入"
          clearable
        />
      </el-form-item>
      <el-form-item label="解析服务器:" prop="hostingServerId">
        <el-select
          v-model="formData.hostingServerId"
          placeholder="请选择"
          style="width: 100%"
        >
          <el-option
            v-for="item in hostingOptions"
            :key="item.id"
            :label="item.srvName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage, FormInstance } from "element-plus";
import { onMounted, reactive, ref } from "vue";
import { prefixAdd, entGetServerList } from "@/api/bms/entPrefixManage";

const emit = defineEmits(["close-dialog", "update-table"]);
const formRef = ref<FormInstance>();

const dialogFormVisible = ref(true);
const loading = ref(false);
const hostingOptions = ref<
  {
    id: number;
    srvName: string;
  }[]
>([]);
const formData = reactive({
  entPrefix: "",
  hostingServerId: null,
});
const rules = reactive({
  entPrefix: [{ required: true, message: "请输入企业前缀", trigger: "blur" }],
  hostingServerId: [
    { required: true, message: "请选择解析服务器", trigger: "blur" },
  ],
});

function handleSubmit() {
  if (!formRef.value) return;
  formRef.value.validate((valid) => {
    if (valid) {
      prefixAdd(formData).then(() => {
        ElMessage.success("新增前缀成功!");
        emit("update-table");
        dialogFormVisible.value = false;
      });
    }
  });
}

function getServerList() {
  entGetServerList().then((res: any) => {
    hostingOptions.value = res;
  });
}

onMounted(() => {
  getServerList();
});
</script>

<style lang="scss"></style>
