<template>
  <SearchLayout>
    <template #left>
      <el-button
        type="primary"
        @click="addPrefix"
        v-permission="AUTH_CODE.ENT_PREFIX_MANAGER_ADD"
        >新增前缀</el-button
      >
    </template>
    <template #right>
      <el-form
        v-permission="AUTH_CODE.ENT_PREFIX_MANAGER_PAGE"
        :inline="true"
        :model="searchForm"
        ref="searchFromref"
        @submit.prevent
      >
        <el-form-item style="width: 240px; max-width: 240px">
          <el-input
            v-model="searchForm.entPrefix"
            placeholder="请输入"
            clearable
            @clear="handleSearch"
          >
            <template #prefix>企业前缀：</template>
          </el-input>
        </el-form-item>
        <el-form-item style="width: 60px; max-width: 60px">
          <el-button
            type="primary"
            :loading="searchLoading"
            @click="handleSearch"
            >搜索</el-button
          >
        </el-form-item>
      </el-form>
    </template>
  </SearchLayout>

  <el-table :data="tableData" v-loading="tableLoading" border size="small">
    <el-table-column label="序号" type="index" width="55" />
    <el-table-column prop="entPrefix" label="企业前缀" show-overflow-tooltip>
      <template #default="scope">
        <span v-copy="scope.row.entPrefix">{{
          scope.row.entPrefix || "-"
        }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="orgName" label="企业名称" show-overflow-tooltip>
      <template #default="scope">
        <span v-copy="scope.row.orgName">{{ scope.row.orgName || "-" }}</span>
      </template>
    </el-table-column>
    <el-table-column
      prop="parentOrgName"
      label="上级单位名称"
      show-overflow-tooltip
    />
    <el-table-column prop="orgAddrProvinceDesc" label="所属省" />
    <el-table-column prop="orgAddrCityDesc" label="所属市" />
    <el-table-column prop="orgAddrDistrictDesc" label="所属区" />
    <el-table-column prop="orgAddress" label="地址" show-overflow-tooltip />
    <el-table-column label="操作" width="180px">
      <template #default="scope">
        <el-button
          text
          type="primary"
          @click="openDetailDialog(scope.row)"
          v-permission="AUTH_CODE.ENT_PREFIX_MANAGER_DETAIL"
        >
          详情
        </el-button>
        <el-button
          text
          type="primary"
          @click="openEntConfig(scope.row)"
          v-permission="AUTH_CODE.ENT_PREFIX_MANAGER_CONFIG"
        >
          配置
        </el-button>
        <el-popconfirm
          :title="`请先删除前缀下的路由解析地址、标识数据再进行删除。`"
          @confirm="doDelete(scope.row)"
        >
          <template #reference>
            <el-button
              text
              type="primary"
              v-permission="AUTH_CODE.ENT_PREFIX_MANAGER_DELETE"
              >删除</el-button
            >
          </template>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
  <el-pagination
    v-model:currentPage="pagination.currentPage"
    v-model:page-size="pagination.pageSize"
    :page-sizes="[10, 20, 30, 40]"
    small
    background
    layout="total,  prev, pager, next, sizes,jumper"
    :total="pagination.total"
  />
  <PrefixDetail
    v-if="dialogDetailVisible"
    :item="selectRow"
    @update-table="handleSearch"
    @close-dialog="dialogDetailVisible = false"
  />
  <AddPrefix
    v-if="dialogAddVisible"
    @close-dialog="dialogAddVisible = false"
    @update-table="handleSearch"
  />
  <EntPrefixConfig
    v-if="dialogEntConfigVisible"
    :item="selectRow"
    @update-table="handleSearch"
    @close-dialog="dialogEntConfigVisible = false"
  />
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onMounted } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { entFetchList, entDeletePrefix } from "@/api/bms/entPrefixManage";
import PrefixDetail from "./prefixDetail.vue";
import AddPrefix from "./addPrefix.vue";
import EntPrefixConfig from "./entPrefixConfig.vue";
import { TableData } from "@/types/nodeManage/prefixManage";
import { AUTH_CODE } from "@/utils/authCode";
import store from "@/store";
import SearchLayout from "@/components/searchLayout/index.vue";

const tableData = ref<TableData[]>();

const searchFromref = ref<FormInstance>();
const searchLoading = ref(false);
const tableLoading = ref(false);

const dialogDetailVisible = ref(false);
const dialogAddVisible = ref(false);
const dialogEntConfigVisible = ref(false);

const selectRow = ref({});
const searchForm = reactive({
  entPrefix: "",
});
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});
onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (AllPermission.includes(AUTH_CODE.NODE_USER_DETAIL)) {
    getTableData(true);
  }
});

watch([() => pagination.currentPage, () => pagination.pageSize], () => {
  getTableData(false);
});

// 详情页面
const openDetailDialog = (row: any) => {
  selectRow.value = row;
  dialogDetailVisible.value = true;
};

const doDelete = (row: any) => {
  entDeletePrefix({ id: row.id }).then(() => {
    ElMessage.success("删除成功!");
    // 成功后刷新
    handleSearch();
  });
};

// 搜索
function handleSearch() {
  searchLoading.value = true;
  pagination.currentPage = 1;
  getTableData(false);
}

// 获取列表
const getTableData = (isGetAll: boolean) => {
  const { currentPage, pageSize } = pagination;
  const prefix = searchForm.entPrefix;
  tableLoading.value = true;
  entFetchList({
    entPrefix: isGetAll ? "" : prefix,
    size: pageSize,
    page: currentPage - 1,
  })
    .then((res: any) => {
      const { content, totalCount } = res;
      tableData.value = content;
      pagination.total = totalCount;
    })
    .finally(() => {
      searchLoading.value = false;
      tableLoading.value = false;
    });
};

const addPrefix = () => {
  dialogAddVisible.value = true;
};
const openEntConfig = (row: any) => {
  selectRow.value = row;
  dialogEntConfigVisible.value = true;
};
</script>
<style lang="scss" scoped>
.app-container {
  background-color: white;
  .box-top {
    display: flex;
    justify-content: flex-end;
    justify-items: center;
  }

  .optioncls {
    font-size: 14px;
    margin-right: 4px;
    cursor: pointer;
  }
  .detailOption {
    color: green;
  }
  .changeOption {
    color: yellowgreen;
  }
  .configOption {
    color: blue;
  }
  .deleteOption {
    color: red;
  }
}

:deep(.el-input--large .el-input__inner) {
  height: 30px;
  line-height: 30px;
  // padding: 0 15px;
}
:deep(.el-select--large) {
  line-height: 30px;
  width: 100%;
}
.button {
  margin-left: 12px;
}
:deep(.el-descriptions__label) {
  width: 72px;
  min-width: 72px;
  display: inline-block;
}
</style>
