<template>
  <div class="app-container">
    <SearchLayout>
      <template #left v-if="!isProxy">
        <el-button
          type="primary"
          @click="openAllotDialog"
          v-permission="AUTH_CODE.PREFIX_MANAGER_ALLOT"
          >分配</el-button
        >
      </template>
      <template #right>
        <el-form
          v-permission="AUTH_CODE.PREFIX_MANAGER_PAGE"
          :inline="true"
          :model="searchForm"
          ref="searchFromref"
          @submit.prevent
        >
          <el-form-item style="width: 240px; max-width: 240px">
            <el-input
              v-model="searchForm.entPrefix"
              placeholder="请输入"
              clearable
            >
              <template #prefix>企业前缀：</template>
            </el-input>
          </el-form-item>
          <el-form-item style="width: 60px; max-width: 60px">
            <el-button
              type="primary"
              :loading="searchLoading"
              @click="handleSearch"
              >搜索</el-button
            >
          </el-form-item>
        </el-form>
      </template>
    </SearchLayout>
    <el-table :data="tableData" v-loading="tableLoading" border size="small">
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column prop="entPrefix" label="企业前缀" show-overflow-tooltip>
        <template #default="scope">
          <span v-copy="scope.row.entPrefix">{{ scope.row.entPrefix }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="orgName" label="企业名称" show-overflow-tooltip />
      <el-table-column
        prop="parentOrgName"
        label="上级单位名称"
        show-overflow-tooltip
      />
      <el-table-column prop="orgAddrProvinceDesc" label="所属省" />
      <el-table-column prop="orgAddrCityDesc" label="所属市" />
      <el-table-column prop="orgAddrDistrictDesc" label="所属区" />
      <el-table-column label="托管状态">
        <template #default="scope">
          <span>{{ scope.row.hostingState === 1 ? "已托管" : "未托管" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="180px">
        <template #default="scope">
          <el-button
            text
            type="primary"
            @click="openDetailDialog(scope.row)"
            v-permission="AUTH_CODE.PREFIX_MANAGER_DETAIL"
          >
            详情
          </el-button>

          <el-popconfirm
            :title="`确认要禁用吗？`"
            @confirm="doStop(scope.row)"
            v-if="scope.row.state === 1"
          >
            <template #reference>
              <el-button
                text
                type="primary"
                v-permission="AUTH_CODE.PREFIX_MANAGER_CHANGESTATE"
                >禁用</el-button
              >
            </template>
          </el-popconfirm>

          <el-button
            v-if="scope.row.state !== 1"
            text
            type="primary"
            @click="doStart(scope.row)"
            v-permission="AUTH_CODE.PREFIX_MANAGER_CHANGESTATE"
          >
            启用
          </el-button>

          <el-button
            text
            type="primary"
            @click="openConfig(scope.row)"
            :disabled="scope.row.hostingState === 1"
            v-permission="AUTH_CODE.PREFIX_MANAGER_CONFIG"
          >
            配置
          </el-button>

          <el-popconfirm
            :title="`请先删除前缀下的路由解析地址、对象标识数据再进行删除。`"
            @confirm="doDelete(scope.row)"
          >
            <template #reference>
              <el-button
                text
                type="primary"
                v-permission="AUTH_CODE.PREFIX_MANAGER_DELETE"
                >删除</el-button
              >
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:currentPage="pagination.currentPage"
      v-model:page-size="pagination.pageSize"
      :page-sizes="[10, 20, 30, 40]"
      small
      background
      layout="total,  prev, pager, next, sizes,jumper"
      :total="pagination.total"
    />
  </div>
  <PrefixConfig
    v-if="dialogConfigVisible"
    :item="selectRow"
    @update-table="handleSearch"
    @close-dialog="dialogConfigVisible = false"
  />
  <PrefixAllot
    v-if="dialogAllotVisible"
    @update-table="handleSearch"
    @close-dialog="dialogAllotVisible = false"
  />
  <PrefixDetail
    v-if="dialogDetailVisible"
    :item="selectRow"
    @update-table="handleSearch"
    @close-dialog="dialogDetailVisible = false"
  />
</template>

<script setup lang="ts">
import { ref, reactive, watch, computed, onMounted } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { useStore } from "vuex";
import { LEVEL_TYPE, SYSTEM_TYPE } from "@/utils/constant";
import {
  fetchList,
  deletePrefix,
  changeState,
} from "@/api/bms/entPrefixManage";
import PrefixConfig from "./prefixConfig.vue";
import PrefixAllot from "./prefixAllot.vue";
import PrefixDetail from "./prefixDetail.vue";
import { TableData } from "@/types/nodeManage/prefixManage";
import { AUTH_CODE } from "@/utils/authCode";
import SearchLayout from "@/components/searchLayout/index.vue";

const tableData = ref<TableData[]>();

const store = useStore();
const userInfo = computed(() => store.getters.userInfo);
const isProxy = computed(() => userInfo.value.levelType === LEVEL_TYPE.PROXY);

const searchFromref = ref<FormInstance>();
const searchLoading = ref(false);
const tableLoading = ref(false);

const dialogAllotVisible = ref(false);
const dialogDetailVisible = ref(false);
const dialogConfigVisible = ref(false);

const selectRow = ref({});
const searchForm = reactive({
  entPrefix: "",
});
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});
onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (AllPermission.includes(AUTH_CODE.PREFIX_MANAGER_PAGE)) {
    getTableData(true);
  }
});

watch([() => pagination.currentPage, () => pagination.pageSize], () => {
  getTableData(false);
});

// 详情页面
const openDetailDialog = (row: any) => {
  selectRow.value = row;
  dialogDetailVisible.value = true;
};

const doDelete = (row: any) => {
  deletePrefix({ id: row.id }).then(() => {
    ElMessage.success("删除成功!");
    // 成功后刷新
    getTableData(true);
  });
};

// 禁用
const doStop = (row: any) => {
  changeState({
    id: row.id,
    state: 0,
  }).then(() => {
    ElMessage.success("禁用成功!");
    getTableData(true);
  });
};

// 启用
const doStart = (row: any) => {
  changeState({
    id: row.id,
    state: 1,
  }).then(() => {
    ElMessage.success("启用成功!");
    getTableData(true);
  });
};

// 打开分配对话框
const openAllotDialog = () => {
  dialogAllotVisible.value = true;
};

// 搜索
function handleSearch() {
  searchLoading.value = true;
  pagination.currentPage = 1;
  getTableData(false);
}

// 获取列表
const getTableData = (isGetAll: boolean) => {
  const { currentPage, pageSize } = pagination;
  const prefix = searchForm.entPrefix;
  tableLoading.value = true;
  fetchList({
    entPrefix: isGetAll ? "" : prefix,
    size: pageSize,
    page: currentPage - 1,
  })
    .then((res: any) => {
      const { content, totalCount } = res;
      tableData.value = content;
      pagination.total = totalCount;
    })
    .finally(() => {
      searchLoading.value = false;
      tableLoading.value = false;
    });
};

// 打开配置对话框
const openConfig = (row: any) => {
  selectRow.value = row;
  dialogConfigVisible.value = true;
};
</script>
<style lang="scss" scoped>
.app-container {
  background-color: white;

  .optioncls {
    font-size: 14px;
    margin-right: 4px;
    cursor: pointer;
  }
  .detailOption {
    color: green;
  }
  .changeOption {
    color: yellowgreen;
  }
  .configOption {
    color: blue;
  }
  .deleteOption {
    color: red;
  }
}

:deep(.el-input--large .el-input__inner) {
  height: 30px;
  line-height: 30px;
  // padding: 0 15px;
}
:deep(.el-select--large) {
  line-height: 30px;
  width: 100%;
}
.button {
  margin-left: 12px;
}
:deep(.el-descriptions__label) {
  width: 72px;
  min-width: 72px;
  display: inline-block;
}
</style>
