<template>
  <el-dialog
    v-model="dialogAllotVisible"
    title="前缀分配"
    width="700px"
    align-center
    @close="$emit('close-dialog')"
  >
    <el-form
      ref="allotFormref"
      label-position="right"
      label-width="100px"
      :model="allotForm"
      :rules="allotRules"
    >
      <el-form-item label="前缀" prop="prefix">
        <el-input
          v-model="allotForm.prefix"
          placeholder="请输入前缀"
          size="large"
        />
      </el-form-item>
      <el-form-item label="分配企业" prop="selectEnt">
        <el-select
          class="width-full"
          v-model="allotForm.selectEnt"
          placeholder="请选择企业"
        >
          <el-option
            v-for="item in options"
            :key="item.id"
            :label="item.orgName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogAllotVisible = false">取消</el-button>
        <el-button type="primary" @click="allotOption" :loading="btnLoading">
          分配
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { ElMessage, FormInstance } from "element-plus";
import { onMounted, reactive, ref } from "vue";
import { allotPrefix, getEntList } from "@/api/bms/entPrefixManage";
import { Options } from "@/types/nodeManage/prefixManage";

const emit = defineEmits(["close-dialog", "update-table"]);
const allotFormref = ref<FormInstance>();
const dialogAllotVisible = ref(true);
const allotForm = reactive<{
  prefix: string;
  selectEnt: number | null;
}>({
  prefix: "",
  selectEnt: null,
});

const options = ref<Options[]>();

const btnLoading = ref(false);
// 前缀校验
const validatePrefix = (rule: any, value: any, callback: any) => {
  if (value === "") {
    callback(new Error("前缀不能为空"));
  } else {
    if (
      value.startsWith(".") ||
      value.endsWith(".") ||
      value.indexOf("..") !== -1 ||
      value.indexOf(".") === -1
    ) {
      callback(new Error("前缀格式错误"));
    }
    callback();
  }
};

// 分配form校验规则
const allotRules = reactive({
  prefix: [{ required: true, validator: validatePrefix, trigger: "blur" }],
  selectEnt: [{ required: true, message: "请选择企业", trigger: "change" }],
});

// 分配操作
const allotOption = () => {
  if (!allotFormref.value) return;
  allotFormref.value.validate((valid) => {
    if (valid) {
      btnLoading.value = true;
      allotPrefix({
        entPrefix: allotForm.prefix ?? "",
        entId: allotForm.selectEnt,
      })
        .then(() => {
          ElMessage.success("分配成功!");
          emit("update-table", true);
          dialogAllotVisible.value = false;
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};

onMounted(() => {
  // 先获取企业list
  getEntList("").then((res: any) => {
    options.value = res;
  });
});
</script>
<style></style>
