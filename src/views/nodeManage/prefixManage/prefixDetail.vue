<template>
  <el-drawer
    v-model="dialogDetailVisible"
    title="前缀详情"
    @close="$emit('close-dialog')"
  >
    <el-descriptions :column="1" direction="horizontal">
      <el-descriptions-item label="企业名称">
        {{ detailForm?.orgName ?? "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="企业代码">
        {{ detailForm?.orgCode ?? "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="企业前缀">
        {{ detailForm?.entPrefix ?? "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="上级单位名称">
        {{ detailForm?.parentOrgName ?? "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="所属省/市/区">
        {{ detailForm?.orgAddrProvinceDesc ?? "-" }}/
        {{ detailForm?.orgAddrCityDesc ?? "-" }}/
        {{ detailForm?.orgAddrDistrictDesc ?? "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="创建时间">
        {{ detailForm?.createdTime ?? "-" }}
      </el-descriptions-item>
    </el-descriptions>
    <el-divider />
  </el-drawer>
</template>
<script setup lang="ts">
import { onMounted, ref } from "vue";
import { prefixDetail } from "@/api/bms/entPrefixManage";
import { DetailForm } from "@/types/nodeManage/prefixManage";

const emit = defineEmits(["close-dialog"]);
const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
});
const dialogDetailVisible = ref(true);

const detailForm = ref<DetailForm>();
function getDetail() {
  prefixDetail({
    id: props.item.id,
  }).then((res: any) => {
    detailForm.value = res;
    if (detailForm.value) {
      detailForm.value.entPrefix = props.item.entPrefix;
    }
  });
}
onMounted(() => {
  getDetail();
});
</script>
<style></style>
