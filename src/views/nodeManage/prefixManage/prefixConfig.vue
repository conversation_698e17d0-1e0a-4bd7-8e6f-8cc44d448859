<template>
  <el-dialog
    v-model="dialogConfigVisible"
    title="前缀配置"
    width="700px"
    align-center
    @close="$emit('close-dialog')"
  >
    <el-form
      ref="configFormref"
      label-position="right"
      label-width="100px"
      :model="configForm"
      :rules="configRules"
    >
      <el-form-item label="前缀" prop="entPrefixId">
        <el-input v-model.trim="configForm.entPrefix" disabled />
      </el-form-item>
      <el-form-item label="IP类型" prop="ipType">
        <el-radio-group v-model="configForm.ipType" class="ml-4">
          <el-radio :label="4" size="large">v4</el-radio>
          <el-radio :label="6" size="large">v6</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="IP地址" prop="ip">
        <el-input v-model.trim="configForm.ip" />
      </el-form-item>
      <el-form-item label="TCP端口" prop="tcpPort">
        <el-input v-model.trim="configForm.tcpPort" />
      </el-form-item>
      <el-form-item label="UDP端口" prop="udpPort">
        <el-input v-model.trim="configForm.udpPort" />
      </el-form-item>
      <el-form-item label="HTTP端口" prop="httpPort">
        <el-input v-model.trim="configForm.httpPort" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogConfigVisible = false">取消</el-button>
        <el-button type="primary" @click="configPrefix"> 确定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { ElMessage, FormInstance } from "element-plus";
import { onMounted, reactive, ref } from "vue";
import { prefixConfig, configDetail } from "@/api/bms/entPrefixManage";

const emit = defineEmits(["close-dialog", "update-table"]);
const props = defineProps({
  item: {
    type: Object,
    default: () => ({}),
  },
});
const configFormref = ref<FormInstance>();
const dialogConfigVisible = ref(true);
const loading = ref(true);
const ipv4 =
  /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/;
const ipv6 =
  /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/;
const configForm = ref({
  entPrefix: "",
  entPrefixId: "",
  ipType: 4,
  ip: "",
  tcpPort: "",
  udpPort: "",
  httpPort: "",
});
// validatePort
const validateTCP = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error("端口不能为空"));
  } else {
    if (/^([1-9][0-9]*)$/.test(value)) {
      if (value > 0 && value < 65535) {
        callback();
      } else {
        callback(new Error("请确认端口范围"));
      }
    } else {
      callback(new Error("请确认端口格式"));
    }
  }
};

const validatePort = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback();
  } else {
    if (/^([1-9][0-9]*)$/.test(value)) {
      if (value > 0 && value < 65535) {
        callback();
      } else {
        callback(new Error("请确认端口范围"));
      }
    } else {
      callback(new Error("请确认端口格式"));
    }
  }
};
const validateIp = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback(new Error("IP不能为空"));
  } else {
    if (
      (configForm.value.ipType === 4 && ipv4.test(value)) ||
      (configForm.value.ipType === 6 && ipv6.test(value))
    ) {
      callback();
    } else {
      callback(new Error("IP格式不正确"));
    }
  }
};
// 配置form校验规则
const configRules = reactive({
  ipType: [
    {
      required: true,
      message: "IP类型不能为空",
      trigger: "change",
    },
  ],
  entPrefixId: [{ required: true, message: "前缀ID不能为空", trigger: "blur" }],
  ip: [{ required: true, validator: validateIp, trigger: "blur" }],
  tcpPort: [{ required: true, validator: validateTCP, trigger: "blur" }],
  udpPort: [{ required: false, validator: validatePort, trigger: "blur" }],
  httpPort: [{ required: false, validator: validatePort, trigger: "blur" }],
});

// 配置操作
const configPrefix = () => {
  if (!configFormref.value) return;
  configFormref.value.validate((valid) => {
    if (valid) {
      prefixConfig(configForm.value).then(() => {
        ElMessage.success("配置成功!");
        emit("update-table");
        dialogConfigVisible.value = false;
      });
    }
  });
};

// 配置详情
const getConfigDetail = () => {
  loading.value = true;
  // 获取配置详情
  configDetail({
    entPrefixId: props.item.id,
  }).then((res: any) => {
    loading.value = false;
    // const { entPrefix } = res;
    configForm.value = res;
    configForm.value.entPrefix = props.item.entPrefix;
    configForm.value.entPrefixId = props.item.id;
  });
};
onMounted(() => {
  getConfigDetail();
});
</script>
<style></style>
