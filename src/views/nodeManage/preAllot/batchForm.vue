<template>
  <el-dialog
    v-model="formData.dialogVisible"
    title="批量上传"
    width="450px"
    align-center
  >
    <table class="demo-typo-size">
      <tbody>
        <tr>
          <td><H2>上传文档格式说明</H2></td>
        </tr>
        <tr>
          <td><br /></td>
        </tr>
        <tr>
          <td>1、下载Excel模板</td>
        </tr>
        <tr>
          <td>2、编辑Excel模板(行数不超过5000行)</td>
        </tr>
        <tr>
          <td>3、点击[上传]按钮，上传Excel文件</td>
        </tr>
        <tr>
          <td>4、若前缀已存在，系统不支持导入</td>
        </tr>
        <tr>
          <td>5、只能上传excel文件，且不超过2MB</td>
        </tr>
      </tbody>
    </table>
    <el-divider />
    <div class="batchUpload">
      <span>要上传的文件</span>
      <el-upload
        ref="uploadRef"
        class="batchUploadDefault"
        v-model:file-list="fileList"
        :auto-upload="false"
        :limit="1"
        :show-file-list="true"
      >
        <el-icon v-if="uploadStatus" class="avatar-uploader-icon"
          ><Plus
        /></el-icon>
        <div v-if="uploadStatus" class="batchUploadText">点击添加</div>
      </el-upload>
    </div>
    <template #footer>
      <span class="dialog-footer" style="display: flex; line-height: 0">
        <el-button @click="props.formData.dialogVisible = false"
          >取消</el-button
        >

        <el-button
          style="margin-left: 12px"
          type="primary"
          :loading="downlowdLoading"
          @click="downlowdFile"
        >
          下载Excel模板
        </el-button>
        <el-button
          style="margin-left: 12px"
          type="primary"
          :loading="submitLoading"
          :disabled="uploadStatus"
          @click="submitUpload"
        >
          上传
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { computed, ref } from "vue";
import { ElMessage, UploadUserFile } from "element-plus";
import { saveAs } from "file-saver";
import { ApiDownload, ApiBatchUpload } from "@/api/bms/entPrefixPreallot";
import { nodeInfo } from "@/api/province/node";

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({}),
  },
  sucessThenQuery: {
    type: Function,
    default: null,
  },
});
// 0默认，1已选择文件未上传 2上传中
const uploadStatus = computed(() => {
  const obj: any = document.getElementsByClassName("el-upload-list");
  if (fileList.value.length === 1) {
    obj[0].style.setProperty("margin-top", "-43px");
    obj[0].style.setProperty("height", "50px");
  }
  if (fileList.value.length === 0) {
    obj[0]?.style.setProperty("margin-top", "0px");
    obj[0]?.style.setProperty("height", "0px");
  }
  return fileList.value.length === 0;
});
// 确定按钮点击效果
const downlowdLoading = ref(false);
const submitLoading = ref(false);
// 文件列表
const fileList = ref<UploadUserFile[]>([]);

const submitUpload = () => {
  const rawFile = fileList.value[0].raw;
  if (!rawFile) {
    return;
  }
  // 后缀获取
  let suffix = "";
  try {
    const flieArr = rawFile.name.split(".");
    suffix = flieArr[flieArr.length - 1];
  } catch (err) {
    suffix = "";
  }
  // 匹配 excel
  const excelist = ["xls", "xlsx"];
  const result = excelist.some((item: any) => item === suffix);
  if (!result) {
    ElMessage.error("文件格式不正确，请上传.xlsx/.xls格式文件");
    submitLoading.value = false;
    return false;
  }
  if (rawFile.size / 1024 / 1024 > 2) {
    ElMessage.error("文件大小不超过2MB");
    submitLoading.value = false;
    return false;
  }
  submitLoading.value = true;
  const fd = new FormData();
  fd.append("file", rawFile);
  ApiBatchUpload(fd)
    .then(
      (res: any) => {
        props.sucessThenQuery();
        ElMessage.success("上传成功");
      },
      (error) => {
        console.log(error);
      }
    )
    .finally(() => {
      submitLoading.value = false;
    });
};

// 获取节点前缀
const nodePrefix = ref("");
async function getNodeInfo() {
  return nodeInfo({}).then((data: any) => {
    nodePrefix.value = data.nodePrefix;
  });
}

async function downlowdFile() {
  downlowdLoading.value = true;
  // 异步获取当前节点前缀
  await getNodeInfo();
  ApiDownload()
    .then(
      (res: any) => {
        const fileName = nodePrefix.value + "-前缀预分配.xlsx";
        const blob = new Blob([res]);
        saveAs(blob, fileName);
      },
      (error) => {
        console.log(error);
        ElMessage.error("下载异常，请稍后再试");
      }
    )
    .finally(() => {
      downlowdLoading.value = false;
    });
}
</script>
<style lang="scss" scoped>
.batchUpload {
  display: flex;
  flex-direction: center;
  align-items: center;
  justify-content: space-around;
}
.batchUploadDefault {
  width: 280px;
  height: 40px;
  margin-left: 20px;
}

.batchUploadText {
  margin-left: 5px;
  color: #8c939d;
  text-align: center;
}
.el-divider--horizontal {
  width: 109.7%;
  margin-left: -4.8%;
}

.avatar-uploader {
  width: 280px;
  height: 40px;
  margin-left: 20px;
}

:deep(.el-upload) {
  width: 100%;
  height: 100%;
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: none;
}

:deep(.el-upload:hover) {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 18px;
  color: #8c939d;
  text-align: center;
}

:deep(.el-icon--document) {
  width: 15px;
  height: 35px;
  background-image: url("@/assets/icons/svg/batchUploadFile.svg");
  color: rgba(0, 0, 0, 0);
  background-repeat: no-repeat;
  background-position: center center;
}
:deep(.el-icon--close) {
  width: 15px;
  height: 20px;
  background-image: url("@/assets/icons/svg/batchUploadRemove.svg");
  color: rgba(0, 0, 0, 0);
  background-repeat: no-repeat;
  background-position: center center;
}
:deep(.el-icon--close:hover) {
  margin-left: 10px;
  width: 15px;
  height: 20px;
  background-image: url("@/assets/icons/svg/batchUploadRemove.svg");
  color: rgba(0, 0, 0, 0);
  background-repeat: no-repeat;
  background-position: center center;
}
:deep(.el-icon--upload-success) {
  width: 15px;
  height: 20px;
  background-image: url("@/assets/icons/svg/batchUploadRemove.svg");
  color: rgba(0, 0, 0, 0);
  background-repeat: no-repeat;
  background-position: center center;
}
:deep(.el-upload-list) {
  width: 280px;
}
:deep(.el-upload-list__item) {
  height: 40px;
}
</style>
