<template>
  <el-dialog
    v-model="formData.dialogVisible"
    :title="title"
    width="30%"
    align-center
    close-on-click-modal="false"
  >
    <el-form
      ref="addFormRef"
      label-position="left"
      :model="addForm"
      label-width="100px"
      :rules="addFormRules"
    >
      <el-form-item label="前缀" prop="entPrefix">
        <el-input
          v-model.trim="addForm.entPrefix"
          clearable
          placeholder="请输入前缀"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="props.formData.dialogVisible = false"
          >取消</el-button
        >
        <el-button
          type="primary"
          :loading="confirmLoading"
          @click="handleConfirm"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { ref, reactive, getCurrentInstance, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { ApiAdd, ApiEdit, ApiQueryDetail } from "@/api/bms/entPrefixPreallot";
import { PREALLOT_TITLE_CODE, PREALLOT_TITLE_MAP } from "@/utils/constant";

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({}),
  },
  sucessThenQuery: {
    type: Function,
    default: null,
  },
});
const { proxy }: any = getCurrentInstance();
// 新增-确定按钮点击效果
const confirmLoading = ref(false);
const title =
  (props.formData.type === PREALLOT_TITLE_CODE.ADD
    ? PREALLOT_TITLE_MAP[PREALLOT_TITLE_CODE.ADD]
    : PREALLOT_TITLE_MAP[PREALLOT_TITLE_CODE.EDIT]) + "前缀";
const addFormRef = ref();
const addForm = reactive({
  id: "",
  entPrefix: "",
});

const entPrefixValidate = (rule: any, value: any, callback: any) => {
  const handleRegex = /^[0-9]\d{1}\.[0-9]\d{3}\.[0-9]\d{3}$/;
  if (!handleRegex.test(value) && value !== "99.1000.1") {
    return callback("格式不正确: 请输入2+4+4格式前缀");
  }
  callback();
};
const addFormRules = reactive({
  entPrefix: [
    { required: true, message: "请填写前缀", trigger: "blur" },
    { max: 30, message: "最大长度为30字符", trigger: "blur" },
    { validator: entPrefixValidate, trigger: "blur" },
  ],
});

// 更新、编辑
async function handleConfirm() {
  proxy.$refs.addFormRef.validate(async (valid: any) => {
    if (valid) {
      confirmLoading.value = true;
      if (props.formData.type === PREALLOT_TITLE_CODE.ADD) {
        ApiAdd(addForm)
          .then(() => {
            ElMessage.success("新增成功");
            props.formData.dialogVisible = false;
            props.sucessThenQuery();
          })
          .finally(() => {
            confirmLoading.value = false;
          });
        return;
      }
      ApiEdit(addForm)
        .then(() => {
          ElMessage.success("更新成功");
          props.formData.dialogVisible = false;
          props.sucessThenQuery();
        })
        .finally(() => {
          confirmLoading.value = false;
        });
    }
  });
}

onMounted(() => {
  if (props.formData.type === PREALLOT_TITLE_CODE.EDIT) {
    ApiQueryDetail({
      id: props.formData.selectData.id,
    }).then((res: any) => {
      addForm.entPrefix = res.entPrefix;
      addForm.id = res.id;
    });
  }
});
</script>
