<template>
  <el-dialog
    v-model="formData.dialogVisible"
    title="预分配前缀审核"
    width="30%"
    align-center
  >
    <el-form
      ref="auditFormRef"
      label-position="left"
      :model="auditForm"
      label-width="100px"
      :rules="auditFormRules"
    >
      <el-descriptions :column="1" direction="horizontal">
        <el-descriptions-item label="认领前缀">
          {{ auditForm?.entPrefix ?? "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="企业编码">
          {{ auditForm?.orgCode ?? "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="企业名称">
          {{ auditForm?.orgName ?? "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="上级单位名称">
          {{ auditForm?.parentOrgName ?? "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="所属省/市/区">
          {{ auditForm?.orgAddrProvinceDesc ?? "-" }}/
          {{ auditForm?.orgAddrCityDesc ?? "-" }}
          {{
            auditForm?.orgAddrDistrictDesc
              ? "/" + auditForm?.orgAddrDistrictDesc
              : ""
          }}
        </el-descriptions-item>
        <el-descriptions-item label="地址">
          {{ auditForm?.orgAddress ?? "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ auditForm?.createdTime ?? "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="认领信息">
          {{ auditForm?.remark ?? "-" }}
        </el-descriptions-item>
      </el-descriptions>
      <el-divider style="width: 109%; margin-left: -4.5%" />
      <el-form-item label="分配审核" prop="status">
        <el-select
          v-model="auditForm.status"
          placeholder="请选择"
          size="large"
          clearable
        >
          <el-option
            v-for="item in data.auditStatusList"
            :key="item.value"
            :value="item.value"
            :label="item.name"
          />
        </el-select>
      </el-form-item>
    </el-form>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="props.formData.dialogVisible = false"
          >取消</el-button
        >
        <el-button
          type="primary"
          :loading="confirmLoading"
          @click="auditHandleConfirm"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { ref, reactive, getCurrentInstance, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { ApiPrefixAudit, ApiQueryDetail } from "@/api/bms/entPrefixPreallot";
import { PREALLOT_AUDIT_CODE, PREALLOT_AUDIT_MAP } from "@/utils/constant";

const props = defineProps({
  formData: {
    type: Object,
    default: () => ({}),
  },
  sucessThenQuery: {
    type: Function,
    default: null,
  },
});
const { proxy }: any = getCurrentInstance();
// 确定按钮点击效果
const confirmLoading = ref(false);

const auditFormRef = ref();

const auditFormRules = reactive({
  status: [{ required: true, message: "请选择审核状态", trigger: "blur" }],
});

const auditForm = ref<DetailForm>({
  entPrefix: "",
  remark: "",
  orgCode: "",
  orgName: "",
  orgAddrProvince: "",
  orgAddrCity: "",
  orgAddrDistrict: "",
  orgAddrProvinceDesc: "",
  orgAddrCityDesc: "",
  orgAddrDistrictDesc: "",
  orgAddress: "",
  createdTime: "",
  id: "",
  parentOrgName: "",
  status: PREALLOT_AUDIT_CODE.PASS,
});

interface DetailForm {
  entPrefix: string;
  remark: string;
  orgCode: string;
  orgName: string;
  orgAddrProvince: string;
  orgAddrCity: string;
  orgAddrDistrict: string;
  orgAddrProvinceDesc: string;
  orgAddrCityDesc: string;
  orgAddrDistrictDesc: string;
  orgAddress: string;
  createdTime: string;
  id: string;
  parentOrgName: string;
  status: number | null;
}

interface Data {
  auditStatusList: {
    value: number;
    name: string;
  }[];
}
const data = reactive<Data>({
  auditStatusList: [
    {
      value: PREALLOT_AUDIT_CODE.PASS,
      name: PREALLOT_AUDIT_MAP[PREALLOT_AUDIT_CODE.PASS],
    },
    {
      value: PREALLOT_AUDIT_CODE.REJECT,
      name: PREALLOT_AUDIT_MAP[PREALLOT_AUDIT_CODE.REJECT],
    },
  ],
});
// 审核提交
async function auditHandleConfirm() {
  proxy.$refs.auditFormRef.validate(async (valid: any) => {
    if (valid) {
      confirmLoading.value = true;
      ApiPrefixAudit(auditForm.value)
        .then(() => {
          ElMessage.success("审批成功");
          props.formData.dialogVisible = false;
          props.sucessThenQuery();
        })
        .finally(() => {
          confirmLoading.value = false;
        });
    }
  });
}

onMounted(() => {
  if (props.formData.dialogVisible) {
    ApiQueryDetail({
      id: props.formData.selectData.id,
    }).then((res: any) => {
      auditForm.value = res;
    });
  }
});
</script>
