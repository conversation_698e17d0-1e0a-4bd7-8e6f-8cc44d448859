<template>
  <div class="summary-card">
    <SearchLayout>
      <template #right>
        <el-form
          v-permission="AUTH_CODE.HOSTING_AUDIT_PAGE"
          :inline="true"
          :model="queryForm"
          @submit.prevent
        >
          <el-form-item
            ><el-input
              v-model.trim="queryForm.entPrefix"
              placeholder="请输入"
              clearable
              @clear="getTableData(true)"
            >
              <template #prefix>企业前缀：</template>
            </el-input></el-form-item
          >
          <el-form-item
            ><el-input
              v-model.trim="queryForm.entName"
              placeholder="请输入"
              clearable
              @clear="getTableData(true)"
            >
              <template #prefix>企业名称：</template>
            </el-input></el-form-item
          >
          <el-form-item
            ><el-select
              v-model="queryForm.auditState"
              placeholder="请选择"
              clearable
              @clear="getTableData(true)"
            >
              <el-option
                v-for="item in auditStateOptions"
                :key="item.id"
                :label="item.value"
                :value="item.id"
              />
              <template #prefix>审核状态：</template>
            </el-select></el-form-item
          >
          <el-form-item
            ><el-date-picker
              v-model="queryForm.dateTime"
              type="daterange"
              range-separator="-"
              start-placeholder="操作开始时间"
              end-placeholder="操作结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled-date="disabledDate"
              clearable
              @change="datePickerChange"
          /></el-form-item>
          <el-form-item style="width: 60px; max-width: 60px"
            ><el-button type="primary" @click="() => getTableData(true)"
              >搜索</el-button
            ></el-form-item
          >
        </el-form>
      </template>
    </SearchLayout>
    <div class="auditTable">
      <el-table
        border
        :data="tableData"
        style="width: 100%"
        v-loading="pageLoading"
        size="small"
      >
        <el-table-column label="序号" type="index" width="55" />
        <el-table-column
          prop="entPrefix"
          label="企业前缀"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span v-copy="scope.row.entPrefix">{{
              scope.row.entPrefix || "-"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="entName" label="企业名称" show-overflow-tooltip>
          <template #default="scope">
            <span v-copy="scope.row.entName">{{
              scope.row.entName || "-"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="auditState" label="审核状态">
          <template #default="scope">
            <span v-if="scope.row.auditState === '1'">已取消</span>
            <span v-if="scope.row.auditState === '2'">待审核</span>
            <span v-if="scope.row.auditState === '3'">已驳回</span>
            <span v-if="scope.row.auditState === '4'">已通过</span>
          </template>
        </el-table-column>
        <el-table-column prop="updatedTime" label="操作时间" />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button
              text
              type="primary"
              @click="handleDetail(scope.row)"
              v-if="scope.row.auditState !== '2'"
              v-permission="AUTH_CODE.HOSTING_AUDIT_DETAIL"
            >
              详情
            </el-button>
            <el-button
              text
              type="primary"
              @click="hosting(scope.row.entPrefix)"
              v-if="scope.row.auditState === '2'"
              v-permission="AUTH_CODE.HOSTING_AUDIT_DISTRBUTION"
              >托管</el-button
            >
            <el-popconfirm
              :width="200"
              confirm-button-text="确定"
              cancel-button-text="取消"
              title="取消托管后，在此托管服务注册的标识数据将无法正常解析，确认取消托管？"
              @confirm="cancelHosting(scope.row.entPrefix)"
              v-if="scope.row.auditState === '4'"
            >
              <template #reference>
                <el-button
                  text
                  type="primary"
                  v-permission="AUTH_CODE.HOSTING_AUDIT_CANCEL"
                  >取消托管</el-button
                >
              </template>
            </el-popconfirm>
            <el-popconfirm
              :width="200"
              confirm-button-text="确定"
              cancel-button-text="取消"
              :title="`确认驳回${scope.row.entPrefix}（企业前缀）的托管申请吗？`"
              @confirm="rejectApply(scope.row.entPrefix)"
              v-if="scope.row.auditState === '2'"
            >
              <template #reference>
                <el-button
                  text
                  type="primary"
                  v-permission="AUTH_CODE.HOSTING_AUDIT_REJECT"
                  >驳回</el-button
                >
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 30, 40]"
        small
        background
        layout="total,  prev, pager, next, sizes,jumper"
        :total="pagination.total"
      />
    </div>
  </div>
  <allotSever
    v-if="allotVisible"
    @update-table="getTableData"
    @close-dialog="allotVisible = false"
    :entPrefix="selectEntPrefix"
  ></allotSever>
  <allotDetail
    v-if="drawerVisible"
    :entPrefix="selectEntPrefix"
    :auditState="selectAuditState"
    @close="drawerVisible = false"
    @update-table="getTableData"
  ></allotDetail>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import {
  getAuditList,
  apiRejectApply,
  apiCancelAllot,
} from "@/api/province/hosting/audit";
import allotSever from "./allotSever.vue";
import allotDetail from "./allotDetail.vue";
import { TableDataItem, ISearchParams } from "@/types/entAllot";
import { AUTH_CODE } from "@/utils/authCode";
import store from "@/store";
import SearchLayout from "@/components/searchLayout/index.vue";

const pageLoading = ref(false);
const tableData = ref<TableDataItem[]>([]);
const queryForm = reactive<{
  entPrefix: string;
  entName: string;
  auditState: string;
  dateTime: Array<any>;
}>({
  entPrefix: "",
  entName: "",
  auditState: "",
  dateTime: [],
});
const auditStateOptions = ref([
  { id: "1", value: "已取消" },
  { id: "2", value: "待审核" },
  { id: "3", value: "已驳回" },
  { id: "4", value: "已通过" },
]);
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

const allotVisible = ref(false);
const drawerVisible = ref(false);
const selectEntPrefix = ref("");
const selectAuditState = ref("");

watch([() => pagination.currentPage, () => pagination.pageSize], () => {
  getTableData(false);
});

const disabledDate = (time: Date) => {
  return time.getTime() > new Date().getTime();
};

const getTableData = (isReset: boolean) => {
  if (isReset) {
    pagination.currentPage = 1;
    pagination.pageSize = 10;
    pagination.total = 0;
  }
  pageLoading.value = true;
  const { currentPage, pageSize } = pagination;
  const params: ISearchParams = {
    entPrefix: queryForm.entPrefix,
    entName: queryForm.entName,
    auditState: queryForm.auditState,
    startTime: "",
    endTime: "",
    page: currentPage - 1,
    size: pageSize,
  };
  if (queryForm.dateTime && queryForm.dateTime.length) {
    const [startTime, endTime] = queryForm.dateTime;
    params.startTime = startTime;
    params.endTime = endTime.replace("00:00:00", "23:59:59");
  }
  getAuditList(params)
    .then((res) => {
      tableData.value = res.content;
      pagination.total = res.totalCount;
    })
    .finally(() => {
      pageLoading.value = false;
    });
};

const handleDetail = (row: TableDataItem) => {
  drawerVisible.value = true;
  selectEntPrefix.value = row.entPrefix;
  selectAuditState.value = row.auditState;
};

const hosting = (entPrefix: string) => {
  allotVisible.value = true;
  selectEntPrefix.value = entPrefix;
};

const cancelHosting = (entPrefix: string) => {
  apiCancelAllot({ entPrefix }).then(() => {
    ElMessage({
      message: "取消托管成功",
      type: "success",
    });
    getTableData(true);
  });
};

const rejectApply = (entPrefix: string) => {
  apiRejectApply({ entPrefix }).then(() => {
    ElMessage({
      message: "驳回申请成功",
      type: "success",
    });
    getTableData(true);
  });
};

const datePickerChange = (val: any) => {
  if (!val) {
    getTableData(true);
  }
};

onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (AllPermission.includes(AUTH_CODE.PREFIX_PREPARE_PAGE)) {
    getTableData(true);
  }
});
</script>
<style lang="scss" scoped></style>
