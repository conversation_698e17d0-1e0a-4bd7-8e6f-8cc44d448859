<template>
  <el-dialog
    v-model="dialogServerVisible"
    title="分配托管服务器"
    width="1000px"
    @close="$emit('close-dialog')"
    destroy-on-close
    align-center
  >
    <el-table
      :data="severTableData"
      highlight-current-row
      style="width: 100%"
      border
      size="small"
      v-loading="pageLoading"
    >
      <el-table-column label="选择" width="65">
        <template #default="scope">
          <el-radio
            v-model="currentRadio"
            :label="scope.row.id"
            @change="handleTableCurrentChange(scope.row.id)"
            >&nbsp;</el-radio
          >
        </template>
      </el-table-column>
      <el-table-column label="托管服务器" show-overflow-tooltip>
        <template #default="scope">
          <span v-copy="scope.row.srvName">{{ scope.row.srvName || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="IP类型" show-overflow-tooltip>
        <template #default="scope">
          <span>{{
            scope.row.ipType === 4
              ? "IPv4"
              : scope.row.ipType === 6
              ? "IPv6"
              : "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="公网IP地址" show-overflow-tooltip>
        <template #default="scope">
          <span>{{ scope.row.ip ? scope.row.ip : "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="HTTP端口">
        <template #default="scope">
          <span>{{ scope.row.httpPort ? scope.row.httpPort : "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="TCP端口">
        <template #default="scope">
          <span>{{ scope.row.tcpPort ? scope.row.tcpPort : "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="UDP端口">
        <template #default="scope">
          <span>{{ scope.row.udpPort ? scope.row.udpPort : "-" }}</span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="pagination.currentPage"
      v-model:page-size="pagination.pageSize"
      :page-sizes="[10, 20, 30, 40]"
      size="small"
      background
      layout="total,  prev, pager, next, sizes,jumper"
      :total="pagination.total"
    />
    <template #footer>
      <span>
        <el-button @click="dialogServerVisible = false">取消</el-button>
        <el-button type="primary" @click="allotSever" v-loading="btnLoading">
          分配
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { onMounted, reactive, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { getSeverList, allotHostingSever } from "@/api/province/hosting/audit";
import { SeverTableDataItem } from "@/types/entAllot";

const emit = defineEmits(["update-table"]);
const props = defineProps({
  entPrefix: {
    type: String,
    default: "",
  },
});
const btnLoading = ref(false);
const pageLoading = ref(false);
const dialogServerVisible = ref(true);
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});
const currentRadio = ref("");

const severTableData = ref<SeverTableDataItem[]>([]);
const currentHostingServerId = ref("");

watch([() => pagination.currentPage, () => pagination.pageSize], () => {
  getTableData(false);
});

const allotSever = () => {
  btnLoading.value = true;
  if (currentHostingServerId.value === "") {
    ElMessage({
      message: "请选择服务器",
      type: "error",
    });
    btnLoading.value = false;
    return;
  }
  allotHostingSever({
    entPrefix: props.entPrefix,
    hostingServerId: currentHostingServerId.value,
  })
    .then(() => {
      dialogServerVisible.value = false;
      emit("update-table");
      ElMessage({
        message: "分配托管服务器成功",
        type: "success",
      });
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

const getTableData = (isReset: boolean) => {
  if (isReset) {
    pagination.currentPage = 1;
    pagination.pageSize = 10;
    pagination.total = 0;
  }
  pageLoading.value = true;
  const { currentPage, pageSize } = pagination;
  getSeverList({
    page: currentPage - 1,
    size: pageSize,
  })
    .then((res) => {
      severTableData.value = res.content;
      pagination.total = res.totalCount;
    })
    .finally(() => {
      pageLoading.value = false;
    });
};

const handleTableCurrentChange = (serverId: string) => {
  currentHostingServerId.value = serverId;
};

onMounted(() => {
  getTableData(true);
});
</script>
<style lang="scss" scoped></style>
