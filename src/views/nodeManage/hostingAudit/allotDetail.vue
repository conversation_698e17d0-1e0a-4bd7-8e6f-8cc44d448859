<template>
  <el-drawer v-model="drawerVisible" title="托管详情">
    <div v-loading="loading">
      <div class="yc-description" v-if="props.auditState !== '3'">
        <el-popconfirm
          :width="200"
          confirm-button-text="确定"
          cancel-button-text="取消"
          title="取消托管后，在此托管服务注册的标识数据将无法正常解析，确认取消托管？"
          v-if="props.auditState === '4'"
          @confirm="cancelHosting"
        >
          <template #reference>
            <el-button text type="primary" class="btn-position"
              >取消托管</el-button
            >
          </template>
        </el-popconfirm>
        <div class="aiiot-detail-title">服务器信息</div>
        <el-descriptions :column="2" direction="horizontal">
          <el-descriptions-item label="企业前缀">{{
            detail?.entPrefix || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="托管服务器">{{
            detail?.srvName || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="公网IP">{{
            detail?.ipReslove || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="IP类型">{{
            detail?.ipTypeReslove === 4
              ? "IPv4"
              : detail?.ipTypeReslove === 6
              ? "IPv6"
              : "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="tcp端口">{{
            detail?.tcpPortReslove || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="udp端口">{{
            detail?.udpPortReslove || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="http端口">{{
            detail?.httpPortReslove || "-"
          }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <el-divider v-if="props.auditState !== '3'" />
      <div class="yc-description">
        <div class="aiiot-detail-title">审核信息</div>
        <el-descriptions :column="2" direction="horizontal">
          <el-descriptions-item label="审核结果">{{
            detail?.auditResultMsg || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="审核时间">{{
            detail?.updatedTime || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="审核人">{{
            detail?.updatedBy || "-"
          }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { onMounted, ref, defineProps } from "vue";
import { ElMessage } from "element-plus";
import {
  apiGetAllotDetail,
  apiCancelAllot,
} from "@/api/province/hosting/audit";
import { IAllotDetail } from "@/types/entAllot";

const emit = defineEmits(["update-table"]);
const props = defineProps({
  entPrefix: {
    type: String,
    default: null,
  },
  auditState: {
    type: String,
    default: null,
  },
});

const drawerVisible = ref(true);

const loading = ref(true);

const detail = ref<IAllotDetail>();

// 获取实例详情
function getAllotDetail() {
  if (!props.entPrefix) return;
  loading.value = true;
  apiGetAllotDetail({ entPrefix: props.entPrefix })
    .then((response) => {
      detail.value = response;
    })
    .finally(() => {
      loading.value = false;
    });
}

function cancelHosting() {
  apiCancelAllot({ entPrefix: props.entPrefix }).then(() => {
    ElMessage({
      message: "取消托管成功",
      type: "success",
    });
    drawerVisible.value = false;
    emit("update-table", true);
  });
}

onMounted(() => {
  getAllotDetail();
});
</script>
<style lang="scss" scoped>
.el-divider {
  margin: 8px 0 24px 0;
}
:deep(.el-descriptions__label) {
  width: 72px;
  min-width: 72px;
  display: inline-block;
}
.aiiot-detail-title {
  color: #86909c;
  margin-bottom: 16px;
  font-size: 12px;
}
.yc-description {
  position: relative;
}
.btn-position {
  font-size: 12px;
  padding: 0;
  height: 12px;
  position: absolute;
  top: 0;
  right: 0;
}
</style>
