<template>
  <SearchLayout>
    <template #right>
      <el-form
        v-permission="AUTH_CODE.HOSTING_APPLY_PAGE"
        :inline="true"
        :model="searchForm"
      >
        <el-form-item style="width: 240px; max-width: 240px">
          <el-input
            v-model.trim="searchForm.entPrefix"
            clearable
            placeholder="请输入"
            @clear="getTableData(true)"
          >
            <template #prefix>企业前缀：</template></el-input
          >
        </el-form-item>
        <el-form-item style="width: 240px; max-width: 240px">
          <el-date-picker
            v-model="searchForm.dateTime"
            type="daterange"
            range-separator="-"
            start-placeholder="操作开始时间"
            end-placeholder="操作结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="disabledDate"
            clearable
            @change="datePickerChange"
          />
        </el-form-item>
        <el-form-item style="width: 60px; max-width: 60px">
          <el-button type="primary" @click="getTableData(true)">搜索</el-button>
        </el-form-item>
      </el-form></template
    >
  </SearchLayout>
  <div class="auditTable">
    <el-table
      border
      :data="tableData"
      style="width: 100%"
      v-loading="pageLoading"
      size="small"
    >
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column prop="entPrefix" label="企业前缀" show-overflow-tooltip>
        <template #default="scope">
          <span v-copy="scope.row.entPrefix">{{
            scope.row.entPrefix || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="hostingState" label="托管状态">
        <template #default="scope">
          <span v-if="scope.row.hostingState === '1'">未托管</span>
          <span v-if="scope.row.hostingState === '2'">审核中</span>
          <span v-if="scope.row.hostingState === '3'">被驳回</span>
          <span v-if="scope.row.hostingState === '4'">已托管</span>
        </template>
      </el-table-column>
      <el-table-column prop="createdTime" label="创建时间">
        <template #default="scope">
          <span>{{ scope.row.createdTime || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="updatedTime" label="操作时间">
        <template #default="scope">
          <span>{{ scope.row.updatedTime || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120">
        <template #default="scope">
          <el-button
            text
            type="primary"
            @click="applyAllot(scope.row.entPrefix)"
            v-if="scope.row.hostingState === '1'"
            v-permission="AUTH_CODE.HOSTING_APPLY_APPLY"
          >
            申请托管
          </el-button>
          <el-button
            text
            type="primary"
            @click="cancelAllot(scope.row.entPrefix)"
            v-if="scope.row.hostingState === '3'"
            v-permission="AUTH_CODE.HOSTING_APPLY_APPLY"
            >取消托管</el-button
          >
          <span
            style="height: 32px; line-height: 32px"
            v-if="
              scope.row.hostingState === '2' || scope.row.hostingState === '4'
            "
          >
            -
          </span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="pagination.currentPage"
      v-model:page-size="pagination.pageSize"
      :page-sizes="[10, 20, 30, 40]"
      small
      background
      layout="total,  prev, pager, next, sizes,jumper"
      :total="pagination.total"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import {
  getApplyList,
  apiApplayAllot,
  apiCancelAllot,
} from "@/api/province/hosting/apply";
import { ApplyTableDataItem } from "@/types/entAllot";
import { AUTH_CODE } from "@/utils/authCode";
import store from "@/store";
import SearchLayout from "@/components/searchLayout/index.vue";

const pageLoading = ref(false);
const searchForm = reactive<{
  entPrefix: string;
  dateTime: Array<any>;
}>({
  entPrefix: "",
  dateTime: [],
});
const tableData = ref<ApplyTableDataItem[]>([]);
const disabledDate = (time: Date) => {
  return time.getTime() > new Date().getTime();
};
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

watch([() => pagination.currentPage, () => pagination.pageSize], () => {
  getTableData(false);
});
const getTableData = (isReset: boolean) => {
  if (isReset) {
    pagination.currentPage = 1;
    pagination.pageSize = 10;
    pagination.total = 0;
  }
  pageLoading.value = true;
  const { currentPage, pageSize } = pagination;
  const params: {
    entPrefix: string;
    startTime: string;
    endTime: string;
    page: number;
    size: number;
  } = {
    entPrefix: searchForm.entPrefix,
    startTime: "",
    endTime: "",
    page: currentPage - 1,
    size: pageSize,
  };
  if (searchForm.dateTime && searchForm.dateTime.length) {
    const [startTime, endTime] = searchForm.dateTime;
    params.startTime = startTime;
    params.endTime = endTime.replace("00:00:00", "23:59:59");
  }
  getApplyList(params)
    .then((res) => {
      tableData.value = res.content;
      pagination.total = res.totalCount;
    })
    .finally(() => {
      pageLoading.value = false;
    });
};

function applyAllot(entPrefix: string) {
  apiApplayAllot({ entPrefix }).then(() => {
    ElMessage({
      message: "申请托管成功",
      type: "success",
    });
    getTableData(true);
  });
}
function cancelAllot(entPrefix: string) {
  apiCancelAllot({ entPrefix }).then(() => {
    ElMessage({
      message: "取消托管成功",
      type: "success",
    });
    getTableData(true);
  });
}

function datePickerChange(val: any) {
  if (!val) {
    getTableData(true);
  }
}

onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (AllPermission.includes(AUTH_CODE.HOSTING_APPLY_PAGE)) {
    getTableData(true);
  }
});
</script>
<style lang="scss" scoped>
.el-form {
  text-align: right;
}
</style>
