<template>
  <SearchLayout>
    <template #left>
      <el-button
        type="primary"
        @click="handleAdd"
        v-permission="AUTH_CODE.PREFIX_PREPARE_ADD"
        >新增前缀</el-button
      >
      <el-button
        type="primary"
        plain
        @click="handleBatch"
        v-permission="AUTH_CODE.PREFIX_PREPARE_BATCH_ADD"
        >批量新增</el-button
      >
    </template>
    <template #right>
      <el-form
        v-permission="AUTH_CODE.PREFIX_PREPARE_PAGE"
        :inline="true"
        :model="searchForm"
        @submit.prevent
      >
        <el-form-item
          ><el-input
            v-model.trim="searchForm.entPrefix"
            clearable
            placeholder="企业前缀"
            @clear="handleSearch"
          >
            <template #prefix>企业前缀：</template></el-input
          ></el-form-item
        >
        <el-form-item
          ><el-select
            v-model="searchForm.status"
            placeholder="请选择"
            clearable
            @clear="handleSearch"
          >
            <el-option
              v-for="item in data.statusList"
              :key="item.value"
              :value="item.value"
              :label="item.name"
            />
            <template #prefix>认领状态：</template>
          </el-select></el-form-item
        >
        <el-form-item
          ><el-date-picker
            v-model="searchForm.claimTime"
            type="daterange"
            range-separator="-"
            start-placeholder="认领开始时间"
            end-placeholder="认领结束时间"
            value-format="YYYY-MM-DD HH:mm:ss"
            :disabled-date="disabledDate"
            clearable
            @change="handleTimeClear()"
        /></el-form-item>
        <el-form-item style="width: 60px; max-width: 60px"
          ><el-button
            type="primary"
            :loading="data.searchLoading"
            @click="handleSearch"
            >搜索</el-button
          ></el-form-item
        >
      </el-form>
    </template>
  </SearchLayout>
  <div class="page-search-body">
    <el-table
      :data="data.tableData"
      v-loading="data.tableLoading"
      border
      size="small"
    >
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column property="id" v-if="false" />
      <el-table-column
        property="entPrefix"
        label="企业前缀"
        show-overflow-tooltip
      >
        <template #default="scope">
          <span v-copy="scope.row.entPrefix">{{
            scope.row.entPrefix || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="认领状态">
        <template #default="scope">
          <span
            v-if="scope.row.status === PREALLOT_AUDIT_STATUS_CODE.UNCLAIMED"
            >{{
              PREALLOT_AUDIT_STATUS_MAP[PREALLOT_AUDIT_STATUS_CODE.UNCLAIMED]
            }}</span
          >
          <span
            v-if="scope.row.status === PREALLOT_AUDIT_STATUS_CODE.CLAIMED"
            >{{
              PREALLOT_AUDIT_STATUS_MAP[PREALLOT_AUDIT_STATUS_CODE.CLAIMED]
            }}</span
          >
          <span
            v-if="scope.row.status === PREALLOT_AUDIT_STATUS_CODE.REJECTED"
            >{{
              PREALLOT_AUDIT_STATUS_MAP[PREALLOT_AUDIT_STATUS_CODE.REJECTED]
            }}</span
          >
        </template>
      </el-table-column>
      <el-table-column
        property="orgName"
        label="认领企业"
        show-overflow-tooltip
      >
        <template #default="scope">
          {{ scope.row.orgName || "-" }}
        </template>
      </el-table-column>
      <el-table-column property="remark" label="认领信息" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.remark || "-" }}
        </template>
      </el-table-column>
      <el-table-column property="claimTime" label="认领时间">
        <template #default="scope">
          {{ scope.row.claimTime || "-" }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button
            v-if="scope.row.status !== PREALLOT_AUDIT_STATUS_CODE.CLAIMED"
            type="primary"
            text
            @click="handleEdit(scope.$index, scope.row)"
            v-permission="AUTH_CODE.PREFIX_PREPARE_EDIT"
            >编辑</el-button
          >
          <el-button
            type="primary"
            text
            v-if="scope.row.status === PREALLOT_AUDIT_STATUS_CODE.CLAIMED"
            @click="handleAudit(scope.$index, scope.row)"
            v-permission="AUTH_CODE.PREFIX_PREPARE_AUDIT"
            >审核</el-button
          >
          <el-popconfirm
            v-if="scope.row.status !== PREALLOT_AUDIT_STATUS_CODE.CLAIMED"
            :width="200"
            confirm-button-text="确定"
            cancel-button-text="取消"
            title="是否确认删除？"
            @confirm="handleDeleteConfirm(scope.row.id)"
          >
            <template #reference>
              <el-button
                text
                type="primary"
                v-permission="AUTH_CODE.PREFIX_PREPARE_DELETE"
                >删除</el-button
              >
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      v-model:page="data.page"
      v-model:page-size="data.size"
      :page-sizes="[10, 20, 30, 40]"
      small
      layout="total, sizes, prev, pager, next, jumper"
      :total="data.totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
  <!-- 新增前缀 -->
  <addForm
    v-if="addDialogFormVisible"
    :formData="addFormData"
    :sucessThenQuery="sucessThenQuery"
  ></addForm>
  <!-- 批量上传 -->
  <batchForm
    v-if="batchDialogFormVisible"
    :formData="batchFormData"
    :sucessThenQuery="sucessThenQuery"
  ></batchForm>
  <!-- 前缀审核 -->
  <auditForm
    v-if="auditDialogFormVisible"
    :formData="auditFormData"
    :sucessThenQuery="sucessThenQuery"
  ></auditForm>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { ApiQueryList, ApiDeleteById } from "@/api/bms/entPrefixPreallot";
import { AUTH_CODE } from "@/utils/authCode";
import addForm from "./preAllot/addForm.vue";
import auditForm from "./preAllot/auditForm.vue";
import batchForm from "./preAllot/batchForm.vue";
import {
  PREALLOT_TITLE_CODE,
  PREALLOT_AUDIT_STATUS_CODE,
  PREALLOT_AUDIT_STATUS_MAP,
} from "@/utils/constant";
import store from "@/store";
import SearchLayout from "@/components/searchLayout/index.vue";

const addDialogFormVisible = ref(false);
const addFormData = reactive<any>({
  dialogVisible: addDialogFormVisible,
  selectData: {
    id: "",
    entPrefix: "",
  },
  type: PREALLOT_TITLE_CODE.ADD,
});

const auditDialogFormVisible = ref(false);
const auditFormData = reactive<any>({
  dialogVisible: auditDialogFormVisible,
});

const batchDialogFormVisible = ref(false);
const batchFormData = reactive<any>({
  dialogVisible: batchDialogFormVisible,
});

const disabledDate = (time: Date) => {
  return time.getTime() > new Date().getTime();
};

const searchForm = reactive({
  entPrefix: "",
  status: "",
  claimTime: [] as any,
});

interface Data {
  tableData: {
    entPrefix: string;
    status: string;
    claimEnt: string;
    claimInfo: string;
    claimTime: string;
  }[];
  statusList: {
    value: number;
    name: string;
  }[];
  page: number;
  size: number;
  totalCount: number;
  selectData: any;
  deleteLoading: boolean;
  tableLoading: boolean;
  searchLoading: boolean;
}
const data = reactive<Data>({
  tableData: [],
  statusList: [
    {
      value: PREALLOT_AUDIT_STATUS_CODE.UNCLAIMED,
      name: PREALLOT_AUDIT_STATUS_MAP[PREALLOT_AUDIT_STATUS_CODE.UNCLAIMED],
    },
    {
      value: PREALLOT_AUDIT_STATUS_CODE.CLAIMED,
      name: PREALLOT_AUDIT_STATUS_MAP[PREALLOT_AUDIT_STATUS_CODE.CLAIMED],
    },
    {
      value: PREALLOT_AUDIT_STATUS_CODE.REJECTED,
      name: PREALLOT_AUDIT_STATUS_MAP[PREALLOT_AUDIT_STATUS_CODE.REJECTED],
    },
  ],
  page: 1,
  size: 10,
  totalCount: 1,
  selectData: null,
  // 列表loading
  tableLoading: false,
  // 搜索按钮状态控制
  searchLoading: false,
  // 删除状态控制
  deleteLoading: false,
});

// 子组件操作成功查询列表
function sucessThenQuery() {
  batchDialogFormVisible.value = false;
  addDialogFormVisible.value = false;
  auditDialogFormVisible.value = false;
  data.page = 1;
  getTableData();
}

// 查询列表
function getTableData() {
  data.tableLoading = true;
  data.tableData = [];
  const params = {
    entPrefix: searchForm.entPrefix,
    status: searchForm.status,
    page: data.page - 1,
    size: data.size,
    startTime: "",
    endTime: "",
  };
  if (searchForm.claimTime && searchForm.claimTime.length) {
    const [startTime, endTime] = searchForm.claimTime;
    params.startTime = startTime;
    params.endTime = endTime.replace("00:00:00", "23:59:59");
  }
  data.tableLoading = false;
  data.searchLoading = false;
  ApiQueryList(params)
    .then((response: any) => {
      const result = response;
      data.tableData = result?.content || [];
      // data.page = result?.pageNumber || 1;
      data.totalCount = result?.totalCount || 0;
    })
    .finally(() => {
      data.tableLoading = false;
      data.searchLoading = false;
    });
}

// 清空操作时间，查询数据
function handleTimeClear() {
  if (!searchForm.claimTime || searchForm.claimTime.length === 0) {
    handleSearch();
  }
}

// 查询按钮
function handleSearch() {
  data.searchLoading = true;
  data.page = 1;
  getTableData();
}

// 修改页码
function handleSizeChange(num: number) {
  data.size = num;
  data.page = 1;
  getTableData();
}

function handleCurrentChange(num: number) {
  data.page = num;
  getTableData();
}

// 批量上传按钮
function handleBatch() {
  batchDialogFormVisible.value = true;
}

// 新增按钮
function handleAdd() {
  addDialogFormVisible.value = true;
  addFormData.selectData = null;
  // 新增模式
  addFormData.type = PREALLOT_TITLE_CODE.ADD;
}

// 审核按钮
function handleAudit(index: any, item: any) {
  auditDialogFormVisible.value = true;
  auditFormData.selectData = item;
}

// 删除按钮
function handleDeleteConfirm(id: any) {
  data.deleteLoading = true;
  ApiDeleteById(id)
    .then(() => {
      ElMessage.success("删除成功");
      data.page = 1;
      getTableData();
    })
    .finally(() => {
      data.deleteLoading = false;
    });
}

// 编辑按钮
function handleEdit(index: any, item: any) {
  addDialogFormVisible.value = true;
  addFormData.selectData = item;
  // 修改模式
  addFormData.type = PREALLOT_TITLE_CODE.EDIT;
}

onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (AllPermission.includes(AUTH_CODE.PREFIX_PREPARE_PAGE)) {
    getTableData();
  }
});
</script>
<style lang="scss" scoped>
.popover-delete-tips {
  margin-bottom: 20px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .popover-delete-tips-text {
    margin-left: 10px;
    font-size: 14px;
  }
}
.popover-delete-wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
</style>
