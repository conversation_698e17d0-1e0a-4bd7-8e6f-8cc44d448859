<template>
  <el-drawer
    v-model="dialogFormVisible"
    @close="$emit('close-password-dialog')"
    title="前缀申请详情"
  >
    <el-descriptions :column="1" direction="horizontal">
      <el-descriptions-item label="企业名称">
        {{ props.itemData.orgName ?? "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="企业代码">
        {{ props.itemData.orgCode ?? "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="上级单位名称">
        {{ props.itemData.parentOrgName ?? "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="所属省/市/区">
        {{ props.itemData.orgAddrProvinceDesc ?? "-" }}/
        {{ props.itemData.orgAddrCityDesc ?? "-" }}/
        {{ props.itemData.orgAddrDistrictDesc ?? "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="地址">
        {{ props.itemData.orgAddress ?? "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="企业前缀">
        {{ props.itemData.entPrefix ?? "-" }}
      </el-descriptions-item>
    </el-descriptions>
    <el-divider />
  </el-drawer>
</template>

<script setup lang="ts">
import { ref } from "vue";

// const props = defineProps(["itemData"]);
const props = defineProps({
  itemData: {
    type: Object,
    default: () => ({}),
  },
});

const dialogFormVisible = ref(true);
</script>

<style lang="scss" scoped>
:deep(.el-descriptions__label) {
  width: 72px;
  min-width: 72px;
  display: inline-block;
}
</style>
