<template>
  <SearchLayout>
    <template #left>
      <el-button
        type="primary"
        @click="add"
        v-permission="AUTH_CODE.PREFIX_APPLY_ADD"
        >申请前缀</el-button
      >
    </template>
    <template #right>
      <el-form
        v-permission="AUTH_CODE.PREFIX_APPLY_PAGE"
        :inline="true"
        :model="formData"
      >
        <el-form-item style="width: 240px; max-width: 240px">
          <el-input
            v-model.trim="formData.entPrefix"
            placeholder="请输入"
            @clear="getTableData(true)"
            clearable
          >
            <template #prefix>企业前缀：</template>
          </el-input>
        </el-form-item>
        <el-form-item style="width: 60px; max-width: 60px">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
        </el-form-item>
      </el-form>
    </template>
  </SearchLayout>
  <el-table :data="tableData.list" border size="small" v-loading="tableLoading">
    <el-table-column label="序号" type="index" width="55" />
    <el-table-column label="企业前缀" :show-overflow-tooltip="true">
      <template #default="scope">
        <span v-copy="scope.row.entPrefix">{{
          scope.row.entPrefix || "-"
        }}</span>
      </template>
    </el-table-column>
    <el-table-column label="企业名称" :show-overflow-tooltip="true">
      <template #default="scope">
        <span v-copy="scope.row.orgName">{{ scope.row.orgName || "-" }}</span>
      </template>
    </el-table-column>
    <el-table-column label="企业代码" :show-overflow-tooltip="true">
      <template #default="scope">
        <span v-copy="scope.row.orgCode">{{ scope.row.orgCode || "-" }}</span>
      </template>
    </el-table-column>
    <el-table-column label="上级单位名称" :show-overflow-tooltip="true">
      <template #default="scope">
        {{ scope.row.parentOrgName || "-" }}
      </template>
    </el-table-column>
    <el-table-column label="所属省">
      <template #default="scope">
        {{ scope.row.orgAddrProvinceDesc || "-" }}
      </template>
    </el-table-column>
    <el-table-column label="所属市">
      <template #default="scope">
        {{ scope.row.orgAddrCityDesc || "-" }}
      </template>
    </el-table-column>
    <el-table-column label="所属区">
      <template #default="scope">
        {{ scope.row.orgAddrDistrictDesc || "-" }}
      </template>
    </el-table-column>
    <el-table-column label="地址" :show-overflow-tooltip="true">
      <template #default="scope">
        {{ scope.row.orgAddress || "-" }}
      </template>
    </el-table-column>
    <el-table-column prop="auditStatus" label="审核状态">
      <template #default="scope">
        <span v-if="scope.row.auditStatus === 1">待审核</span>
        <span v-else-if="scope.row.auditStatus === 2">审核通过</span>
        <span v-else-if="scope.row.auditStatus === 3">被驳回</span>
        <span v-else>-</span>
      </template>
    </el-table-column>
    <el-table-column label="操作" width="60px">
      <template #default="scope">
        <el-button
          text
          type="primary"
          @click="clickPasswordBtn(scope.row)"
          v-permission="AUTH_CODE.PREFIX_APPLY_DETAIL"
        >
          查看
        </el-button>
      </template>
    </el-table-column>
  </el-table>
  <el-pagination
    v-model:current-page="pagination.currentPage"
    v-model:page-size="pagination.pageSize"
    :page-sizes="[10, 20, 30, 40]"
    small
    background
    layout="total,  prev, pager, next, sizes,jumper"
    :total="pagination.total"
  />
  <ApplyInfo
    v-if="infoVisible"
    @close-password-dialog="infoVisible = false"
    :itemData="itemData"
  />
  <ApplyDetail
    v-if="applyVisible"
    @close-password-dialog="applyVisible = false"
    :applyData="applyData"
    @update-table="getTableData"
  />
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch } from "vue";
import { AUTH_CODE } from "@/utils/authCode";
import {
  applyList,
  applyDetail,
  applyItemDetail,
} from "@/api/province/prefix/apply";
import ApplyInfo from "./applyInfo.vue";
import ApplyDetail from "./applyDetail.vue";
import store from "@/store";
import SearchLayout from "@/components/searchLayout/index.vue";

const formData = reactive({
  entPrefix: "",
});

const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

const tableLoading = ref(false);

interface List {
  auditStatus: number;
  createdTime: string;
  entPrefix: string | null;
  id: number;
  orgAddrCity: string;
  orgAddrCityDesc: string;
  orgAddrDistrict: string;
  orgAddrDistrictDesc: string;
  orgAddrProvince: string;
  orgAddrProvinceDesc: string;
  orgAddress: string;
  orgCode: string;
  orgName: string;
  parentOrgName: string;
  updatedTime: string;
}
const tableData: { list: List[] } = reactive({
  list: [],
});

const infoVisible = ref(false);
const applyVisible = ref(false);

const itemData = ref();
const applyData = ref();

onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (AllPermission.includes(AUTH_CODE.PREFIX_APPLY_PAGE)) {
    getTableData(true);
  }
});

watch([() => pagination.currentPage, () => pagination.pageSize], () => {
  getTableData(false);
});

const getTableData = (isReset: boolean) => {
  if (isReset) {
    pagination.currentPage = 1;
    pagination.pageSize = 10;
    pagination.total = 0;
  }
  const { currentPage, pageSize } = pagination;
  tableLoading.value = true;
  applyList({
    entPrefix: formData.entPrefix,
    page: currentPage - 1,
    size: pageSize,
  })
    .then((res: any) => {
      const { content, totalCount } = res;
      pagination.total = totalCount;
      tableData.list = content || [];
    })
    .finally(() => {
      tableLoading.value = false;
    });
};

const clickPasswordBtn = async (row: any) => {
  itemData.value = await applyItemDetail({ id: row.id });
  infoVisible.value = true;
};
const add = async () => {
  applyData.value = await applyDetail();
  applyVisible.value = true;
};

function handleSearch() {
  getTableData(true);
}
</script>

<style lang="scss" scoped></style>
