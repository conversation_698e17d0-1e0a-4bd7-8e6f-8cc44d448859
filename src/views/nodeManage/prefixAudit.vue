<template>
  <div class="app-container">
    <SearchLayout>
      <template #right
        ><el-form :inline="true" @submit.prevent>
          <el-form-item style="width: 240px; max-width: 240px">
            <el-input
              v-model="searchForm.entPrefix"
              placeholder="请输入"
              clearable
            >
              <template #prefix>企业前缀：</template>
            </el-input>
          </el-form-item>
          <el-form-item style="width: 60px; max-width: 60px">
            <el-button
              :loading="searchLoading"
              type="primary"
              @click="handleSearch"
              v-permission="AUTH_CODE.PREFIX_AUDIT_PAGE"
              >搜索</el-button
            >
          </el-form-item>
        </el-form></template
      >
    </SearchLayout>

    <el-table
      :data="tableData"
      v-loading="tableLoading"
      type="selection"
      size="small"
      border
    >
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column prop="entPrefix" label="企业前缀" show-overflow-tooltip>
        <template #default="scope">
          <span v-copy="scope.row.entPrefix">{{
            scope.row.entPrefix || "-"
          }}</span>
        </template>
      </el-table-column>

      <el-table-column prop="orgName" label="单位名称" show-overflow-tooltip />

      <el-table-column
        prop="parentOrgName"
        label="上级单位名称"
        show-overflow-tooltip
      />
      <el-table-column prop="orgAddrProvinceDesc" label="所属省" />

      <el-table-column prop="orgAddrCityDesc" label="所属市" />
      <el-table-column prop="orgAddrDistrictDesc" label="所属区" />

      <!-- <el-table-column prop="createdTime" label="创建时间"></el-table-column> -->

      <el-table-column label="操作" width="100">
        <template #default="scope">
          <el-button
            text
            type="primary"
            @click="openDetailDialog(scope.$index, scope.row)"
            v-permission="AUTH_CODE.PREFIX_AUDIT_DETAIL"
          >
            详情
          </el-button>
          <el-button
            text
            type="primary"
            @click="openAuditDialog(scope.$index, scope.row)"
            v-permission="AUTH_CODE.PREFIX_AUDIT_AUDIT"
          >
            审核
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:currentPage="pagination.currentPage"
      v-model:page-size="pagination.pageSize"
      :page-sizes="[10, 20, 30, 40]"
      small
      background
      layout="total,  prev, pager, next, sizes,jumper"
      :total="pagination.total"
    />
    <el-dialog
      v-model="dialogAuditVisible"
      :before-close="closeDialog"
      title="前缀审核"
      width="700px"
      append-to-body
      align-center
    >
      <el-form label-position="right" label-width="100px" :model="auditForm">
        <el-form-item label="组织机构名称">
          <el-input v-model.trim="auditForm.data.orgName" disabled />
        </el-form-item>
        <el-form-item label="组织机构代码">
          <el-input v-model.trim="auditForm.data.orgCode" disabled />
        </el-form-item>
        <el-form-item label="企业前缀">
          <el-input v-model.trim="auditForm.data.entPrefix" disabled />
        </el-form-item>
        <el-form-item label="上级单位名称">
          <el-input v-model.trim="auditForm.data.parentOrgName" disabled />
        </el-form-item>
        <el-form-item label="所属省">
          <el-input
            v-model.trim="auditForm.data.orgAddrProvinceDesc"
            disabled
          />
        </el-form-item>
        <el-form-item label="所属市">
          <el-input v-model.trim="auditForm.data.orgAddrCityDesc" disabled />
        </el-form-item>

        <el-form-item label="所属区">
          <el-input
            v-model.trim="auditForm.data.orgAddrDistrictDesc"
            disabled
          />
        </el-form-item>

        <el-form-item label="审核结果">
          <el-select v-model="auditReusult" placeholder="请选择" size="large">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialog">取消</el-button>
          <el-button
            type="primary"
            :loading="auditBtnLoading"
            @click="auditPrefix"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
    <el-drawer
      v-model="dialogAuditDetailVisible"
      title="审核详情"
      v-loading="detailLoading"
    >
      <el-descriptions :column="1" direction="horizontal">
        <el-descriptions-item label="审核状态"> 未审核 </el-descriptions-item>
        <el-descriptions-item label="组织机构名称">
          {{ auditForm.data.orgName ?? "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="组织机构代码">
          {{ auditForm.data.orgCode ?? "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="企业前缀">
          {{ auditForm.data.entPrefix ?? "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="上级单位名称">
          {{ auditForm.data.parentOrgName ?? "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="所属省/市/区">
          {{ auditForm.data.orgAddrProvinceDesc ?? "-" }}/
          {{ auditForm.data.orgAddrCityDesc ?? "-" }}/
          {{ auditForm.data.orgAddrDistrictDesc ?? "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ auditForm.data.createdTime ?? "-" }}
        </el-descriptions-item>
      </el-descriptions>
      <el-divider />
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted } from "vue";
import { ElMessage } from "element-plus";
import {
  fetchAuditList,
  prefixAuditdetail,
  PrefixAudit,
} from "@/api/bms/entPrefixAudit";
import { AUTH_CODE } from "@/utils/authCode";
import store from "@/store";
import SearchLayout from "@/components/searchLayout/index.vue";

interface TableData {
  auditStatus: number;
  createdTime: string;
  entId: number;
  entPrefix: string;
  id: number;
  orgAddrCity: string;
  orgAddrCityDesc: string;
  orgAddrDistrict: string;
  orgAddrDistrictDesc: string;
  orgAddrProvince: string;
  orgAddrProvinceDesc: string;
  orgAddress: string;
  orgCode: string;
  orgName: string;
  parentOrgName: string;
  updatedTime: string;
}
const tableData = ref<TableData[]>([]);

const auditReusult = ref(true);

const tableLoading = ref(false);

const detailLoading = ref(false);

const searchLoading = ref(false);

const auditBtnLoading = ref(false);

const searchForm = reactive({
  entPrefix: "",
});

const auditForm = reactive({
  data: {
    id: 0,
    auditStatus: null,
    entPrefix: "",
    orgName: "67",
    orgCode: 0,
    parentOrgName: 0,
    orgAddrProvinceDesc: "",
    orgAddrCityDesc: "",
    orgAddrDistrictDesc: "",
    orgAddrProvinceName: "",
    createdTime: "",
    updatedTime: "",
  },
});

const options = [
  {
    value: true,
    label: "审核通过",
  },
  {
    value: false,
    label: "驳回",
  },
];

const dialogAuditVisible = ref(false);
const dialogAuditDetailVisible = ref(false);
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});
onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (AllPermission.includes(AUTH_CODE.PREFIX_AUDIT_PAGE)) {
    getTableData(true);
  }
});

watch([() => pagination.currentPage, () => pagination.pageSize], () => {
  getTableData(false);
});

function handleSearch() {
  searchLoading.value = true;
  pagination.currentPage = 1;
  getTableData(false);
}

const closeDialog = () => {
  dialogAuditVisible.value = false;
  auditReusult.value = true;
};
const closeDetailDialog = () => {
  dialogAuditDetailVisible.value = false;
};

// 前缀审核
const auditPrefix = () => {
  auditBtnLoading.value = true;
  // 审核接口
  PrefixAudit({ id: auditForm.data.id, auditPass: auditReusult.value })
    .then(() => {
      ElMessage.success("操作成功!");
      closeDialog();
      auditReusult.value = true;
      getTableData(false);
    })
    .finally(() => {
      auditBtnLoading.value = false;
    });
};

// 审核详情页面
const openDetailDialog = (index: any, row: any) => {
  detailLoading.value = true;
  prefixAuditdetail({
    id: row.id,
  })
    .then((res: any) => {
      dialogAuditDetailVisible.value = true;
      auditForm.data = res;
      auditForm.data.entPrefix = row.entPrefix;
    })
    .finally(() => {
      detailLoading.value = false;
    });
};

// 审核列表
const getTableData = (isGetAll: boolean) => {
  if (isGetAll) {
    pagination.currentPage = 1;
    pagination.pageSize = 10;
    pagination.total = 0;
  }
  const { currentPage, pageSize } = pagination;
  const prefix = searchForm.entPrefix;
  tableLoading.value = true;
  fetchAuditList({
    entPrefix: isGetAll ? "" : prefix,
    size: pageSize,
    page: currentPage - 1,
  })
    .then((res: any) => {
      const { content, totalCount } = res;
      tableData.value = content;
      pagination.total = totalCount;
    })
    .finally(() => {
      searchLoading.value = false;
      tableLoading.value = false;
    });
};
// 审核页面
const openAuditDialog = (index: any, row: any) => {
  dialogAuditVisible.value = true;
  prefixAuditdetail({
    id: row.id,
  }).then((res: any) => {
    dialogAuditVisible.value = true;
    auditForm.data = res;
  });
};
</script>
<style lang="scss" scoped>
.app-container {
  background-color: white;

  .optioncls {
    font-size: 14px;
    margin-right: 4px;
    cursor: pointer;
  }
  .detailOption {
    color: yellowgreen;
  }
  .auditOption {
    color: blue;
  }
}
:deep(.el-input--large .el-input__inner) {
  height: 32px;
  line-height: 32px;
  // padding: 0 15px;
}
.el-row {
  margin: 20px 0;
  span {
    font-size: 14px;
    font-weight: 500;
  }
}

:deep(.el-descriptions__label) {
  width: 72px;
  min-width: 72px;
  display: inline-block;
}
</style>
