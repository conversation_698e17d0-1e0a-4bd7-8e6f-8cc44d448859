<template>
  <div class="industry-portal" v-loading="true"></div>
</template>

<script setup lang="ts">
import { onMounted, computed, ref, onUnmounted } from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
import request from "@/utils/request";
import { setToken, removeToken } from "@/utils/auth";

const timer = ref();

const globalConfig = computed(() => store.getters.globalConfig);

const store = useStore();

onMounted(() => {
  removeToken();
  request({
    url: "/v1/industry-portal/login",
    method: "POST",
  })
    .then((res: any) => {
      setToken(res.token);
      store.dispatch("SET_TOKEN", res.token);
      window.location.href = "/";
    })
    .catch((error: any) => {
      timer.value = setTimeout(() => {
        window.location.href = globalConfig.value?.industryPortalUrl;
      }, 2 * 1000);
    });
});

onUnmounted(() => {
  if (timer.value) {
    clearTimeout(timer.value);
  }
});
</script>

<style scoped lang="scss">
.industry-portal {
  height: 100%;
  width: 100%;
}
</style>
