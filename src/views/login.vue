<template>
  <div class="login-content main-login-content">
    <div
      class="login-left"
      :style="{
        backgroundImage: 'url(' + proxyBackground + ')',
      }"
    >
      <div
        v-if="globalConfig.systemType === SYSTEM_TYPE.SYSTEM_PROVINCE"
        class="proxy"
      >
        <div class="shadow"></div>
        <img src="../assets/logo/logo-transparent.png" alt="" class="icon" />
        <div class="title">
          <h2>
            {{ SYSTEM_NAME.SYSTEM_NAME_PROVINCE }}
          </h2>
          <h1>省级标识业务管理系统</h1>
        </div>
      </div>

      <div
        v-if="globalConfig.systemType === SYSTEM_TYPE.SYSTEM_PROXY"
        class="proxy"
      >
        <div class="shadow"></div>
        <img src="../assets/logo/logo-transparent.png" alt="" class="icon" />
        <div class="title">
          <h2>
            {{ SYSTEM_NAME.SYSTEM_NAME_PROXY }}
          </h2>
          <h1>企业标识业务管理系统</h1>
        </div>
      </div>
    </div>
    <div class="login-right">
      <!-- 用户名密码 -->
      <div class="login-box">
        <img src="../assets/logo/logo-gradient.png" alt="" class="icon" />
        <div class="login-box-title">
          <h2 v-if="globalConfig.systemType === SYSTEM_TYPE.SYSTEM_PROVINCE">
            {{ SYSTEM_NAME.SYSTEM_NAME_PROVINCE }}
          </h2>
          <h2 v-if="globalConfig.systemType === SYSTEM_TYPE.SYSTEM_PROXY">
            {{ SYSTEM_NAME.SYSTEM_NAME_PROXY }}
          </h2>
          <h3 v-if="globalConfig.systemType === SYSTEM_TYPE.SYSTEM_PROVINCE">
            省级标识业务管理系统
          </h3>
          <h3 v-if="globalConfig.systemType === SYSTEM_TYPE.SYSTEM_PROXY">
            企业标识业务管理系统
          </h3>
          <p>欢迎登录</p>
        </div>
        <div class="login-box-tips">
          {{ isSocialUser ? "业务中台登录" : "密码登录" }}
        </div>
        <!-- 登录框 输入用户名密码-->
        <div class="login-form">
          <el-form
            ref="loginRef"
            :model="loginForm"
            :rules="loginRules"
            key="ruleForm"
          >
            <el-form-item
              prop="provinceId"
              class="form-item"
              v-if="globalConfig.isTenant === IS_TENANT.TENANT_Y"
            >
              <el-select
                v-model="loginForm.provinceId"
                placeholder="请选择节点"
                style="width: 100%"
                clearable
              >
                <el-option
                  v-for="item in tenantOptions.list"
                  :key="item.provinceId"
                  :label="item.name"
                  :value="item.provinceId"
                />
                <template #prefix
                  ><svg-icon icon-class="tree" class="tenant-Option"
                /></template>
              </el-select>
            </el-form-item>
            <el-form-item prop="username" class="form-item">
              <el-input
                name="username"
                v-model="loginForm.username"
                type="text"
                :placeholder="isSocialUser ? '请输入业务中台账号' : '账号'"
                icon="User"
                clearable
              >
                <template #prefix
                  ><svg-icon
                    icon-class="user"
                    class="el-input__icon input-icon"
                /></template>
              </el-input>
            </el-form-item>
            <el-form-item prop="password" class="form-item">
              <el-input
                v-model.trim="loginForm.password"
                type="password"
                :placeholder="isSocialUser ? '请输入业务中台密码' : '密码'"
                show-password
                clearable
                @keyup.enter="handleLogin"
              >
                <template #prefix
                  ><svg-icon
                    icon-class="password"
                    class="el-input__icon input-icon"
                /></template>
              </el-input>
            </el-form-item>
            <!-- <div class="find-password">
              <el-button text type="primary" @click="recoverPassword()"
                >找回密码</el-button
              >
            </div> -->
            <el-form-item class="form-item">
              <el-button
                :loading="loading"
                type="primary"
                size="large"
                class="submit-btn"
                @click.prevent="handleLogin"
              >
                {{ !loading ? "登 录" : "登 录 中..." }}
              </el-button>
            </el-form-item>
            <!-- <el-form-item
              class="form-item"
              v-if="globalConfig.systemType === SYSTEM_TYPE.SYSTEM_PROVINCE"
            >
              <el-button
                type="primary"
                size="large"
                class="submit-btn"
                @click.prevent="changeBusinessMiddle"
              >
                业务中台账号登录
              </el-button>
            </el-form-item> -->
            <!-- <el-form-item class="form-item">
              <el-button
                type="primary"
                size="large"
                class="submit-btn"
                @click.prevent="handleGoQuery"
              >
                标识查询
              </el-button>
            </el-form-item> -->
          </el-form>
        </div>
      </div>
      <!-- 业务中台登录 -->
      <!-- <businessMiddleForm :data="data" /> -->
      <bindAccount
        v-if="data.currentPage === CURRENT_PAGE.BIND_ACCOUNT"
        :data="data"
        :tenantOptions="tenantOptions.list"
      />
      <forgetPassword
        v-if="data.currentPage === CURRENT_PAGE.FORGET_PASSWORD"
        :data="data"
        :tenantOptions="tenantOptions.list"
      ></forgetPassword>
      <login-footer></login-footer>
    </div>
    <verify
      v-if="showVerify"
      :captcha-type="'blockPuzzle'"
      :img-size="{ width: '400px', height: '200px' }"
      @cancel="handleCancel"
      @success="handleSuccess"
    ></verify>
  </div>
</template>

<script setup lang="ts">
import Cookies from "js-cookie";
import { useStore } from "vuex";
import { useRouter } from "vue-router";
import {
  computed,
  getCurrentInstance,
  ref,
  reactive,
  onMounted,
  toRefs,
} from "vue";
import forgetPassword from "./login/forgetPassword.vue";
import businessMiddleForm from "./login/businessMiddleForm.vue";
import bindAccount from "./login/bindAccount.vue";
import Verify from "@/components/verifition/Verify.vue";
import LoginFooter from "@/layout/LoginFooter.vue";
import { getPublicKey, encrypt } from "@/utils/forgeEncrypt";
import {
  SYSTEM_TYPE,
  SYSTEM_NAME,
  CURRENT_PAGE,
  IS_TENANT,
  LOGIN_MODEL,
} from "@/utils/constant";
import provinceBackground from "../assets/images/login/login_bg_left.png";
import proxyBackground from "../assets/images/login/Frame.jpg";
import { getProvinceTenantList } from "@/api/login";

const data = reactive<any>({
  currentPage: CURRENT_PAGE.LOGIN,
});
const store = useStore();
const router = useRouter();
const { proxy }: any = getCurrentInstance();

const { isSocialUser } = toRefs(store.getters);

const globalConfig = computed(() => store.getters.globalConfig);

interface TenantList {
  provinceId: string;
  name: string;
}
const tenantOptions: { list: TenantList[] } = reactive({
  list: [],
});

interface LoginForm {
  provinceId: string;
  username: string;
  password: string;
  rememberMe?: boolean;
  code?: string;
  uuid?: string;
}

const loginForm = ref<LoginForm>({
  provinceId: "",
  username: "",
  password: "",
  rememberMe: false,
  code: "",
  uuid: "",
});

// 校验租户id
const checkProvinceId = (rule: any, value: string, callback: any) => {
  const checkVal = loginForm.value.provinceId + "";
  if (globalConfig.value.isTenant === IS_TENANT.TENANT_Y && !checkVal) {
    return callback(new Error("请选择节点"));
  }
  callback();
};

const loginRules = {
  provinceId: [{ required: true, validator: checkProvinceId, trigger: "blur" }],
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "blur", message: "请输入验证码" }],
};

const loading = ref(false);
const redirect = ref(undefined);

// 滑块验证码是否显示
const showVerify = ref(false);

// 找回密码
function recoverPassword() {
  data.currentPage = CURRENT_PAGE.FORGET_PASSWORD;
}

// 滑块-取消
function handleCancel() {
  loading.value = false;
  showVerify.value = false;
}

// 滑块-成功
async function handleSuccess(data: any) {
  const { captchaVerification } = data;
  const publicKey = await getPublicKey().catch(() => {
    loading.value = false;
  });
  console.log("globalConfig:", globalConfig.value);
  const loginFormTemp = {
    username: loginForm.value.username,
    password: globalConfig.value?.login.passwordEncrypted
      ? encrypt(loginForm.value.password, publicKey)
      : loginForm.value.password,
    captcha: captchaVerification,
    provinceId: loginForm.value.provinceId,
  };
  if (isSocialUser.value) {
    store
      .dispatch("LoginWithMiddle", loginFormTemp)
      .then(() => {
        router.push({ path: redirect.value || "/" });
      })
      .catch(() => {
        loading.value = false;
      });
    return;
  }
  store
    .dispatch("Login", loginFormTemp)
    .then(() => {
      router.push({ path: redirect.value || "/" });
    })
    .catch(() => {
      loading.value = false;
    });
}

function handleLogin() {
  proxy.$refs.loginRef.validate(async (valid: any) => {
    if (valid) {
      loading.value = true;
      if (globalConfig.value.login.captchaEnable) {
        showVerify.value = true;
        return;
      }
      const publicKey = await getPublicKey().catch(() => {
        loading.value = false;
      });
      console.log("globalConfig:", globalConfig.value);
      const loginFormTemp = {
        username: loginForm.value.username,
        password: globalConfig.value?.login.passwordEncrypted
          ? encrypt(loginForm.value.password, publicKey)
          : loginForm.value.password,
        provinceId: loginForm.value.provinceId,
      };
      if (isSocialUser.value) {
        store
          .dispatch("LoginWithMiddle", loginFormTemp)
          .then(() => {
            router.push({ path: redirect.value || "/" });
          })
          .catch(() => {
            loading.value = false;
          });
        return;
      }
      store
        .dispatch("Login", loginFormTemp)
        .then(() => {
          router.push({ path: redirect.value || "/" });
        })
        .catch(() => {
          loading.value = false;
        });
    }
  });
}

function getCookie() {
  const username = Cookies.get("username");
  const password = Cookies.get("password");
  const rememberMe = Cookies.get("rememberMe");
  const provinceId = Cookies.get("provinceId");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password:
      password === undefined ? loginForm.value.password : encrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
    provinceId:
      provinceId === undefined ? loginForm.value.provinceId : provinceId,
  };
}

getCookie();

function changeBusinessMiddle() {
  data.currentPage = CURRENT_PAGE.BUSINESS_MIDDLE;
}

function handleGoQuery() {
  router.push("/query");
}

// 获取租户列表
const getTenantOptions = () => {
  // 0所有1过滤国家烟草
  const formTemp = {
    type: 0,
  };
  getProvinceTenantList(formTemp).then((res) => {
    tenantOptions.list = Array.isArray(res) ? res : [];
  });
};

onMounted(() => {
  if (globalConfig.value?.loginModel === LOGIN_MODEL.SSO) {
    window.location.href = globalConfig.value?.industryPortalUrl;
    return;
  }
  // 多租户模式初始化多租户下拉框（isTenant 0不是 1是）
  if (globalConfig.value.isTenant === IS_TENANT.TENANT_Y) {
    getTenantOptions();
  }
});
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.login-content {
  width: 100%;
  height: 100%;
  min-height: 7rem;
  overflow: hidden;
  position: relative;
  display: flex;
  .login-left {
    width: calc(100% - 5.5rem);
    height: 100%;
    background: url(../assets/images/login/Frame.jpg) no-repeat;
    background-size: 100% 100%;
    background-position: top;
    position: relative;
    .province {
      .icon {
        width: 1.69rem;
        height: 1.87rem;
        position: absolute;
        left: 1rem;
        bottom: 1.06rem;
      }
      .title {
        width: 6rem;
        position: absolute;
        left: 3.15rem;
        bottom: 1.1rem;
        h1 {
          font-size: 0.44rem;
          font-family: PingFang SC-中黑体, PingFang SC;
          font-weight: normal;
          color: rgba(255, 255, 255, 0.85);
          line-height: 0.5rem;
          margin: 0;
        }
        h2 {
          font-size: 0.34rem;
          color: #ffffff;
        }
        p {
          font-size: 0.19rem;
          font-family: PingFang SC-常规体, PingFang SC;
          font-weight: normal;
          color: rgba(255, 255, 255, 0.69);
          line-height: 0.25rem;
          margin: 0;
        }
      }
    }
    .proxy {
      .icon {
        // width: 1.69rem;
        height: 1.4rem;
        position: absolute;
        left: 1rem;
        bottom: 0.3rem;
      }
      .shadow {
        width: 100%;
        height: 2rem;
        background-color: rgba(0, 0, 0, 0.7);
        position: absolute;
        bottom: 0;
      }
      .title {
        width: 6rem;
        position: absolute;
        left: 2.6rem;
        bottom: 0.4rem;
        h1 {
          font-size: 0.44rem;
          font-family: PingFang SC-中黑体, PingFang SC;
          font-weight: normal;
          color: rgba(255, 255, 255, 0.85);
          line-height: 0.5rem;
          margin: 0;
        }
        h2 {
          font-size: 0.34rem;
          color: #ffffff;
          margin-bottom: 0.15rem;
        }
        p {
          font-size: 0.19rem;
          font-family: PingFang SC-常规体, PingFang SC;
          font-weight: normal;
          color: rgba(255, 255, 255, 0.69);
          line-height: 0.25rem;
          margin: 0;
        }
      }
    }
  }
  .login-right {
    width: 5.5rem;
    height: 100%;
    background: url(../assets/images/login/login_bg_right.png) no-repeat;
    background-size: 100% 100%;
    position: relative;
    .login-box {
      width: 3.7rem;
      height: 4.5rem;
      position: absolute;
      left: 0.9rem;
      top: calc(50% - 4rem);
      .icon {
        display: block;
        width: 0.98rem;
        height: 1.11rem;
        margin: 0 auto 0.35rem;
      }
      .login-box-title {
        text-align: center;
        h2 {
          font-size: 0.33rem;
          font-family: PingFang SC-中黑体, PingFang SC;
          color: rgba(0, 0, 0, 0.85);
          line-height: 0.46rem;
          margin: 0;
        }
        h3 {
          margin-top: 0.05rem;
        }
        p {
          font-size: 0.14rem;
          color: rgba(0, 0, 0, 0.45);
          line-height: 0.2rem;
          margin: 0.2rem 0 0 0;
        }
      }
      .login-box-tips {
        width: calc(100% - 0.28rem);
        margin: 0.05rem auto 0;
        line-height: 0.5rem;
        font-size: 0.16rem;
        color: #00a57c;
        font-weight: 600;
        border-bottom: 1px solid #bdc2cf;
        position: relative;
        &::after {
          content: "";
          width: 1.37rem;
          height: 2px;
          background-color: #00a57c;
          position: absolute;
          bottom: 0;
          left: 0;
        }
      }
      .login-form {
        padding-top: 0.24rem;
        tenant-Option {
          display: flex;
          align-items: center;
        }
        .form-item {
          margin: 0 0 0.26rem;
          .el-form-item__content {
            line-height: 0.4rem;
            display: flex;
            justify-content: space-between;
          }
          .el-input {
            height: 0.4rem;
            line-height: 0.4rem;
            input {
              height: 0.4rem;
              line-height: 0.4rem;
            }
          }
          &.code {
            .el-input {
              width: 58%;
            }
            .login-code {
              width: 40%;
              height: 0.38rem;
              line-height: 0.38rem;
              img {
                width: 100%;
                height: inherit;
                // vertical-align: middle;
                border-radius: 3px;
              }
            }
          }
          .submit-btn {
            width: 100%;
            height: 0.4rem;
            line-height: 0.4rem;
            padding: 0;
            border-radius: 0.2rem;
            background-color: #00a57c;
          }
          .cancle-btn {
            width: 100%;
            height: 0.4rem;
            line-height: 0.4rem;
            padding: 0;
            margin-top: 0.2rem;
            border-radius: 0.2rem;
          }
        }
        .find-password {
          text-align: right;
          width: 100%;
          margin-top: -0.3rem;
          margin-bottom: 0.3rem;
          .el-button {
            font-size: 10px;
            padding: 0;
          }
        }
      }
    }
  }
}
</style>
