<template>
  <el-dialog
    class="handle-detail-dialog"
    v-model="dialogVisible"
    :title="title"
    @close="handleClose"
  >
    <div class="add-or-edit-dialog" v-loading="loading">
      <el-form
        :model="form"
        ref="formRef"
        label-width="90px"
        :rules="rules"
        label-position="left"
      >
        <el-form-item
          class="form-item-handle"
          required
          label="数据服务"
          prop="dataServiceId"
        >
          <el-select
            v-model="form.dataServiceId"
            :disabled="isEdit"
            placeholder="请选择"
            @change="handleServiceChange"
          >
            <el-option
              v-for="item in dataServiceList"
              :key="item.id"
              :value="item.id"
              :label="item.dataServiceName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数据库名称" prop="databaseId">
          <el-select
            v-model="form.databaseId"
            placeholder="请选择"
            :disabled="isEdit"
            @change="handleDatabaseChange"
          >
            <el-option
              v-for="item in databaseList"
              :key="item.databaseId"
              :value="item.databaseId"
              :label="item.databaseName"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="数据库IP" required>
          <el-input
            v-model.trim="form.databaseIp"
            placeholder="选择数据库后自动获取"
            disabled
          />
        </el-form-item>
        <el-form-item
          :class="{ 'form-item-pl10': isEdit }"
          label="建库语句"
          :required="!isEdit"
        >
          <el-upload
            ref="uploadRef"
            action=""
            :class="[
              'batchUploadDefault',
              fileList.length ? 'fileUpload' : 'fileUpload',
            ]"
            :file-list="fileList"
            :limit="1"
            :show-file-list="true"
            :before-upload="beforeUpload"
            :http-request="handleUpload"
            :on-remove="handleRemove"
            :on-change="handleChange"
            accept=".sql"
          >
            <el-icon v-if="showUpload" class="uploader-icon"><Plus /></el-icon>
            <div v-if="showUpload" class="batchUploadText">点击添加</div>
          </el-upload>
        </el-form-item>
        <el-form-item label="描述" class="form-item-pl10" prop="description">
          <el-input
            v-model.trim="form.description"
            maxLength="200"
            placeholder="请输入描述"
          />
        </el-form-item>
      </el-form>
    </div>
    <el-divider class="dialog-divider"></el-divider>
    <div class="desc-wrapper">
      <div class="desc-title">建库语句导入说明</div>
      <div class="desc-content">
        <div class="desc-item">1、上传.sql建表语句；</div>
        <div class="desc-item">2、建库语句需要包含表和字段的描述；</div>
        <div class="desc-item">3、重新导入建库语句会覆盖已存在的建库语句。</div>
      </div>
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="confirmLoading"
          @click="handleConfirm"
        >
          上传
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, UploadUserFile } from "element-plus";
import {
  getDataServiceApi,
  getDatabaseListApi,
  addMetaDataApi,
  updateMetaDataApi,
  getMetaDataDetailApi,
} from "@/api/metaData";

interface Idatabase {
  databaseId: number;
  databaseName: string;
  databaseIp: string;
}
const props = defineProps({
  id: String || Number,
});

const databaseList = ref<Idatabase[]>([]);

const emit = defineEmits(["close", "success"]);

const formRef = ref();

const dataServiceList = ref<any>([]);

const form = reactive<any>({
  dataServiceId: "", // 数据服务ID
  databaseName: "", // 数据库名称
  databaseId: "", // 数据库ID
  databaseIp: "", // 数据库IP
  description: "", // 描述
  file: [],
});

const isEdit = ref(false);

const title = ref("导入元数据对象");

const dialogVisible = ref(true);

const confirmLoading = ref(false);

const loading = ref(false);

const handleClose = () => {
  emit("close");
};

const fileValidate = (rule: any, value: any, callback: any) => {
  if (!isEdit.value && !form.file) {
    return callback("请上传建语句");
  }
  callback();
};

const rules = reactive({
  dataServiceId: [
    { required: true, message: "请选择数据服务", trigger: "change" },
  ],
  databaseId: [
    { required: true, message: "请选择数据库名称", trigger: "change" },
  ],
  file: [{ required: true, validator: fileValidate }],
});

const handleConfirm = async () => {
  const basicInfoValidate = await formValidate();
  if (basicInfoValidate) return;
  if (!isEdit.value) {
    addMetaData();
    return;
  }
  updateMetaData();
};

// 新增标识
const addMetaData = () => {
  confirmLoading.value = true;
  const formData = new FormData();
  for (const formField of Object.keys(form)) {
    formData.append(formField, form[formField]);
  }
  addMetaDataApi(formData)
    .then(() => {
      ElMessage.success("新增成功");
      emit("close");
      emit("success");
    })
    .finally(() => {
      confirmLoading.value = false;
    });
};

// 编辑标识
const updateMetaData = () => {
  confirmLoading.value = true;
  const formData = new FormData();
  for (const formField of Object.keys(form)) {
    formData.append(formField, form[formField]);
  }
  formData.append("id", props.id as any);
  updateMetaDataApi(formData)
    .then(() => {
      ElMessage.success("更新成功");
      emit("close");
      emit("success");
    })
    .finally(() => {
      confirmLoading.value = false;
    });
};

const getDataServiceList = () => {
  getDataServiceApi().then((response: any) => {
    dataServiceList.value = response;
  });
};
const handleServiceChange = (val: number) => {
  form.databaseId = "";
  form.databaseName = "";
  form.databaseIp = "";
  databaseList.value = [];
  getDatabaseListApi(val).then((res: any) => {
    if (res instanceof Array) {
      databaseList.value = res;
    }
  });
};
const handleDatabaseChange = (val: number) => {
  const databaseItem = databaseList.value.filter(
    (item) => item.databaseId === val
  )[0];
  form.databaseName = databaseItem.databaseName;
  form.databaseIp = databaseItem.databaseIp;
};
const beforeUpload = (file: any) => {
  const fileSuffix = file.name.substring(file.name.lastIndexOf(".") + 1);
  if (fileSuffix !== "sql") {
    ElMessage.error("上传文件格式不正确");
    return false;
  }
};
const handleUpload = (file: any) => {
  form.file = file.file;
  fileList.value = [file.file];
  formRef.value.validateField("file");
};
const showUpload = ref(true);
const handleRemove = () => {
  form.file = "";
  fileList.value = [];
  formRef.value.validateField("file");
  setTimeout(() => {
    showUpload.value = true;
  }, 300);
};
const handleChange = () => {
  showUpload.value = false;
};
const fileList = ref<UploadUserFile[]>([]);

async function formValidate() {
  let isError = false;
  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  return isError;
}

function getMetaDataDetail() {
  loading.value = true;
  return getMetaDataDetailApi(props.id as any)
    .then(async (response: any) => {
      [
        "dataServiceId",
        "databaseName",
        "databaseId",
        "databaseIp",
        "description",
      ].forEach((field: string) => {
        form[field] = response[field];
      });
      form.file = "";
      if (form.dataServiceId) {
        getDatabaseListApi(form.dataServiceId).then((res: any) => {
          if (res instanceof Array) {
            databaseList.value = res;
          }
        });
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

onMounted(async () => {
  isEdit.value = !!props.id;
  title.value = `${isEdit.value ? "编辑元数据对象" : "导入元数据对象"}`;

  getDataServiceList();
  if (isEdit.value) {
    await getMetaDataDetail();
  }
});
</script>
<style scoped lang="scss">
.add-or-edit-dialog {
  padding: 0 40px;
  .el-select {
    width: 100%;
  }
  .batchUploadDefault {
    width: 100%;
    height: 32px;
    // padding: 6px 12px;
    background-color: #eef2f1;
    display: flex;
  }
  .fileUpload {
    :deep(.el-upload-list) {
      width: 100%;
      margin: 0;
    }
  }
  .el-icon.uploader-icon {
    font-size: 18px;
    color: #8c939d;
    text-align: center;
    margin-left: 12px;
  }
  .batchUploadText {
    margin-left: 5px;
    color: #8c939d;
    text-align: center;
    line-height: 20px;
    min-width: 56px;
  }
}
.dialog-divider {
  margin: 24px -20px;
  width: calc(100% + 40px);
  border-top: 1px solid #e7e9f0;
}
.desc-wrapper {
  padding: 0 40px;
  .desc-title {
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    margin-bottom: 10px;
    color: #272e2c;
  }
  .desc-content {
    color: #272e2c;
    font-style: normal;
    font-size: 12px;
    font-weight: 400;
    line-height: 22px;
  }
}
.components-handle-detail-line {
  border-top: 1px solid #e5e8ef;
  margin-bottom: 24px;
}

.form-item-handle {
  :deep(.el-input-group__prepend) {
    padding-right: 0;
  }
  .form-item-handle-line {
    border-left: 1px solid #dcdfe6;
    padding: 0 10px;
    margin-left: 20px;
  }
}
</style>
