<template>
  <el-drawer
    v-model="drawerVisible"
    title="数据库详情"
    size="850"
    @closed="handleClose"
  >
    <search-layout>
      <template #right>
        <el-form :inline="true" :model="searchForm" @submit.prevent>
          <el-form-item style="width: 240px; max-width: 240px">
            <el-input
              v-model.trim="searchForm.tableName"
              clearable
              @clear="handleSearch"
              placeholder="请输入表名称"
            >
              <template #prefix>表名称：</template></el-input
            >
          </el-form-item>
          <el-form-item style="width: 60px; max-width: 60px"
            ><el-button
              type="primary"
              :loading="data.searchLoading"
              @click="handleSearch"
              >搜索</el-button
            ></el-form-item
          >
        </el-form>
      </template>
    </search-layout>
    <div class="yc-description">
      <el-table
        :data="tableDataDisplay"
        border
        size="small"
        v-loading="data.tableLoading"
        show-overflow-tooltip
      >
        <el-table-column label="序号" type="index" width="55" />
        <el-table-column label="表名称" min-width="260">
          <template #default="scope">
            <ellipsisText :value="scope.row.tableName"></ellipsisText>
          </template>
        </el-table-column>
        <el-table-column label="描述" min-width="260">
          <template #default="scope">
            <ellipsisText :value="scope.row.description"></ellipsisText>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="140">
          <template #default="scope">
            <el-button
              size="small"
              type="primary"
              text
              @click="handleViewReference(scope.row)"
              >详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        v-model:currentPage="data.page"
        v-model:page-size="data.size"
        :page-sizes="[10, 20, 30, 40]"
        small
        layout="total, sizes, prev, pager, next, jumper"
        :total="data.totalCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-drawer>
  <ItemDrawer
    v-if="itemDrawerVisible"
    :id="metaTableId"
    @close="itemDrawerVisible = false"
  ></ItemDrawer>
</template>

<script lang="ts" setup>
import { onMounted, ref, reactive, computed } from "vue";
import { getMetaDataTableListApi } from "@/api/metaData";
import ItemDrawer from "./itemDraw.vue";
import ellipsisText from "@/components/ellipsisText/index.vue";
import SearchLayout from "@/components/searchLayout/index.vue";

const searchForm = reactive({
  tableName: "",
});
const data = reactive<Record<string, any>>({
  responseData: [],
  tableData: [],
  page: 1,
  size: 10,
  totalCount: 1,
});
const emit = defineEmits(["close"]);

const props = defineProps({
  id: {
    type: String,
    default: undefined,
  },
});

const drawerVisible = ref(true);
const metaTableId = ref(0);

const itemDrawerVisible = ref(false);

const tableDataDisplay = computed(() => {
  return data.tableData.slice(
    (data.page - 1) * data.size,
    data.page * data.size
  );
});

function handleSizeChange(num: number) {
  data.size = num;
  data.page = 1;
}

function handleCurrentChange(num: number) {
  data.page = num;
}

function handleSearch() {
  data.page = 1;
  data.tableData = data.responseData.filter((item: any) => {
    return item.tableName
      .toLowerCase()
      .includes(searchForm.tableName.toLocaleLowerCase());
  });
  data.totalCount = data.tableData.length;
}
function getTableData() {
  data.tableLoading = true;
  data.tableData = [];
  console.log("searchForm.tableName:", searchForm.tableName);
  const params = {
    tableName: searchForm.tableName,
    id: props.id,
  };

  // 真实接口请求
  getMetaDataTableListApi(params)
    .then((response: any) => {
      data.responseData = Array.isArray(response) ? response : [];
      data.tableData = Array.isArray(response) ? response : [];
      data.totalCount = data.tableData.length;
    })
    .finally(() => {
      data.tableLoading = false;
      data.searchLoading = false;
    });
}

// 基础属性详情按钮
function handleViewReference(data: any) {
  metaTableId.value = data.id;
  itemDrawerVisible.value = true;
}

function handleClose() {
  emit("close");
}

onMounted(() => {
  getTableData();
});
</script>
<style lang="scss" scoped>
:deep(.el-descriptions__label) {
  width: 72px;
  min-width: 72px;
  display: inline-block;
}
:deep(.el-tabs__nav-wrap::after) {
  content: none;
}
:deep(.el-tabs__active-bar) {
  display: none;
}
:deep(.el-tabs__item) {
  height: auto;
  line-height: normal;
}
:deep(.el-tabs--top .el-tabs__item.is-top:nth-child(2)) {
  border-right: 2px solid #c1c9c7;
}

:deep(.el-descriptions__content) {
  display: inline-block;
  word-wrap: word-break;
  word-break: break-all;
  width: 1040px;
  max-width: 1040px;
  min-width: 1040px;
  display: inline-block;
  vertical-align: middle;
}
</style>
