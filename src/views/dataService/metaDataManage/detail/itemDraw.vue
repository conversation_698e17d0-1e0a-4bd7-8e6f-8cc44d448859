<template>
  <el-drawer
    v-model="drawerVisible"
    title="字段信息"
    size="1109"
    @closed="handleClose"
  >
    <search-layout>
      <template #right>
        <el-form :inline="true" :model="searchForm" @submit.prevent>
          <el-form-item style="width: 240px; max-width: 240px">
            <el-input
              v-model.trim="searchForm.columnName"
              clearable
              @clear="handleSearch"
              placeholder="请输入字段名称"
            >
              <template #prefix>字段名称：</template></el-input
            >
          </el-form-item>
          <el-form-item style="width: 60px; max-width: 60px"
            ><el-button
              type="primary"
              :loading="data.searchLoading"
              @click="handleSearch"
              >搜索</el-button
            ></el-form-item
          >
        </el-form>
      </template>
    </search-layout>
    <div class="yc-description">
      <el-table
        :data="tableDataDisplay"
        border
        size="small"
        v-loading="data.tableLoading"
      >
        <el-table-column label="序号" type="index" width="55" />
        <el-table-column label="字段名称" property="description">
          <template #default="scope">
            <ellipsisText :value="scope.row.columnName"></ellipsisText>
          </template>
        </el-table-column>
        <el-table-column label="类型" property="description">
          <template #default="scope">
            <ellipsisText :value="scope.row.columnType"></ellipsisText>
          </template>
        </el-table-column>

        <el-table-column label="描述" property="field">
          <template #default="scope">
            <ellipsisText :value="scope.row.description"></ellipsisText>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        v-model:currentPage="data.page"
        v-model:page-size="data.size"
        :page-sizes="[10, 20, 30, 40]"
        small
        layout="total, sizes, prev, pager, next, jumper"
        :total="data.totalCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { onMounted, ref, computed, reactive } from "vue";
import { getMetaDetailListApi } from "@/api/metaData";
import ellipsisText from "@/components/ellipsisText/index.vue";
import SearchLayout from "@/components/searchLayout/index.vue";

const searchForm = reactive({
  columnName: "",
});
const data = reactive<Record<string, any>>({
  responseData: [],
  tableData: [],
  page: 1,
  size: 10,
  totalCount: 1,
});
const emit = defineEmits(["close"]);

const props = defineProps({
  id: {
    type: Number,
  },
});

const drawerVisible = ref(true);

const tableDataDisplay = computed(() => {
  return data.tableData.slice(
    (data.page - 1) * data.size,
    data.page * data.size
  );
});

function handleSizeChange(num: number) {
  data.size = num;
  data.page = 1;
}

function handleCurrentChange(num: number) {
  data.page = num;
}

function handleSearch() {
  data.page = 1;
  data.tableData = data.responseData.filter((item: any) => {
    return item.columnName
      .toLowerCase()
      .includes(searchForm.columnName.toLocaleLowerCase());
  });
  data.totalCount = data.tableData.length;
}
function getTableData() {
  data.tableLoading = true;
  data.tableData = [];
  const params = {
    tableId: props.id,
  };

  getMetaDetailListApi(params)
    .then((response: any) => {
      data.responseData = Array.isArray(response) ? response : [];
      data.tableData = Array.isArray(response) ? response : [];
      data.totalCount = data.tableData.length;
    })
    .finally(() => {
      data.tableLoading = false;
      data.searchLoading = false;
    });
}

function handleClose() {
  emit("close");
}

onMounted(() => {
  getTableData();
});
</script>
<style lang="scss" scoped>
:deep(.el-descriptions__label) {
  width: 72px;
  min-width: 72px;
  display: inline-block;
}
:deep(.el-tabs__nav-wrap::after) {
  content: none;
}
:deep(.el-tabs__active-bar) {
  display: none;
}
:deep(.el-tabs__item) {
  height: auto;
  line-height: normal;
}
:deep(.el-tabs--top .el-tabs__item.is-top:nth-child(2)) {
  border-right: 2px solid #c1c9c7;
}

:deep(.el-descriptions__content) {
  display: inline-block;
  word-wrap: word-break;
  word-break: break-all;
  width: 1040px;
  max-width: 1040px;
  min-width: 1040px;
  display: inline-block;
  vertical-align: middle;
}
</style>
