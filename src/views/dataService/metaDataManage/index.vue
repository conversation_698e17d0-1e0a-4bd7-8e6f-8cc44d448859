<template>
  <div>
    <search-layout>
      <template #left>
        <el-button type="primary" @click="handleAdd">导入</el-button>
      </template>
      <template #right>
        <el-form :inline="true" :model="searchForm" @submit.prevent>
          <el-form-item style="width: 240px; max-width: 240px">
            <el-input
              v-model.trim="searchForm.databaseName"
              clearable
              @clear="handleSearch"
              placeholder="请输入数据库名称"
            >
              <template #prefix>数据库：</template></el-input
            >
          </el-form-item>
          <el-form-item style="width: 300px; max-width: 300px"
            ><el-date-picker
              v-model="searchForm.maintainTime"
              type="daterange"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled-date="disabledDate"
              clearable
              @change="handleTimeClear()"
          /></el-form-item>
          <el-form-item style="width: 60px; max-width: 60px"
            ><el-button
              type="primary"
              :loading="data.searchLoading"
              @click="handleSearch"
              >搜索</el-button
            ></el-form-item
          >
        </el-form>
      </template>
    </search-layout>
    <el-table
      :data="data.tableData"
      v-loading="data.tableLoading"
      border
      size="small"
    >
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column property="databaseName" label="数据库名称">
        <template #default="scope">
          <ellipsisText :value="scope.row.databaseName">{{
            scope.row.databaseName || "-"
          }}</ellipsisText>
        </template>
      </el-table-column>
      <el-table-column property="databaseIp" label="数据库IP">
        <template #default="scope">
          <ellipsisText :value="scope.row.databaseIp">{{
            scope.row.databaseIp || "-"
          }}</ellipsisText>
        </template>
      </el-table-column>
      <el-table-column property="dataServiceName" label="所属数据服务">
        <template #default="scope">
          <ellipsisText :value="scope.row.dataServiceName">{{
            scope.row.dataServiceName || "-"
          }}</ellipsisText>
        </template>
      </el-table-column>
      <el-table-column property="description" label="描述">
        <template #default="scope">
          <ellipsisText :value="scope.row.description">{{
            scope.row.description || "-"
          }}</ellipsisText>
        </template>
      </el-table-column>

      <el-table-column property="updatedTime" label="操作时间" />
      <el-table-column label="操作">
        <template #default="scope">
          <el-button type="primary" text @click="handleView(scope.row)"
            >详情</el-button
          >
          <el-button
            type="primary"
            text
            @click="handleEdit(scope.$index, scope.row)"
            >编辑</el-button
          >
          <el-popconfirm
            :width="200"
            confirm-button-text="确定"
            cancel-button-text="取消"
            title="是否确认删除数据库？"
            @confirm="handleDeleteConfirm(scope.row.id)"
          >
            <template #reference>
              <el-button text type="primary">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      v-model:currentPage="data.page"
      v-model:page-size="data.size"
      :page-sizes="[10, 20, 30, 40]"
      small
      layout="total, sizes, prev, pager, next, jumper"
      :total="data.totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
  <!-- 新增或者编辑弹窗 -->
  <AddOrEdit
    v-if="data.dialogVisible"
    :id="selectHandle"
    @close="data.dialogVisible = false"
    @success="updateHandleSuccess"
  ></AddOrEdit>
  <!-- 详情抽屉 -->
  <DetailDrawer
    v-if="detailDrawerVisible"
    :id="selectHandle"
    @close="detailDrawerVisible = false"
  ></DetailDrawer>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, toRefs } from "vue";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
import { getDataListApi, deleteMetaDataApi } from "@/api/metaData";
import SearchLayout from "@/components/searchLayout/index.vue";
import ellipsisText from "@/components/ellipsisText/index.vue";

import DetailDrawer from "./detail/index.vue";
import AddOrEdit from "./addOrEdit/index.vue";

const store = useStore();

const detailDrawerVisible = ref(false);

const disabledDate = (time: Date) => {
  return time.getTime() > new Date().getTime();
};

const searchForm = reactive({
  databaseName: "",
  maintainTime: [] as any,
});

const selectHandle = ref("");

const data = reactive<Record<string, any>>({
  tableData: [],
  appList: [],
  page: 1,
  size: 10,
  totalCount: 1,
  dialogTitle: "",
  dialogVisible: false,
  dialogDisabled: false,
  deleteLoading: false,
  tableLoading: false,
  searchLoading: false,
  confirmLoading: false,
});

function getTableData() {
  data.tableLoading = true;
  data.tableData = [];
  const params = {
    databaseName: searchForm.databaseName,
    startTime: "",
    endTime: "",
    page: data.page - 1,
    size: data.size,
  };
  if (searchForm.maintainTime?.length) {
    const [startTime, endTime] = searchForm.maintainTime;
    params.startTime = startTime;
    params.endTime = endTime.replace("00:00:00", "23:59:59");
  }

  // // 真实接口请求
  getDataListApi(params)
    .then((response: any) => {
      const result = response;
      data.tableData = result?.content || [];
      data.page = result?.pageNumber || 1;
      data.totalCount = result?.totalCount || 0;
    })
    .finally(() => {
      data.tableLoading = false;
      data.searchLoading = false;
    });
}

const updateHandleSuccess = () => {
  data.page = 1;
  getTableData();
};

// 详情按钮
function handleView(item: any) {
  selectHandle.value = item.id;
  detailDrawerVisible.value = true;
}

// 清空操作时间，查询数据
function handleTimeClear() {
  if (!searchForm.maintainTime || searchForm.maintainTime.length === 0) {
    handleSearch();
  }
}

function handleSearch() {
  data.searchLoading = true;
  data.page = 1;
  getTableData();
}

function handleSizeChange(num: number) {
  data.size = num;
  data.page = 1;
  getTableData();
}

function handleCurrentChange(num: number) {
  data.page = num;
  getTableData();
}

function handleAdd() {
  data.dialogVisible = true;
  selectHandle.value = "";
  data.dialogDisabled = false;
}

function handleDeleteConfirm(id: any) {
  data.deleteLoading = true;
  deleteMetaDataApi({ id })
    .then(() => {
      ElMessage.success("删除成功");
      data.page = 1;
      getTableData();
    })
    .finally(() => {
      data.deleteLoading = false;
    });
}

// 编辑按钮
function handleEdit(index: any, item: any) {
  selectHandle.value = item.id;
  data.dialogVisible = true;
  data.dialogDisabled = false;
}

onMounted(() => {
  getTableData();
});
</script>
<style lang="scss" scoped></style>
