<template>
  <el-dialog
    v-model="dialogFormVisible"
    :title="`${isAdd ? '新增角色' : '编辑角色'}`"
    width="700px"
    @close="$emit('close-dialog')"
    destroy-on-close
    align-center
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="角色名称" prop="roleName">
        <el-input
          v-model.trim="formData.roleName"
          placeholder="请输入角色名称"
          autocomplete
          clearable
        />
      </el-form-item>
      <el-form-item label="角色编码" prop="roleCode">
        <el-input
          v-model.trim="formData.roleCode"
          placeholder="只允许输入字母、下划线"
          clearable
          :disabled="!isAdd"
        />
      </el-form-item>
      <el-form-item label="角色排序" prop="sort">
        <el-input-number v-model="formData.sort" :min="1" />
        <span class="input-tip">最小排序从 1 开始</span>
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="validForm" :loading="btnLoading">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { addRoleApi, updateRoleApi } from "@/api/systemManage/role";

const emit = defineEmits(["dialog-visible", "update-table"]);

// const props = defineProps(["roleData", "isAdd"]);
const props = defineProps({
  roleData: {
    type: Object as () => {
      createdBy: string | number | null;
      createdTime: string;
      entId: number;
      id: number;
      roleCode: string;
      roleName: string;
      sort: number;
      undelete: number;
      updatedBy: string | number;
      updatedTime: string;
      creatorName?: string;
      reviserName?: string;
    },
    default: () => ({}),
  },
  isAdd: {
    type: Boolean,
    default: true,
  },
});

const btnLoading = ref(false);
const formRef = ref<FormInstance>();
const dialogFormVisible = ref(true);

const { roleName = null, roleCode = null, sort = 1 } = props.roleData || {};
const formData = reactive({
  roleName,
  roleCode,
  sort,
});
const rules = reactive({
  roleName: [
    {
      required: true,
      message: "请输入角色名称",
      trigger: "blur",
    },
    {
      max: 30,
      message: "角色名称最大长度不超过30",
      trigger: "blur",
    },
  ],
  roleCode: [
    {
      required: true,
      message: "请输入角色编码",
      trigger: "blur",
    },
    {
      max: 30,
      message: "角色编码最大长度不超过30",
      trigger: "blur",
    },
    {
      trigger: "change",
      validator: (rule: any, value: any, cb: any) => {
        if (/^[a-zA-Z_]{1,}$/.test(value)) {
          cb();
        } else {
          cb(new Error("只允许输入字母、下划线"));
        }
      },
    },
  ],
  sort: [
    {
      required: true,
      message: "请输入角色排序",
      trigger: "blur",
    },
  ],
});

const validForm = () => {
  if (!formRef.value) return;
  formRef.value.validate((valid) => {
    if (valid) {
      addOrEditRole();
    } else {
      ElMessage.error("校验不通过!");
      return false;
    }
  });
};

const addOrEditRole = () => {
  const api = props.isAdd ? addRoleApi : updateRoleApi;
  const params = props.isAdd
    ? formData
    : { ...formData, id: props.roleData?.id };

  btnLoading.value = true;
  api(params)
    .then(() => {
      dialogFormVisible.value = false;
      emit("update-table", true);
      ElMessage({
        message: `${props.isAdd ? "新增" : "编辑"}角色成功!`,
        type: "success",
      });
    })
    .finally(() => {
      btnLoading.value = false;
    });
};
</script>

<style lang="scss" scoped>
.input-tip {
  display: block;
  font-size: 12px;
  margin-left: 5px;
  color: #a8abb2;
}
:deep(
    .el-input-number__decrease:hover
      ~ .el-input:not(.is-disabled)
      .el-input__wrapper,

  ) {
  box-shadow: none;
}
:deep(
    .el-input-number__increase:hover
      ~ .el-input:not(.is-disabled)
      .el-input__wrapper
  ) {
  box-shadow: none;
}
</style>
