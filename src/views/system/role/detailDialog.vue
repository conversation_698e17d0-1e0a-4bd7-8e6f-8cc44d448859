<template>
  <!-- <el-dialog
    v-model="dialogFormVisible"
    title="角色详情"
    width="700px"
    @close="$emit('close-dialog')"
    destroy-on-close
  >
    <el-form label-width="100px">
      <el-form-item label="角色名称:">
        <span>{{ formData.data.roleName }}</span>
      </el-form-item>
      <el-form-item label="角色编码:">
        <span>{{ formData.data.roleCode }}</span>
      </el-form-item>
      <el-form-item label="角色排序:">
        <span>{{ formData.data.sort }}</span>
      </el-form-item>
      <el-form-item label="更新人:">
        <span>{{ formData.data.reviserName }}</span>
      </el-form-item>
      <el-form-item label="更新时间:">
        <span>{{ formData.data.updatedTime }}</span>
      </el-form-item>
      <el-form-item label="创建人:">
        <span>{{ formData.data.creatorName }}</span>
      </el-form-item>
      <el-form-item label="创建时间:">
        <span>{{ formData.data.createdTime }}</span>
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogFormVisible = false">返回</el-button>
      </span>
    </template>
  </el-dialog> -->
  <el-drawer
    v-model="dialogFormVisible"
    title="角色详情"
    @close="$emit('close-dialog')"
  >
    <el-descriptions :column="1" direction="horizontal">
      <el-descriptions-item label="角色名称:">
        {{ formData.data.roleName ?? "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="角色编码:">
        {{ formData.data.roleCode ?? "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="角色排序:">
        {{ formData.data.sort ?? "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="更新人:">
        {{ formData.data.reviserName ?? "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="更新时间:">
        {{ formData.data.updatedTime ?? "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="创建人:">
        {{ formData.data.creatorName ?? "-" }}
      </el-descriptions-item>
      <el-descriptions-item label="创建时间:">
        {{ formData.data.createdTime ?? "-" }}
      </el-descriptions-item>
    </el-descriptions>
    <el-divider />
  </el-drawer>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref } from "vue";
import { getDetailRoleApi } from "@/api/systemManage/role";

const emit = defineEmits(["dialog-visible"]);
// const props = defineProps(["roleData"]);
const props = defineProps({
  roleData: {
    type: Object as () => {
      createdBy: string | number | null;
      createdTime: string;
      entId: number;
      id: number;
      roleCode: string;
      roleName: string;
      sort: number;
      undelete: number;
      updatedBy: string | number;
      updatedTime: string;
      creatorName?: string;
      reviserName?: string;
    },
    default: () => ({}),
  },
});

const dialogFormVisible = ref(true);

interface FormData {
  data: {
    createdBy: string | number | null;
    createdTime: string | null;
    entId: number | null;
    id: number | null;
    roleCode: string;
    roleName: string;
    sort: number | null;
    undelete: number | null;
    updatedBy: string | number | null;
    updatedTime: string;
    creatorName?: string;
    reviserName?: string;
  };
}

const formData = reactive<FormData>({
  data: {
    createdBy: null,
    createdTime: "",
    creatorName: "",
    entId: null,
    id: null,
    reviserName: "",
    roleCode: "",
    roleName: "",
    sort: null,
    undelete: null,
    updatedBy: null,
    updatedTime: "",
  },
});

onMounted(() => {
  getDetailRoleApi({ id: props.roleData?.id }).then((res) => {
    formData.data = res || {};
  });
});
</script>

<style lang="scss" scoped>
:deep(.el-descriptions__label) {
  width: 52px;
  min-width: 52px;
  display: inline-block;
}
</style>
