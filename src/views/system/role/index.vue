<template>
  <SearchLayout>
    <template #left>
      <el-button
        type="primary"
        @click="() => openDialog(true)"
        v-permission="AUTH_CODE.ROLE_MANAGER_ADD"
        >新增角色</el-button
      >
    </template>
    <template #right>
      <el-form
        v-permission="AUTH_CODE.ROLE_MANAGER_PAGE"
        :inline="true"
        :model="formData"
        @submit.prevent
        style="text-align: right"
      >
        <el-form-item style="width: 240px; max-width: 240px">
          <el-input
            v-model.trim="formData.roleName"
            placeholder="请输入"
            @clear="getTableData(true)"
            clearable
          >
            <template #prefix>角色名：</template>
          </el-input>
        </el-form-item>
        <el-form-item style="width: 60px; max-width: 60px">
          <el-button type="primary" @click="() => getTableData(true)"
            >搜索</el-button
          >
        </el-form-item>
      </el-form>
    </template>
  </SearchLayout>

  <el-table
    border
    :data="tableData.list"
    style="width: 100%"
    v-loading="tableLoading"
    size="small"
  >
    <el-table-column label="序号" type="index" width="55" />
    <el-table-column prop="roleName" label="角色名称">
      <template #default="scope">
        <span v-copy="scope.row.roleName">{{ scope.row.roleName }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="roleCode" label="角色编码" />
    <el-table-column prop="sort" label="排序" />
    <el-table-column
      label="操作"
      width="180
      "
    >
      <template #default="scope">
        <el-button
          text
          type="primary"
          @click="() => openDialog(false, scope.row)"
          v-permission="AUTH_CODE.ROLE_MANAGER_EDIT"
          >编辑</el-button
        >

        <el-button
          text
          type="primary"
          @click="() => openDetailDialog(scope.row)"
          v-permission="AUTH_CODE.ROLE_MANAGER_DETAIL"
          >详情</el-button
        >

        <el-popconfirm
          v-if="ROLE_TYPE.SYSTEM_ADMIN !== scope.row.id"
          :title="`该角色可能已绑定用户，删除后所有绑定该角色用户将失去该角色权限，是否确定删除？`"
          width="250"
          @confirm="deleteRole(scope.row)"
        >
          <template #reference>
            <el-button
              text
              type="primary"
              v-permission="AUTH_CODE.ROLE_MANAGER_DELETE"
              >删除</el-button
            >
          </template>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
  <el-pagination
    v-model:current-page="pagination.currentPage"
    v-model:page-size="pagination.pageSize"
    :page-sizes="[10, 20, 30, 40]"
    size="small"
    background
    layout="total,  prev, pager, next, sizes,jumper"
    :total="pagination.total"
  />
  <AddOrEdit
    v-if="addOrEditVisible"
    @close-dialog="addOrEditVisible = false"
    @update-table="getTableData"
    :roleData="selectRow.data"
    :isAdd="isAddDialog"
  />
  <DetailDialog
    v-if="detailVisible"
    :roleData="selectRow.data"
    @close-dialog="detailVisible = false"
  />
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { roleListApi, deleteRoleApi } from "@/api/systemManage/role";
import { ROLE_TYPE } from "@/utils/constant";
import AddOrEdit from "./addOrEdit.vue";
import DetailDialog from "./detailDialog.vue";
import { AUTH_CODE } from "@/utils/authCode";
import store from "@/store";
import SearchLayout from "@/components/searchLayout/index.vue";

const formData = reactive({
  roleName: "",
});
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

const tableLoading = ref(false);

interface TableData {
  createdBy: string | number | null;
  createdTime: string;
  entId: number;
  id: number;
  roleCode: string;
  roleName: string;
  sort: number;
  undelete: number;
  updatedBy: string | number;
  updatedTime: string;
  creatorName?: string;
  reviserName?: string;
}
const tableData: { list: TableData[] } = reactive({
  list: [],
});

const selectRow: { data: TableData } = reactive({
  data: {
    createdBy: 0,
    createdTime: "",
    entId: 0,
    id: 0,
    roleCode: "",
    roleName: "",
    sort: 0,
    undelete: 0,
    updatedBy: 0,
    updatedTime: "",
  },
});
const isAddDialog = ref(true);

const addOrEditVisible = ref(false);
const detailVisible = ref(false);

onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (AllPermission.includes(AUTH_CODE.ROLE_MANAGER_PAGE)) {
    getTableData(true);
  }
});

watch([() => pagination.currentPage, () => pagination.pageSize], () => {
  getTableData(false);
});

const getTableData = (isReset: boolean) => {
  if (isReset) {
    pagination.currentPage = 1;
    pagination.pageSize = 10;
    pagination.total = 0;
  }
  const { currentPage, pageSize } = pagination;
  tableLoading.value = true;
  roleListApi({
    roleName: formData.roleName,
    currentPage,
    pageSize,
  })
    .then((res) => {
      const { content, totalCount } = res;

      pagination.total = totalCount;
      tableData.list = content || [];
    })
    .finally(() => {
      tableLoading.value = false;
    });
};

const openDetailDialog = (row: any) => {
  detailVisible.value = true;
  selectRow.data = row;
};

const openDialog = (isAdd: boolean, row?: any) => {
  isAddDialog.value = isAdd;
  addOrEditVisible.value = true;
  selectRow.data = row;
};

const deleteRole = (row: any) => {
  deleteRoleApi({ id: row.id }).then(() => {
    ElMessage.success("删除角色成功!");
    getTableData(true);
  });
};
</script>

<style lang="scss" scoped></style>
