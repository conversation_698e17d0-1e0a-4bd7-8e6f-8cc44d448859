<template>
  <el-dialog
    v-model="dialogFormVisible"
    title="修改登录配置"
    width="600px"
    @close="$emit('close-bindHdl-dialog')"
    destroy-on-close
    align-center
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="160px">
      <el-form-item label="双因子登录" prop="doubleFactorLogin">
        <el-radio-group v-model="formData.doubleFactorLogin">
          <el-radio :label="1" size="large">开启</el-radio>
          <el-radio :label="0" size="large">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="密码错误锁定"
        prop="passwordErrLockCount"
        width="200px"
      >
        <el-input
          v-model.trim="formData.passwordErrLockCount"
          type="number"
          clearable
        >
          <template #suffix><span>次</span></template>
        </el-input>
      </el-form-item>
      <el-form-item label="密码错误锁定时间" prop="passwordErrLockTime">
        <el-input
          v-model.trim="formData.passwordErrLockTime"
          type="number"
          clearable
        >
          <template #suffix><span>分钟</span></template>
        </el-input>
      </el-form-item>
      <el-form-item label="邮箱验证码错误锁定" prop="emialErrLockCount">
        <el-input
          v-model.trim="formData.emialErrLockCount"
          type="number"
          clearable
        >
          <template #suffix><span>次</span></template>
        </el-input>
      </el-form-item>
      <el-form-item label="邮箱验证码错误锁定时间" prop="emialErrLockTime">
        <el-input
          v-model.trim="formData.emialErrLockTime"
          type="number"
          clearable
        >
          <template #suffix><span>分钟</span></template>
        </el-input>
      </el-form-item>
      <el-form-item label="静止登出时间" prop="staticLogoutTime">
        <el-input
          v-model.trim="formData.staticLogoutTime"
          type="number"
          clearable
        >
          <template #suffix><span>分钟</span></template>
        </el-input>
      </el-form-item>
      <el-form-item label="更换密码周期" prop="changePasswordTime">
        <el-input
          v-model.trim="formData.changePasswordTime"
          type="number"
          clearable
        >
          <template #suffix><span>天</span></template>
        </el-input>
      </el-form-item>
      <el-form-item label="首次登录修改密码" prop="firstLoginChange">
        <el-radio-group v-model="formData.firstLoginChange">
          <el-radio :label="1" size="large">开启</el-radio>
          <el-radio :label="0" size="large">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="密码复杂度" prop="firstLoginChange">
        <el-checkbox checked disabled label="大写字母" size="large" />
        <el-checkbox checked disabled label="小写字母" size="large" />
        <el-checkbox checked disabled label="数字" size="large" />
        <el-checkbox checked disabled label="特殊符号" size="large" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="bindHdl"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { getCurrentInstance, reactive, ref } from "vue";
import { ElMessage } from "element-plus";
import { update } from "@/api/systemManage/loginConfig";

const emit = defineEmits(["dialog-visible", "update-table"]);
const { proxy }: any = getCurrentInstance();
const props = defineProps({
  infoData: {
    type: Object as () => {
      doubleFactorLogin: number;
      passwordErrLockCount: string;
      passwordErrLockTime: string;
      emialErrLockCount: string;
      emialErrLockTime: string;
      staticLogoutTime: string;
      changePasswordTime: string;
      firstLoginChange: number;
    },
    default: () => ({}),
  },
});

const dialogFormVisible = ref(true);
const formData = ref({
  doubleFactorLogin: props.infoData.doubleFactorLogin,
  passwordErrLockCount: props.infoData.passwordErrLockCount,
  passwordErrLockTime: props.infoData.passwordErrLockTime,
  emialErrLockCount: props.infoData.emialErrLockCount,
  emialErrLockTime: props.infoData.emialErrLockTime,
  staticLogoutTime: props.infoData.staticLogoutTime,
  changePasswordTime: props.infoData.changePasswordTime,
  firstLoginChange: props.infoData.firstLoginChange,
});

/**
 * 校验数字
 */
const checkNumber = (rule: any, value: any, callback: any) => {
  var reg = /^\+?[1-9]\d*$/; // 大于0的正整数
  if (value < 0 || !reg.test(value)) {
    return callback(new Error("参数必须正整数"));
  }
  callback();
};

/**
 * 静止登出时间
 */
const checkStaticLogoutTime = (rule: any, value: any, callback: any) => {
  const reg = /^\+?[1-9]\d*$/; // 大于0的正整数
  if (value < 0 || !reg.test(value)) {
    return callback(new Error("参数必须正整数"));
  }
  if (value < 10) {
    return callback(new Error("静止登出时间须大于等于10分钟"));
  }
  callback();
};

/**
 * 更换密码周期
 */
const checkChangePasswordTime = (rule: any, value: any, callback: any) => {
  const reg = /^\+?[1-9]\d*$/; // 大于0的正整数
  if (value < 0 || !reg.test(value)) {
    return callback(new Error("更换密码周期必须正整数"));
  }
  if (value < 10) {
    return callback(new Error("更换密码周期须大于等于10天"));
  }
  callback();
};
const rules = reactive({
  doubleFactorLogin: [
    {
      required: true,
      message: "请设置双因子登录",
      trigger: "blur",
    },
  ],
  passwordErrLockCount: [
    {
      required: true,
      message: "请输入密码错误锁定",
      trigger: "blur",
    },
    { required: true, validator: checkNumber, trigger: "blur" },
  ],
  passwordErrLockTime: [
    {
      required: true,
      message: "请输入密码错误锁定时间",
      trigger: "blur",
    },
    { required: true, validator: checkNumber, trigger: "blur" },
  ],
  emialErrLockCount: [
    {
      required: true,
      message: "请输入邮箱验证码错误锁定次数",
      trigger: "blur",
    },
    { required: true, validator: checkNumber, trigger: "blur" },
  ],
  emialErrLockTime: [
    {
      required: true,
      message: "请输入邮箱验证码错误锁定时间",
      trigger: "blur",
    },
    { required: true, validator: checkNumber, trigger: "blur" },
  ],
  staticLogoutTime: [
    {
      required: true,
      message: "请输入静止登出时间",
      trigger: "blur",
    },
    { required: true, validator: checkStaticLogoutTime, trigger: "blur" },
  ],
  changePasswordTime: [
    {
      required: true,
      message: "请输入更换密码周期",
      trigger: "blur",
    },
    { required: true, validator: checkChangePasswordTime, trigger: "blur" },
  ],
  firstLoginChange: [
    {
      required: true,
      message: "请设置首次登录修改密码",
      trigger: "blur",
    },
  ],
});

function bindHdl() {
  proxy.$refs.formRef.validate(async (valid: any) => {
    if (valid) {
      update(formData.value).then((res: any) => {
        dialogFormVisible.value = false;
        emit("update-table", true);
        ElMessage({
          message: "修改登录配置成功!",
          type: "success",
        });
      });
    }
  });
}
</script>

<style lang="scss"></style>
