<template>
  <div>
    <div class="home-section">
      <div class="section-wrap">
        <div class="section-title">
          <div class="section-title-line"></div>
          <span class="section-title-text"> 登录配置 </span>
        </div>
        <div class="info-content">
          <div class="info-line">
            <span class="info-item">
              双因子登录：
              {{ infoData.doubleFactorLogin === 1 ? "开启" : "关闭" }}
            </span>
            <span class="info-item">
              密码错误锁定：
              {{
                infoData.passwordErrLockCount
                  ? infoData.passwordErrLockCount + "次"
                  : "-"
              }}
            </span>
          </div>
          <div class="info-line">
            <span class="info-item">
              密码错误锁定时间：
              {{
                infoData.passwordErrLockTime
                  ? infoData.passwordErrLockTime + "分钟"
                  : "-"
              }}
            </span>
            <span class="info-item">
              邮箱验证码错误锁定：
              {{
                infoData.emialErrLockCount
                  ? infoData.emialErrLockCount + "次"
                  : "-"
              }}
            </span>
          </div>

          <div class="info-line">
            <span class="info-item">
              邮箱验证码错误锁定时间：
              {{
                infoData.emialErrLockTime
                  ? infoData.emialErrLockTime + "分钟"
                  : "-"
              }}
            </span>
            <span class="info-item">
              静止登出时间：
              {{
                infoData.staticLogoutTime
                  ? infoData.staticLogoutTime + "分钟"
                  : "-"
              }}
            </span>
          </div>
          <div class="info-line">
            <span class="info-item">
              更换密码周期：
              {{
                infoData.changePasswordTime
                  ? infoData.changePasswordTime + "天"
                  : "-"
              }}
            </span>
            <span class="info-item">
              首次登录修改密码：
              {{ infoData.firstLoginChange === 1 ? "开启" : "关闭" }}
            </span>
          </div>
          <div class="info-line">
            <span class="info-item">
              密码复杂度：大写英文；小写英文；数字；特殊符号
            </span>
          </div>
        </div>
        <div class="edit-content">
          <el-button
            @click="showEdit"
            type="primary"
            v-permission="AUTH_CODE.LOGIN_CONFIG_EDIT"
            >编辑</el-button
          >
        </div>
      </div>
    </div>
  </div>
  <EditAccount
    v-if="bindHdlVisible"
    @close-bindHdl-dialog="bindHdlVisible = false"
    @update-table="fetchData"
    :infoData="infoData"
  />
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { configInfo } from "@/api/systemManage/loginConfig";
import EditAccount from "./edit.vue";
import { AUTH_CODE } from "@/utils/authCode";
import store from "@/store";

const bindHdlVisible = ref(false);
const infoData = ref({
  doubleFactorLogin: 0,
  passwordErrLockCount: "",
  passwordErrLockTime: "",
  emialErrLockCount: "",
  emialErrLockTime: "",
  staticLogoutTime: "",
  changePasswordTime: "",
  firstLoginChange: 0,
});

const fetchData = () => {
  configInfo().then((data: any) => {
    infoData.value = data;
  });
};

const showEdit = () => {
  bindHdlVisible.value = true;
};
onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (AllPermission.includes(AUTH_CODE.LOGIN_CONFIG_DETAIL)) {
    fetchData();
  }
});
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import "./style/common";

// 数据汇总
.data-content {
  width: 100%;
  padding-bottom: 16px;

  .data-item {
    padding-right: 35px;
    cursor: pointer;
    transition: all 0.2s linear;

    .data-title {
      height: 32px;
      line-height: 32px;
      padding: 0 20px;
      background-color: #007457;
      border-radius: 5px 5px 0 0;
      color: #fff;
      font-size: 16px;
      transition: all 0.2s linear;

      .icon {
        float: left;
        width: 5px;
        height: 16px;
        background-color: #fff;
        margin: 8px 8px 0 0;
      }
    }

    .data-num {
      height: 108px;
      line-height: 108px;
      border: 1px solid #007457;
      border-radius: 0 0 5px 5px;
      text-align: center;
      transition: all 0.2s linear;

      .num {
        display: inline-block;
        line-height: 40px;
        height: 40px;
        font-size: 36px;
        color: #007457;
        border-bottom: 1px solid #007457;
      }
    }

    .data-num1 {
      line-height: 1;

      .num {
        margin-top: 15px;
      }

      .line2 {
        margin-top: 15px;
        font-size: 14px;

        .num2 {
          margin-left: 15px;
          border-bottom: 1px solid #444;
        }
      }
    }

    &:hover {
      box-shadow: 0 3px 7px 0 rgba(191, 195, 205, 0.19);

      .data-title {
        background-color: #007457;
      }

      .data-num {
        border-color: #007457;

        .num {
          color: #007457;
          border-bottom-color: #007457;
        }
      }
    }
  }

  .item1 {
    border-color: #007457;

    .data-title {
      background-color: #007457;
    }

    .data-num {
      border: 1px solid #007457;

      .num {
        color: $colorGreen;
        border-bottom: 1px solid #007457;
      }
    }

    &:hover {
      box-shadow: 0 3px 7px 0 rgba(191, 195, 205, 0.19);

      .data-title {
        background-color: #007457;
      }

      .data-num {
        border-color: #007457;

        .num {
          color: #007457;
          border-bottom-color: #007457;
        }
      }
    }
  }

  .data-item:last-child {
    margin-right: 0;
  }
}
.echarts-container {
  // background: #00f;
  width: 45%;
  height: 528px;
  padding: 10px 0 0px 0;
  display: inline-block;

  .my-chat-title {
    margin: 0 0 0 40px;
    font-size: medium;
    font-weight: 900;
  }
}
.edit-content {
  display: flex;
  flex-direction: row;
  justify-content: right;
  padding-bottom: 10px;
}
</style>
