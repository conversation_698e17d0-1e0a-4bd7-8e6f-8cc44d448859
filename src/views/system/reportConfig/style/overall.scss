@import "./config";
@import "./dialog.scss";

/* 身份证、企业证件上传组件的样式 */

.idcard-box,
.companyid-box {
  display: block;
  float: left;
  margin-right: 10px;
  // padding: 5px;
  // width: 130px;
  width: 142px;
  height: 86px;
  border: none;
  border-radius: 10px;
  > div {
    width: 100%;
    height: 100%;
    padding: 5px;
    // border: 1px solid #f2f2f2;
    border: 1px solid $border-color-1;
    border-radius: 6px;
  }
  img {
    // width: 120px;
    // height: 76px;
    width: 100%;
    height: 100%;
    border-radius: 6px;
  }
  .avatar-uploader {
    width: inherit;
    height: inherit;
    display: inline-block;
    line-height: 1;
    border: none;
    border-radius: 0;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    &:hover {
      border-color: $colorBlue;
    }
    .el-upload {
      width: inherit;
      height: inherit;
    }
    .avatar-uploader-icon {
      width: inherit;
      height: inherit;
      font-size: 28px;
      color: #8c939d;
      line-height: inherit;
      text-align: center;
    }
    .avatar {
      display: block;
      padding: 5px;
      width: inherit;
      height: inherit;
      border-radius: 6px;
    }
  }
}

// dialog样式
.tl-dialog {
  .el-dialog__header {
    border-top: 3px solid $colorBlue;
  }
  .dialog-title {
    span {
      display: inline-block;
      padding: 0 10px 3px;
      font-size: 14px;
      font-weight: bolder;
      border-bottom: 2px solid #e9e9e9;
    }
  }
  .dialog-footer {
    .el-button {
      margin: 0 20px;
      padding: 9px 30px;
    }
  }
}

// 请求错误提示
.tl-messagebox {
  padding-bottom: 0;
  border-top: 3px solid $colorBlue;
  .el-message-box__header {
    span {
      display: inline-block;
      padding: 0 12px 4px;
      font-size: 14px;
      font-weight: bolder;
      border-bottom: 2px solid #e9e9e9;
      color: #333;
    }
  }
  .error-box {
    line-height: 24px;
  }
  .error-img {
    margin: 10px 0 20px;
  }
  .error-code {
    color: #e9e9e9;
  }
  .error-msg {
    color: #666;
  }
}
// content head
.content-crumbs {
  margin: 0 -18px;
  padding-bottom: 15px;
  background-color: $bg-color;
  overflow: hidden;
  .crumbs-container {
    min-height: $content-crumbs-height;
    padding: 8px 18px;
    background-color: $colorWhite;
    box-shadow: 0 3px 7px 0 rgba(191, 195, 205, 0.19);
  }
}
// 过滤条件输入框
.table-option-row {
  text-align: right;
  .el-form-item {
    margin: 0 0 0 20px;
  }
}

/* 上方筛选行样式 */
.top-filter-row {
  min-height: 50px;
  padding: 6px 0;
  .filter-item {
    display: inline-block;
    margin: 0 20px 10px 0;
    .el-input {
      width: 140px;
    }
    //代管单位输入框宽度
    .filter-item_input_escrow-name {
      width: 160px;
    }
    .el-date-editor {
      width: 260px;
    }
    .el-select {
      width: 140px;
    }
    .select-checkbox {
      width: auto;
      .el-input {
        width: auto;
      }
    }
    //1030版本：将之前蓝色和黄色的圆角按钮的样式重置
    button.el-button.el-button--mini.is-round {
      border-radius: 4px;
      font-size: 12px;
      &.el-button--primary {
        border: #4072f6;
        background-color: #4072f6;
      }
      &.el-button--warning {
        margin-left: 20px;
        border: 1px solid #c8c9cd;
        background-color: #fff;
        color: #444;
      }
      i {
        display: none;
      }
      span {
        margin-left: 0;
      }
    }
  }

  //1030版本：搜索按钮的位置居右
  .filter-item:last-of-type {
    float: right;
    margin-right: 0;
  }

  .el-form {
    margin: 0;
  }
  .el-form-item {
    margin-right: 58px;
    label {
      padding-right: 5px;
      color: #333;
    }
    .el-input__inner {
      width: 150px;
    }
  }
  .el-date-editor {
    width: 260px;
    vertical-align: middle;
    .el-range-separator {
      padding: 0 1px;
      width: 10%;
    }
  }
  .label {
    vertical-align: middle;
  }
  .filter-btn {
    margin: 0 0 0 30px;
  }
  .top-filter-datepicker {
    width: 208px;
    border: none;
    text-align: center;
  }
  .top-filter-dropdown {
    height: 28px;
    line-height: 28px;
    .el-dropdown {
      font-size: 12px;
    }
  }
}

// 重置了所有dropdown下面的label
.el-radio__label,
.el-checkbox__label {
  font-size: 12px !important;
}

.el-form-item .el-form-item__label {
  font-size: 12px !important;
  font-weight: 400;
  color: #86909c !important;
}
.el-form-item .el-form-item {
  margin-bottom: 0 !important;
  font-weight: 400;
  color: $font-color-primary !important;
}

// table中操作按钮
.btn {
  display: inline-block;
  padding: 0 5px;
  font-size: 12px;
  cursor: pointer;
  &:hover {
    opacity: 0.8;
  }
  &-edit,
  &-edit .el-icon-edit {
    color: $colorBlue;
  }
  &-delete,
  &-delete .el-icon-delete {
    color: $colorRed;
  }
  &-disabled {
    cursor: not-allowed;
    color: #c0c4cc;
  }
}

// 分割线条
.separation-bar {
  margin: 0 -18px;
  height: 10px;
  background-color: $bg-color;
  // background: linear-gradient(top,#cccccc,#fafafb)
}

// 时间选择器
.el-date-range-picker {
  width: 600px;
}

.el-picker-panel {
  line-height: 12px;
}

.el-date-table td {
  width: 28px;
  height: 26px;
}

.el-date-range-picker__header div {
  font-size: 14px;
}

.el-date-range-picker__header {
  line-height: 28px;
}

.el-date-range-picker__content {
  padding: 10px;
  .el-date-table th {
    padding: 6px;
  }
  .el-date-table td {
    padding: 2px 0;
  }
}

/*============================================================
	** 内容区： 面包屑和页脚之间的区域
==============================================================*/
.module {
  padding: 20px 0;
  // 模块标题：灰色背景块
  &-head {
    height: 40px;
    margin-bottom: 23px;
    padding: 0 13px;
    line-height: 40px;
    background-color: $bg-color-1;
    font-weight: 500;

    //模块标题右侧的添加按钮
    .button-add {
      float: right;
      position: relative;
      top: 6px;
      border: 1px solid #c8c9cd;
      padding: 7px;
      font-size: 12px;
      color: #444;
    }
  }

  //小标题：左侧带竖线
  .module-title {
    float: left;
    position: relative;
    width: 100%;
    margin-bottom: 24px;
    padding: 0 0 10px 13px;
    border-bottom: 1px solid $border-color-primary;
    line-height: 18px;
    font-weight: 500;
    color: $font-color-primary;
    &::before {
      content: "";
      position: absolute;
      top: 2.5px;
      left: 0;
      width: 2px;
      height: 12px;
      background-color: $colorBlue;
    }
  }

  .edit-table {
    padding: 20px 20px 0 20px;
    .btn-operation {
      width: 120px;
    }
    .el-form-item__content {
      word-break: break-all;
    }
  }
}

.input-small-width {
  width: 320px;
}

//步骤
.module-steps {
  width: 80%;
  margin: 60px auto 0;
  .el-step {
    height: 100px;
  }
  .view-steps {
    width: 500px;
    margin: 10px auto;
    .el-form-item {
      position: relative;
      .btn-countdown {
        position: absolute;
        top: 0;
        left: 460px;
      }
    }
    .btn-group {
      margin: 100px 0 0 40px;
    }
    .el-button {
      width: 120px;
    }
    .scuuess-tip {
      width: 200px;
      margin: 20px auto;
      text-align: center;
      p {
        font-weight: bold;
      }
      .el-button {
        margin-top: 100px;
      }
    }
  }
}

//节点管理module
.module-table {
  .edit-table {
    margin-top: 0 !important;
  }
  .el-row {
    min-height: 42px;
    line-height: 42px;
    margin: 0;
    border-bottom: 1px solid #e9e9e9;
    .el-col {
      font-size: 12px;
      color: #666;
      .el-form-item {
        margin-bottom: 0;
      }
      .el-form-item__content {
        line-height: 20px;
        padding: 11px 0;
        font-size: 12px;
      }
      .idcard-box,
      .companyid-box {
        margin-top: 10px;
        margin-right: 15px;
        height: 92px;
        display: inline-block;
        padding: 5px;
        border-radius: 10px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .btn {
        font-size: 14px;
      }
      &:last-child {
        text-align: right;
      }
    }
    &.row-large {
      height: 120px;
    }
    &.company-desc {
      height: auto;
      min-height: 42px;
      .el-form-item__content {
        padding: 10px 0;
        line-height: 20px;
      }
    }
  }
  .module-row-head {
    height: 38px;
    line-height: 38px;
    font-weight: bold;
    .el-col {
      color: #333;
    }
  }
}

.module-input-content {
  margin-top: 20px;
  .el-row {
    margin: 10px 0;
    padding-left: 20px;
    color: #666;
    .small {
      .el-input__inner {
        width: 670px;
      }
    }
    textarea {
      width: 782px;
      height: 168px;
    }
  }
  .el-input__inner {
    width: 782px;
  }
  .input-row {
    margin-top: 20px;
  }
  .col-label {
    width: 92px;
    color: #333;
  }
  .prefix-list {
    margin: 0 26px 0 0;
    overflow: hidden;
    text-overflow: ellipsis;
    span {
      margin-right: 55px;
    }
  }
}

.edit-table {
  .btn-group {
    margin: 30px 0 20px 366px;
  }
  .btn-operation {
    width: 160px;
    height: 40px;
  }
}

// 整页样式布局
.single-page {
  padding-top: $header-height;
}

//tab样式修改
.module-tab {
  .el-tabs {
    .el-tabs__item {
      font-size: 12px;
    }
    .el-tabs__nav-wrap::after {
      height: 0 !important;
    }
    .el-tabs__nav::after {
      content: "";
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 2px;
      background-color: #e4e7ed;
      z-index: 0;
    }
  }
}

//审核详情页面字体大小
.auditDetail .el-form-item__content {
  font-size: 12px;
}

.el-form.two-column {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  padding: 20px 0 0;
  .el-form-item {
    width: 40%;
    margin: 9px 2%;
  }
  &.center {
    justify-content: center;
  }
}
.prefix-container {
  padding-left: 20px;
  margin: 20px;
  .prefix-span {
    display: inline-block;
    width: 200px;
  }
}
// 首页table
.home-table {
  margin-top: 10px;
  .el-table {
    .el-table__row {
      height: 40px;
      &:nth-child(2n) {
        td {
          background-color: #f2f3f8;
        }
      }
    }
    td {
      background-color: #fff;
      .cell {
        padding-left: 20px;
      }
    }
    th {
      height: 40px;
      .cell {
        color: #333;
        padding-left: 20px;
      }
      &.is-leaf {
        background-color: #e1e2e8;
      }
    }
  }
}

// 收费标准tabs
.finance-tabs {
  padding-top: 10px;
  .el-tabs--border-card {
    border: none;
    box-shadow: none;
  }
}

// 首页
.home-section {
  background-color: #fff;
  box-shadow: 0 3px 7px 0 rgba(191, 195, 205, 0.19);
  margin-bottom: 10px;
  &:last-child {
    box-shadow: none;
    margin-bottom: 0;
  }
  .section-wrap {
    margin: 0 auto;
  }
  .data-collection-container {
    margin-top: 20px;
    height: 350px;
  }
  .section-title-twocol {
    height: 56px;
    line-height: 56px;
    line-height: 56px;
    box-shadow: 0 3px 7px 0 rgba(191, 195, 205, 0.19);
    .title-item {
      width: 50%;
      float: left;
      font-size: 14px;
      color: #333;
      .iconfont {
        float: left;
        font-size: 22px;
        color: $colorBlue;
        margin-right: 4px;
      }
      &:last-child {
        padding-left: 10px;
      }
    }
  }
  .table-content {
    padding-bottom: 60px;
    overflow: hidden;
    .table-item {
      float: left;
      width: 50%;
      padding-right: 10px;
      &:last-child {
        padding-left: 10px;
        padding-right: 0;
      }
    }
  }
}
// vuep模板
.CodeMirror {
  height: 100%;
  .CodeMirror-code > div {
    line-height: 24px;
  }
}

// 新增修改查看标识，标识模板列表中文名太长  自动换行
.dialog-prefix-add,
.dialog-prefix-change,
.dialog-prefix-detail {
  .el-form-item__label {
    word-break: break-word;
  }
}

// TODO: css隐藏pagination最后页的按钮
.el-icon.more.btn-quicknext + .number {
  display: inline-block;
  width: 0 !important;
  height: 0 !important;
  min-width: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
  overflow: hidden !important;
}

/*============================================================
	** 上传后面的文字提示样式
==============================================================*/

.upload_tip {
  position: relative;
  top: -29px;
  display: inline-block;
  line-height: 15px;
  font-size: 10px;
  color: $font-color-2;
}

.upload_tip1 {
  position: relative;
  top: -15px;
  transform: scale(0.95);
}

/*============================================================
	** 审核页面： 企业信息列表
==============================================================*/
/*企业信息列表*/
.entInfo_table {
  padding: 0 20px;
  margin-bottom: 20px;
  .el-row {
    padding: 17px 0;
    border-bottom: 1px solid $border-color-primary;
    .el-col {
      min-height: 13px;
      padding-right: 10px;
      // word-wrap: break-word;
      // word-break: normal;
      word-break: break-all;
    }
    .el-col:first-child {
      padding-left: 20px;
    }
  }
  // .el-row:last-child {
  //   border-bottom: none;
  // }
  .el-row.module-row-head {
    padding: 0 0 10px;
    border-bottom: solid $border-color-primary;
    font-weight: 500;
  }
}
/*============================================================
	** 审核页面： 锚点
==============================================================*/
/* 锚点滚动: 解决fixed失效，将滚动条transform禁掉*/
.gm-scrollbar-container .gm-scroll-view {
  transform: none;
}

/*锚点位置偏离，修复锚点*/
.anchor-fix {
  position: relative;
  top: -60px;
  display: block;
  height: 0;
  overflow: hidden;
}

/*============================================================
	** 表格：通用表格样式
==============================================================*/

.module-table {
  .table-title {
    height: 30px;
    line-height: 30px;
    margin: 10px 0 10px 0;
    h5 {
      font-size: 14px;
      float: left;
      font-weight: 500;
      margin: 0 15px 0 0;
    }
    .el-button {
      float: right;
      position: relative;
      top: 1px;
      border: 1px solid #c8c9cd;
      padding: 7px;
      font-size: 12px;
      color: #444;
    }
  }
  // 表格样式
  .el-table {
    overflow: visible;
    margin: 10px 0;
    color: $font-color-primary;
    .btn-table-edit {
      font-size: 14px;
    }
    .el-table__row {
      height: 50px;
    }
    .el-table__body-wrapper {
      overflow: visible;
      tr {
        td:first-child .cell {
          padding-left: 20px;
        }
      }
    }
    .el-table__header-wrapper {
      tr {
        height: 40px;
        border-bottom: 1px solid $border-color-primary;
        th {
          background-color: $bg-color-1;
          color: $font-color-3;
        }
        th:first-child .cell {
          padding-left: 20px;
        }
      }
    }
  }
}
/*============================================================
	** 分页
==============================================================*/
:deep(.table-page) {
  height: 50px;
  padding-top: 6px;
  text-align: center;
  .btn-prev,
  .btn-next {
    width: 26px;
    min-width: 26px;
    height: 26px;
    padding: 0;
    background: $bg-color-4;
    border-radius: 4px;
  }
  .btn-prev {
    margin-right: 5px;
  }
  .btn-next {
    margin-left: 5px;
  }
  .el-pager {
    .number {
      min-width: 26px;
      height: 26px;
      margin: 0 5px;
      line-height: 26px;
      font-size: 12px;
      color: $font-color-primary;
      &.active {
        color: #fff;
        background-color: $colorBlue;
        border-radius: 4px;
      }
    }
  }
}

/*============================================================
	** 全屏对话框： 居中，两边留白
==============================================================*/
.dialog-middle {
  .dialog-content {
    min-height: 100%;
    padding: 10px 2%;
    // margin: 0 16%;
  }
  .dialog-footer {
    border: none;
    text-align: center;
    margin-bottom: 30px;
  }

  .footer-content {
    text-align: left;
  }
}

/*============================================================
	** 全屏对话框： 里面的简单table
==============================================================*/
.simple-table {
  .el-table__row {
    height: 50px;
    font-size: 12px;
    border: 1px solid $border-color-primary;
    td {
      padding: 0;
    }
  }
  th {
    height: 40px;
    padding: 5px 0;
    font-size: 12px;
    background-color: $bg-color-1;
  }
  .el-form-item {
    margin-bottom: 0;
  }
  .el-form-item__error {
    top: 26px;
  }
}

/*============================================================
	** el-switch开关
==============================================================*/
:deep(.licence_switch) {
  .el-switch__core {
    position: relative;
    top: -1px;
    width: 36px !important;
    height: 18px;
    cursor: default;
    &::after {
      width: 14px;
      height: 14px;
    }
  }
}

// 操作的图标
.img-btn {
  position: relative;
  top: 3px;
  margin-right: 8px;
}
i.icon-common {
  position: relative;
  top: 1.5px;
  margin-right: 8px;
  color: #484848;
  font-size: 19px;
}

/*============================================================
	** 1030版本：替换导航栏和左侧菜单栏的图标
==============================================================*/
//一级菜单栏，调整图标大小
.top-nav-container .header-items .iconfont {
  font-size: 16px;
  &.icon-data {
    font-size: 14px;
  }
  &.icon-peizhiguanli {
    font-size: 17px;
  }
  &.icon-dingdan {
    font-size: 15px;
  }
}

// 侧边栏图标: 默认font-size:16px;margin-right:2px;
.side-nav .sidebar-menu .el-menu-item {
  .icon-ceshi,
  .icon-PDM {
    font-size: 12px;
    margin-right: 3px;
  }
  .icon-EPP,
  .icon-rizhi,
  .icon-yonghurizhi,
  .icon-yonghurizhi1,
  .icon-rizhi1 {
    font-size: 14px;
    margin-right: 4px;
  }
  .icon-tuoguan1 {
    font-size: 18px;
    margin-right: 0;
  }
}

// 有多级的，增加边距
.icon-ys_tongjifenxi,
.icon-regulatory-reporting {
  margin-right: 4px;
}

/*============================================================
	** 单行字符串溢出
==============================================================*/
.textOverflow {
  overflow: hidden; /*溢出的部分隐藏*/
  white-space: nowrap; /*文本不换行*/
  text-overflow: ellipsis; /*ellipsis:文本溢出显示省略号（...）*/
}

.atooltip.el-tooltip__popper[x-placement^="top"] .popper__arrow {
  border-top-color: #fff3cb;
}
.atooltip.el-tooltip__popper[x-placement^="top"] .popper__arrow:after {
  border-top-color: #fff3cb;
}
.atooltip {
  background: #fff3cb !important;
  border: #fff3cb !important;
}

// form表单
.snms-form {
  .is-error {
    .el-input__inner,
    .el-textarea__inner {
      background: #ffece8;
    }
  }
  .el-input__inner,
  .el-textarea__inner {
    background: #f2f3f8;
    border-color: transparent !important;
    color: #1d2129;
    border-radius: 2px;
  }
  .el-form-item__label {
    padding: 0 16px 0 0;
  }
  .el-checkbox__inner {
    border: 2px solid #c9cdd4;
  }
}

// button
.snms-button-default {
  background: #f2f3f8;
  border-radius: 2px;
  color: #1d2129;
  font-weight: 400;
}

.snms-button-primary {
  background: #1664ff;
  border-radius: 2px;
  color: #1d2129;
  font-weight: 400;
}

// confirm
.snms-confirm-warning {
  .el-message-box__header {
    display: none;
  }
  .el-message-box__content {
    padding: 24px 25px 20px 25px;
  }
  .el-icon-warning {
    color: #ff7d00;
    font-size: 20px !important;
  }
  .el-message-box__message {
    padding-left: 30px !important;
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    color: #1d2129;
  }
  .el-button--default {
    background: #f2f3f5;
    border-radius: 2px;
    border-color: transparent !important;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: #1d2129;
    padding: 5px 16px;
  }
  .el-button--primary {
    background-color: #1664ff;
    font-weight: 600;
    color: #fff;
  }
  .el-message-box__btns button:nth-child(2) {
    margin-left: 12px;
  }
}
