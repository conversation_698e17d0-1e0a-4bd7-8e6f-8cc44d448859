<template>
  <el-dialog
    v-model="dialogFormVisible"
    title="编辑"
    width="500px"
    @close="$emit('close-bindHdl-dialog')"
    destroy-on-close
    align-center
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="省级前缀" prop="prefix">
        <el-input v-model.trim="formData.prefix" clearable disabled />
      </el-form-item>
      <el-form-item label="节点名称" prop="nodeName">
        <el-input v-model.trim="formData.nodeName" clearable disabled />
      </el-form-item>
      <el-form-item label="上报地址" prop="reportUrl">
        <el-input v-model.trim="formData.reportUrl" clearable />
      </el-form-item>
      <el-form-item label="Appid" prop="appid">
        <el-input v-model.trim="formData.appid" clearable />
      </el-form-item>
      <el-form-item label="私钥" prop="privateKey">
        <el-input
          v-model.trim="formData.privateKey"
          type="textarea"
          clearable
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="bindHdl"> 确认 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { getCurrentInstance, reactive, ref } from "vue";
import { ElMessage } from "element-plus";
import { update } from "@/api/systemManage/reportConfig";
import { IReportConfig } from "@/types/system/reportConfig";

const { proxy }: any = getCurrentInstance();
const emit = defineEmits(["dialog-visible", "update-table"]);
const props = defineProps({
  infoData: {
    type: Object as () => IReportConfig,
    default: () => ({}),
  },
});

const dialogFormVisible = ref(true);
const formData = ref({
  prefix: props.infoData.prefix,
  nodeName: props.infoData.nodeName,
  appid: props.infoData.appid,
  reportUrl: props.infoData.reportUrl,
  privateKey: "",
});

/**
 * 校验上报地址
 */
const checkReportUrl = (rule: any, value: any, callback: any) => {
  if (value.indexOf("http://") !== 0 && value.indexOf("https://") !== 0) {
    return callback(new Error("上报地址格式不正确"));
  }
  callback();
};

const rules = reactive({
  reportUrl: [
    {
      required: true,
      message: "请输入上报地址",
      trigger: "blur",
    },
    { required: true, validator: checkReportUrl, trigger: "blur" },
  ],
  appid: [
    {
      required: true,
      message: "请输入Appid",
      trigger: "blur",
    },
  ],
  privateKey: [
    {
      required: true,
      message: "请输入私钥",
      trigger: "blur",
    },
  ],
});

function bindHdl() {
  proxy.$refs.formRef.validate(async (valid: any) => {
    if (valid) {
      update(formData.value).then((res: any) => {
        dialogFormVisible.value = false;
        emit("update-table", true);
        ElMessage({
          message: "修改成功!",
          type: "success",
        });
      });
    }
  });
}
</script>

<style lang="scss"></style>
