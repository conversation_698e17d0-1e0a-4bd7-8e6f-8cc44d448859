<template>
  <div>
    <el-tabs
      v-model="activeName"
      class="demo-tabs"
      @tab-change="handleTabChange"
    >
      <el-tab-pane label="企业账号" name="entAccount">
        <Index v-if="!loadAppaccount" />
      </el-tab-pane>
      <el-tab-pane label="应用账号" name="appAccount">
        <appAccount v-if="loadAppaccount" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import Index from "./index.vue";
import appAccount from "./appAccount/index.vue";

const activeName = ref("entAccount");
const loadAppaccount = ref(false);

const handleTabChange = (val: string) => {
  if (val === "entAccount") {
    loadAppaccount.value = false;
  } else if (val === "appAccount") {
    loadAppaccount.value = true;
  }
};
</script>

<style lang="scss" scoped></style>
