<template>
  <el-dialog
    v-model="dialogFormVisible"
    title="账号解绑"
    width="700px"
    @close="$emit('close-dialog')"
    align-center
  >
    <div v-loading="loading">
      <div class="yc-description">
        <div class="yc-description-title">
          <div class="yc-description-title-tip"></div>
          <div class="yc-description-title-text">账号信息</div>
        </div>
        <el-descriptions :column="1" direction="horizontal">
          <el-descriptions-item label="用户名">{{
            bindInfo?.username || "-"
          }}</el-descriptions-item>
          <!-- <el-descriptions-item label="密码">{{
            bindInfo?.password || "-"
          }}</el-descriptions-item> -->
        </el-descriptions>
      </div>
      <el-divider />
      <div class="yc-description">
        <div class="yc-description-title">
          <div class="yc-description-title-tip"></div>
          <div class="yc-description-title-text">账号绑定</div>
        </div>
        <el-descriptions :column="1" direction="horizontal">
          <el-descriptions-item label="账号">{{
            bindInfo?.account || "-"
          }}</el-descriptions-item>
          <!-- <el-descriptions-item label="密码">{{
            bindInfo?.password || "-"
          }}</el-descriptions-item> -->
          <el-descriptions-item label="手机号">{{
            bindInfo.phone
              ? bindInfo.phone.replace(/^(.{3}).*(.{4})$/, "$1****$2")
              : "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="邮箱">
            {{
              bindInfo?.email
                ? bindInfo?.email.split("@")[0].substring(0, 3) +
                  "****@" +
                  bindInfo?.email.split("@")[1]
                : "-"
            }}</el-descriptions-item
          >
          <el-descriptions-item label="组织机构代码">{{
            bindInfo?.orgCode || "-"
          }}</el-descriptions-item>
          <el-descriptions-item label="组织/单位名称">{{
            bindInfo?.orgName || "-"
          }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <template #footer>
      <span>
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-popconfirm :title="`确定要解除绑定吗？`" @confirm="unBind">
          <template #reference>
            <el-button type="primary" :loading="unBindBtnLoading">
              解绑
            </el-button>
          </template>
        </el-popconfirm>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";
import { ElMessage } from "element-plus";
import { unbindHdlApi, ApiGetBindInfo } from "@/api/systemManage/account";
import { IBindInfo } from "@/types/system/account";

const emit = defineEmits(["close-dialog", "update-table"]);
const props = defineProps({
  userId: {
    type: Number,
    default: null,
  },
});
const unBindBtnLoading = ref(false);
const loading = ref(false);
const dialogFormVisible = ref(true);
const bindInfo = ref<IBindInfo>({
  username: "",
  password: "",
  account: "",
  orgCode: "",
  orgName: "",
  phone: "",
  email: "",
  id: null,
});
function unBind() {
  unBindBtnLoading.value = true;
  unbindHdlApi({ id: bindInfo.value.id }).then(() => {
    ElMessage.success("解绑成功!");
    emit("update-table", true);
    dialogFormVisible.value = false;
    unBindBtnLoading.value = false;
  });
}

function getBindInfo() {
  loading.value = true;
  ApiGetBindInfo({ id: props.userId }).then((res) => {
    bindInfo.value = res;
    loading.value = false;
  });
}

onMounted(() => {
  getBindInfo();
});
</script>

<style lang="scss"></style>
