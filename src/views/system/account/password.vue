<template>
  <el-dialog
    v-model="dialogFormVisible"
    title="修改密码"
    width="500px"
    @close="$emit('close-password-dialog')"
    destroy-on-close
    align-center
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <!-- <el-form-item label="原始密码" prop="oldPwd">
        <el-input v-model.trim="formData.oldPwd" clearable />
      </el-form-item> -->
      <el-form-item label="新密码" prop="newPwd">
        <el-input v-model.trim="formData.newPwd" show-password clearable />
      </el-form-item>
      <el-form-item label="确认密码" prop="duplicatePwd">
        <el-input
          v-model.trim="formData.duplicatePwd"
          autocomplete="off"
          show-password
          clearable
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="updatePwd(formRef)"
          :loading="btnLoading"
        >
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { useStore } from "vuex";
import { updatePwdApi } from "@/api/systemManage/account";
import { getPublicKey, encrypt } from "@/utils/forgeEncrypt";

const emit = defineEmits(["dialog-visible", "update-table"]);

// const props = defineProps(["userId"]);
const props = defineProps({
  userId: {
    type: Number,
    default: null,
  },
});

const btnLoading = ref(false);
const dialogFormVisible = ref(true);

const formRef = ref<FormInstance>();
const formData = reactive({
  oldPwd: "",
  newPwd: "",
  duplicatePwd: "",
});
const checkPassWord = (rule: any, value: string, callback: any) => {
  // ⾄8-16位，必须包含大写字母、小写字母 、数字、 特殊字符（四种里至少三种，8-16位）
  const reg =
    /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,20}$/;
  if (!reg.test(value)) {
    callback(new Error("8-20位，包含大小写字母、数字、特殊符号至少三种"));
  } else {
    callback();
  }
};
const rules = reactive({
  oldPwd: [
    {
      required: true,
      message: "请输入原始密码",
      trigger: "blur",
    },
  ],
  newPwd: [
    { required: true, trigger: "blur", message: "请输入您的新密码" },
    { required: true, validator: checkPassWord, trigger: "blur" },
  ],
  duplicatePwd: [
    {
      required: true,
      message: "请输入确认密码",
      trigger: "blur",
    },
  ],
});

const store = useStore();
const globalConfig = computed(() => store.getters.globalConfig);

const updatePwd = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  const publicKey = await getPublicKey();
  await formEl.validate((valid) => {
    if (valid) {
      const { oldPwd, newPwd, duplicatePwd } = formData;
      if (newPwd !== duplicatePwd)
        return ElMessage.warning("新密码与确认密码不一致!");

      btnLoading.value = true;

      updatePwdApi({
        id: props.userId,
        oldPwd: globalConfig.value?.login.passwordEncrypted
          ? encrypt(oldPwd, publicKey)
          : oldPwd,
        newPwd: globalConfig.value?.login.passwordEncrypted
          ? encrypt(newPwd, publicKey)
          : newPwd,
        duplicatePwd: globalConfig.value?.login.passwordEncrypted
          ? encrypt(duplicatePwd, publicKey)
          : duplicatePwd,
      })
        .then(() => {
          dialogFormVisible.value = false;
          emit("update-table", true);
          ElMessage.success("修改密码成功!");
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};
</script>

<style lang="scss"></style>
