<template>
  <div>
    <proIndex v-if="isProvince && !isSuperAdmin" />
    <entIndex v-if="isProxy" />
    <superIndex v-if="isSuperAdmin" />
  </div>
</template>
<script setup lang="ts">
import { useStore } from "vuex";
import { computed } from "vue";
import { LEVEL_TYPE, ROLE_TYPE } from "@/utils/constant";
import proIndex from "./index.vue";
import entIndex from "./entIndex.vue";
import superIndex from "./superIndex.vue";

const store = useStore();
const userInfo = computed(() => store.getters.userInfo);
const isProxy = computed(() => userInfo.value.levelType === LEVEL_TYPE.PROXY);
const isProvince = computed(
  () => userInfo.value.levelType === LEVEL_TYPE.PROVINCE
);

// roleType=0,为超级管理员
const isSuperAdmin = computed(() =>
  userInfo.value.roleInfos.some(
    (item: any) => item.roleType === ROLE_TYPE.SUPER_ADMIN
  )
);
</script>
<style></style>
