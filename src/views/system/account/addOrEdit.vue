<template>
  <el-dialog
    v-model="dialogFormVisible"
    :title="`${isAdd ? '新增用户' : '编辑账户'}`"
    width="800px"
    height="600px"
    @close="$emit('close-dialog')"
    destroy-on-close
    align-center
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="组织" prop="orgId">
        <el-select
          clearable
          v-model="formData.orgId"
          placeholder="请选择组织"
          style="width: 100%"
        >
          <el-option
            v-for="item in orgList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="用户" prop="userId">
        <div class="inline" style="width: 100%">
          <el-input
            v-model.trim="formData.userId"
            placeholder="请在下表中选择"
            style="width: 240px"
            disabled
            clearable
          />
          <el-input
            v-model="searchText"
            style="width: 180px"
            placeholder="请搜索姓名或登录名"
            :suffix-icon="Search"
          />
        </div>
      </el-form-item>
      <el-table
        :data="tableData"
        size="small"
        style="margin: 0 0 20px 100px; width: 660px"
        border
      >
        <el-table-column label="序号">
          <template #default="scope">
            <span>{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="员工姓名" property="name">
          <template #default="scope">
            <ellipsisText :value="scope.row.name"></ellipsisText>
          </template>
        </el-table-column>
        <el-table-column label="登录名" property="username">
          <template #default="scope">
            <ellipsisText :value="scope.row.username"></ellipsisText>
          </template>
        </el-table-column>
        <el-table-column label="职务" property="position">
          <template #default="scope">
            <ellipsisText :value="scope.row.position"></ellipsisText>
          </template>
        </el-table-column>
      </el-table>
      <el-form-item label="角色" prop="roles" v-if="isProvinceActive">
        <el-select
          v-model="formData.roles"
          multiple
          placeholder="请选择角色"
          style="width: 100%"
        >
          <el-option
            v-for="item in roleOptions.list"
            :key="item.id"
            :label="item.roleName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属企业" prop="roles" v-if="isProvinceActive">
        <el-select
          v-model="formData.roles"
          multiple
          placeholder="请选择企业"
          style="width: 100%"
        >
          <el-option
            v-for="item in roleOptions.list"
            :key="item.id"
            :label="item.roleName"
            :value="item.id"
          />
        </el-select>
        <el-button type="primary" @click="addCompany">新增企业</el-button>
      </el-form-item>
      <el-form-item label="所属应用" prop="roles" v-if="isProvinceActive">
        <el-select
          v-model="formData.roles"
          multiple
          placeholder="请选择企业"
          style="width: 100%"
        >
          <el-option
            v-for="item in roleOptions.list"
            :key="item.id"
            :label="item.roleName"
            :value="item.id"
          />
        </el-select>
        <el-button type="primary" @click="addCompany">新增应用</el-button>
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="validForm" :loading="btnLoading">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { Search } from "@element-plus/icons-vue";
import {
  addAdminApi,
  updateAdminApi,
  getAdminDetailApi,
} from "@/api/systemManage/account";
import { getProvinceTenantList } from "@/api/login";
import { roleDropdownNoApp } from "@/api/systemManage/role";

const emit = defineEmits(["dialog-visible", "update-table"]);

interface TenantList {
  provinceId: string;
  name: string;
}
const tenantOptions: { list: TenantList[] } = reactive({
  list: [],
});
// const props = defineProps(["isAdd", "rowData", "isProvinceActive"]);
const props = defineProps({
  isAdd: {
    type: Boolean,
    default: true,
  },
  rowData: {
    type: Object as () => {
      address: string | null;
      email: string | null;
      entName: string | null;
      handleUser: string | null;
      id: number;
      nickName: string | null;
      phone: string | null;
      remark: string | null;
      roles: string | null;
      username: string | null;
      provinceId?: string | null;
    },
    default: null,
  },
  isProvinceActive: {
    type: Boolean,
    default: true,
  },
  isSupperAccount: {
    type: Boolean,
    default: false,
  },
});
const btnLoading = ref(false);
const formRef = ref<FormInstance>();
const dialogFormVisible = ref(true);

interface RoleList {
  id: number;
  roleName: string;
}
const roleOptions: { list: RoleList[] } = reactive({ list: [] });

interface FormData {
  roles?: number[];
  provinceId: string | null;
  username: string;
  nickName: string | null;
  phone: string;
  email: string | null;
  address: string | null;
  remark: string | null;
}
const formData = ref<FormData>({
  roles: [],
  provinceId: "",
  username: "",
  nickName: "",
  phone: "",
  email: "",
  address: "",
  remark: null,
});

/**
 * 校验邮箱正则
 */
const checkEmail = (rule: any, value: any, callback: any) => {
  const reg = /^$|^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
  if (!reg.test(value)) {
    return callback(new Error("邮箱格式不正确"));
  }
  callback();
};

/**
 * 校验角色id
 */
const checkRoles = (rule: any, value: any, callback: any) => {
  if (!props.isSupperAccount && !value) {
    return callback(new Error("请选择角色id"));
  }
  callback();
};

const rules = reactive({
  orgId: [
    { required: true, message: "请输入登录名", trigger: "blur" },
    { max: 30, message: "登录名最大长度不超过30", trigger: "blur" },
    {
      trigger: "change",
      validator: (rule: any, value: any, cb: any) => {
        if (/^[0-9a-zA-Z_]{1,}$/.test(value)) {
          cb();
        } else {
          cb(new Error("只允许输入数字、字母、下划线"));
        }
      },
    },
  ],
  userId: [{ required: true, message: "", trigger: "blur" }],
  roles: [{ required: true, validator: checkRoles, trigger: "change" }],
});

// 获取租户列表
const getTenantOptions = () => {
  // 0所有1过滤国家烟草
  const formTemp = {
    type: 1,
  };
  getProvinceTenantList(formTemp).then((res) => {
    tenantOptions.list = Array.isArray(res) ? res : [];
  });
};

onMounted(() => {
  getRoleList();
  if (!props.isAdd) {
    getAdminDetail();
  }
  if (props.isSupperAccount) {
    getTenantOptions();
  }
});

const getAdminDetail = () => {
  getAdminDetailApi({ id: props.rowData?.id }).then((res) => {
    formData.value = res || {};
    const { roleInfos } = res || {};
    const roles: number[] = [];
    roleInfos.forEach((item: any) => {
      roles.push(item.id);
    });
    formData.value.roles = roles;
  });
};

const getRoleList = () => {
  if (!props.isSupperAccount) {
    roleDropdownNoApp().then((res) => {
      roleOptions.list = Array.isArray(res) ? res : [];
    });
  }
};

const validForm = () => {
  if (!formRef.value) return;
  formRef.value.validate((valid) => {
    if (valid) {
      addOrEditAccount();
    } else {
      ElMessage.error("校验不通过!");
      return false;
    }
  });
};

const addOrEditAccount = () => {
  const api = props.isAdd ? addAdminApi : updateAdminApi;

  btnLoading.value = true;
  api(formData.value)
    .then(() => {
      dialogFormVisible.value = false;
      emit("update-table", true);
      ElMessage({
        message: `${props.isAdd ? "新增" : "编辑"}账户成功!`,
        type: "success",
      });
    })
    .finally(() => {
      btnLoading.value = false;
    });
};
</script>

<style lang="scss">
.inline {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
</style>
