<template>
  <div>
    <SearchLayout>
      <template #left>
        <el-button
          v-if="enableAddUser"
          type="primary"
          @click="() => openDialog(true)"
          >新增</el-button
        >
      </template>
      <template #right>
        <el-form :inline="true" :model="formData" @submit.prevent>
          <el-form-item style="width: 240px; max-width: 240px">
            <el-select
              v-model="formData.provinceId"
              placeholder="请选择省级节点"
              style="width: 100%"
              @clear="getTableData(true)"
              clearable
            >
              <el-option
                v-for="item in tenantOptions.list"
                :key="item.provinceId"
                :label="item.name"
                :value="item.provinceId"
              />
              <template #prefix>省级节点：</template>
            </el-select>
          </el-form-item>
          <el-form-item style="width: 240px; max-width: 240px">
            <el-input
              v-model.trim="formData.username"
              maxlength="30"
              placeholder="请输入登录名"
              @clear="getTableData(true)"
              clearable
            >
              <template #prefix>登录名：</template>
            </el-input>
          </el-form-item>
          <el-form-item style="width: 60px; max-width: 60px">
            <el-button type="primary" @click="() => getTableData(true)"
              >搜索</el-button
            >
          </el-form-item>
        </el-form>
      </template>
    </SearchLayout>

    <el-table
      border
      :data="tableData.list"
      style="width: 100%"
      v-loading="tableLoading"
      size="small"
    >
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column prop="username" label="登录名">
        <template #default="scope">
          <span v-copy="scope.row.username">{{
            scope.row.username || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="nodeName" label="省级节点" show-overflow-tooltip>
        <template #default="scope">
          <span v-copy="scope.row.nodeName">{{
            scope.row.nodeName || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="roles" label="角色" />
      <el-table-column prop="email" label="邮箱">
        <template #default="scope">
          {{
            scope.row.email
              ? scope.row.email.split("@")[0].substring(0, 3) +
                "****@" +
                scope.row.email.split("@")[1]
              : "-"
          }}
        </template>
      </el-table-column>
      <el-table-column prop="phone" label="手机">
        <template #default="scope">
          {{
            scope.row.phone
              ? scope.row.phone.replace(/^(.{3}).*(.{4})$/, "$1****$2")
              : "-"
          }}
        </template>
      </el-table-column>
      <el-table-column prop="address" label="地址" show-overflow-tooltip>
        <template #default="scope">
          {{ scope.row.address || "-" }}
        </template>
      </el-table-column>
      <el-table-column prop="remark" :show-overflow-tooltip="true" label="备注">
        <template #default="scope">
          {{ scope.row.remark || "-" }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button
            text
            type="primary"
            @click="() => openDialog(false, scope.row)"
            >编辑</el-button
          >
          <span style="margin: 0 12px">
            <el-button
              text
              type="primary"
              v-if="scope.row.binding"
              @click="clickUnBindBtn(scope.row)"
            >
              解绑
            </el-button>
            <el-button
              text
              type="primary"
              v-else
              @click="clickBindBtn(scope.row)"
              >绑定</el-button
            >
          </span>
          <el-button text type="primary" @click="clickPasswordBtn(scope.row)">
            修改密码
          </el-button>

          <el-popconfirm
            :title="`确认要删除用户[${scope.row.username}]吗？`"
            width="250"
            @confirm="clickDelBtn(scope.row)"
          >
            <template #reference>
              <el-button text type="primary">删除</el-button>
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="pagination.currentPage"
      v-model:page-size="pagination.pageSize"
      :page-sizes="[10, 20, 30, 40]"
      size="small"
      layout="total,  prev, pager, next, sizes,jumper"
      :total="pagination.total"
    />
    <AddOrEdit
      v-if="addOrEditVisible"
      @close-dialog="addOrEditVisible = false"
      @update-table="getTableData"
      :rowData="selectRow.data"
      :isAdd="isAddDialog"
      :isProvinceActive="isProvinceActive"
      :isSupperAccount="isSupperAccount"
    />
    <Password
      v-if="passwordVisible"
      @close-password-dialog="passwordVisible = false"
      @update-table="getTableData(true)"
      :userId="selectUserId"
    />
    <BindAccount
      v-if="bindHdlVisible"
      @close-dialog="bindHdlVisible = false"
      @update-table="getTableData"
      :userId="selectUserId"
    />
    <UnbindAccount
      v-if="unbindHdlVisible"
      @close-dialog="unbindHdlVisible = false"
      @update-table="getTableData"
      :userId="selectUserId"
    />
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, watch, computed, ref } from "vue";
import { useStore } from "vuex";
import axios from "axios";
// import { useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { accountList, deleteAccountApi } from "@/api/systemManage/account";
import { getProvinceTenantList } from "@/api/login";
import { ADMIN } from "@/utils/constant";
// import Password from "./password.vue";
// import AddOrEdit from "./addOrEdit.vue";
// import BindAccount from "./bindAccount.vue";
// import UnbindAccount from "./unbindAccount.vue";
import { List } from "@/types/system/account";
// import SearchLayout from "@/components/searchLayout/index.vue";

interface TenantList {
  provinceId: string;
  name: string;
}
const store = useStore();
const enableAddUser = computed(() => {
  return store.getters.enableAddUser;
});

const tenantOptions: { list: TenantList[] } = reactive({
  list: [],
});
const formData = reactive({
  username: "",
  entName: "",
  provinceId: "",
});
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

const tableLoading = ref(false);

const tableData: { list: List[] } = reactive({
  list: [],
});

const selectUserId = ref<number>();

const passwordVisible = ref(false);
const isProvinceActive = ref(false);
const isSupperAccount = ref(true);
const bindHdlVisible = ref(false);
const unbindHdlVisible = ref(false);

const selectRow: { data: List } = reactive({
  data: {
    address: null,
    email: null,
    entName: null,
    handleUser: null,
    id: 0,
    nickName: null,
    phone: null,
    remark: null,
    roles: null,
    username: null,
    provinceId: null,
  },
});
const isAddDialog = ref(true);

const addOrEditVisible = ref(false);

onMounted(() => {
  getTableData(true);
});

watch([() => pagination.currentPage, () => pagination.pageSize], () => {
  getTableData(false);
});

const getTableData = (isReset: boolean) => {
  if (isReset) {
    pagination.currentPage = 1;
    pagination.pageSize = 10;
    pagination.total = 0;
  }
  const { currentPage, pageSize } = pagination;
  tableLoading.value = true;
  const type = 1;

  accountList({
    username: formData.username,
    entName: formData.entName,
    provinceId: formData.provinceId,
    type,
    currentPage,
    pageSize,
  })
    .then((res) => {
      const { content, totalCount } = res;
      pagination.total = totalCount;

      tableData.list = content || [];
    })
    .finally(() => {
      tableLoading.value = false;
    });
};

const clickPasswordBtn = (row: any) => {
  passwordVisible.value = true;
  selectUserId.value = row.id;
};

const clickDelBtn = (row: any) => {
  deleteAccountApi({ id: row.id }).then(() => {
    ElMessage.success("账号删除成功!");
    getTableData(true);
  });
};

const clickBindBtn = (row: any) => {
  bindHdlVisible.value = true;
  selectUserId.value = row.id;
};

const clickUnBindBtn = (row: any) => {
  unbindHdlVisible.value = true;
  selectUserId.value = row.id;
};

const openDialog = (isAdd: boolean, row?: any) => {
  isAddDialog.value = isAdd;
  addOrEditVisible.value = true;
  selectRow.data = row;
};

// 获取租户列表
const getTenantOptions = () => {
  // 0所有1过滤国家烟草
  const formTemp = {
    type: 1,
  };
  getProvinceTenantList(formTemp).then((res) => {
    tenantOptions.list = Array.isArray(res) ? res : [];
  });
};

onMounted(() => {
  // 初始化多租户下拉框
  getTenantOptions();
});
</script>

<style lang="scss" scoped>
.level-type {
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 2px solid #ebeef5;
  // :deep(.el-button--primary:active),
  // :deep(.el-button--primary:hover) {
  //   color: #fff !important;
  //   border-color: #00a57c !important;
  //   background-color: #00a57c !important;
  // }
  :deep(.el-button) {
    &:focus {
      border-color: #00a57c;
      background-color: #00a57c;
    }
  }
}
.table-opt {
  display: inline-block;
  width: 60px;
  text-align: right;
  margin-right: 12px;
}
</style>
