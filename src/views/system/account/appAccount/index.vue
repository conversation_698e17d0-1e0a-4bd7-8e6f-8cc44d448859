<template>
  <SearchLayout>
    <template #left
      ><el-button
        v-if="enableAddUser"
        type="primary"
        @click="() => openDialog(true)"
        v-permission="AUTH_CODE.USER_MANAGER_ADD"
        >新增账号</el-button
      ></template
    >
    <template #right>
      <el-form
        v-permission="AUTH_CODE.USER_MANAGER_APP_PAGE"
        :inline="true"
        :model="formData"
      >
        <el-form-item style="width: 240px; max-width: 240px">
          <el-input
            v-model.trim="formData.username"
            maxlength="30"
            placeholder="请输入"
            @clear="getTableData(true)"
            clearable
          >
            <template #prefix>登录名：</template>
          </el-input>
        </el-form-item>
        <el-form-item style="width: 240px; max-width: 240px">
          <el-input
            v-model.trim="formData.appName"
            maxlength="30"
            placeholder="请输入"
            @clear="getTableData(true)"
            clearable
          >
            <template #prefix>应用名称：</template>
          </el-input>
        </el-form-item>
        <el-form-item style="width: 60px; max-width: 60px">
          <el-button type="primary" @click="() => getTableData(true)"
            >搜索</el-button
          >
        </el-form-item>
      </el-form>
    </template>
  </SearchLayout>

  <el-table border :data="tableData.list" size="small" v-loading="tableLoading">
    <el-table-column label="序号" type="index" width="55" />
    <el-table-column prop="username" label="登录名">
      <template #default="scope">
        <span v-copy="scope.row.username">{{ scope.row.username || "-" }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="appName" label="应用名称">
      <template #default="scope">
        <span v-copy="scope.row.appName">{{ scope.row.appName || "-" }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="appPrefix" label="应用前缀">
      <template #default="scope">
        <span v-copy="scope.row.appPrefix">{{
          scope.row.appPrefix || "-"
        }}</span>
      </template>
    </el-table-column>
    <el-table-column prop="operator" label="操作人">
      <template #default="scope">
        {{ scope.row.operator || "-" }}
      </template>
    </el-table-column>
    <el-table-column prop="remark" :show-overflow-tooltip="true" label="备注">
      <template #default="scope">
        {{ scope.row.remark || "-" }}
      </template>
    </el-table-column>
    <el-table-column label="操作" width="200">
      <template #default="scope">
        <el-button
          text
          type="primary"
          @click="() => openDialog(false, scope.row)"
          v-permission="AUTH_CODE.USER_MANAGER_EDIT"
          >编辑</el-button
        >
        <span v-if="!isEntSystem" style="margin: 0 12px">
          <el-button
            text
            type="primary"
            v-if="scope.row.binding"
            @click="clickUnBindBtn(scope.row)"
            v-permission="AUTH_CODE.USER_MANAGER_UNBINDING"
          >
            解绑
          </el-button>
          <el-button
            text
            type="primary"
            v-else
            @click="clickBindBtn(scope.row)"
            v-permission="AUTH_CODE.USER_MANAGER_BINDING"
            >绑定</el-button
          >
        </span>
        <el-button
          text
          type="primary"
          @click="clickPasswordBtn(scope.row)"
          v-permission="AUTH_CODE.USER_MANAGER_PWDEDIT"
        >
          修改密码
        </el-button>

        <el-popconfirm
          :title="`确认要删除用户[${scope.row.username}]吗？`"
          width="250"
          @confirm="clickDelBtn(scope.row)"
        >
          <template #reference>
            <el-button
              text
              type="primary"
              v-permission="AUTH_CODE.USER_MANAGER_DELETE"
              >删除</el-button
            >
          </template>
        </el-popconfirm>
      </template>
    </el-table-column>
  </el-table>
  <el-pagination
    v-model:current-page="pagination.currentPage"
    v-model:page-size="pagination.pageSize"
    :page-sizes="[10, 20, 30, 40]"
    size="small"
    layout="total,  prev, pager, next, sizes,jumper"
    :total="pagination.total"
  />
  <addOrEdit
    v-if="addOrEditVisible"
    @update-table="getTableData(true)"
    @close-dialog="addOrEditVisible = false"
    :rowData="selectRow.data"
    :isAdd="isAddDialog"
  />
  <Password
    v-if="passwordVisible"
    @close-password-dialog="passwordVisible = false"
    @update-table="getTableData(true)"
    :userId="selectUserId"
  />
  <BindAccount
    v-if="bindHdlVisible"
    @close-dialog="bindHdlVisible = false"
    @update-table="getTableData"
    :userId="selectUserId"
  />
  <UnbindAccount
    v-if="unbindHdlVisible"
    @close-dialog="unbindHdlVisible = false"
    @update-table="getTableData"
    :userId="selectUserId"
  />
</template>
<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
import addOrEdit from "./addOrEdit.vue";
import Password from "../password.vue";
import BindAccount from "../bindAccount.vue";
import UnbindAccount from "../unbindAccount.vue";
import { accountList, deleteAccountApi } from "@/api/systemManage/appAccount";
import { List } from "@/types/system/appAccount";
import { SYSTEM_TYPE } from "@/utils/constant";
import { AUTH_CODE } from "@/utils/authCode";
import SearchLayout from "@/components/searchLayout/index.vue";

const store = useStore();
const isEntSystem = computed(
  () => store.getters.globalConfig.systemType === SYSTEM_TYPE.SYSTEM_PROXY
);
const enableAddUser = computed(() => {
  return store.getters.enableAddUser;
});
const tableLoading = ref(false);
const passwordVisible = ref(false);
const addOrEditVisible = ref(false);
const bindHdlVisible = ref(false);
const unbindHdlVisible = ref(false);
const isAddDialog = ref(true);

const formData = reactive({
  username: "",
  appName: "",
});
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});
const tableData: { list: List[] } = reactive({
  list: [
    {
      id: null,
      username: "",
      appName: "",
      appPrefix: "",
      operator: "",
      remark: "",
    },
  ],
});

const selectUserId = ref<number>();
const selectRow: { data: List } = reactive({
  data: {
    id: null,
    appName: null,
    username: null,
    appPrefix: null,
    operator: null,
    remark: null,
  },
});

onMounted(() => {
  getTableData(true);
});

watch([() => pagination.currentPage, () => pagination.pageSize], () => {
  getTableData(false);
});

const getTableData = (isReset: boolean) => {
  if (isReset) {
    pagination.currentPage = 1;
    pagination.pageSize = 10;
    pagination.total = 0;
  }
  const { currentPage, pageSize } = pagination;
  tableLoading.value = true;
  accountList({
    username: formData.username,
    appName: formData.appName,
    currentPage,
    pageSize,
  })
    .then((res) => {
      const { content, totalCount } = res;
      pagination.total = totalCount;
      tableData.list = content || [];
    })
    .finally(() => {
      tableLoading.value = false;
    });
};

const clickPasswordBtn = (row: any) => {
  passwordVisible.value = true;
  selectUserId.value = row.id;
};

const clickDelBtn = (row: any) => {
  deleteAccountApi({ id: row.id }).then(() => {
    ElMessage.success("账号删除成功!");
    getTableData(true);
  });
};

const openDialog = (isAdd: boolean, row?: any) => {
  isAddDialog.value = isAdd;
  addOrEditVisible.value = true;
  selectRow.data = row;
};

const clickBindBtn = (row: any) => {
  bindHdlVisible.value = true;
  selectUserId.value = row.id;
};

const clickUnBindBtn = (row: any) => {
  unbindHdlVisible.value = true;
  selectUserId.value = row.id;
};
</script>
<style></style>
