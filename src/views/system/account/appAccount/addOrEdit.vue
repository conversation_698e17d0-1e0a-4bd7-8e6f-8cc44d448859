<template>
  <el-dialog
    v-model="dialogFormVisible"
    :title="`${isAdd ? '新增账户' : '编辑账户'}`"
    width="700px"
    @close="$emit('close-dialog')"
    destroy-on-close
    align-center
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="登录名" prop="username">
        <el-input
          v-model.trim="formData.username"
          placeholder="请输入登录名，只允许输入数字、字母、下划线"
          clearable
          :disabled="!isAdd"
        />
      </el-form-item>
      <el-form-item label="应用" prop="appId">
        <el-select
          v-model="formData.appId"
          placeholder="请选择"
          :disabled="!isAdd"
          style="width: 100%"
        >
          <el-option
            v-for="item in appListData"
            :key="item.id"
            :value="item.id"
            :label="item.appName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model.trim="formData.remark"
          placeholder="请输入备注"
          clearable
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="validForm" :loading="btnLoading">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import {
  addAppApi,
  updateAppApi,
  getAppDetailApi,
} from "@/api/systemManage/appAccount";
import { FormData, List } from "@/types/system/appAccount";
import { appList } from "@/api/objHandle/manager";

const emit = defineEmits(["dialog-visible", "update-table"]);

const props = defineProps({
  isAdd: {
    type: Boolean,
    default: true,
  },
  rowData: {
    type: Object as () => List,
    default: null,
  },
});

const btnLoading = ref(false);
const formRef = ref<FormInstance>();
const dialogFormVisible = ref(true);

const appListData = ref<{ id: number; appName: string }[]>([]);

const formData = ref<FormData>({
  id: null,
  appId: null,
  username: "",
  remark: "",
});
const rules = reactive({
  username: [
    { required: true, message: "请输入登录名", trigger: "blur" },
    { max: 30, message: "登录名最大长度不超过30", trigger: "blur" },
    {
      trigger: "change",
      validator: (rule: any, value: any, cb: any) => {
        if (/^[0-9a-zA-Z_]{1,}$/.test(value)) {
          cb();
        } else {
          cb(new Error("只允许输入数字、字母、下划线"));
        }
      },
    },
  ],
  appId: [{ required: true, trigger: "change", message: "请选应用" }],
  remark: [{ max: 100, message: "备注最大长度不超过100", trigger: "blur" }],
});

const validForm = () => {
  if (!formRef.value) return;
  formRef.value.validate((valid) => {
    if (valid) {
      addOrEditAccount();
    }
  });
};

const addOrEditAccount = () => {
  const api = props.isAdd ? addAppApi : updateAppApi;

  btnLoading.value = true;
  api(formData.value)
    .then(() => {
      dialogFormVisible.value = false;
      emit("update-table", true);
      ElMessage({
        message: `${props.isAdd ? "新增" : "编辑"}账户成功!`,
        type: "success",
      });
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

const getAppDetail = () => {
  getAppDetailApi({ id: props.rowData?.id }).then((res) => {
    formData.value = res || {};
  });
};

// 获取应用列表下拉框
function getAppList() {
  appList().then((response: any) => {
    appListData.value = Array.isArray(response) ? response : [];
  });
}

onMounted(() => {
  getAppList();
  if (!props.isAdd) {
    getAppDetail();
  }
});
</script>

<style lang="scss"></style>
