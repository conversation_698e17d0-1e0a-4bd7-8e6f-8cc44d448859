<template>
  <el-dialog
    v-model="dialogFormVisible"
    title="绑定账号"
    width="700px"
    @close="$emit('close-dialog')"
    align-center
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="账号" prop="userName">
        <el-input
          v-model.trim="formData.userName"
          placeholder="请输入业务中台账号"
          clearable
        />
      </el-form-item>
      <el-form-item label="密码" prop="password">
        <el-input
          type="password"
          v-model.trim="formData.password"
          placeholder="请输入业务中台密码"
          show-password
          clearable
          :readonly="readonlyInput"
          @focus="cancelReadOnly()"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="validForm" :loading="btnLoading">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { bindHdlApi } from "@/api/systemManage/account";

const emit = defineEmits(["close-dialog", "update-table"]);
const props = defineProps({
  userId: {
    type: Number,
    default: null,
  },
});
const btnLoading = ref(false);
const formRef = ref<FormInstance>();
const dialogFormVisible = ref(true);
const readonlyInput = ref(true);
const cancelReadOnly = () => {
  readonlyInput.value = false;
};

const formData = reactive({
  userName: "",
  password: "",
});

const rules = reactive({
  userName: [{ required: true, message: "请输入账号", trigger: "blur" }],
  password: [{ required: true, message: "请输入密码", trigger: "blur" }],
});

const validForm = () => {
  if (!formRef.value) return;
  formRef.value.validate((valid) => {
    if (valid) {
      btnLoading.value = true;
      const params = {
        username: formData.userName,
        password: formData.password,
        id: props.userId,
      };
      bindHdlApi(params)
        .then(() => {
          ElMessage.success("绑定成功！");
          emit("update-table", true);
          dialogFormVisible.value = false;
        })
        .finally(() => {
          btnLoading.value = false;
        });
    }
  });
};
</script>

<style lang="scss"></style>
