<template>
  <el-dialog
    v-model="dialogFormVisible"
    title="新增企业账户"
    width="700px"
    @close="$emit('close-dialog')"
    align-center
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="登录名" prop="username">
        <el-input
          v-model.trim="formData.username"
          placeholder="请输入登录名，只允许输入数字、字母、下划线"
          clearable
        />
      </el-form-item>
      <el-form-item label="手机" prop="phone">
        <el-input
          v-model.trim="formData.phone"
          placeholder="请输入手机号"
          type="password"
          show-password
          clearable
        />
      </el-form-item>
      <el-form-item label="操作类型" prop="operateType">
        <el-radio-group v-model="operateType">
          <el-radio :label="1">新增企业</el-radio>
          <el-radio :label="2">已有企业</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="企业名称" prop="entName" v-if="operateType === 1">
        <el-input
          v-model.trim="formData.entName"
          placeholder="请输入企业名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="选择企业" prop="entId" v-if="operateType === 2">
        <el-select
          v-model="formData.entId"
          placeholder="请选择企业"
          style="width: 100%"
        >
          <el-option
            v-for="item in entOptions.list"
            :key="item.id"
            :label="item.orgName"
            :value="item.id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input
          v-model.trim="formData.email"
          placeholder="请输入邮箱"
          type="password"
          show-password
          clearable
        />
      </el-form-item>
      <!-- <el-form-item label="标识身份" prop="handleUser">
        <el-input
          v-model="formData.handleUser"
          placeholder="请输入标识身份"
          clearable
        />
      </el-form-item> -->
      <el-form-item label="地址" prop="address">
        <el-input
          v-model.trim="formData.address"
          placeholder="请输入地址"
          clearable
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model.trim="formData.remark"
          placeholder="请输入备注"
          clearable
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="validForm" :loading="btnLoading">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { addEntApi, getEntInfoApi } from "@/api/systemManage/account";
import { FormData, EntInfo } from "@/types/system/account";

const emit = defineEmits(["dialog-visible", "update-table"]);

const btnLoading = ref(false);
const formRef = ref<FormInstance>();
const dialogFormVisible = ref(true);

const operateType = ref(1);

const entOptions: { list: EntInfo[] } = reactive({ list: [] });

/**
 * 校验邮箱正则
 */
const checkEmail = (rule: any, value: any, callback: any) => {
  const reg = /^$|^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
  if (!reg.test(value)) {
    return callback(new Error("邮箱格式不正确"));
  }
  callback();
};

const formData = ref<FormData>({
  entId: null,
  username: "",
  nickName: "",
  phone: "",
  email: "",
  address: "",
  remark: "",
  entName: "",
});
const rules = reactive({
  username: [
    { required: true, message: "请输入登录名", trigger: "blur" },
    { max: 30, message: "登录名最大长度不超过30", trigger: "blur" },
    {
      trigger: "change",
      validator: (rule: any, value: any, cb: any) => {
        if (/^[0-9a-zA-Z_]{1,}$/.test(value)) {
          cb();
        } else {
          cb(new Error("只允许输入数字、字母、下划线"));
        }
      },
    },
  ],
  phone: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    {
      required: true,
      trigger: "change",
      validator: (rule: any, value: any, cb: any) => {
        if (!value) return cb();
        if (/^(\+\d+)?1[3456789]\d{9}$/.test(value)) {
          cb();
        } else {
          cb(new Error("手机号格式不正确"));
        }
      },
    },
  ],
  entId: [{ required: true, trigger: "change", message: "请选企业" }],
  entName: [
    { required: true, message: "请输入企业名称", trigger: "blur" },
    { max: 30, message: "企业名称大长度不超过30", trigger: "blur" },
  ],
  email: [
    { required: true, message: "请输入邮箱", trigger: "blur" },
    { max: 30, message: "邮箱最大长度不超过30", trigger: "blur" },
    { required: true, validator: checkEmail, trigger: "blur" },
  ],
  address: [{ max: 100, message: "地址最大长度不超过100", trigger: "blur" }],
  remark: [{ max: 100, message: "备注最大长度不超过100", trigger: "blur" }],
});

const validForm = () => {
  if (!formRef.value) return;
  formRef.value.validate((valid) => {
    if (valid) {
      addAccount();
    } else {
      ElMessage.error("校验不通过!");
      return false;
    }
  });
};

const addAccount = () => {
  btnLoading.value = true;
  addEntApi(formData.value)
    .then(() => {
      dialogFormVisible.value = false;
      emit("update-table", true);
      ElMessage({
        message: "新增企业账号成功",
        type: "success",
      });
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

const getEntInfo = () => {
  getEntInfoApi().then((res) => {
    entOptions.list = Array.isArray(res) ? res : [];
  });
};

onMounted(() => {
  getEntInfo();
});
</script>

<style lang="scss"></style>
