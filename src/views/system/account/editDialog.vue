<template>
  <div>
    <el-drawer
      v-model="dialogFormVisible"
      title="编辑账户"
      size="645px"
      @close="$emit('close-dialog')"
      destroy-on-close
    >
      <div class="form-container">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="80px"
        >
          <el-form-item label="员工姓名" prop="manageOrgName">
            <el-input v-model.trim="formData.manageOrgName" disabled />
          </el-form-item>
          <el-form-item label="登录名" prop="manageOrgCrtCode">
            <el-input v-model.trim="formData.manageOrgCrtCode" disabled />
          </el-form-item>
          <el-form-item label="角色" prop="manageOrgNature" style="width: 100%">
            <el-select
              v-model="formData.manageOrgNature"
              style="width: 100%"
              placeholder="请选择角色"
            >
              <el-option
                v-for="item in entTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="所属企业"
            prop="manageOrgNature"
            style="width: 100%"
          >
            <div class="inline">
              <el-select
                v-model="formData.manageOrgNature"
                style="width: 421px"
                placeholder="请选择企业"
              >
                <el-option
                  v-for="item in entTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
              <el-button style="width: 80px">新增企业</el-button>
            </div>
          </el-form-item>
          <el-form-item label="所属应用" prop="manageOrgIndustry">
            <div class="inline">
              <el-select
                v-model="formData.manageOrgIndustry"
                placeholder="请选择所属应用"
                style="width: 421px"
                clearable
              >
                <el-option key="1" value="1" label="烟草" />
              </el-select>
              <el-button style="width: 80px">新增应用</el-button>
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-divider style="margin: 0 0 16px 0"></el-divider>
        <div class="footer-buttons">
          <el-button type="info" plain @click="() => $emit('close-dialog')">
            取消
          </el-button>
          <el-button
            type="primary"
            :v-loading="saveBtnLoading"
            @click="
              () => {
                saveOptBody();
              }
            "
          >
            保存
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import ChinaAreaCascader from "@/components/ChinaAreaCascader/index.vue";
import { saveOptBodyApi } from "@/api/systemManage/operation";

const emit = defineEmits(["close-dialog", "edit-done"]);

const props = defineProps<{
  bodyInfo: object;
  province: Array<string>;
}>();

const dialogFormVisible = ref(true);

const formData = reactive({
  manageOrgAddr: "",
  manageOrgCity: "",
  manageOrgCrtCode: "",
  manageOrgDesc: "",
  manageOrgIndustry: "",
  manageOrgName: "",
  manageOrgNature: "",
  manageOrgProvince: "",
  manageWebsite: "",
  manageContactAddr: "",
});

const rules = reactive({
  appName: [
    {
      required: true,
      message: "请输入应用名称",
      trigger: "blur",
    },
    {
      max: 30,
      message: "应用名称最大长度不超过30",
      trigger: "blur",
    },
  ],
});

const saveBtnLoading = ref(false);

const selectProvince = ref<string[]>([]);

const entTypeList = [
  {
    value: "1",
    label: "国有控股",
  },
  {
    value: "2",
    label: "民营控股",
  },
  {
    value: "3",
    label: "外商控股",
  },
  {
    value: "4",
    label: "事业单位",
  },
  {
    value: "5",
    label: "民营非盈利",
  },
];
const areaChange = (areas: string[]) => {
  selectProvince.value = areas;
};

const saveOptBody = () => {
  saveBtnLoading.value = true;
  const [manageOrgProvince, manageOrgCity, manageOrgAddr] =
    selectProvince.value;
  saveOptBodyApi({
    ...formData,
    manageOrgProvince,
    manageOrgCity,
    manageOrgAddr,
  })
    .then(() => {
      ElMessage.success("运营主体信息保存成功!");
      emit("edit-done");
    })
    .finally(() => {
      saveBtnLoading.value = false;
    });
};

onMounted(() => {
  Object.assign(formData, props.bodyInfo);
  Object.assign(selectProvince.value, props.province);
  if (selectProvince.value.length === 3 && selectProvince.value[2] === null) {
    selectProvince.value.splice(2, 1);
  }
});
</script>

<style lang="scss" scoped>
.form-container {
  margin: 4px 0;
}
.el-form {
  margin-left: 10px;
}
.el-form-item {
  margin-bottom: 20px;
}
:deep(.el-form-item__label) {
  justify-content: flex-start;
}

:deep(.el-drawer__footer) {
  padding: 10px 0 16px 0;
  .footer-buttons {
    margin-right: 20px;
  }
}
.inline {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
</style>
