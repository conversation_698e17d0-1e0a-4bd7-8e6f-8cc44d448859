<template>
  <div>
    <!-- <div class="level-type" v-if="!isProxy">
      <el-button
        size="large"
        :type="isProvinceActive ? 'primary' : ''"
        @click="changeLevelType(true)"
        >省级账号</el-button
      >
      <el-button
        size="large"
        :type="!isProvinceActive ? 'primary' : ''"
        @click="changeLevelType(false)"
        >企业账号</el-button
      >
    </div> -->
    <SearchLayout>
      <template #left>
        <el-button
          v-if="enableAddUser && isProvinceActive"
          type="primary"
          :icon="Plus"
          @click="() => openDialog(true)"
          v-permission="AUTH_CODE.USER_MANAGER_ADD"
          >新增用户</el-button
        >
        <!-- <el-button
          v-if="enableAddUser && !isProvinceActive"
          type="primary"
          @click="() => openAddEntDialog()"
          v-permission="AUTH_CODE.USER_MANAGER_ADD"
          >新增账号</el-button
        > -->
      </template>
      <template #right>
        <el-form
          v-permission="AUTH_CODE.USER_MANAGER_PROVINCE_PAGE"
          :inline="true"
          :model="formData"
          style="text-align: right"
          @submit.prevent
        >
          <el-form-item style="width: 240px; max-width: 240px">
            <el-input
              v-model.trim="formData.username"
              maxlength="30"
              placeholder="请输入登录名"
              @clear="getTableData(true)"
              clearable
            >
              <template #prefix>登录名：</template>
            </el-input>
          </el-form-item>
          <!-- <el-form-item
            v-if="!isProvinceActive"
            style="width: 240px; max-width: 240px"
          >
            <el-input
              v-model.trim="formData.entName"
              maxlength="30"
              placeholder="请输入企业名称"
              @clear="getTableData(true)"
              clearable
            >
              <template #prefix>企业名称：</template>
            </el-input>
          </el-form-item> -->
          <el-form-item style="width: 60px; max-width: 60px">
            <el-button type="primary" @click="() => getTableData(true)"
              >搜索</el-button
            >
          </el-form-item>
        </el-form>
      </template>
    </SearchLayout>
    <el-table
      border
      :data="tableData.list"
      size="small"
      v-loading="tableLoading"
    >
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column prop="roles" label="员工姓名" />
      <el-table-column prop="username" label="登录名">
        <template #default="scope">
          <span v-copy="scope.row.username">{{
            scope.row.username || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="roles" label="角色" />
      <el-table-column prop="entName" label="企业名称">
        <template #default="scope">
          <span v-copy="scope.row.entName">{{ scope.row.entName || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="entName" label="所属应用">
        <template #default="scope">
          <span v-copy="scope.row.entName">{{ scope.row.entName || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="entName" label="应用前缀">
        <template #default="scope">
          <span v-copy="scope.row.entName">{{ scope.row.entName || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button
            text
            type="primary"
            @click="() => openDialog(false, scope.row)"
            v-permission="AUTH_CODE.USER_MANAGER_EDIT"
            >编辑</el-button
          >
          <el-button
            text
            type="primary"
            @click="clickPasswordBtn(scope.row)"
            v-permission="AUTH_CODE.USER_MANAGER_PWDEDIT"
          >
            重置密码
          </el-button>

          <el-popconfirm
            :title="`确认要删除用户[${scope.row.username}]吗？`"
            width="250"
            @confirm="clickDelBtn(scope.row)"
          >
            <template #reference>
              <el-button
                text
                type="primary"
                v-permission="AUTH_CODE.USER_MANAGER_DELETE"
                >删除</el-button
              >
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="pagination.currentPage"
      v-model:page-size="pagination.pageSize"
      :page-sizes="[10, 20, 30, 40]"
      size="small"
      layout="total,  prev, pager, next, sizes,jumper"
      :total="pagination.total"
    />
    <AddOrEdit
      v-if="addOrEditVisible"
      @close-dialog="addOrEditVisible = false"
      @update-table="getTableData"
      :rowData="selectRow.data"
      :isAdd="isAddDialog"
      :isProvinceActive="isProvinceActive"
    />
    <AddEnt
      v-if="addVisible"
      @update-table="getTableData(true)"
      @close-dialog="addVisible = false"
    />
    <Password
      v-if="passwordVisible"
      @close-password-dialog="passwordVisible = false"
      @update-table="getTableData(true)"
      :userId="selectUserId"
    />
    <BindAccount
      v-if="bindHdlVisible"
      @close-dialog="bindHdlVisible = false"
      @update-table="getTableData"
      :userId="selectUserId"
    />
    <UnbindAccount
      v-if="unbindHdlVisible"
      @close-dialog="unbindHdlVisible = false"
      @update-table="getTableData"
      :userId="selectUserId"
    />
    <EditDialog></EditDialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, watch, computed, ref } from "vue";
import { useStore } from "vuex";
// import { useRoute } from "vue-router";
import { Plus } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { accountList, deleteAccountApi } from "@/api/systemManage/account";
import { LEVEL_TYPE, SYSTEM_TYPE } from "@/utils/constant";
import Password from "./password.vue";
import BindAccount from "./bindAccount.vue";
import UnbindAccount from "./unbindAccount.vue";
import AddOrEdit from "./addOrEdit.vue";
import AddEnt from "./addEnt.vue";
import { List } from "@/types/system/account";
import { AUTH_CODE } from "@/utils/authCode";
import SearchLayout from "@/components/searchLayout/index.vue";
import EditDialog from "./editDialog.vue";

const store = useStore();
const enableAddUser = computed(() => {
  return store.getters.enableAddUser;
});

const userInfo = computed(() => store.getters.userInfo);

const isProxy = computed(() => userInfo.value.levelType === LEVEL_TYPE.PROXY);

const isEntSystem = computed(
  () => store.getters.globalConfig.systemType === SYSTEM_TYPE.SYSTEM_PROXY
);

const formData = reactive({
  username: "",
  entName: "",
});
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

const tableLoading = ref(false);

const tableData: { list: List[] } = reactive({
  list: [],
});

const selectUserId = ref<number>();

const passwordVisible = ref(false);
const bindHdlVisible = ref(false);
const unbindHdlVisible = ref(false);

const selectRow: { data: List } = reactive({
  data: {
    address: null,
    email: null,
    entName: null,
    handleUser: null,
    id: 0,
    nickName: null,
    phone: null,
    remark: null,
    roles: null,
    username: null,
  },
});
const isAddDialog = ref(true);

const addOrEditVisible = ref(false);
const addVisible = ref(false);

const isProvinceActive = ref(true);

onMounted(() => {
  getTableData(true);
});

watch(
  [
    () => pagination.currentPage,
    () => pagination.pageSize,
    () => isProvinceActive.value,
  ],
  () => {
    getTableData(false);
  }
);

const emailHide = (email: any) => {
  return email.split("@")[0].substring(0, 3) + "***@" + email.split("@")[1];
};

const getTableData = (isReset: boolean) => {
  if (isReset) {
    pagination.currentPage = 1;
    pagination.pageSize = 10;
    pagination.total = 0;
  }
  const { currentPage, pageSize } = pagination;
  tableLoading.value = true;
  let type;
  if (isProxy.value) {
    type = 2;
  } else {
    type = isProvinceActive.value ? 1 : 2;
  }

  accountList({
    username: formData.username,
    entName: formData.entName,
    type,
    currentPage,
    pageSize,
  })
    .then((res) => {
      const { content, totalCount } = res;
      pagination.total = totalCount;

      tableData.list = content || [];
    })
    .finally(() => {
      tableLoading.value = false;
    });
};

const clickBindBtn = (row: any) => {
  bindHdlVisible.value = true;
  selectUserId.value = row.id;
};

const clickUnBindBtn = (row: any) => {
  unbindHdlVisible.value = true;
  selectUserId.value = row.id;
};

const clickPasswordBtn = (row: any) => {
  passwordVisible.value = true;
  selectUserId.value = row.id;
};

const clickDelBtn = (row: any) => {
  deleteAccountApi({ id: row.id }).then(() => {
    ElMessage.success("账号删除成功!");
    getTableData(true);
  });
};

const openDialog = (isAdd: boolean, row?: any) => {
  isAddDialog.value = isAdd;
  addOrEditVisible.value = true;
  selectRow.data = row;
};

const openAddEntDialog = () => {
  addVisible.value = true;
};

const changeLevelType = (type: boolean) => {
  isProvinceActive.value = type;
  formData.username = "";
  formData.entName = "";
};
</script>

<style lang="scss" scoped>
.level-type {
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 2px solid #ebeef5;
  // :deep(.el-button--primary:active),
  // :deep(.el-button--primary:hover) {
  //   color: #fff !important;
  //   border-color: #00a57c !important;
  //   background-color: #00a57c !important;
  // }
  :deep(.el-button) {
    &:focus {
      border-color: #00a57c;
      background-color: #00a57c;
    }
  }
}
.table-opt {
  display: inline-block;
  width: 60px;
  text-align: right;
  margin-right: 12px;
}
</style>
