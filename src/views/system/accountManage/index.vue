<template>
  <div class="box">
    <SearchLayout>
      <template #left>
        <el-button
          v-if="enableAddUser"
          type="primary"
          @click="clickAddUser"
          v-permission="AUTH_CODE.USER_MANAGER_ADD"
          >新增用户</el-button
        >
      </template>
      <template #right>
        <el-form
          :inline="true"
          :model="formData"
          style="text-align: right"
          @submit.prevent
        >
          <el-form-item style="width: 240px; max-width: 240px">
            <el-input
              v-model.trim="formData.username"
              maxlength="30"
              placeholder="请输入登录名"
              @clear="getTableData(true)"
              clearable
            >
              <template #prefix>登录名：</template>
            </el-input>
          </el-form-item>
          <el-form-item style="width: 60px; max-width: 60px">
            <el-button type="primary" @click="() => getTableData(true)">
              搜索
            </el-button>
          </el-form-item>
        </el-form>
      </template>
    </SearchLayout>
    <el-table
      border
      :data="tableData.list"
      size="small"
      v-loading="tableLoading"
      show-overflow-tooltip
    >
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column v-if="isSocialUser" label="员工姓名" prop="nickName">
        <template #default="scope">
          <span v-copy="scope.row.nickName">{{
            scope.row.nickName || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="username" label="登录名">
        <template #default="scope">
          <span v-copy="scope.row.username">{{
            scope.row.username || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="roles" label="角色" />
      <el-table-column prop="orgName" label="企业名称">
        <template #default="scope">
          <span v-copy="scope.row.orgName">{{ scope.row.orgName || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="appName" label="所属应用">
        <template #default="scope">
          <span v-copy="scope.row.appName">{{ scope.row.appName || "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="appPrefix" label="应用前缀">
        <template #default="scope">
          <span v-copy="scope.row.appPrefix">{{
            scope.row.appPrefix || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button
            text
            type="primary"
            @click="() => clickEditUser(scope.row)"
            v-permission="AUTH_CODE.USER_MANAGER_EDIT"
            >编辑</el-button
          >
          <el-button
            v-if="!isAddSocialUser"
            text
            type="primary"
            @click="clickPasswordBtn(scope.row)"
            v-permission="AUTH_CODE.USER_MANAGER_PWDEDIT"
          >
            重置密码
          </el-button>

          <el-popconfirm
            class="delete-pop"
            :title="`确认要删除用户[${scope.row.username}]吗？`"
            width="250"
            @confirm="clickDelBtn(scope.row)"
          >
            <template #reference>
              <el-button
                text
                type="primary"
                v-permission="AUTH_CODE.USER_MANAGER_DELETE"
                >删除</el-button
              >
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="pagination.currentPage"
      v-model:page-size="pagination.pageSize"
      :page-sizes="[10, 20, 30, 40]"
      size="small"
      layout="total,  prev, pager, next, sizes,jumper"
      :total="pagination.total"
    />
  </div>
  <AddUser
    v-if="addUserVisible && isAddSocialUser"
    @success="successAddUser"
    @close="addUserVisible = false"
  ></AddUser>
  <AddUserInner
    v-if="addUserVisible && !isAddSocialUser"
    @success="addUserInnerSuccess"
    @close="addUserVisible = false"
  >
  </AddUserInner>
  <EditUser
    v-if="editUserVisible"
    :data="editData"
    @success="successEdit"
    @close="editUserVisible = false"
  ></EditUser>
  <ResetPassword
    v-if="resetPasswordVisible"
    :userId="selectUserId"
    @success="resetPasswordSuccess"
    @close="resetPasswordVisible = false"
  ></ResetPassword>
</template>

<script setup lang="ts">
import { onMounted, reactive, watch, computed, ref, toRefs } from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
import { accountList, deleteAccountApi } from "@/api/systemManage/account";
import { LEVEL_TYPE, SYSTEM_TYPE } from "@/utils/constant";
import { List } from "@/types/system/account";
import { AUTH_CODE } from "@/utils/authCode";
import SearchLayout from "@/components/searchLayout/index.vue";
import AddUser from "./components/social/addUser.vue";
import EditUser from "./components/editUser.vue";
import ResetPassword from "./components/resetPassword.vue";
import AddUserInner from "./components/addUserInner.vue";

const store = useStore();
const { isAddSocialUser, isSocialUser } = toRefs(store.getters);
const addUserVisible = ref(false);
const editUserVisible = ref(false);
const resetPasswordVisible = ref(false);
const editData = ref();

const clickAddUser = () => {
  addUserVisible.value = true;
};

const clickEditUser = (data: any) => {
  editData.value = data;
  editUserVisible.value = true;
};

const enableAddUser = computed(() => {
  return store.getters.enableAddUser;
});

const userInfo = computed(() => store.getters.userInfo);

const formData = reactive({
  username: "",
  entName: "",
});
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

const tableLoading = ref(false);

const tableData: { list: List[] } = reactive({
  list: [],
});

const selectUserId = ref<number>();

const selectRow: { data: List } = reactive({
  data: {
    address: null,
    email: null,
    entName: null,
    handleUser: null,
    id: 0,
    nickName: null,
    phone: null,
    remark: null,
    roles: null,
    username: null,
  },
});

const isProvinceActive = ref(true);

onMounted(() => {
  getTableData(true);
});

watch(
  [
    () => pagination.currentPage,
    () => pagination.pageSize,
    () => isProvinceActive.value,
  ],
  () => {
    getTableData(false);
  }
);

const getTableData = (isReset: boolean) => {
  if (isReset) {
    pagination.currentPage = 1;
    pagination.pageSize = 10;
    pagination.total = 0;
  }
  const { currentPage, pageSize } = pagination;
  tableLoading.value = true;

  const type = isAddSocialUser.value ? 2 : 1;

  accountList({
    username: formData.username,
    entName: formData.entName,
    type,
    currentPage,
    pageSize,
  })
    .then((res) => {
      const { content, totalCount } = res;
      pagination.total = totalCount;

      tableData.list = content || [];
    })
    .finally(() => {
      tableLoading.value = false;
    });
};

const clickPasswordBtn = (row: any) => {
  resetPasswordVisible.value = true;
  selectUserId.value = row.id;
};

const clickDelBtn = (row: any) => {
  deleteAccountApi({ id: row.id }).then(() => {
    ElMessage.success("账号删除成功!");
    getTableData(true);
  });
};

const addUserInnerSuccess = () => {
  addUserVisible.value = false;
  getTableData(true);
};

const resetPasswordSuccess = () => {
  resetPasswordVisible.value = false;
  getTableData(false);
};

const successEdit = () => {
  editUserVisible.value = false;
  if (editData.value.username === userInfo.value.username) {
    // 退出登录
    store.dispatch("LogOut").then(() => {
      location.href = "/";
    });
  }
  getTableData(true);
};

const successAddUser = () => {
  addUserVisible.value = false;
  getTableData(true);
};
</script>

<style lang="scss" scoped>
.level-type {
  padding-bottom: 15px;
  margin-bottom: 15px;
  border-bottom: 2px solid #ebeef5;

  :deep(.el-button) {
    &:focus {
      border-color: #00a57c;
      background-color: #00a57c;
    }
  }
}
.box {
  :deep(.el-popper) {
    &.is-dark {
      max-width: 800px !important; //宽度可根据自己需要进行设置
    }
  }
}
</style>
