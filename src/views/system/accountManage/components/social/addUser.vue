<template>
  <div class="my-dialog">
    <el-dialog
      v-model="dialogFormVisible"
      title="新增用户"
      width="800px"
      @close="$emit('close')"
      destroy-on-close
      align-center
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item label="组织" prop="orgId">
          <el-tree-select
            v-model="formData.orgId"
            lazy
            node-key="orgUnitId"
            :props="treeProps"
            :load="loadMoreTree"
            style="width: 100%"
            check-strictly
            accordion
            @current-change="orgChanged"
          />
        </el-form-item>
        <el-form-item label="用户" prop="employeeName" class="yong-hu">
          <div class="inline">
            <el-input
              v-model.trim="formData.employeeName"
              placeholder="请在下表中选择"
              style="width: 240px"
              disabled
              clearable
            />
            <el-input
              v-model="searchText"
              class="search-input"
              style="width: 180px"
              placeholder="请搜索姓名或登录名"
              clearable
            >
              <template #suffix>
                <el-icon style="cursor: pointer" @click="doSearch"
                  ><search
                /></el-icon>
              </template>
            </el-input>
          </div>
        </el-form-item>
        <el-table
          :data="tableData"
          v-loading="tableLoading"
          size="small"
          style="margin: 0 0 20px 100px; width: 660px"
          height="290"
          @current-change="handleCurrentChange"
          border
        >
          <el-table-column width="64" label="">
            <template #default="scope">
              <el-radio
                v-model="currentData"
                :value="scope.$index"
                :label="scope.$index"
                @click.prevent
                >{{
              }}</el-radio>
            </template>
          </el-table-column>
          <el-table-column width="90" label="序号" type="index">
          </el-table-column>
          <el-table-column width="150" label="员工姓名" property="name">
            <template #default="scope">
              {{ scope.row.name || "-" }}
            </template>
          </el-table-column>
          <el-table-column width="204" label="登录名" property="identification">
            <template #default="scope">
              {{ scope.row.identification || "-" }}
            </template>
          </el-table-column>
          <el-table-column label="职务" property="station">
            <template #default="scope">
              {{ scope.row.station || "-" }}
            </template></el-table-column
          >

          <template #append>
            <div
              v-if="!hiddenMore"
              style="
                height: 41px;
                display: flex;
                justify-content: center;
                align-items: center;
              "
            >
              <el-button
                type="primary"
                @click="loadMoreData"
                :loading="loading"
                text
                >显示更多</el-button
              >
            </div>
          </template>
        </el-table>
        <el-form-item prop="roles" label-width="0">
          <RoleSelect ref="roleSelectRef"></RoleSelect>
        </el-form-item>
      </el-form>

      <template #footer>
        <span>
          <el-button @click="dialogFormVisible = false">取消</el-button>
          <el-button type="primary" @click="validForm" :loading="btnLoading">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from "vue";
import { ElMessage } from "element-plus";
import {
  apiOrgList,
  apiRootOrgList,
  apiExtOrgList,
  apiEmployeeList,
  apiAddSocialUser,
} from "@/api/systemManage/account";
import RoleSelect from "../role-select/index.vue";

const roleSelectRef = ref();
const emit = defineEmits(["close", "success"]);
const loading = ref(false);
const tableLoading = ref(false);
const currentData = ref(-1);
const handleCurrentChange = (val: any) => {
  console.log(val);
  if (val == null) {
    currentData.value = -1;
    formData.value.userId = "";
    formData.value.username = "";
    formData.value.employeeName = "";
    return;
  }
  formData.value.userId = val.userId;
  formData.value.username = val.identification;
  formData.value.employeeName = val.name;
  var index = tableData.value.indexOf(val);
  currentData.value = index;
};

const searchText = ref("");
let orgBizCode = "";
let orgUnitId = "";
const treeProps = {
  label: "orgNodeName",
  children: "children",
  isLeaf: "leaf",
};
const loadMoreTree = (node: any, resolve: any, reject: any) => {
  console.log(node);
  if (node.level === 0) {
    resolve([
      {
        orgUnitId: "-1",
        orgNodeName: "行政组织",
        orgBizCode: "default",
      },
      {
        orgUnitId: "-2",
        orgNodeName: "外部组织",
        orgBizCode: "externalDefault",
      },
      {
        orgUnitId: "-3",
        orgNodeName: "应用开发商",
        orgBizCode: "isv",
      },
    ]);
    return;
  }
  if (node.data.orgBizCode === "default") {
    if (node.data.orgUnitId === "-1") {
      apiRootOrgList().then((res) => {
        if (res instanceof Array) {
          const list = res as Array<any>;
          const r: Array<any> = [];
          for (const e of list) {
            r.push({
              orgNodeName: e.orgNodeName,
              orgUnitId: e.orgUnitId,
              orgBizCode: "default",
              leaf: false,
            });
          }
          resolve(r);
        } else {
          resolve([]);
        }
      });
      return;
    }
    apiOrgList(node.data.orgUnitId, node.data.orgBizCode)
      .then((res) => {
        if (res instanceof Array) {
          const list = res as Array<any>;
          const r: Array<any> = [];
          for (const e of list) {
            r.push({
              orgNodeName: e.orgNodeName,
              orgUnitId: e.orgUnitId,
              orgBizCode: "default",
              leaf: false,
            });
          }
          resolve(r);
        } else {
          resolve([]);
        }
      })
      .catch(() => {
        resolve([]);
      });
    return;
  }
  if (node.data.orgUnitId === "-2") {
    apiExtOrgList(node.data.orgBizCode)
      .then((res) => {
        const r: Array<any> = [];
        if (res instanceof Array) {
          const list = res as Array<any>;
          for (const e of list) {
            r.push({
              orgNodeName: e.orgNodeName,
              orgUnitId: e.orgUnitId,
              orgBizCode: "externalDefault",
              leaf: true,
            });
          }
        }
        resolve(r);
      })
      .catch(() => {
        resolve([]);
      });
    return;
  }
  if (node.data.orgUnitId === "-3") {
    apiExtOrgList(node.data.orgBizCode)
      .then((res) => {
        const r: Array<any> = [];
        if (res instanceof Array) {
          const list = res as Array<any>;
          for (const e of list) {
            r.push({
              orgNodeName: e.orgNodeName,
              orgUnitId: e.orgUnitId,
              orgBizCode: "isv",
              leaf: e.leafNode ?? false,
            });
          }
        }
        resolve(r);
      })
      .catch(() => {
        resolve([]);
      });
    return;
  }
  apiOrgList(node.data.orgUnitId, node.data.orgBizCode)
    .then((res) => {
      if (res instanceof Array) {
        const list = res as Array<any>;
        const r: Array<any> = [];
        for (const e of list) {
          r.push({
            orgNodeName: e.orgNodeName,
            orgUnitId: e.orgUnitId,
            orgBizCode: "isv",
            leaf: e.leafNode ?? false,
          });
        }
        resolve(r);
      } else {
        resolve([]);
      }
    })
    .catch(() => {
      resolve([]);
    });
};

let dataIndex = 1;
const tableData = ref<any>([]);
const hiddenMore = ref(true);
const loadUser = () => {
  if (orgBizCode === "" || orgUnitId === "") {
    ElMessage.error("请先选择组织");
    return;
  }
  if (orgUnitId === "-1" || orgUnitId === "-2" || orgUnitId === "-3") {
    ElMessage.error("顶级组织无法搜索用户");
    return;
  }
  tableLoading.value = true;
  apiEmployeeList(orgBizCode, orgUnitId, searchText.value, dataIndex)
    .then((res) => {
      if (res instanceof Array) {
        const list = res as Array<any>;
        if (dataIndex === 1) {
          tableData.value = list;
        } else {
          tableData.value = tableData.value.concat(list);
        }
        hiddenMore.value = list.length < 5;
      } else {
        hiddenMore.value = true;
      }
    })
    .finally(() => {
      tableLoading.value = false;
      loading.value = false;
    });
};

const orgChanged = (data: any) => {
  orgBizCode = "";
  orgUnitId = "";
  dataIndex = 1;
  tableData.value = [];
  currentData.value = -1;
  formData.value.userId = "";
  formData.value.username = "";
  formData.value.employeeName = "";
  if (
    data.orgUnitId !== "-1" &&
    data.orgUnitId !== "-2" &&
    data.orgUnitId !== "-3"
  ) {
    orgBizCode = data.orgBizCode;
    orgUnitId = data.orgUnitId;
    loadUser();
  }
};

const doSearch = () => {
  dataIndex = 1;
  tableData.value = [];
  loadUser();
};

const btnLoading = ref(false);
const formRef = ref();
const dialogFormVisible = ref(true);

interface FormData {
  orgId: string;
  userId: string;
  username: string;
  employeeName: string;
}
const formData = ref<FormData>({
  orgId: "",
  userId: "",
  username: "",
  employeeName: "",
});

const rules = reactive({
  orgId: [{ required: true, message: "请选择组织", trigger: "change" }],
  employeeName: [{ required: true, message: "用户不能为空", trigger: "input" }],
});

const addOrEditAccount = () => {
  btnLoading.value = true;
  const roleData = roleSelectRef.value.getData();
  const params = {
    username: formData.value.username,
    nickName: formData.value.employeeName,
    userId: formData.value.userId,
    roleId: roleData.roleId,
    entId: roleData.entId ? roleData.entId : undefined,
    appId: roleData.appId ? roleData.appId : undefined,
  };
  apiAddSocialUser(params)
    .then(() => {
      emit("success", true);
      ElMessage({
        message: `新增账户成功!`,
        type: "success",
      });
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

const validForm = async () => {
  const roleValidate = await roleSelectRef.value.formValidate();
  const valid = await formRef.value.validate();
  if (roleValidate) {
    return false;
  }
  if (valid) {
    addOrEditAccount();
  } else {
    return false;
  }
};

const loadMoreData = () => {
  loading.value = true;
  dataIndex += 1;
  loadUser();
};
</script>

<style lang="scss">
.my-dialog {
  .yong-hu {
    margin-bottom: 22px;
  }
  .inline {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    .search-input {
      .el-input__wrapper {
        border-color: transparent;
        &.is-focus {
          border-color: #00a57c;
        }
      }
    }
  }
}
</style>
