<template>
  <el-dialog
    v-model="dialogFormVisible"
    title="新增用户"
    width="700px"
    @close="$emit('close')"
    destroy-on-close
    align-center
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="登录名" prop="username">
        <el-input
          v-model.trim="formData.username"
          placeholder="请输入登录名，只允许输入数字、字母、下划线"
          clearable
        />
      </el-form-item>
      <el-form-item prop="roles" label-width="0">
        <RoleSelect ref="roleSelectRef"></RoleSelect>
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="$emit('close')">取消</el-button>
        <el-button type="primary" @click="validForm" :loading="btnLoading">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from "vue";
import { ElMessage } from "element-plus";

import { apiAddInternalAccount } from "@/api/systemManage/account";
import RoleSelect from "./role-select/index.vue";

const dialogFormVisible = ref(true);

const emit = defineEmits(["close", "success"]);

const btnLoading = ref(false);

const formRef = ref();

const roleSelectRef = ref();

const formData = ref<any>({
  roles: [],
  username: "",
  // address: "",
  // remark: null,
});

const rules = reactive({
  username: [
    { required: true, message: "请输入登录名", trigger: "blur" },
    { max: 30, message: "登录名最大长度不超过30", trigger: "blur" },
    {
      trigger: "blur",
      validator: (rule: any, value: any, cb: any) => {
        if (/^[0-9a-zA-Z_]{1,}$/.test(value)) {
          cb();
        } else {
          cb(new Error("只允许输入数字、字母、下划线"));
        }
      },
    },
  ],
  address: [{ max: 100, message: "地址最大长度不超过100", trigger: "blur" }],
  remark: [{ max: 100, message: "备注最大长度不超过100", trigger: "blur" }],
});

const addOrEditAccount = () => {
  btnLoading.value = true;
  const roleData = roleSelectRef.value.getData();
  const params = {
    username: formData.value.username,
    roleId: roleData.roleId,
    // address: formData.value.address,
    // remark: formData.value.remark,
    entId: roleData.roleId ? roleData.entId : undefined,
    appId: roleData.appId ? roleData.appId : undefined,
  };

  apiAddInternalAccount(params)
    .then(() => {
      emit("success", true);
      ElMessage({
        message: `新增账户成功!`,
        type: "success",
      });
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

const validForm = async () => {
  console.log(222);
  const roleValidate = await roleSelectRef.value.formValidate();
  const valid = await formRef.value.validate();
  if (roleValidate) {
    return false;
  }
  if (valid) {
    addOrEditAccount();
  } else {
    return false;
  }
};
</script>

<style scoped></style>
