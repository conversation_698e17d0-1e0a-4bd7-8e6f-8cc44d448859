<template>
  <el-form
    label-width="100px"
    :model="formData"
    style="width: 100%"
    :rules="rules"
    ref="formRef"
  >
    <el-form-item label="角色" prop="roleId">
      <el-select
        v-model="formData.roleId"
        placeholder="请选择角色"
        style="width: 100%"
        clearable
        filterable
        @change="changeRole"
      >
        <el-option
          v-for="item in roleOptions.list"
          :key="item.id"
          :label="item.roleName"
          :value="item.id"
        />
      </el-select>
    </el-form-item>
    <el-form-item
      v-if="checkRoleCode && isProvinceManager"
      label="企业名称"
      prop="entId"
      style="margin-top: 32px"
    >
      <div class="select-btn">
        <el-select
          v-model="formData.entId"
          clearable
          filterable
          @change="changeEnt"
        >
          <el-option
            v-for="item in entList"
            :key="item.id"
            :label="item.orgName"
            :value="item.id"
        /></el-select>
        <el-button
          @click="clickAddEnt"
          v-if="canAddEnt && isRoleCodeSysEntAdmin"
          >新增企业</el-button
        >
      </div>
    </el-form-item>
    <el-form-item
      v-if="isRoleCodeSysAppUser"
      label="应用名称"
      prop="appId"
      style="margin-top: 32px"
    >
      <div class="select-btn">
        <el-select clearable filterable v-model="formData.appId">
          <el-option
            v-for="item in appList"
            :key="item.id"
            :label="item.appName"
            :value="item.id"
        /></el-select>
        <el-button @click="clickAddApp" v-if="canAddEnt">新增应用</el-button>
      </div>
    </el-form-item>
  </el-form>
  <AddEnt
    v-if="addEntVisible"
    @success="successAddEnt"
    @close="addEntVisible = false"
  ></AddEnt>
  <AddApp
    v-if="addAppVisible"
    :ent-id="formData.entId"
    @success="successAddApp"
    @close="addAppVisible = false"
  ></AddApp>
</template>

<script setup lang="ts">
import { ref, onMounted, toRefs, reactive, computed, watch } from "vue";
import { useStore } from "vuex";
import { ElMessage } from "element-plus";
import {
  apiEntList,
  apiRoleAll,
  apiAppInfoList,
} from "@/api/systemManage/account";
import AddEnt from "./addEnt.vue";
import AddApp from "./addApp.vue";

const store = useStore();
const userInfo = computed(() => store.getters.userInfo);

const rules = {
  roleId: [
    {
      required: true,
      message: "请选择角色",
    },
  ],
  entId: [
    {
      required: true,
      message: "请选择企业名称",
    },
  ],
  appId: [
    {
      required: true,
      message: "请选择应用名称",
    },
  ],
};

const formRef = ref();

const props = defineProps({
  data: Object,
});

const { isSocialUser, isProvinceSystem, isProvinceUser, isProvinceManager } =
  toRefs(store.getters);

const formData = ref<Record<string, any>>({
  roleId: null,
  entId: null,
  appId: "",
  roleName: "",
});

const entList = ref<any[]>([]);

const appList = ref<any[]>([]);

const roleOptions: {
  list: { id: number; roleName: string; roleCode: string }[];
} = reactive({
  list: [],
});

const addEntVisible = ref(false);

const addAppVisible = ref(false);

watch(
  () => props.data,
  (val: any) => {
    if (val && val.roleId) {
      formData.value.roleId = val.roleId;
      formData.value.entId = val.entId || null;
      formData.value.appId = val.appId || null;
      const list = roleOptions.list;
      const has = list.some((item: any) => item.id === props.data?.roleId);
      if (!has) {
        list.push({
          id: props.data?.roleId,
          roleName: props.data?.roleName,
          roleCode: props.data?.roleCode,
        });
        roleOptions.list = list;
      }
      if (formData.value.appId) {
        getAppInfoList();
      }
    }
  },
  { immediate: true, deep: true }
);

const canAddEnt = computed(() => {
  return isProvinceSystem.value && isProvinceUser.value;
});

const isRoleCodeSysAppUser = computed(() => {
  const id = formData.value.roleId;
  const l = roleOptions.list.filter((item: any) => item.id === id);
  if (l.length) {
    return l[0].roleCode === "sys_app_user";
  }
  return false;
});

const changeRole = () => {
  formData.value.entId = null;
  formData.value.appId = "";
  if (isRoleCodeSysAppUser.value && !isProvinceManager.value) {
    getEntAppInfoList();
  }
};

const getEntList = () => {
  apiEntList().then((res) => {
    entList.value = Array.isArray(res) ? res : [];
  });
};

const getRoleList = () => {
  const type = isSocialUser.value ? 2 : 1;
  apiRoleAll(type).then((res) => {
    const list = Array.isArray(res) ? res : [];
    roleOptions.list = list;
  });
};

const changeEnt = () => {
  formData.value.appId = "";
  getAppInfoList();
};

// 获取应用列表
const getAppInfoList = () => {
  if (!formData.value.entId) {
    appList.value = [];
    return;
  }
  apiAppInfoList(formData.value.entId).then((res) => {
    appList.value = Array.isArray(res) ? res : [];
  });
};

// 获取应用列表
const getEntAppInfoList = () => {
  apiAppInfoList(userInfo.value.entId).then((res) => {
    appList.value = Array.isArray(res) ? res : [];
  });
};

const clickAddEnt = () => {
  addEntVisible.value = true;
};

const clickAddApp = () => {
  if (formData.value.entId === null || formData.value.entId === "") {
    ElMessage.warning("请先选择企业");
    return;
  }
  addAppVisible.value = true;
};

const successAddApp = (res: any) => {
  addAppVisible.value = false;
  getAppInfoList();
  formData.value.appId = res.id;
};

const successAddEnt = (res: any) => {
  console.log("res: ", res.id);
  formData.value.entId = res.id;
  console.log("formData.value.entId: ", formData.value.entId);
  addEntVisible.value = false;
  getEntList();
};

const getData = () => {
  return { ...formData.value };
};

const formValidate = async () => {
  let isError = false;
  await formRef.value.validate((valid: any) => {
    if (!valid) {
      isError = true;
    }
  });
  return isError;
};

const checkRoleCode = computed(() => {
  const id = formData.value.roleId;
  const l = roleOptions.list.filter((item: any) => item.id === id);
  if (l.length) {
    return (
      l[0].roleCode === "sys_ent_admin" || l[0].roleCode === "sys_app_user"
    );
  }
  return false;
});

const isRoleCodeSysEntAdmin = computed(() => {
  const id = formData.value.roleId;
  const l = roleOptions.list.filter((item: any) => item.id === id);
  if (l.length) {
    return l[0].roleCode === "sys_ent_admin";
  }
  return false;
});

onMounted(() => {
  getRoleList();
  getEntList();
});

defineExpose({
  getData,
  formValidate,
});
</script>

<style scoped lang="scss">
.select-btn {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .el-select {
    flex: 1;
    min-width: 0;
  }
  .el-button {
    margin-left: 10px;
  }
}
</style>
