<template>
  <el-dialog
    v-model="dialogFormVisible"
    title="新增企业"
    width="700px"
    @close="$emit('close')"
    destroy-on-close
    align-center
    append-to-body
  >
    <el-form
      class="add-ent-form"
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="120px"
    >
      <el-form-item label="企业名称:" prop="orgName">
        <el-input
          v-model.trim="formData.orgName"
          clearable
          placeholder="请输入企业名称"
        />
      </el-form-item>
      <el-form-item label="企业代码:" prop="orgCode">
        <el-input
          v-model.trim="formData.orgCode"
          clearable
          placeholder="请输入企业代码"
        />
      </el-form-item>
      <el-form-item label="上级单位名称:" prop="parentOrgName">
        <el-input
          v-model.trim="formData.parentOrgName"
          clearable
          placeholder="请输入上级单位名称"
        />
      </el-form-item>
      <el-form-item label="所属省市:" prop="selectProvince">
        <ChinaAreaCascader
          v-model="formData.selectProvince"
          @change="areaChange"
          style="width: 100%"
          clearable
        ></ChinaAreaCascader>
      </el-form-item>
      <el-form-item label="地址:" prop="selectAddress">
        <el-input
          v-model.trim="formData.selectAddress"
          clearable
          placeholder="请输入地址"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="$emit('close')">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleApply">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import { computed, getCurrentInstance, reactive, ref } from "vue";
import { applyPrefix } from "@/api/province/prefix/apply";
import ChinaAreaCascader from "@/components/ChinaAreaCascader/index.vue";
import { apiEntAdd } from "@/api/systemManage/account";

const emit = defineEmits(["close", "success"]);
const { proxy }: any = getCurrentInstance();

const props = defineProps({
  applyData: {
    type: Object,
    default: () => ({}),
  },
});

const loading = ref(false);

const selectProvinceValidate = (rule: any, value: any, callback: any) => {
  // if (!value || !value.length || (value.length && !value[0])) {
  //   return callback("请选择所属省市");
  // }
  if (!value || !value.length) {
    return callback("请选择所属省市");
  }
  // if (value.length < 3 || !value[2]) {
  //   return callback("请选择所属省市");
  // }
  callback();
};
const handleRegex = /^[0-9]\d{1}\.[0-9]\d{3}\.[0-9]\d{3}$/;

const rules = reactive({
  orgName: [
    { required: true, message: "请输入企业名称", trigger: "blur" },
    { max: 30, message: "最大长度不超过30", trigger: "blur" },
  ],
  orgCode: [
    { required: true, message: "请输入企业代码", trigger: "blur" },
    { max: 100, message: "最大长度不超过100", trigger: "blur" },
  ],
  parentOrgName: [
    { required: true, message: "请输入上级单位名称", trigger: "blur" },
    { max: 100, message: "最大长度不超过100", trigger: "blur" },
  ],
  selectProvince: [
    {
      required: true,
      message: "请输入所属省",
      trigger: "change",
      validator: selectProvinceValidate,
    },
  ],
  orgAddrCity: [{ required: true, message: "请输入所属市", trigger: "blur" }],
  selectAddress: [
    { required: true, message: "请输入地址", trigger: "change" },
    { max: 100, message: "最大长度不超过100", trigger: "blur" },
  ],
});

const dialogFormVisible = ref(true);
const selectProvinceList = computed(() => {
  if (props.applyData.orgAddrDistrict) {
    return [
      props.applyData.orgAddrProvince,
      props.applyData.orgAddrCity,
      props.applyData.orgAddrDistrict,
    ];
  }
  return [props.applyData.orgAddrProvince, props.applyData.orgAddrCity];
});
const formData = ref({
  orgName: props.applyData.orgName,
  orgCode: props.applyData.orgCode,
  parentOrgName: props.applyData.parentOrgName,
  selectAddress: props.applyData.orgAddress,
  selectProvince: selectProvinceList,
});

const areaChange = (areas: any[]) => {
  console.log(areas);
  if (!areas || areas.length < 3) {
    formData.value.selectProvince[0] = "";
    return;
  }
  formData.value.selectProvince[0] = areas[0];
  formData.value.selectProvince[1] = areas[1];
  formData.value.selectProvince[2] = areas[2];
};

function handleApply() {
  proxy.$refs.formRef.validate(async (valid: any) => {
    if (valid) {
      const params = {
        orgName: formData.value.orgName,
        orgCode: formData.value.orgCode,
        parentOrgName: formData.value.parentOrgName,
        orgAddrProvince: formData.value.selectProvince[0],
        orgAddrCity: formData.value.selectProvince[1],
        orgAddrDistrict: formData.value.selectProvince[2],
        orgAddress: formData.value.selectAddress,
      };
      loading.value = true;
      apiEntAdd(params)
        .then((res) => {
          console.log(res);
          emit("success", res);
          ElMessage({
            message: "新增企业成功!",
            type: "success",
          });
        })
        .finally(() => {
          loading.value = false;
        });
    }
  });
}
</script>

<style lang="scss" scoped></style>
