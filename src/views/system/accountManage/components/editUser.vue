<template>
  <div>
    <el-drawer
      v-model="dialogFormVisible"
      title="编辑账号"
      size="645px"
      @close="$emit('close')"
      destroy-on-close
    >
      <div class="form-container">
        <el-form ref="formRef" label-width="100px" :rules="rules">
          <el-form-item v-if="isAddSocialUser" label="员工姓名" prop="nickName">
            <el-input v-model.trim="formData.nickName" disabled />
          </el-form-item>
          <el-form-item label="登录名" prop="username">
            <el-input v-model.trim="formData.username" disabled />
          </el-form-item>
          <el-form-item prop="roles" label-width="0">
            <RoleSelect ref="roleSelectRef" :data="formData"></RoleSelect>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-divider style="margin: 0 0 16px 0"></el-divider>
        <div class="footer-buttons">
          <el-button plain @click="() => $emit('close')"> 取消 </el-button>
          <el-button
            type="primary"
            :v-loading="saveBtnLoading"
            @click="
              () => {
                validForm();
              }
            "
          >
            确定
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, toRefs, onMounted, reactive } from "vue";
import { ElMessage } from "element-plus";
import { useStore } from "vuex";
import {
  apiUpdateSocialUser,
  apiUsersDetail,
} from "@/api/systemManage/account";
import RoleSelect from "./role-select/index.vue";

const store = useStore();
const { isAddSocialUser } = toRefs(store.getters);

const roleSelectRef = ref();
const emit = defineEmits(["close", "success"]);

const props = defineProps<{
  data: any;
}>();

const formData = reactive<Record<string, any>>({
  id: "",
  username: "",
  nickName: "",
  roleId: "",
  entId: "",
  appId: "",
  roleName: "",
});

const dialogFormVisible = ref(true);

const saveBtnLoading = ref(false);

const addOrEditAccount = () => {
  saveBtnLoading.value = true;
  const roleData = roleSelectRef.value.getData();
  console.log(roleData);
  const params = {
    id: props.data.id,
    roleId: roleData.roleId,
    entId: roleData.entId ? roleData.entId : undefined,
    appId: roleData.appId ? roleData.appId : undefined,
  };
  apiUpdateSocialUser(params)
    .then(() => {
      emit("success", true);
      ElMessage({
        message: `编辑账户成功`,
        type: "success",
      });
    })
    .finally(() => {
      saveBtnLoading.value = false;
    });
};

const validForm = async () => {
  const roleValidate = await roleSelectRef.value.formValidate();
  if (roleValidate) {
    return false;
  }
  addOrEditAccount();
};

// 获取用户详情详情
const getDetail = () => {
  apiUsersDetail(props.data.id).then((res) => {
    const { id, username, nickName, roleInfos, entId, appId } = res || {};

    formData.id = id;
    formData.username = username || "";
    formData.nickName = nickName || "";
    if (roleInfos && Array.isArray(roleInfos) && roleInfos.length) {
      formData.roleId = roleInfos[0].id;
      formData.roleName = roleInfos[0].roleName;
    }
    formData.entId = entId || "";
    formData.appId = appId || "";
  });
};

const rules = reactive({
  nickName: [{ required: true, message: "请输入员工名称", trigger: "blur" }],
  username: [{ required: true, message: "请输入登录名", trigger: "blur" }],
  address: [{ max: 100, message: "地址最大长度不超过100", trigger: "blur" }],
  remark: [{ max: 100, message: "备注最大长度不超过100", trigger: "blur" }],
});

onMounted(() => {
  if (props.data?.id) {
    getDetail();
  }
});
</script>

<style lang="scss" scoped>
.form-container {
  margin: 4px 0;
}
.el-form {
  margin-left: 10px;
}
.el-form-item {
  margin-bottom: 32px;
}
:deep(.el-form-item__label) {
  justify-content: flex-start;
}
:deep(.el-drawer__footer) {
  padding: 10px 0 16px 0;
  .footer-buttons {
    margin-right: 20px;
  }
}
.inline {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
</style>
