<template>
  <div>
    <div class="summary-card">
      <search-layout>
        <template #left>
          <el-button
            type="primary"
            @click="addProvinceNode"
            v-permission="AUTH_CODE.TENANT_MANAGER_ADD"
            >新增</el-button
          >
        </template>
        <template #right>
          <el-form
            v-permission="AUTH_CODE.TENANT_MANAGER_PAGE"
            :inline="true"
            :model="queryForm"
            @submit.prevent
          >
            <el-form-item
              ><el-input
                v-model.trim="queryForm.prefix"
                placeholder="请输入"
                clearable
                @clear="getTableData(true)"
              >
                <template #prefix>节点前缀：</template>
              </el-input></el-form-item
            >
            <el-form-item
              ><el-input
                v-model.trim="queryForm.nodeName"
                placeholder="请输入"
                clearable
                @clear="getTableData(true)"
              >
                <template #prefix>节点名称：</template>
              </el-input></el-form-item
            >
            <el-form-item
              ><el-date-picker
                v-model="queryForm.createdTimeList"
                type="daterange"
                range-separator="-"
                start-placeholder="操作开始时间"
                end-placeholder="操作结束时间"
                value-format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="disabledDate"
                clearable
                @change="datePickerChange"
            /></el-form-item>
            <el-form-item style="width: 60px; max-width: 60px"
              ><el-button type="primary" @click="() => getTableData(true)"
                >搜索</el-button
              ></el-form-item
            >
          </el-form>
        </template>
      </search-layout>
      <div class="auditTable">
        <el-table
          border
          :data="tableData"
          style="width: 100%"
          v-loading="pageLoading"
          size="small"
        >
          <el-table-column label="序号" type="index" width="55" />
          <el-table-column
            prop="prefix"
            label="节点前缀"
            :show-overflow-tooltip="true"
          >
            <template #default="scope">
              <span v-copy="scope.row.prefix">{{
                scope.row.prefix || "-"
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="nodeName"
            label="节点名称"
            :show-overflow-tooltip="true"
          >
            <template #default="scope">
              <span v-copy="scope.row.nodeName">{{
                scope.row.nodeName || "-"
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="bizCode"
            label="组织机构代码"
            :show-overflow-tooltip="true"
          >
            <template #default="scope">
              <span v-copy="scope.row.bizCode">{{
                scope.row.bizCode || "-"
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="entName"
            label="企业名称"
            :show-overflow-tooltip="true"
          >
            <template #default="scope">
              <span v-copy="scope.row.entName">{{
                scope.row.entName || "-"
              }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="nodeAddress"
            label="节点地址"
            :show-overflow-tooltip="true"
          >
            <template #default="scope">
              <span v-copy="scope.row.nodeAddress">{{
                scope.row.nodeAddress || "-"
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="createdTime" label="操作时间" />
          <el-table-column label="操作" width="120">
            <template #default="scope">
              <el-popconfirm
                :title="`确认要删除节点前缀吗？`"
                width="250"
                @confirm="handleDelete(scope.row.id)"
              >
                <template #reference>
                  <el-button
                    text
                    type="primary"
                    v-permission="AUTH_CODE.TENANT_MANAGER_DELETE"
                    >删除</el-button
                  >
                </template>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 30, 40]"
          small
          background
          layout="total,  prev, pager, next, sizes,jumper"
          :total="pagination.total"
        />
      </div>
    </div>
    <AddProvinceNode
      v-if="addDialogVisible"
      @update-table="getTableData"
      @close-dialog="addDialogVisible = false"
    ></AddProvinceNode>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, watch } from "vue";
import { ElMessage } from "element-plus";
import AddProvinceNode from "./addProvinceNode.vue";
import {
  apiGetProvinceList,
  apiDeleteProvince,
} from "@/api/systemManage/provinceMnager";
import { ITableDataItem } from "@/types/system/provinceManager";
import { AUTH_CODE } from "@/utils/authCode";
import store from "@/store";
import SearchLayout from "@/components/searchLayout/index.vue";

const pageLoading = ref(false);
const addDialogVisible = ref(false);
const disabledDate = (time: Date) => {
  return time.getTime() > new Date().getTime();
};
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});
const queryForm = reactive<{
  prefix: string;
  nodeName: string;
  createdTimeList: Array<any>;
}>({
  prefix: "",
  nodeName: "",
  createdTimeList: [],
});

const tableData = ref<ITableDataItem[]>([]);
watch([() => pagination.currentPage, () => pagination.pageSize], () => {
  getTableData(false);
});
const getTableData = (isReset: boolean) => {
  if (isReset) {
    pagination.currentPage = 1;
    pagination.pageSize = 10;
    pagination.total = 0;
  }
  pageLoading.value = true;
  const { currentPage, pageSize } = pagination;
  const params = {
    prefix: queryForm.prefix,
    nodeName: queryForm.nodeName,
    startTime: "",
    endTime: "",
    page: currentPage - 1,
    size: pageSize,
  };
  if (queryForm.createdTimeList && queryForm.createdTimeList.length) {
    const [startTime, endTime] = queryForm.createdTimeList;
    params.startTime = startTime;
    params.endTime = endTime.replace("00:00:00", "23:59:59");
  }
  apiGetProvinceList(params)
    .then((res) => {
      tableData.value = res.content;
      pagination.total = res.totalCount;
    })
    .finally(() => {
      pageLoading.value = false;
    });
};
const datePickerChange = (val: any) => {
  if (!val) {
    getTableData(true);
  }
};
const addProvinceNode = () => {
  addDialogVisible.value = true;
};
const handleDelete = (id: number) => {
  apiDeleteProvince({ id }).then(() => {
    ElMessage.success("删除成功!");
    getTableData(true);
  });
};

onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (AllPermission.includes(AUTH_CODE.TENANT_MANAGER_PAGE)) {
    getTableData(true);
  }
});
</script>
<style lang="scss" scoped></style>
