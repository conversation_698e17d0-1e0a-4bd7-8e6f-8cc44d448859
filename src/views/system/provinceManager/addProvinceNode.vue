<template>
  <el-dialog
    v-model="dialogFormVisible"
    title="新增省级节点"
    width="700px"
    @close="$emit('close-dialog')"
    align-center
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px">
      <el-form-item label="节点前缀" prop="prefix">
        <el-input
          v-model.trim="formData.prefix"
          placeholder="请输入节点前缀"
          clearable
        />
      </el-form-item>
      <el-form-item label="组织机构代码" prop="bizCode">
        <el-input
          v-model.trim="formData.bizCode"
          placeholder="请输入组织机构代码"
          clearable
        />
      </el-form-item>
      <el-form-item label="省级节点" prop="nodeName">
        <el-input
          v-model.trim="formData.nodeName"
          placeholder="请输入省级节点"
          disabled
        />
      </el-form-item>
      <el-form-item label="企业名称" prop="entName">
        <el-input
          v-model.trim="formData.entName"
          placeholder="请输入企业名称"
          disabled
        />
      </el-form-item>
      <el-form-item label="节点地址" prop="nodeAddress">
        <el-input
          v-model.trim="formData.nodeAddress"
          placeholder="请输入节点地址"
          disabled
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button type="primary" @click="validForm" :loading="btnLoading">
          确认
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, onMounted, ref } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { apiAddNode, apiGetOrgInfo } from "@/api/systemManage/provinceMnager";
import { IFormData } from "@/types/system/provinceManager";

const emit = defineEmits(["close-dialog", "update-table"]);

const btnLoading = ref(false);
const formRef = ref<FormInstance>();
const dialogFormVisible = ref(true);

const formData = ref<IFormData>({
  prefix: "",
  bizCode: "",
  nodeName: "",
  entName: "",
  nodeAddress: "",
});
const prefixValidate = (rule: any, value: any, callback: any) => {
  const handleRegex = /^[0-9]\d{1}\.[0-9]\d{3}$/;
  if (!value) {
    return callback(new Error("请输入节点前缀"));
  }
  if (!handleRegex.test(value) && value !== "99.1000.1") {
    return callback("格式不正确: 请输入2+4格式前缀");
  }
  callback();
};
const checkorgCode = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback(new Error("请输入组织机构代码"));
  }
  if (value) {
    apiGetOrgInfo({ bizCode: value })
      .then((res) => {
        formData.value.nodeName = res.nodeName;
        formData.value.entName = res.entName;
        formData.value.nodeAddress = res.nodeAddress;
        callback();
      })
      .catch(() => {
        callback(new Error("请输入正确组织机构代码"));
      });
  }
};
const rules = reactive({
  prefix: [{ required: true, validator: prefixValidate, trigger: "blur" }],
  bizCode: [{ required: true, validator: checkorgCode, trigger: "blur" }],
  entName: [{ required: true, message: "请输入企业名称", trigger: "blur" }],
  nodeName: [{ required: true, message: "请输入省级节点", trigger: "blur" }],
  nodeAddress: [{ required: true, message: "请输入节点地址", trigger: "blur" }],
});

const validForm = () => {
  if (!formRef.value) return;
  formRef.value.validate((valid) => {
    if (valid) {
      addNode();
    }
  });
};

const addNode = () => {
  btnLoading.value = true;
  apiAddNode(formData.value)
    .then(() => {
      dialogFormVisible.value = false;
      emit("update-table", true);
      ElMessage({
        message: "新增成功",
        type: "success",
      });
    })
    .finally(() => {
      btnLoading.value = false;
    });
};
</script>

<style lang="scss"></style>
