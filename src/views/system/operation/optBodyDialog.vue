<template>
  <div>
    <el-drawer
      v-model="dialogFormVisible"
      title="编辑运营主体"
      size="700px"
      @close="$emit('close-dialog')"
      destroy-on-close
    >
      <div class="form-container">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="72px"
        >
          <el-form-item label="企业名称" prop="manageOrgName">
            <el-input
              v-model.trim="formData.manageOrgName"
              placeholder="请输入企业名称"
              clearable
            />
          </el-form-item>
          <el-form-item
            label="企业性质"
            prop="manageOrgNature"
            style="width: 100%"
          >
            <el-select
              v-model="formData.manageOrgNature"
              style="width: 100%"
              placeholder="请选择企业性质"
            >
              <el-option
                v-for="item in entTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="信用代码" prop="manageOrgCrtCode">
            <el-input
              v-model.trim="formData.manageOrgCrtCode"
              placeholder="请输入信用代码"
              clearable
            />
          </el-form-item>
          <el-form-item label="官方网址" prop="manageWebsite">
            <el-input
              v-model.trim="formData.manageWebsite"
              placeholder="请输入官方网址"
              clearable
            />
          </el-form-item>
          <el-form-item label="所属行业" prop="manageOrgIndustry">
            <el-select
              v-model="formData.manageOrgIndustry"
              placeholder="请选择所属行业"
              style="width: 100%"
              clearable
            >
              <el-option key="1" value="1" label="烟草" />
            </el-select>
          </el-form-item>
          <el-form-item label="企业简介" prop="manageOrgDesc">
            <el-input
              v-model.trim="formData.manageOrgDesc"
              type="textarea"
              placeholder="请输入企业简介"
              :rows="7"
              style="width: 100%"
              clearable
            />
          </el-form-item>
          <el-form-item label="所在省市">
            <ChinaAreaCascader
              v-model="selectProvince"
              @change="areaChange"
              style="width: 100%; margin-left: 0"
              clearable
            ></ChinaAreaCascader>
          </el-form-item>
          <el-form-item label="通讯地址" prop="manageContactAddr">
            <el-input
              v-model.trim="formData.manageContactAddr"
              placeholder="请输入通讯地址"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-divider style="margin: 0 0 16px 0"></el-divider>
        <div class="footer-buttons">
          <el-button type="info" plain @click="() => $emit('close-dialog')">
            取消
          </el-button>
          <el-button
            type="primary"
            :v-loading="saveBtnLoading"
            @click="
              () => {
                saveOptBody();
              }
            "
          >
            保存
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import ChinaAreaCascader from "@/components/ChinaAreaCascader/index.vue";
import { saveOptBodyApi } from "@/api/systemManage/operation";

const emit = defineEmits(["close-dialog", "edit-done"]);

const props = defineProps<{
  bodyInfo: object;
  province: Array<string>;
}>();

const dialogFormVisible = ref(true);

const formData = reactive({
  manageOrgAddr: "",
  manageOrgCity: "",
  manageOrgCrtCode: "",
  manageOrgDesc: "",
  manageOrgIndustry: "",
  manageOrgName: "",
  manageOrgNature: "",
  manageOrgProvince: "",
  manageWebsite: "",
  manageContactAddr: "",
});

const rules = reactive({
  appName: [
    {
      required: true,
      message: "请输入应用名称",
      trigger: "blur",
    },
    {
      max: 30,
      message: "应用名称最大长度不超过30",
      trigger: "blur",
    },
  ],
});

const saveBtnLoading = ref(false);

const selectProvince = ref<string[]>([]);

const entTypeList = [
  {
    value: "1",
    label: "国有控股",
  },
  {
    value: "2",
    label: "民营控股",
  },
  {
    value: "3",
    label: "外商控股",
  },
  {
    value: "4",
    label: "事业单位",
  },
  {
    value: "5",
    label: "民营非盈利",
  },
];
const areaChange = (areas: string[]) => {
  selectProvince.value = areas;
};

const saveOptBody = () => {
  saveBtnLoading.value = true;
  const [manageOrgProvince, manageOrgCity, manageOrgAddr] =
    selectProvince.value;
  saveOptBodyApi({
    ...formData,
    manageOrgProvince,
    manageOrgCity,
    manageOrgAddr,
  })
    .then(() => {
      ElMessage.success("运营主体信息保存成功!");
      emit("edit-done");
    })
    .finally(() => {
      saveBtnLoading.value = false;
    });
};

onMounted(() => {
  Object.assign(formData, props.bodyInfo);
  Object.assign(selectProvince.value, props.province);
  if (selectProvince.value.length === 3 && selectProvince.value[2] === null) {
    selectProvince.value.splice(2, 1);
  }
});
</script>

<style lang="scss" scoped>
.form-container {
  margin: 4px 0;
}
.el-form {
  margin-left: 10px;
}
.el-form-item {
  margin-bottom: 20px;
}
:deep(.el-form-item__label) {
  justify-content: flex-start;
}

:deep(.el-drawer__footer) {
  padding: 10px 0 16px 0;
  .footer-buttons {
    margin-right: 20px;
  }
}
</style>
