<template>
  <div class="content">
    <div class="section-title">
      <div class="section-title-line"></div>
      <span class="section-title-text"> 联系人 </span>
      <div class="opt-sub-btns">
        <el-button
          @click="addContact"
          type="primary"
          size="small"
          style="min-width: 92px"
          v-permission="AUTH_CODE.OPERATION_MANAGER_MAINEDIT"
          >添加联系人</el-button
        >
      </div>
    </div>
    <div>
      <el-table
        :data="formData.items"
        style="width: 100%"
        max-height="250"
        border
        size="small"
      >
        <el-table-column type="index" label="序号" min-width="64" />
        <el-table-column label="类型" min-width="112">
          <template #default="{ row }">
            {{ row.dutyType }}
          </template>
        </el-table-column>
        <el-table-column label="姓名" min-width="134"
          ><template #default="{ row }">
            {{ row.contactName }}
          </template>
        </el-table-column>
        <el-table-column label="职位" min-width="261"
          ><template #default="{ row }">
            {{ row.position }}
          </template>
        </el-table-column>
        <el-table-column label="手机号" min-width="140"
          ><template #default="{ row }">
            {{ row.phone }}
          </template>
        </el-table-column>
        <el-table-column label="邮箱" min-width="280"
          ><template #default="{ row }">
            {{ row.email }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="153">
          <template #default="scope">
            <el-button
              text
              type="primary"
              size="small"
              @click.prevent="editRow(scope.$index)"
            >
              编辑
            </el-button>
            <el-button
              text
              type="primary"
              size="small"
              @click.prevent="deleteRow(scope.$index)"
            >
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
  <ContactDialog
    v-if="dialogVisible"
    :edit-index="editIndex"
    :all-data="formData.items"
    @close-dialog="dialogVisible = false"
    @action-done="() => editDone()"
  />
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { AUTH_CODE } from "@/utils/authCode";
import { getContactApi, saveContactApi } from "@/api/systemManage/operation";
import ContactDialog from "./contactDialog.vue";

const dialogVisible = ref(false);
const editIndex = ref(-1);
const formData = reactive<{
  items: {
    dutyType: string | null;
    contactName: string | null;
    position: string | null;
    phone: string | null;
    email: string | null;
    id?: number | null;
  }[];
}>({
  items: [],
});

const editRow = (index: number) => {
  editIndex.value = index;
  dialogVisible.value = true;
};

const deleteRow = (index: number) => {
  formData.items.splice(index, 1);
  saveContactApi(formData.items).then(() => {
    getContact();
    ElMessage.success("保存成功");
  });
};

const addContact = () => {
  editIndex.value = -1;
  dialogVisible.value = true;
};

const getContact = () => {
  getContactApi().then((res) => {
    const temRes = Array.isArray(res) ? res : [];
    formData.items = temRes;
  });
};

const editDone = () => {
  dialogVisible.value = false;
  getContact();
};

onMounted(() => {
  getContact();
});
</script>

<style lang="scss" scoped>
.el-table {
  border: 1px solid #dfe4e3;
  border-bottom: 0;

  .is-error {
    height: 70px;
    :deep(.el-form-item__error) {
      top: 50px;
    }
  }
}
:deep(.el-table__header) {
  background-color: #eef2f1;
  .cell {
    color: #1d2129;
    font-weight: 500;
  }
}
:deep(.el-table--small) {
  .cell {
    padding: 0 16px;
  }
}
</style>
