<template>
  <div class="content">
    <div class="section-title">
      <div class="section-title-line"></div>
      <div class="section-title-text">运营主体</div>
      <div class="opt-sub-btns">
        <el-button
          @click="showEdit"
          type="primary"
          size="small"
          style="min-width: 56px"
          v-permission="AUTH_CODE.OPERATION_MANAGER_MAINEDIT"
          >编辑</el-button
        >
      </div>
    </div>
    <div class="node-info-basic-line">
      <span class="baseic-title">企业名称</span>
      <span class="baseic-content">
        <ellipsisText :value="infoData.manageOrgName"></ellipsisText
      ></span>
      <span class="baseic-title">企业性质</span>
      <span class="baseic-content"
        ><ellipsisText :value="manageOrgNature()"></ellipsisText
      ></span>
      <span class="baseic-title">信用代码</span>
      <span class="baseic-content"
        ><ellipsisText :value="infoData.manageOrgCrtCode"></ellipsisText
      ></span>
    </div>
    <div class="node-info-basic-line">
      <span class="baseic-title">官方网址</span>
      <span class="baseic-content"
        ><ellipsisText :value="infoData.manageWebsite"></ellipsisText
      ></span>
      <span class="baseic-title">所属行业</span>
      <span class="baseic-content"
        ><ellipsisText :value="manageOrgIndustry()"></ellipsisText
      ></span>
    </div>
    <div class="node-info-basic-line">
      <span class="baseic-title">企业简介</span>
      <span class="baseic-content"
        ><ellipsisText :value="infoData.manageOrgDesc"></ellipsisText
      ></span>
    </div>
    <div class="node-info-basic-line">
      <span class="baseic-title">所在省市</span>
      <span class="baseic-content"
        ><ellipsisText :value="provinceDesc()"></ellipsisText
      ></span>
    </div>
    <div class="node-info-basic-line">
      <span class="baseic-title">通讯地址</span>
      <span class="baseic-content"
        ><ellipsisText :value="infoData.manageContactAddr"></ellipsisText
      ></span>
    </div>
  </div>
  <OptBodyDialog
    v-if="dialogVisible"
    :province="selectProvince"
    :bodyInfo="infoData"
    @close-dialog="dialogVisible = false"
    @edit-done="() => editDone()"
  />
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import {
  optBodyDetailApi,
  provinceListApi,
} from "@/api/systemManage/operation";
import { AUTH_CODE } from "@/utils/authCode";
import OptBodyDialog from "./optBodyDialog.vue";
import ellipsisText from "@/components/ellipsisText/index.vue";

const dialogVisible = ref(false);

const areaData = ref();

const infoData = ref({
  manageOrgAddr: "",
  manageOrgCrtCode: "",
  manageOrgDesc: "",
  manageOrgIndustry: "",
  manageOrgName: "",
  manageOrgNature: "",
  manageWebsite: "",
  manageContactAddr: "",
});

const showEdit = () => {
  dialogVisible.value = true;
};

const selectProvince = ref<string[]>([]);

const dumpArea = (key: string, areaList: Array<any>) => {
  for (const area of areaList) {
    if (area.value === key) {
      return area;
    }
  }
  return null;
};

const provinceDesc = () => {
  if (
    selectProvince.value &&
    selectProvince.value.length >= 3 &&
    areaData.value &&
    areaData.value.length > 0
  ) {
    const descList: Array<string> = [];
    const key1 = selectProvince.value[0];
    const key2 = selectProvince.value[1];
    const key3 = selectProvince.value[2];
    const sheng = dumpArea(key1, areaData.value);
    if (sheng) {
      descList.push(sheng.label);
      if (sheng.children) {
        const shi = dumpArea(key2, sheng.children);
        if (shi) {
          descList.push(shi.label);
          if (shi.children) {
            const xian = dumpArea(key3, shi.children);
            if (xian) {
              descList.push(xian.label);
            }
          }
        }
      }
    }
    return descList.join(" / ");
  }
  return "";
};

const EntTypeEnum: Record<string, string> = {
  1: "国有控股",
  2: "民营控股",
  3: "外商控股",
  4: "事业单位",
  5: "民营非盈利",
};

const manageOrgNature = () => {
  return EntTypeEnum[infoData.value.manageOrgNature];
};

const manageOrgIndustry = () => {
  return infoData.value.manageOrgIndustry === "1" ? "烟草" : "";
};

const getAreas = () => {
  provinceListApi().then((data: any) => {
    const areaCas = [];
    const provinces = data[86];
    for (const province in provinces) {
      const province_item: any = {
        label: provinces[province],
        value: province,
      };
      const provinceChild = [];
      // 如果省下面有市
      if (Object.keys(data).includes(province)) {
        const cities = data[province];
        for (const city in cities) {
          const city_item: any = {
            label: cities[city],
            value: city,
          };
          const cityChild = [];
          // 如果市下面有区
          if (Object.keys(data).includes(city)) {
            const countrys = data[city];
            for (const country in countrys) {
              const country_item = {
                label: countrys[country],
                value: country,
              };
              cityChild.push(country_item);
            }
          }
          if (cityChild.length > 0) {
            city_item.children = cityChild;
          }
          provinceChild.push(city_item);
        }
      }
      if (provinceChild.length > 0) {
        province_item.children = provinceChild;
      }
      areaCas.push(province_item);
    }
    areaData.value = areaCas;
  });
};

const getDetail = () => {
  optBodyDetailApi().then((res) => {
    const { manageOrgProvince, manageOrgCity, manageOrgAddr } = res || {};
    infoData.value = res || {};
    selectProvince.value = [manageOrgProvince, manageOrgCity, manageOrgAddr];
  });
};

const editDone = () => {
  dialogVisible.value = false;
  getDetail();
};
onMounted(() => {
  getAreas();
  getDetail();
});
</script>

<style lang="scss">
// .el-row {
//   margin-bottom: 20px;
// }
</style>
