<template>
  <div class="content">
    <div>
      <NodeInfo />
      <el-divider />
      <OptBody />
      <el-divider />
      <Contact />
      <el-divider />
      <TechDetail />
      <el-divider />
      <BizOpt />
    </div>
  </div>
</template>

<script setup lang="ts">
import NodeInfo from "./nodeInfo.vue";
import OptBody from "./optBody.vue";
import Contact from "./contact.vue";
import TechDetail from "./techDetail.vue";
import BizOpt from "./bizOpt.vue";
</script>

<style lang="scss">
.content {
  background-color: #ffffff;

  .opt-header {
    margin-bottom: 8px;
    font-weight: 600;
    font-size: 18px;
  }

  .sub-label {
    margin-right: 5px;
  }

  .el-row {
    margin-bottom: 10px;
  }

  .el-cascader {
    width: 70%;
    margin-left: 10px;
  }
}
.el-divider--horizontal {
  margin: 8px 0 24px 0;
}
</style>
