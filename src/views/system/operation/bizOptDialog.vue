<template>
  <div>
    <el-drawer
      v-model="dialogFormVisible"
      title="编辑业务运营"
      size="548px"
      @close="$emit('close-dialog')"
      destroy-on-close
    >
      <div class="form-container">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="90px"
        >
          <el-form-item label="资金来源" prop="capitalSource">
            <el-input
              v-model.trim="formData.capitalSource"
              placeholder="请输入资金来源"
              clearable
            />
          </el-form-item>
          <el-form-item label="投入人员数" prop="inputPeople">
            <el-input
              v-model.trim="formData.inputPeople"
              placeholder="请输入投入人员数"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item label="所获奖项" prop="receivedAwards">
            <el-input
              v-model.trim="formData.receivedAwards"
              type="textarea"
              placeholder="请输入所获奖项"
              :rows="7"
              style="width: 100%"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-divider style="margin: 0 0 16px 0"></el-divider>
        <div class="footer-buttons">
          <el-button type="info" plain @click="() => $emit('close-dialog')">
            取消
          </el-button>
          <el-button
            type="primary"
            :v-loading="saveBtnLoading"
            @click="
              () => {
                saveBussDetail();
              }
            "
          >
            保存
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { saveBussDetailApi } from "@/api/systemManage/operation";

const emit = defineEmits(["close-dialog", "edit-done"]);

const props = defineProps<{
  infoData: object;
}>();

const dialogFormVisible = ref(true);

const formData = ref({
  capitalSource: "",
  inputPeople: 0,
  receivedAwards: "",
});

const rules = reactive({
  capitalSource: [{ required: true, message: "请输入资金来源" }],
  inputPeople: [
    {
      required: true,
      trigger: "change",
      validator: (rule: any, value: any, cb: any) => {
        if (!value) return cb(new Error("请输入投入人员数"));
        if (/^[+]{0,1}(\d+)$/.test(value)) {
          cb();
        } else {
          cb(new Error("投入人员数应为不小于0的整数"));
        }
      },
    },
  ],
  receivedAwards: [{ required: true, message: "请输入所获奖项" }],
});

const formRef = ref<FormInstance>();
const saveBtnLoading = ref(false);

const saveBussDetail = () => {
  if (!formRef.value) return;
  formRef.value.validate((valid) => {
    if (valid) {
      saveBtnLoading.value = true;
      saveBussDetailApi(formData.value)
        .then(() => {
          ElMessage.success("保存成功");
          emit("edit-done");
        })
        .finally(() => {
          saveBtnLoading.value = false;
        });
    } else {
      ElMessage.warning("请填写完整信息后再提交!");
    }
  });
};

onMounted(() => {
  Object.assign(formData.value, props.infoData);
});
</script>

<style lang="scss" scoped>
.form-container {
  margin: 4px 0;
}
.el-form {
  margin-left: 0px;
}
.el-form-item {
  margin-bottom: 20px;
}
:deep(.el-form-item__label) {
  justify-content: flex-start;
}
:deep(.el-drawer__footer) {
  padding: 10px 0 16px 0;
  .footer-buttons {
    margin-right: 20px;
  }
}
</style>
