<template>
  <div>
    <el-drawer
      v-model="dialogFormVisible"
      title="编辑技术详情"
      size="548px"
      @close="$emit('close-dialog')"
      destroy-on-close
    >
      <div class="form-container">
        <el-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          label-width="90px"
        >
          <el-form-item label="编码类型" prop="codeType">
            <el-input
              v-model.trim="formData.codeType"
              placeholder="请输入编码类型"
              clearable
            />
          </el-form-item>
          <el-form-item label="技术提供方" prop="techSupporter">
            <el-input
              v-model.trim="formData.techSupporter"
              placeholder="请输入技术提供方"
              clearable
            />
          </el-form-item>
          <el-form-item label="服务IP" prop="serviceIp">
            <el-input
              v-model.trim="formData.serviceIp"
              placeholder="请输入服务IP"
              clearable
            />
          </el-form-item>
          <el-form-item label="网络带宽" prop="networkBandWidth">
            <el-input
              v-model.trim="formData.networkBandWidth"
              placeholder="请输入网络带宽"
              clearable
            />
          </el-form-item>
          <el-form-item label="运营商线路" prop="opeLine">
            <el-input
              v-model.trim="formData.opeLine"
              placeholder="请输入运营商线路"
              clearable
            />
          </el-form-item>
          <el-form-item label="服务端口" prop="servicePort">
            <el-input
              v-model.trim="formData.servicePort"
              placeholder="请输入服务端口"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-divider style="margin: 0 0 16px 0"></el-divider>
        <div class="footer-buttons">
          <el-button type="info" plain @click="() => $emit('close-dialog')">
            取消
          </el-button>
          <el-button
            type="primary"
            :v-loading="saveBtnLoading"
            @click="
              () => {
                saveTechDetail();
              }
            "
          >
            保存
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { saveTechDetailApi } from "@/api/systemManage/operation";

const emit = defineEmits(["close-dialog", "edit-done"]);

const props = defineProps<{
  infoData: object;
}>();

const dialogFormVisible = ref(true);

const formData = ref({
  id: 0,
  codeType: "",
  networkBandWidth: "",
  opeLine: "",
  serviceIp: "",
  servicePort: 0,
  techSupporter: "",
});

const formRef = ref<FormInstance>();
const saveBtnLoading = ref(false);

const saveTechDetail = () => {
  if (!formRef.value) return;
  formRef.value?.validate((valid) => {
    if (valid) {
      saveBtnLoading.value = true;
      saveTechDetailApi(formData.value)
        .then(() => {
          ElMessage.success("保存成功");
          emit("edit-done");
        })
        .finally(() => {
          saveBtnLoading.value = false;
        });
    } else {
      ElMessage.warning("请填写完整信息后再提交!");
    }
  });
};

const rules = reactive({
  codeType: [{ required: true, message: "请输入编码类型" }],
  techSupporter: [{ required: true, message: "请输入技术提供方" }],
  serviceIp: [{ required: true, message: "请输入服务IP" }],
  networkBandWidth: [{ required: true, message: "请输入网络带宽" }],
  opeLine: [{ required: true, message: "请输入运营商线路" }],
  servicePort: [
    {
      required: true,
      trigger: "change",
      validator: (rule: any, value: any, cb: any) => {
        if (!value) return cb(new Error("请输入服务端口"));
        if (/^[+]{0,1}(\d+)$/.test(value)) {
          cb();
        } else {
          cb(new Error("服务端口应为不小于0的整数"));
        }
      },
    },
  ],
});

onMounted(() => {
  Object.assign(formData.value, props.infoData);
});
</script>

<style lang="scss" scoped>
.form-container {
  margin: 4px 0;
}
.el-form {
  margin-left: 0px;
}
.el-form-item {
  margin-bottom: 20px;
}
:deep(.el-form-item__label) {
  justify-content: flex-start;
}

:deep(.el-drawer__footer) {
  padding: 10px 0px 16px 0;
  .footer-buttons {
    margin-right: 20px;
  }
}
</style>
