<template>
  <div class="content">
    <div class="section-title">
      <div class="section-title-line"></div>
      <span class="section-title-text"> 节点信息 </span>
    </div>
    <div class="node-info-basic-line">
      <span class="baseic-title">省级节点</span>
      <span class="baseic-content">{{ nodeDetail.nodeName }}</span>
      <span class="baseic-title">节点前缀</span>
      <span class="baseic-content">{{ nodeDetail.nodePrefix }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive } from "vue";
import { AUTH_CODE } from "@/utils/authCode";
import { nodeDetailApi } from "@/api/systemManage/operation";
import store from "@/store";

interface NodeDetail {
  nodePrefix: string | null;
  nodeName: string | null;
}
const nodeDetail = reactive<NodeDetail>({ nodePrefix: null, nodeName: null });

onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (AllPermission.includes(AUTH_CODE.OPERATION_MANAGER_DETAIL)) {
    nodeDetailApi().then((res) => {
      const { nodePrefix, nodeName } = res || {};
      nodeDetail.nodePrefix = nodePrefix;
      nodeDetail.nodeName = nodeName;
    });
  }
});
</script>
<style lang="scss">
.basic-content {
  display: flex;
  justify-content: start;
  align-items: center;
  margin-bottom: 16px;
  background-color: #ffffff;
  height: 20px;
  font-size: 12px;
  .baseic-title {
    width: 68px;
    margin-right: 4px;
  }
  .baseic-content {
    width: 240px;
    margin-right: 96px;
  }
}
</style>
