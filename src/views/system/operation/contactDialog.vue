<template>
  <div>
    <el-dialog
      v-model="dialogFormVisible"
      :title="`${props.editIndex === -1 ? '添加联系人' : '编辑联系人'}`"
      width="546px"
      @close="$emit('close-dialog')"
      destroy-on-close
      align-center
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-width="66px"
      >
        <el-form-item label="类型" prop="dutyType">
          <el-input
            v-model.trim="formData.dutyType"
            placeholder="请输入类型"
            clearable
          />
        </el-form-item>
        <el-form-item label="姓名" prop="contactName">
          <el-input
            v-model.trim="formData.contactName"
            placeholder="请输入姓名"
            clearable
          />
        </el-form-item>

        <el-form-item label="职位" prop="position">
          <el-input
            v-model.trim="formData.position"
            placeholder="请输入职位"
            clearable
          />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input
            v-model.trim="formData.phone"
            placeholder="请输入手机号"
            clearable
          />
        </el-form-item>
        <el-form-item label="邮箱" prop="email" class="remove-last-padding">
          <el-input
            v-model.trim="formData.email"
            placeholder="请输入邮箱"
            clearable
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span>
          <el-button type="info" plain @click="dialogFormVisible = false"
            >取消</el-button
          >
          <el-button type="primary" @click="validForm" :loading="btnLoading">
            {{ props.editIndex === -1 ? "添加" : "确定" }}
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { saveContactApi } from "@/api/systemManage/operation";

const emit = defineEmits(["close-dialog", "action-done"]);
const saveBtnLoading = ref(false);

const props = defineProps({
  editIndex: {
    type: Number,
  },
  allData: {
    type: Array,
  },
});

const allContacts = ref<Array<any>>([]);

const formData = ref({
  dutyType: "",
  contactName: "",
  position: "",
  phone: "",
  email: "",
});

/**
 * 校验邮箱正则
 */
const checkEmail = (rule: any, value: any, callback: any) => {
  const reg = /^$|^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
  if (!reg.test(value)) {
    return callback(new Error("邮箱格式不正确"));
  }
  callback();
};

const rules = reactive({
  dutyType: [{ required: true, message: "请输入类型", trigger: "change" }],
  contactName: [{ required: true, message: "请输入姓名", trigger: "change" }],
  position: [{ required: true, message: "请输入职位", trigger: "change" }],
  phone: [
    {
      required: true,
      trigger: "change",
      validator: (rule: any, value: any, cb: any) => {
        if (!value) return cb(new Error("请输入手机号"));
        if (/^(\+\d+)?1[3456789]\d{9}$/.test(value)) {
          cb();
        } else {
          cb(new Error("手机号格式不正确"));
        }
      },
    },
  ],
  email: [
    { required: true, trigger: "change", message: "请输入邮箱" },
    { max: 30, message: "邮箱最大长度不超过30", trigger: "blur" },
    { required: true, validator: checkEmail, trigger: "blur" },
  ],
});

const btnLoading = ref(false);
const formRef = ref<FormInstance>();
const dialogFormVisible = ref(true);

const validForm = () => {
  if (!formRef.value) return;
  formRef.value.validate((valid) => {
    if (valid) {
      saveContact();
    } else {
      return false;
    }
  });
};

const saveContact = () => {
  if (!formRef.value) return;
  if (props.editIndex === -1) {
    allContacts.value.push(formData.value);
  } else {
    allContacts.value[props.editIndex!] = formData.value;
  }
  formRef.value.validate((valid) => {
    if (valid) {
      saveBtnLoading.value = true;
      saveContactApi(allContacts.value)
        .then(() => {
          ElMessage.success("保存成功");
          emit("action-done");
        })
        .finally(() => {
          saveBtnLoading.value = false;
        });
    }
  });
};

onMounted(() => {
  allContacts.value = props.allData ?? [];
  if (props.editIndex !== -1) {
    formData.value = allContacts.value[props.editIndex!];
  }
});
</script>

<style lang="scss" scoped>
.form-container {
  margin: 0 0;
}
.el-form-item {
  margin-bottom: 20px;
  &:last-child {
    margin-bottom: 0px;
  }
}
.el-form {
  margin-left: 0px;
}
:deep(.el-form-item__label) {
  justify-content: flex-start;
}

:deep(.el-dialog__header) {
  padding-left: 20px;
  font-weight: 500;
  color: #1d2129;
  text-align: left;
}
:deep(.el-dialog__body) {
  padding: 24px 20px;
}
</style>
