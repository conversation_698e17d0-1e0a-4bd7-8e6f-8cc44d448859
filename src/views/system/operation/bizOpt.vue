<template>
  <div class="content">
    <div class="section-title">
      <div class="section-title-line"></div>
      <span class="section-title-text"> 业务运营 </span>

      <div class="opt-sub-btns">
        <el-button
          @click="showEdit"
          size="small"
          type="primary"
          style="min-width: 56px"
          v-permission="AUTH_CODE.OPERATION_MANAGER_BUSSEDIT"
          >编辑</el-button
        >
      </div>
    </div>
    <div class="node-info-basic-line">
      <span class="baseic-title">资金来源</span>
      <span class="baseic-content">{{ formData.data.capitalSource }}</span>
      <span class="baseic-title">投入人员数</span>
      <span class="baseic-content">{{ formData.data.inputPeople }}</span>
    </div>
    <div class="node-info-basic-line">
      <span class="baseic-title">所获奖项</span>
      <span class="baseic-content">{{ formData.data.receivedAwards }}</span>
    </div>
  </div>
  <BizOptDialog
    v-if="dialogVisible"
    :infoData="formData.data"
    @close-dialog="dialogVisible = false"
    @edit-done="() => editDone()"
  />
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { AUTH_CODE } from "@/utils/authCode";
import { getBussDetailApi } from "@/api/systemManage/operation";
import BizOptDialog from "./bizOptDialog.vue";

const dialogVisible = ref(false);
const showEdit = () => {
  dialogVisible.value = true;
};

const formData = reactive({
  data: {
    capitalSource: "",
    inputPeople: 0,
    receivedAwards: "",
  },
});

const getDetail = () => {
  getBussDetailApi().then((res) => {
    formData.data = res;
  });
};

const editDone = () => {
  dialogVisible.value = false;
  getDetail();
};

onMounted(() => {
  getDetail();
});
</script>

<style lang="scss"></style>
