<template>
  <div class="content">
    <div class="section-title">
      <div class="section-title-line"></div>
      <span class="section-title-text"> 技术详情 </span>
      <div class="opt-sub-btns">
        <el-button
          @click="showEdit"
          size="small"
          type="primary"
          style="min-width: 56px"
          v-permission="AUTH_CODE.OPERATION_MANAGER_TECHEDIT"
          >编辑</el-button
        >
      </div>
    </div>
    <div class="node-info-basic-line">
      <span class="baseic-title">编码类型</span>
      <span class="baseic-content">{{ formData.data.codeType }}</span>
      <span class="baseic-title">技术提供方</span>
      <span class="baseic-content">{{ formData.data.techSupporter }}</span>
      <span class="baseic-title">服务IP</span>
      <span class="baseic-content">{{ formData.data.serviceIp }}</span>
    </div>
    <div class="node-info-basic-line">
      <span class="baseic-title">网络带宽</span>
      <span class="baseic-content">{{ formData.data.networkBandWidth }}</span>
      <span class="baseic-title">运营商线路</span>
      <span class="baseic-content">{{ formData.data.opeLine }}</span>
      <span class="baseic-title">服务端口</span>
      <span class="baseic-content">{{ formData.data.servicePort }}</span>
    </div>
  </div>
  <TechDetailDialog
    v-if="dialogVisible"
    :infoData="formData.data"
    @close-dialog="dialogVisible = false"
    @edit-done="() => editDone()"
  />
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import { AUTH_CODE } from "@/utils/authCode";
import { getTechDetailApi } from "@/api/systemManage/operation";
import TechDetailDialog from "./techDetailDialog.vue";

const dialogVisible = ref(false);
const showEdit = () => {
  dialogVisible.value = true;
};

interface FormData {
  data: {
    id: number;
    codeType: string;
    networkBandWidth: string;
    opeLine: string;
    serviceIp: string;
    servicePort: number;
    techSupporter: string;
  };
}
const formData = reactive<FormData>({
  data: {
    id: 0,
    codeType: "",
    networkBandWidth: "",
    opeLine: "",
    serviceIp: "",
    servicePort: 0,
    techSupporter: "",
  },
});

const getDetail = () => {
  getTechDetailApi().then((res) => {
    formData.data = res;
  });
};

const editDone = () => {
  dialogVisible.value = false;
  getDetail();
};

onMounted(() => {
  getDetail();
});
</script>

<style lang="scss"></style>
