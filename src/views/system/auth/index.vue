<template>
  <div>
    <div class="auth-header">
      <el-select
        v-model="selectRoleId"
        placeholder="请选择角色"
        @change="onSelectChange"
      >
        <el-option
          v-for="item in roleOptions.list"
          :key="item.id"
          :label="item.roleName"
          :value="item.id"
        />
      </el-select>

      <el-button
        style="margin-left: 24px"
        @click="updatePerm"
        :loading="saveBtnLoading"
        type="primary"
        :disabled="isUnEditRole"
        v-permission="AUTH_CODE.AUTH_MANAGER_TREEEDIT"
        >保存</el-button
      >
      <el-button @click="() => getTreeData()" :disabled="isUnEditRole"
        >取消</el-button
      >
    </div>
    <div class="tree-wrap">
      <el-tree
        ref="authTree"
        :data="treeData.list"
        :props="defaultProps"
        show-checkbox
        :check-on-click-node="false"
        :highlight-current="false"
        node-key="id"
        class="authtree"
      >
        <template #default="{ node }">
          <!-- <span class="grandNodes" v-if="node.level === 1">
            <span>{{ node.label }}</span>
          </span> -->
          <span class="grandNodes" v-if="!node.isLeaf">
            <span>{{ node.label }}</span>
          </span>
          <span class="leafNodes" v-if="node.isLeaf">
            <span>{{ node.label }}</span>
          </span>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, getCurrentInstance, ref, onMounted, computed } from "vue";
import { ElMessage } from "element-plus";
import { permListApi, updatePermApi } from "@/api/systemManage/auth";
import { roleDropdown } from "@/api/systemManage/role";
import { ROLE_TYPE } from "@/utils/constant";
import { IRoleListItem } from "@/types/system/role";
import { AUTH_CODE } from "@/utils/authCode";
import store from "@/store";

const selectRoleId = ref();
const { proxy } = getCurrentInstance();
const roleOptions: { list: IRoleListItem[] } = reactive({ list: [] });
interface TreeData {
  authCode: string;
  authDesc: string;
  authName: string;
  childNodes: TreeData[];
  id: number;
  parentId: number;
  authType: number;
}
const treeData: { list: TreeData[] } = reactive({ list: [] });
const loading = ref(false);
const saveBtnLoading = ref(false);
const selectedAuthIds = ref([]);

// 系统管理员权限不可编辑
const isUnEditRole = computed(() => {
  return (
    roleOptions.list.find((item) => item.id === selectRoleId.value)
      ?.undelete === 1
  );
});

const defaultProps = {
  children: "childNodes",
  label: "authName",
};

onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (AllPermission.includes(AUTH_CODE.AUTH_MANAGER_TREELIST)) {
    getRoleList();
    getTreeData();
  }
});

const getRoleList = () => {
  roleDropdown().then((res) => {
    roleOptions.list = [];
    if (Array.isArray(res)) {
      roleOptions.list = res;

      // const findAdmin = res.find((item) => item.id === ROLE_TYPE.SYSTEM_ADMIN);
      // const findEnt = res.find(
      //   (item) => item.id === ROLE_TYPE.ENT_SYSTEM_ADMIN
      // );

      // if (findAdmin) {
      //   selectRoleId.value = ROLE_TYPE.SYSTEM_ADMIN;
      //   getTreeData();
      // }
      // if (findEnt) {
      //   selectRoleId.value = ROLE_TYPE.ENT_SYSTEM_ADMIN;
      //   getTreeData();
      // }
    }
  });
};

const onSelectChange = () => {
  getTreeData();
};

// 默认查询系统管理员
const getTreeData = () => {
  if (!selectRoleId.value) return;
  loading.value = true;
  permListApi({ roleId: selectRoleId.value })
    .then((res) => {
      treeData.list = [];
      const list = res || [];
      treeData.list = list;
      proxy.$refs.authTree.setCheckedKeys(filterSelectNode(list));
    })
    .finally(() => {
      loading.value = false;
    });
};

const updatePerm = () => {
  if (!selectRoleId.value) {
    return ElMessage.warning("请先选择角色!");
  }
  // 获取勾选的数组及祖级
  const checkList = proxy.$refs.authTree
    .getCheckedKeys()
    .concat(proxy.$refs.authTree.getHalfCheckedKeys());
  saveBtnLoading.value = true;
  updatePermApi({
    roleId: selectRoleId.value,
    authIdList: checkList,
  })
    .then(() => {
      ElMessage.success("权限更改成功!");
    })
    .catch(() => {
      getTreeData();
    })
    .finally(() => {
      saveBtnLoading.value = false;
    });
};

const filterSelectNode = (tree: any = [], selectList: any = []) => {
  tree.forEach((item: any) => {
    if (item.childNodes.length) {
      filterSelectNode(item.childNodes, selectList);
    } else if (item.selected === 1) {
      selectList.push(item.id);
    }
  });

  return selectList;
};
</script>

<style lang="scss" scoped>
.auth-header {
  display: flex;
  justify-content: start;
  align-items: center;
}

.tree-wrap {
  margin-top: 15px;
  padding: 15px 12px;
  max-height: 500px;
  overflow-y: auto;
  background-color: #fff;
  position: relative;
}

// :deep(.el-checkbox) {
//   display: none;
// }
:deep(.is-leaf + .el-checkbox) {
  display: inline-flex;
}

:deep(.el-collapse-item__header) {
  padding-left: 20px;
  font-size: 14px;
}
:deep(.el-collapse-item__arrow) {
  position: absolute;
  left: 18px;
}
:deep(.el-collapse-item__content) {
  padding-bottom: 0;
}
.childNodes {
  position: relative;
  .el-collapse {
    border-bottom: 0;
    :deep(.el-collapse-item) {
      padding: 0 14px;
    }
    border-bottom: 0;
    :deep(.el-collapse-item__wrap) {
      border-bottom: 0;
      margin: 0 -20px;
    }
    :deep(.el-collapse-item__content) {
      border-top: 1px solid #dfe4e3;
    }
    :deep(.el-collapse-item__header) {
      border-bottom: 0;
    }
  }
}
.grandNodes {
  padding: 12px 40px;
  width: 100%;
  // background: rgba(245, 247, 247, 0.7);
}

:deep(
    .el-tree-node__children:has(
        > .el-tree-node > .el-tree-node__content > .leafNodes
      )
  ) {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  background: rgba(245, 247, 247, 0.7);
}

.leafNodes {
  padding: 12px 8px;
}

.childNodes-noleaf {
  border-top: 1px solid #dfe4e3;
  padding: 12px 40px;
}

:deep(.el-tree-node__content) {
  display: flex;
  align-items: center;
  height: 50%;
  cursor: pointer;
}

// :deep(.el-tree .is-current) {
//   --el-tree-node-hover-bg-color: rgba(245, 247, 247, 0.7);
//   position: relative;
// }
// :deep(.el-tree-node > .el-tree-node__children) {
//   display: block;
//   // background: rgba(245, 247, 247, 0.7);
//   flex-wrap: wrap;
//   align-items: center;
// }

.el-tree {
  --el-tree-node-hover-bg-color: rgba(245, 247, 247, 0)
    var(--el-fill-color-light);
  --el-tree-text-color: var(--el-text-color-regular);
  --el-tree-expand-icon-color: var(--el-text-color-placeholder);
}

:deep(.el-tree .is-current) {
  background-color: rgba(245, 247, 247, 0) !important;
  --el-tree-node-hover-bg-color: rgba(245, 247, 247, 0) !important;
  position: relative;
}
:deep(.el-tree .is-current::before) {
  display: none;
  // background-color: rgba(37, 216, 216, 0.7);
}
:deep(.el-tree-node) {
  background: rgba(245, 247, 247, 0);
}

// :deep(.authtree > .el-tree-node) {
//   //叶子节点显示复选框，其他节点不显示复选框
//   .is-leaf ~ .el-checkbox .el-checkbox__inner {
//     display: flex;
//   }

//   .el-checkbox__input > .el-checkbox__inner {
//     display: none;
//   }
// }

// //非一级节点
// :deep(.authtree > .el-tree-node__children) {
//   //节点展开显示复选框
//   .expanded ~ .el-checkbox .el-checkbox__inner {
//     display: flex;
//   }
//   .el-checkbox__input > .el-checkbox__inner {
//     display: none;
//   }
// }
</style>
