<template>
  <div v-if="!data.showRecord">
    <SearchLayout>
      <template #left>
        <el-button
          type="primary"
          @click="handleAdd"
          v-permission="AUTH_CODE.DATA_SERVICE_ADD"
          >+新增</el-button
        >
      </template>
      <template #right>
        <el-form
          v-permission="AUTH_CODE.DATA_SERVICE_PAGE"
          :inline="true"
          :model="searchForm"
          @submit.prevent
        >
          <el-form-item style="width: 240px; max-width: 240px"
            ><el-input
              v-model.trim="searchForm.dataServiceName"
              clearable
              placeholder="请输入"
              @clear="handleSearch"
            >
              <template #prefix>数据服务:</template></el-input
            ></el-form-item
          >
          <el-form-item style="width: 60px; max-width: 60px"
            ><el-button
              type="primary"
              :loading="data.searchLoading"
              @click="handleSearch"
              >搜索</el-button
            ></el-form-item
          >
        </el-form>
      </template>
    </SearchLayout>
    <div class="page-search-body">
      <el-table
        :data="data.tableData"
        v-loading="data.tableLoading"
        border
        size="small"
      >
        <el-table-column label="序号" type="index" width="55" />
        <el-table-column property="id" v-if="false" />
        <el-table-column
          property="dataServiceName"
          label="数据服务名称"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-copy="scope.row.dataServiceName">{{
              scope.row.dataServiceName || "-"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          property="serviceAddress"
          label="数据服务地址"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-copy="scope.row.serviceAddress">{{
              scope.row.serviceAddress || "-"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          property="dataServiceUuid"
          label="数据服务ID"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-copy="scope.row.dataServiceUuid">{{
              scope.row.dataServiceUuid || "-"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          property="serviceToken"
          label="token"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span v-copy="scope.row.serviceToken">{{
              scope.row.serviceToken || "-"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          property="dataServiceType"
          label="类型"
          :show-overflow-tooltip="true"
        >
          <template #default="scope">
            <span>{{
              scope.row.dataServiceType === 1 ? "标准版" : "自研版"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button type="primary" text @click="toSelfDevelop(scope.row)">
              数据库配置
            </el-button>
            <el-button
              type="primary"
              text
              @click="handleDetail(scope.$index, scope.row)"
              v-permission="AUTH_CODE.DATA_SERVICE_DETAIL"
              >详情</el-button
            >

            <el-dropdown
              trigger="click"
              class="drop-btn"
              :hide-on-click="false"
            >
              <el-button type="primary" text>
                <img src="@/assets/icons/svg/more.svg" />
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    @click="handleEdit(scope.$index, scope.row)"
                    v-permission="AUTH_CODE.DATA_SERVICE_EDIT"
                  >
                    <el-button type="primary" text>编辑</el-button>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <el-popconfirm
                      :width="200"
                      confirm-button-text="确定"
                      cancel-button-text="取消"
                      title="是否确认删除？"
                      @confirm="handleDeleteConfirm(scope.row.id)"
                    >
                      <template #reference>
                        <el-button
                          text
                          type="primary"
                          v-permission="AUTH_CODE.DATA_SERVICE_DELETE"
                          >删除</el-button
                        >
                      </template>
                    </el-popconfirm>
                  </el-dropdown-item>
                  <el-dropdown-item @click="handleMonitor(scope.row)">
                    <el-button text type="primary"> 监测</el-button>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        v-model:page="data.page"
        v-model:page-size="data.size"
        :page-sizes="[10, 20, 30, 40]"
        small
        layout="total, sizes, prev, pager, next, jumper"
        :total="data.totalCount"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <addForm
      v-if="addDialogFormVisible"
      :sucessThenQuery="sucessThenQuery"
      :cancleAddForm="cancleAddForm"
      :selectDataServiceId="addFormData.id"
      :isAddService="addFormData.isAddService"
      :dialogVisible="addDialogFormVisible"
      @closeDialog="() => (addDialogFormVisible = false)"
    ></addForm>
    <detailService
      v-if="detailDrawerVisible"
      :selectDataServiceId="addFormData.id"
      @close="detailDrawerVisible = false"
    >
    </detailService>
  </div>
  <div v-if="data.showRecord">
    <self-develop-list :serviceData="selectData" @backToList="backToList" />
  </div>
  <monitor-drawer
    v-if="monitorVisiable"
    @close="monitorVisiable = false"
    :itemId="addFormData.id"
  />
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage } from "element-plus";
import addForm from "@/views/system/dataService/addService.vue";
import detailService from "@/views/system/dataService/detailService.vue";
import {
  getServiceListPageApi,
  deleteServiceApi,
} from "@/api/objHandle/manager";
import { AUTH_CODE } from "@/utils/authCode";
import store from "@/store";
import SearchLayout from "@/components/searchLayout/index.vue";
import selfDevelopList from "./components/selfDevelopList.vue";
import monitorDrawer from "./components/monitorDrawer.vue";

const addDialogFormVisible = ref(false);
const detailDrawerVisible = ref(false);
const monitorVisiable = ref(false);
const addFormData = reactive<any>({
  dialogVisible: addDialogFormVisible,
  id: "",
  isAddService: true,
});

const searchForm = reactive({
  dataServiceName: "",
});
interface TableItem {
  id: string;
  dataServiceName: string;
  serviceAddress: string;
  serviceToken: string;
  type: string;
  dataServiceType: number | string;
}
interface Data {
  tableData: {
    id: string;
    dataServiceName: string;
    serviceAddress: string;
    serviceToken: string;
    type: string;
  }[];
  page: number;
  size: number;
  totalCount: number;
  // selectData: any;
  deleteLoading: boolean;
  tableLoading: boolean;
  searchLoading: boolean;
  showRecord: boolean;
  // recordHandle: string;
}
const data = reactive<Data>({
  tableData: [],
  page: 1,
  size: 10,
  totalCount: 1,
  // selectData: null,
  // 列表loading
  tableLoading: false,
  // 搜索按钮状态控制
  searchLoading: false,
  // 删除状态控制
  deleteLoading: false,
  showRecord: false,
  // recordHandle: "",
});

const selectData = reactive<{
  id: number;
  dataServiceName: string;
  dataServiceType: number;
}>({
  id: -1,
  dataServiceName: "",
  dataServiceType: 1, // 1-标准，2-自研
});

// 子组件操作成功查询列表
function sucessThenQuery() {
  addDialogFormVisible.value = false;
  data.page = 1;
  getTableData();
}
// 子组件取消
function cancleAddForm() {
  addDialogFormVisible.value = false;
}

// 查询列表
function getTableData() {
  data.tableLoading = true;
  data.tableData = [];
  const params = {
    dataServiceName: searchForm.dataServiceName,
    page: data.page - 1,
    size: data.size,
  };
  data.tableLoading = false;
  data.searchLoading = false;
  getServiceListPageApi(params)
    .then((response: any) => {
      const result = response;
      data.tableData = result?.content || [];
      // data.page = result?.pageNumber || 1;
      data.totalCount = result?.totalCount || 0;
    })
    .finally(() => {
      data.tableLoading = false;
      data.searchLoading = false;
    });
}

// 查询按钮
function handleSearch() {
  data.searchLoading = true;
  data.page = 1;
  getTableData();
}

// 修改页码
function handleSizeChange(num: number) {
  data.size = num;
  data.page = 1;
  getTableData();
}

function handleCurrentChange(num: number) {
  data.page = num;
  getTableData();
}

// 新增按钮
function handleAdd() {
  addDialogFormVisible.value = true;
  addFormData.selectData = null;
  // 新增模式
  addFormData.isAddService = true;
}

// 删除按钮
function handleDeleteConfirm(id: any) {
  data.deleteLoading = true;
  deleteServiceApi({ id })
    .then(() => {
      ElMessage.success("删除成功");
      data.page = 1;
      getTableData();
    })
    .finally(() => {
      data.deleteLoading = false;
    });
}

// 编辑按钮
function handleEdit(index: any, item: any) {
  addDialogFormVisible.value = true;
  addFormData.id = item.id;
  // 修改模式
  addFormData.isAddService = false;
}

// 详情按钮
function handleDetail(index: any, item: any) {
  detailDrawerVisible.value = true;
  addFormData.id = item.id;
}

// 监测按钮
const handleMonitor = (item: any) => {
  monitorVisiable.value = true;
  addFormData.id = item.id;
};
onMounted(() => {
  // 判断用户是否有初始化查询权限
  const AllPermission = store.getters.auths;
  if (AllPermission.includes(AUTH_CODE.DATA_SERVICE_PAGE)) {
    getTableData();
  }
});
function toSelfDevelop(record: TableItem) {
  data.showRecord = true;
  selectData.id = record.id;
  selectData.dataServiceName = record.dataServiceName;
  selectData.dataServiceType = record.dataServiceType;
}
function backToList() {
  data.showRecord = false;
  getTableData();
}
</script>
<style lang="scss" scoped>
.popover-delete-tips {
  margin-bottom: 20px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;

  .popover-delete-tips-text {
    margin-left: 10px;
    font-size: 14px;
  }
}

.popover-delete-wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}

.table-service-type {
  color: #1664ff;
  cursor: pointer;
}

.drop-btn {
  margin-left: 16px;
}
</style>
