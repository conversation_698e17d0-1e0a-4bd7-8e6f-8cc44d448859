<template>
  <el-dialog
    append-to-body
    align-center
    v-model="dialogVisible"
    :title="title"
    width="700px"
    @close="$emit('close-dialog')"
    destroy-on-close
  >
    <el-form
      ref="formRef"
      label-position="left"
      :model="formData"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="类型" required>
        <el-radio-group
          v-model="formData.dataServiceType"
          :disabled="!props.isAddService"
        >
          <el-radio :label="1">标准版</el-radio>
          <el-radio :label="2">自研版</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="应用系统" required>
        <el-input
          v-model.trim="userInfo.appName"
          placeholder="应用系统"
          disabled
          clearable
        />
      </el-form-item>
      <el-form-item label="数据服务名称" prop="dataServiceName">
        <el-input
          v-model.trim="formData.dataServiceName"
          placeholder="请输入数据服务名称，例如：XXX数据服务"
          clearable
        />
      </el-form-item>
      <el-form-item label="地址" prop="serviceAddress">
        <div class="service-address-wrap">
          <el-select
            v-model="formData.type"
            placeholder="请选择"
            style="width: 98px"
          >
            <el-option
              v-for="item in httpType"
              :key="item"
              :value="item"
              :label="item"
            ></el-option>
          </el-select>
          <div class="service-address-wrap-line">/</div>
          <el-input
            v-model.trim="formData.serviceAddress"
            placeholder="请输入"
            clearable
          >
          </el-input>
        </div>
      </el-form-item>
      <el-form-item label="token" prop="serviceToken">
        <div class="service-token-wrap">
          <el-input
            v-model.trim="formData.serviceToken"
            placeholder="请点击生成按钮自动生成token令牌"
            disabled
            clearable
          />
          <el-button
            class="generate-token-btn"
            :loading="tokenBtnLoading"
            @click="clickCreateToken"
          >
            {{ formData.serviceToken ? "重新生成" : "生成" }}
          </el-button>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="cancle">取消</el-button>
        <el-button
          type="primary"
          @click="validForm(formRef)"
          v-loading="btnLoading"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, computed } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { useStore } from "vuex";
import {
  addServiceApi,
  editServiceApi,
  getServiceDetailApi,
} from "@/api/objHandle/manager";
import { apiGenerateToken } from "@/api/systemManage/dataService";

const props = defineProps({
  selectDataServiceId: {
    type: Number,
    default: null,
  },
  isAddService: {
    type: Boolean,
    default: true,
  },
  dialogVisible: {
    type: Boolean,
    default: true,
  },
  sucessThenQuery: {
    type: Function,
    default: null,
  },
  cancleAddForm: {
    type: Function,
    default: null,
  },
});
const btnLoading = ref(false);
const tokenBtnLoading = ref(false);
const title = (props.isAddService ? "新增" : "编辑") + "数据服务";
const httpType = ref(["http://", "https://"]);

const userInfo = computed(() => useStore().getters.userInfo);
const formRef = ref<FormInstance>();
const formData = ref({
  id: null,
  dataServiceName: "",
  serviceAddress: "",
  serviceToken: "",
  type: "http://",
  dataServiceType: 1,
});
const serviceAddressValidate = (rule: any, value: any, callback: any) => {
  const reg =
    /(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,4})*(\/[a-zA-Z0-9\&%_\.\/-~-]*)?/;

  const domainReg =
    /((?!-)[A-Za-z0-9-]{1,63}(?<!-)\.)+[A-Za-z]{2,6}(\/.+)?(:\d+)?$/; // 域名校验规则：字符串:端口号
  if (!value) {
    return callback("请填写服务器地址");
  }
  if (!reg.test(value) && !domainReg.test(value)) {
    return callback("服务地址格式错误，请重新填写");
  }
  callback();
};
const rules = reactive({
  dataServiceName: [
    { required: true, message: "请输入数据服务名称", trigger: "blur" },
    { max: 20, message: "最大长度不超过20字符", trigger: "blur" },
  ],
  serviceAddress: [
    { required: true, validator: serviceAddressValidate, trigger: "blur" },
  ],
  serviceToken: [
    { required: true, message: "请生成数据服务token" },
    { max: 50, message: "最大长度不超过50字符" },
  ],
});

const validForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid) => {
    if (valid) {
      addOrEditService();
    }
  });
};

// 生成Token
const clickCreateToken = () => {
  tokenBtnLoading.value = true;
  apiGenerateToken()
    .then((res: any) => {
      formData.value.serviceToken = res;
    })
    .catch(() => {
      formData.value.serviceToken = "";
    })
    .finally(() => {
      tokenBtnLoading.value = false;
    });
};

const addOrEditService = () => {
  const api = props.isAddService ? addServiceApi : editServiceApi;
  btnLoading.value = true;
  api({
    id: formData.value.id,
    dataServiceName: formData.value.dataServiceName,
    serviceAddress: formData.value.serviceAddress
      ? formData.value.type + formData.value.serviceAddress
      : "",
    serviceToken: formData.value.serviceToken,
    dataServiceType: formData.value.dataServiceType,
  })
    .then((res) => {
      props.sucessThenQuery();
      ElMessage({
        message: `${props.isAddService ? "新增" : "编辑"}数据服务成功!`,
        type: "success",
      });
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

const getServiceDetail = () => {
  getServiceDetailApi({ id: props.selectDataServiceId }).then((res: any) => {
    formData.value = res || {};
  });
};

const cancle = () => {
  props.cancleAddForm();
};

onMounted(() => {
  if (!props.isAddService) {
    getServiceDetail();
  }
});
</script>

<style lang="scss" scoped>
.service-address-wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  .service-address-wrap-line {
    padding: 0 4px;
    color: #000;
  }
}
.service-token-wrap {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .generate-token-btn {
    margin-left: 12px;
    background-color: #f5f6f6;
  }
}
</style>
