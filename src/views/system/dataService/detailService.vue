<template>
  <el-drawer
    v-model="drawerVisible"
    title="数据服务详情"
    v-loading="loading"
    @closed="handleClose"
    size="700px"
  >
    <div class="yc-description">
      <div class="yc-description-title">
        <div class="yc-description-title-tip"></div>
        <div class="yc-description-title-text">基本信息</div>
      </div>
      <el-descriptions :column="1" direction="horizontal">
        <el-descriptions-item label="类型">
          {{ detail?.dataServiceType === 1 ? "标准版" : "自研版" }}
        </el-descriptions-item>
        <el-descriptions-item label="数据服务名称">
          {{ detail?.dataServiceName || "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="数据服务地址">
          {{ detail?.serviceAddress || "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="数据服务ID">
          {{ detail?.dataServiceUuid || "-" }}
        </el-descriptions-item>
        <el-descriptions-item label="token">
          {{ detail?.serviceToken || "-" }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { onMounted, ref, defineProps, defineEmits } from "vue";
import { getServiceDetailApi } from "@/api/objHandle/manager";
// import DataTab from "./components/dataTab.vue";
// import MonitorTab from "./components/monitorTab.vue";

const emit = defineEmits(["close"]);
const props = defineProps({
  selectDataServiceId: {
    type: Number,
    default: null,
  },
});

const drawerVisible = ref(true);

const loading = ref(false);
const activeTabName = ref("dataTab");

const detail = ref({
  id: null,
  dataServiceName: "",
  serviceAddress: "",
  serviceToken: "",
  dataServiceUuid: "",
  dataSource: [],
  dataServiceType: 1,
});

// 获取数据服务详情
const getServiceDetail = () => {
  getServiceDetailApi({ id: props.selectDataServiceId }).then((res: any) => {
    detail.value = res || {};
  });
};

// 关闭
function handleClose() {
  emit("close");
}
onMounted(() => {
  getServiceDetail();
});
</script>
<style lang="scss" scoped>
.el-divider {
  margin: 8px 0 24px 0;
}
:deep(.el-descriptions__label) {
  width: 90px;
  min-width: 90px;
  display: inline-block;
}
:deep(.el-tabs__nav-wrap::after) {
  content: none;
}
:deep(.el-tabs__active-bar) {
  display: none;
}
:deep(.el-tabs__item) {
  height: auto;
  line-height: normal;
}
:deep(.el-tabs--top .el-tabs__item.is-top:nth-child(2)) {
  border-right: 2px solid #c1c9c7;
}
</style>
