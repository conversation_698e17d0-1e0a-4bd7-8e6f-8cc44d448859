<template>
  <el-table
    v-loading="data.tableLoading"
    :data="data?.tableData"
    border
    size="small"
  >
    <el-table-column label="序号" type="index" />
    <el-table-column label="数据通道名称" property="name">
      <template #default="scope">
        <span v-copy="scope.row.name">{{ scope.row.name || "-" }}</span>
      </template>
    </el-table-column>
    <el-table-column label="操作">
      <template #default="scope">
        <el-button
          size="small"
          type="primary"
          text
          @click="handleViewReference(scope.row)"
          >详情</el-button
        >
      </template>
    </el-table-column>
  </el-table>
  <el-pagination
    background
    v-model:page="data.page"
    v-model:page-size="data.size"
    :page-sizes="[10, 20, 30, 40]"
    small
    layout="total, sizes, prev, pager, next, jumper"
    :total="data.totalCount"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  />

  <handleItemDrawer
    v-if="handleItemDrawerVisible"
    :itemData="itemData"
    @close="handleItemDrawerVisible = false"
  ></handleItemDrawer>
</template>

<script lang="ts" setup>
import { onMounted, ref, defineProps, reactive } from "vue";
import { getDataSourceApi } from "@/api/objHandle/manager";
import handleItemDrawer from "./handleItemDrawer.vue";

const props = defineProps({
  dataSourceId: {
    type: Number,
    default: null,
  },
});

const data = reactive<Record<string, any>>({
  tableData: [],
  page: 1,
  size: 10,
  totalCount: 0,
  // 列表loading
  tableLoading: false,
});

const itemData = ref({
  dataServiceId: "",
  dataSourceName: "",
  dataSourceId: "",
});
const handleItemDrawerVisible = ref(false);

// 基础属性详情按钮
function handleViewReference(data: any) {
  itemData.value.dataSourceId = data.id;
  itemData.value.dataSourceName = data.name;
  itemData.value.dataServiceId = props.dataSourceId + "";
  handleItemDrawerVisible.value = true;
}

// 获取数据源详情
const getTableData = () => {
  data.tableLoading = true;
  getDataSourceApi({
    id: props.dataSourceId,
    page: data.page - 1,
    size: data.size,
  })
    .then((res: any) => {
      data.tableData = res?.content || [];
      data.totalCount = res?.totalCount || 0;
    })
    .finally(() => {
      data.tableLoading = false;
    });
};

// 修改页码
const handleSizeChange = (num: number) => {
  data.size = num;
  data.page = 1;
  getTableData();
};

const handleCurrentChange = (num: number) => {
  data.page = num;
  getTableData();
};

onMounted(() => {
  getTableData();
});
</script>
<style lang="scss" scoped>
.el-divider {
  margin: 8px 0 24px 0;
}
:deep(.el-descriptions__label) {
  width: 72px;
  min-width: 72px;
  display: inline-block;
}
:deep(.el-tabs__nav-wrap::after) {
  content: none;
}
:deep(.el-tabs__active-bar) {
  display: none;
}
:deep(.el-tabs__item) {
  height: auto;
  line-height: normal;
}
:deep(.el-tabs--top .el-tabs__item.is-top:nth-child(2)) {
  border-right: 2px solid #c1c9c7;
}
</style>
