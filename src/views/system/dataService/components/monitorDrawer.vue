<template>
  <el-drawer
    v-model="drawerVisible"
    @closed="handleClosed"
    title="监测"
    size="680px"
    style="text-align: center"
  >
    <div class="title">数据服务监测</div>
    <el-table
      :data="data.serviceData"
      border
      size="small"
      class="bottom"
      v-loading="data.tableLoading"
    >
      <el-table-column label="数据服务" property="dataServiceName">
        <template #default="scope">
          <ellipsisText :value="scope.row.dataServiceName">{{
            scope.row.dataServiceName || "-"
          }}</ellipsisText>
        </template>
      </el-table-column>
      <el-table-column label="数据服务地址" property="serviceAddress">
        <template #default="scope">
          <ellipsisText :value="scope.row.serviceAddress">{{
            scope.row.serviceAddress || "-"
          }}</ellipsisText>
        </template>
      </el-table-column>
      <el-table-column label="类型" property="dataServiceType">
        <template #default="scope">
          <span>{{
            scope.row.dataServiceType === 1 ? "标准版" : "自研版"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="数据服务状态">
        <template #default="scope">
          <div class="status" v-if="scope.row.status === 1">
            <img src="@/assets/images/empower/Success.png" alt="" />
            <span style="color: #00aa2a">正常</span>
          </div>
          <div class="status" v-else>
            <img src="@/assets/images/empower/error.png" alt="" />
            <span style="color: #e63f3f">异常</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div
      v-if="
        (data.serviceData[0]?.dataServiceType === 1 &&
          data.serviceData[0]?.status === 1) ||
        data.serviceData[0]?.dataServiceType === 0
      "
    >
      <div class="title">数据源监测</div>
      <el-table :data="data.sourceData" border size="small">
        <el-table-column label=" 数据库名称" property="dataSourceName" />
        <el-table-column label="数据库IP" property="dataSourceAddress" />
        <el-table-column label="数据库状态" property="status">
          <template #default="scope">
            <div class="status" v-if="scope.row.status === 1">
              <img src="@/assets/images/empower/Success.png" alt="" />
              <span style="color: #00aa2a">正常</span>
            </div>
            <div class="status" v-else>
              <img src="@/assets/images/empower/error.png" alt="" />
              <span style="color: #e63f3f">异常</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { defineProps, ref, defineEmits, onMounted, reactive } from "vue";
import { ApiServiceMonitor } from "@/api/objHandle/manager";
import ellipsisText from "@/components/ellipsisText/index.vue";

interface Data {
  serviceData: {
    dataServiceName: string;
    serviceAddress: string;
    dataServiceType: number;
    status: number;
  }[];
  sourceData: {
    dataSourceName: string;
    dataSourceAddress: string;
    status: number;
  }[];
  tableLoading: boolean;
}
const data = reactive<Data>({
  serviceData: [],
  sourceData: [],
  tableLoading: false,
});
const drawerVisible = ref(true);
const props = defineProps({
  itemId: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(["close"]);

function handleClosed() {
  emit("close");
}

// 获取监控数据
function getList() {
  data.tableLoading = true;
  ApiServiceMonitor(props.itemId)
    .then((res: any) => {
      data.serviceData.push(res?.dataServiceMonitorVO);
      data.sourceData = res?.dataSourceMonitorVOList;
      data.tableLoading = false;
    })
    .catch((err: any) => {
      data.tableLoading = false;
    });
}
onMounted(() => {
  console.log(props.itemId, "itemData");
  getList();
});
</script>
<style lang="scss" scoped>
.title {
  color: var(--t-3535-f-5-c, #535f5c);
  text-align: left;
  font-family: "PingFang SC";
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
  margin-bottom: 12px;
}

.bottom {
  margin-bottom: 20px;
}

.status {
  display: flex;
  flex-basis: row;
  align-items: center;

  img {
    margin-right: 8px;
  }
}
</style>
