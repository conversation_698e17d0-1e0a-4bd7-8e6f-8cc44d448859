<template>
  <el-space fill>
    <el-space>
      <div class="color-86909c" @click="backToList">数据服务接入</div>
      <div>/</div>
      <div class="color-4e5969">数据库配置</div>
    </el-space>
    <el-space>
      <el-button
        text
        bg
        type="info"
        @click="backToList"
        style="width: 24px; height: 24px"
      >
        <el-icon><ArrowLeftBold /></el-icon>
      </el-button>
      <div>{{ serviceData?.dataServiceName || "-" }}</div>
    </el-space>
  </el-space>
  <el-divider />

  <SearchLayout>
    <template #left>
      <el-button type="primary" @click="handleAdd">新增配置</el-button>
    </template>
    <template #right>
      <el-form :inline="true" :model="searchForm" @submit.prevent>
        <el-form-item style="width: 240px; max-width: 240px"
          ><el-input
            v-model.trim="searchForm.databaseName"
            clearable
            placeholder="请输入"
            @clear="handleSearch"
          >
            <template #prefix>数据库名称:</template></el-input
          ></el-form-item
        >
        <el-form-item style="width: 60px; max-width: 60px"
          ><el-button
            type="primary"
            :loading="data.searchLoading"
            @click="handleSearch"
            >搜索</el-button
          ></el-form-item
        >
      </el-form>
    </template>
  </SearchLayout>
  <div class="page-search-body">
    <el-table
      :data="data.tableData"
      v-loading="data.tableLoading"
      border
      size="small"
    >
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column property="id" v-if="false" />
      <el-table-column
        property="databaseName"
        label="数据库名称"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <span v-copy="scope.row.databaseName">{{
            scope.row.databaseName || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        property="databaseIp"
        label="数据库IP"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <span v-copy="scope.row.databaseIp">{{
            scope.row.databaseIp || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        property="description"
        label="描述"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <span v-copy="scope.row.description">{{
            scope.row.description || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column
        property="updatedTime"
        label="操作时间"
        :show-overflow-tooltip="true"
      >
        <template #default="scope">
          <span v-copy="scope.row.updatedTime">{{
            scope.row.updatedTime || "-"
          }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template #default="scope">
          <el-button
            type="primary"
            text
            @click="handleEdit(scope.$index, scope.row)"
            >编辑</el-button
          >
          <el-popconfirm
            :width="200"
            confirm-button-text="确定"
            cancel-button-text="取消"
            title="是否确认删除？"
            @confirm="handleDeleteConfirm(scope.row.id)"
          >
            <template #reference>
              <el-button
                text
                type="primary"
                v-permission="AUTH_CODE.DATA_SERVICE_DELETE"
                >删除</el-button
              >
            </template>
          </el-popconfirm>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      v-model:page="data.page"
      v-model:page-size="data.size"
      :page-sizes="[10, 20, 30, 40]"
      small
      layout="total, sizes, prev, pager, next, jumper"
      :total="data.totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
  <addForm
    v-if="addDialogFormVisible && props?.serviceData?.dataServiceType === 2"
    @success="successThenQuery"
    @cancel="cancelAddForm"
    :isAdd="addFormData.isAdd"
    :item="itemData"
    :dataServiceId="props.serviceData?.id"
    @closeDialog="() => (addDialogFormVisible = false)"
  ></addForm>
  <addStandardForm
    v-if="addDialogFormVisible && props?.serviceData?.dataServiceType === 1"
    @success="successThenQuery"
    @cancel="cancelAddForm"
    :isAdd="addFormData.isAdd"
    :item="itemData"
    :dataServiceId="props.serviceData?.id"
  ></addStandardForm>
</template>
<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage } from "element-plus";
import addForm from "@/views/system/dataService/components/addSelfDevelop.vue";
import addStandardForm from "../components/addStandard";
import {
  getSelfDevelopListApi,
  deleteSelfDevelopApi,
} from "@/api/objHandle/manager";
import { AUTH_CODE } from "@/utils/authCode";
import store from "@/store";
import SearchLayout from "@/components/searchLayout/index.vue";

const emit = defineEmits(["backToList"]);
const props = defineProps({
  serviceData: Object,
});
const addDialogFormVisible = ref(false);

const addFormData = reactive<any>({
  dialogVisible: addDialogFormVisible,
  isAdd: true,
});

const itemData = ref<any | null>(null);

const searchForm = reactive({
  databaseName: "",
});
interface TableItem {
  id: string;
  databaseName: string;
  databaseIp: string;
  updatedTime: string;
  type: string;
  description: any;
}
interface Data {
  tableData: {
    id: string;
    databaseName: string;
    databaseIp: string;
    updatedTime: string;
    type: string;
  }[];
  page: number;
  size: number;
  totalCount: number;
  selectData: any;
  deleteLoading: boolean;
  tableLoading: boolean;
  searchLoading: boolean;
}
const data = reactive<Data>({
  tableData: [],
  page: 1,
  size: 10,
  totalCount: 1,
  selectData: null,
  // 列表loading
  tableLoading: false,
  // 搜索按钮状态控制
  searchLoading: false,
  // 删除状态控制
  deleteLoading: false,
});

// 子组件操作成功查询列表
function successThenQuery() {
  addDialogFormVisible.value = false;
  data.page = 1;
  getTableData();
}
// 子组件取消
function cancelAddForm() {
  addDialogFormVisible.value = false;
}

// 查询列表
function getTableData() {
  data.tableLoading = true;
  data.tableData = [];
  const params = {
    databaseName: searchForm.databaseName,
    dataServiceId: props.serviceData.id,
    page: data.page - 1,
    size: data.size,
  };
  data.tableLoading = false;
  data.searchLoading = false;
  getSelfDevelopListApi(params)
    .then((response: any) => {
      const result = response;
      data.tableData = result?.content || [];
      // data.page = result?.pageNumber || 1;
      data.totalCount = result?.totalCount || 0;
    })
    .finally(() => {
      data.tableLoading = false;
      data.searchLoading = false;
    });
}

// 查询按钮
function handleSearch() {
  data.searchLoading = true;
  data.page = 1;
  getTableData();
}

// 修改页码
function handleSizeChange(num: number) {
  data.size = num;
  data.page = 1;
  getTableData();
}

function handleCurrentChange(num: number) {
  data.page = num;
  getTableData();
}

// 新增按钮
function handleAdd() {
  addDialogFormVisible.value = true;
  addFormData.isAdd = true;
  itemData.value = null;
}
// 编辑按钮
function handleEdit(index: any, item: any) {
  addDialogFormVisible.value = true;
  itemData.value = { ...item };
  addFormData.isAdd = false;
}

// 删除按钮
function handleDeleteConfirm(id: any) {
  data.deleteLoading = true;
  deleteSelfDevelopApi({ id })
    .then(() => {
      ElMessage.success("删除成功");
      data.page = 1;
      getTableData();
    })
    .finally(() => {
      data.deleteLoading = false;
    });
}

onMounted(() => {
  getTableData();
});
function backToList() {
  emit("backToList");
}
</script>
<style lang="scss" scoped>
.el-divider {
  margin: 16px 0;
}
.popover-delete-tips {
  margin-bottom: 20px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .popover-delete-tips-text {
    margin-left: 10px;
    font-size: 14px;
  }
}
.popover-delete-wrap {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}
.table-service-type {
  color: #1664ff;
  cursor: pointer;
}
.color-86909c {
  color: #86909c;
}
.color-4e5969 {
  color: #4e5969;
}
.display-block {
  display: block;
}
</style>
