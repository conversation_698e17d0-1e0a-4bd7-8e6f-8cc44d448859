<template>
  <el-drawer
    v-model="drawerVisible"
    @closed="handleClosed"
    title="数据通道详情"
    size="400px"
  >
    <div class="yc-description">
      <div class="yc-description-title">
        <div class="yc-description-title-tip"></div>
        <div class="yc-description-title-text">基本信息</div>
      </div>
      <el-descriptions :column="1" direction="horizontal">
        <el-descriptions-item label="数据通道">{{
          props.itemData.dataSourceName || "-"
        }}</el-descriptions-item>
      </el-descriptions>
    </div>
    <el-divider />

    <div class="yc-description">
      <div class="yc-description-title">
        <div class="yc-description-title-tip"></div>
        <div class="yc-description-title-text">列表</div>
      </div>
      <el-table :data="data.tableData" border size="small">
        <el-table-column label="序号" type="index" />
        <el-table-column label="英文名" property="field" />
      </el-table>
    </div>
  </el-drawer>
</template>

<script lang="ts" setup>
import { defineProps, ref, defineEmits, onMounted, reactive } from "vue";
import { getFieldListApi } from "@/api/objHandle/manager";

interface Data {
  tableData: {
    field: string;
  }[];
}
const data = reactive<Data>({
  tableData: [],
});
const drawerVisible = ref(true);
const props = defineProps({
  itemData: {
    type: Object,
    default: () => null,
  },
});

const emit = defineEmits(["close"]);

function handleClosed() {
  emit("close");
}

// 获取数据源英文名并去重
function getFieldList() {
  getFieldListApi({
    dataServiceId: props.itemData.dataServiceId,
    dataSourceId: props.itemData.dataSourceId,
  }).then((res: any) => {
    res.forEach((element: any) => {
      return data.tableData.push({
        field: element,
      });
    });
  });
}
onMounted(() => {
  getFieldList();
});
</script>
<style lang="scss" scoped>
:deep(.el-descriptions__label) {
  width: 72px;
  min-width: 72px;
  display: inline-block;
}
.relate-handle-matrix-graph {
  height: 600px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
:deep(.handle-item-drawer-table) {
  margin-top: 16px;
}
.handle-item-tooltip {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  .el-icon {
    margin-left: 4px;
  }
}

:deep(th.el-table__cell.is-leaf) {
  border-right: none;
}
:deep(.table-column-style) {
  border-right: 1px solid #dfe4e3 !important;
}
</style>
