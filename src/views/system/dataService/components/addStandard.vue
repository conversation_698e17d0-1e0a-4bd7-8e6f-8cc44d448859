<template>
  <el-dialog
    append-to-body
    align-center
    v-model="dialogVisible"
    :title="title"
    width="700px"
    @close="$emit('cancel')"
    destroy-on-close
  >
    <el-alert
      title="数据服务关联对象标识注册等多个场景，请仔细核对信息是否填写正确，以防影响后续功能的使用。"
      type="info"
      show-icon
    />
    <el-form
      label-position="left"
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="90px"
    >
      <el-form-item label="数据源" prop="dataSource">
        <el-select
          v-model="formData.dataSource"
          placeholder="请输入"
          clearable
          value-key="id"
          style="width: 100%"
          @change="changeDataSource"
        >
          <el-option
            v-for="item in dataService"
            :key="item.id"
            :value="item"
            :label="item.dataSourceName"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="数据库名称" prop="databaseName">
        <el-input
          v-model.trim="formData.databaseName"
          placeholder="请输入"
          clearable
          disabled
        />
      </el-form-item>
      <el-form-item label="数据库IP" prop="databaseIp">
        <el-input
          v-model.trim="formData.databaseIp"
          placeholder="请输入"
          clearable
          disabled
        />
      </el-form-item>
      <el-form-item class="form-item-pl10" label="描述">
        <el-input
          v-model.trim="formData.description"
          placeholder="请输入"
          clearable
          maxLength="200"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="cancel">取消</el-button>
        <el-button
          type="primary"
          @click="validForm(formRef)"
          v-loading="btnLoading"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref, computed, PropType } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import {
  addSelfDevelopApi,
  editSelfDevelopApi,
  ApiServiceBasicInfoList,
} from "@/api/objHandle/manager";

const props = defineProps({
  isAdd: {
    type: Boolean,
    default: true,
  },
  item: Object,
  dataServiceId: {
    type: String,
    default: "",
  },
});
const emit = defineEmits(["success", "cancel"]);

const dialogVisible = ref(true);

const btnLoading = ref(false);
const title = (props.isAdd ? "新增" : "编辑") + "配置";

const formRef = ref<FormInstance>();
const formData = ref({
  id: null,
  dataSourceId: null,
  databaseName: "",
  databaseIp: "",
  description: "",
  dataSource: null,
});

const dataService = ref([]);

const serviceAddressValidate = (rule: any, value: any, callback: any) => {
  if (!value) {
    return callback("请填写数据库IP");
  }
  const reg =
    /(([a-zA-Z0-9\._-]+\.[a-zA-Z]{2,6})|([0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}))(:[0-9]{1,4})*(\/[a-zA-Z0-9\&%_\.\/-~-]*)?/;
  const domainReg =
    /((?!-)[A-Za-z0-9-]{1,63}(?<!-)\.)+[A-Za-z]{2,6}(\/.+)?(:\d+)?$/; // 域名校验规则：字符串:端口号

  if (!reg.test(value) && !domainReg.test(value)) {
    return callback("数据库IP格式错误，请重新填写");
  }
  callback();
};
const databaseNameValidate = (rule: any, value: any, callback: any) => {
  const reg = /^[A-Za-z0-9-_]+$/;
  if (!reg.test(value)) {
    return callback("数据库名称格式错误，请重新填写");
  }
  callback();
};
const rules = reactive({
  dataSource: [{ required: true, message: "请选择数据源" }],
  databaseName: [
    { required: true, message: "请输入数据库名称" },
    { validator: databaseNameValidate },
    { max: 50, message: "最大长度不超过50字符" },
  ],
  databaseIp: [
    { required: true, message: "请输入数据库IP" },
    { validator: serviceAddressValidate },
  ],
});

const validForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid) => {
    if (valid) {
      addOrEditService();
    }
  });
};

const changeDataSource = (item: any) => {
  formData.value.dataSourceId = item.id;
  formData.value.databaseName = item.databaseName;
  formData.value.databaseIp = item.databaseIp;
};

const addOrEditService = () => {
  const api = props.isAdd ? addSelfDevelopApi : editSelfDevelopApi;
  btnLoading.value = true;
  api({
    id: formData.value.id,
    dataSourceId: formData.value.dataSourceId,
    databaseName: formData.value.databaseName,
    databaseIp: formData.value.databaseIp,
    description: formData.value.description,
    dataServiceId: props.dataServiceId,
  })
    .then((res) => {
      emit("success");
      ElMessage({
        message: `${props.isAdd ? "新增" : "编辑"}配置成功!`,
        type: "success",
      });
    })
    .finally(() => {
      btnLoading.value = false;
    });
};

const cancel = () => {
  emit("cancel");
};

const getDataService = () => {
  ApiServiceBasicInfoList(props?.dataServiceId).then((res) => {
    dataService.value = res;
  });
};

onMounted(() => {
  getDataService();
  if (!props.isAdd) {
    formData.value = { ...props.item };
    formData.value.dataSource = {
      id: props.item.dataSourceId,
      databaseName: props.item.databaseName,
    };
  }
});
</script>

<style lang="scss" scoped>
.el-alert {
  background: rgb(232, 244, 255);
  width: 700px;
  margin-top: -30px;
  margin-left: -20px;
  margin-bottom: 20px;
  color: rgb(29, 33, 41);
  :deep(.el-icon) {
    color: rgb(22, 100, 255);
    &.el-alert__close-btn {
      color: #909399;
    }
  }
}
</style>
