import axios from "axios";
import { ElMessage } from "element-plus";
import { FileSaverOptions, saveAs } from "file-saver";
import { getToken } from "@/utils/auth";
import errorCode from "@/utils/errorCode";
import { blobValidate } from "@/utils/ruoyi";

const baseURL = import.meta.env.VITE_APP_BASE_API;

export default {
  zip(url: any, name: any) {
    const tempUrl = baseURL + url;
    axios({
      method: "get",
      url: tempUrl,
      responseType: "blob",
      headers: { Authorization: "Bearer " + getToken() },
    }).then(async (res) => {
      const isLogin = await blobValidate(res.data);
      if (isLogin) {
        const blob = new Blob([res.data], { type: "application/zip" });
        this.saveAs(blob, name);
      } else {
        this.printErrMsg(res.data);
      }
    });
  },
  saveAs(
    text: string | Blob,
    name: string | undefined,
    opts?: FileSaverOptions | undefined
  ) {
    saveAs(text, name, opts);
  },
  async printErrMsg(data: { text: () => any }) {
    const resText = await data.text();
    const rspObj = JSON.parse(resText);
    const errMsg =
      errorCode[rspObj.code as keyof typeof errorCode] ||
      rspObj.msg ||
      errorCode.default;
    ElMessage.error(errMsg);
  },
};
