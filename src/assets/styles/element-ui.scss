// cover some element-ui styles

.el-pagination {
  margin-top: 12px;
  justify-content: right;
}
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// message-box 通用样式调整
.el-message-box {
  padding-bottom: 0 !important;
  border: none !important;

  .el-message-box__header {
    height: 0.62rem;
    line-height: 0.62rem;
    padding: 0 0 0 0.3rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    margin-right: 0;
    .el-message-box__title {
      line-height: 0.6rem;
      span {
        line-height: inherit;
        color: #262626;
        font-size: 0.19rem;
        font-weight: 600;
      }
    }
  }
  .el-message-box__content {
    padding: 0.2rem;
    .el-message-box__message {
      font-size: 0.18rem;
    }
  }
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse
  > div
  > .el-submenu
  > .el-submenu__title
  .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link {
  color: var(--el-color-primary) !important;
}

.el-message {
  font-size: 0.2rem;
  .el-icon svg {
    height: 0.2rem;
    width: 0.2rem;
  }
}

// 修改tabs margin-bottom
.el-tabs .el-tabs__header {
  margin-bottom: 0.15rem;
}

// 修改树形图样式
.el-tree {
  .is-current {
    background-color: rgba(0, 111, 209, 0.1) !important;
    --el-tree-node-hover-bg-color: rgba(0, 111, 209, 0.1) !important;
    position: relative;
    &::before {
      content: "";
      width: 2px;
      height: 100%;
      background-color: #00a57c;
      position: absolute;
      top: 0;
      left: 0;
    }
  }
}

// 修改左边菜单样式
.sidebar-container .el-scrollbar .el-scrollbar__view {
  // padding-top: 0.2rem;
  .el-menu {
    .el-menu-item,
    .el-sub-menu__title {
      // height: 0.5rem;
      // line-height: 0.5rem;
      // font-size: 0.16rem;
      color: #5e6164;
      &.is-active {
        position: relative;
        &::after {
          content: "";
          width: 3px;
          height: 100%;
          position: absolute;
          top: 0;
          right: 0;
        }
      }
    }
  }
}

.el-table {
  .el-table__body {
    tr {
      height: 41px;
    }
  }

  .el-button.is-text {
    font-size: 12px;
    line-height: 20px;
    padding: 0;
  }
  .el-button + .el-button {
    margin-left: 16px;
  }
}

// 表格 字体大小
.el-table {
  .cell {
    font-size: 12px;
    font-weight: normal;
  }
  // 表格头部颜色
  th.el-table__cell {
    background-color: #eef2f1 !important;
    height: 44px;
  }
  .el-table__cell {
    border-right: none;
  }
}

// el-dialog 通用样式调整
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  .el-dialog__header {
    height: 46px;
    line-height: 46px;
    text-align: center;
    border-bottom: 1px solid #e8f1ef;
    padding: 0;
    margin-right: 0;
    font-size: 14px;
    .el-dialog__headerbtn {
      top: 0;
    }
  }
  .el-dialog__footer {
    background-color: #fff;
    height: 64px;
    line-height: 64px;
    border-top: 1px solid #e8f1ef;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    padding: 0;
    padding-right: 20px;
  }
}

.el-button {
  &:hover,
  &:active {
    border-color: #dcdfe6 !important;
    color: #606266 !important;
    background-color: #eef2f1 !important;
  }
  &.el-button--primary {
    &:hover,
    &:active {
      color: #fff !important;
      border-color: #00d39f !important;
      background-color: #00d39f !important;
    }
    &.is-text,
    &.is-link {
      color: #1664ff !important;
      border-color: transparent !important;
      background-color: transparent !important;
      &:hover,
      &:active {
        color: #4086ff !important;
        border-color: transparent !important;
        background-color: transparent !important;
      }
      &.is-disabled {
        color: #94c2ff !important;
        border-color: transparent !important;
        background-color: transparent !important;
        &:hover,
        &:active {
          color: #94c2ff !important;
          border-color: transparent !important;
          background-color: transparent !important;
        }
      }
    }
    &.is-disabled {
      &:hover,
      &:active {
        color: #fff !important;
        border-color: #6de6c7 !important;
        background-color: #6de6c7 !important;
      }
    }
    &.is-plain {
      border-color: #00a57c !important;
      background-color: #fff !important;
      color: #00a57c !important;
      &:hover {
        border-color: #00d39f !important;
        background-color: #fff !important;
        color: #00d39f !important;
      }
      &:active {
        border-color: #007457 !important;
        background-color: #fff !important;
        color: #007457 !important;
      }
      &.is-disabled {
        color: #6de6c7 !important;
        border-color: #6de6c7 !important;
        background-color: #fff !important;
        &:hover,
        &:active {
          color: #6de6c7 !important;
          border-color: #6de6c7 !important;
          background-color: #fff !important;
        }
      }
    }
  }
}

// 输入框
.main-container,
.el-overlay {
  .el-button {
    border-radius: 2px;
  }
  // 日期选择器
  .el-date-editor {
    width: 100%;
    box-shadow: none;
    background: #eef2f1;
    border-radius: 2px;
    .el-range-input {
      font-size: 12px;
    }
  }
  .el-input {
    height: 32px;
    .el-input__inner {
      height: 32px;
      line-height: 32px;
    }
  }
  // 行内表单
  .el-form--inline {
    .el-form-item {
      margin-bottom: 16px;
      margin-right: 0;
      margin-left: 12px;
    }
  }
  .el-form-item {
    margin-bottom: 32px;
    // 表单嵌套表单
    .el-form-item {
      margin-bottom: 0;
    }

    &.is-error {
      .el-input__wrapper {
        border-color: #e63f3f;
      }
    }
    // 表单文字样式
    .el-form-item__label {
      padding-top: 6px;
      font-size: 12px;
      line-height: 20px;
      color: #272e2c;
    }
    .el-form-item__error {
      font-weight: 400;
      font-size: 13px;
      line-height: 22px;

      color: #e63f3f;
    }
  }
  // 下拉框
  .el-select {
    &:hover:not(.el-select--disabled) .el-input__wrapper {
      box-shadow: none;
      border: 1px solid #eef2f1;
    }
    .el-input__wrapper.is-focus {
      // el-select选中态
      box-shadow: none !important;
    }
    .el-select__tags .el-tag--info {
      background-color: #fff;
      color: #000;
    }
  }
  .el-textarea {
    // 表单边框样式
    .el-textarea__inner {
      box-shadow: none;
      background: #eef2f1;
      border: 1px solid #eef2f1;
      font-size: 12px;
      border-radius: 2px;
    }
  }
  .el-input {
    // 表单边框样式
    .el-input__wrapper {
      box-shadow: none;
      background: #eef2f1;
      border: 1px solid #eef2f1;
      font-size: 12px;
      border-radius: 2px;
      &.is-focus {
        background-color: #fff;
        border-color: #00a57c;
      }
    }
    .el-input__prefix {
      color: #535f5c;
    }
    .is-focus {
      .el-input__prefix {
        color: #000;
      }
    }
  }
  .el-drawer {
    .el-drawer__header {
      box-shadow: inset 0px -1px 0px #e5e8ef;
      height: 48px;
      padding: 13px 20px;
      margin-bottom: 0;
    }

    .el-drawer__title {
      font-weight: 500;
      font-size: 14px;
      line-height: 22px;
      color: #1d2129;
    }
    .el-drawer__body {
      padding: 20px;
    }
  }
  .el-descriptions {
    .el-descriptions__cell {
      padding-bottom: 16px !important;
    }
    .el-descriptions__label {
      font-size: 12px;
      line-height: 20px;
      color: #4e5969;
      margin-right: 12px;
    }

    .el-descriptions__content {
      font-size: 12px;
      line-height: 20px;
      color: #1d2129;
    }
  }
}

.width-full {
  width: 100%;
}

.el-message-box__btns {
  height: 64px;
}
.el-message .el-message__badge {
  display: none;
}
