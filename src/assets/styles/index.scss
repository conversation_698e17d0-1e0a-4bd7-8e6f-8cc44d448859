@import "./variables.module.scss";
@import "./mixin.scss";
@import "./transition.scss";
@import "./element-ui.scss";
@import "./sidebar.scss";

body {
  height: 100%;
  margin: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
  font-size: 14px;
}

div,
p,
span,
h1,
h2,
h3 {
  margin: 0;
  box-sizing: border-box;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
  min-width: 1440px;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

ul {
  list-style: 0;
  margin: 0;
  padding: 0;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  width: 100%;
  margin-top: 15px;
}

.text-center {
  text-align: center;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(
    90deg,
    rgba(32, 182, 249, 1) 0%,
    rgba(32, 182, 249, 1) 0%,
    rgba(33, 120, 241, 1) 100%,
    rgba(33, 120, 241, 1) 100%
  );

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

// 页面分割线
.separation-bar {
  margin: 0 -20px;
  height: 8px;
  background-color: #fafafb;
  border: 1px solid #e7e9f0;
  border-left: none;
  border-right: none;
}

// 标题
.section-title {
  display: flex;
  flex-direction: row;
  justify-content: start;
  align-items: center;
  margin-bottom: 16px;
  height: 22px;
  font-weight: 500;
  .section-title-line {
    height: 12px;
    width: 3px;
    background-color: #007457;
    margin-right: 8px;
  }

  .opt-sub-btns {
    display: flex;
    flex: 1;
    min-width: 0;
    flex-direction: row;
    justify-content: end;
    align-items: center;
  }
}

.info-content {
  padding-left: 20px;
  margin-bottom: 24px;
  .info-line {
    margin-bottom: 10px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-start;
    .info-item {
      width: 45%;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;

      .info-item-title {
        white-space: nowrap;
      }
      .info-item-content {
        flex: 1;
        min-width: 0;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        .info-item-content-tooltip {
          max-width: 50%;
        }
        .info-item-content-text {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      &:first-child {
        padding-right: 10px;
      }
    }
  }
}

.prefix-detail-dialog-item {
  margin-bottom: 16px;
  padding: 0 100px;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .prefix-detail-dialog-label {
    width: 120px;
    min-width: 120px;
    text-align: right;
    display: inline-block;
    color: #7b9790;
    margin-right: 10px;
  }
  .prefix-detail-dialog-body {
    flex: 1;
  }
}

.margin-bottom10 {
  margin-bottom: 10px;
}

// 输入框复制粘贴样式
.el-input__inner::selection {
  background-color: #a0c8fd;
}

.width328 {
  width: 328px !important;
}

.width200 {
  width: 200px !important;
}

.yc-description-title {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 16px;
  .yc-description-title-tip {
    width: 4px;
    height: 12px;
    background: #00a57c;
    margin-right: 8px;
  }
  .yc-description-title-text {
    font-weight: 500;
    font-size: 14px;
    line-height: 22px;
    color: #272e2c;
  }
}

.page-search {
  .page-search-header {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
  }
}

.handle-detail-drawer {
  width: 70% !important;
  .el-drawer__body {
    min-width: 1067px;
  }
}

.handle-item-drawer {
  width: 700px !important;
  .el-drawer__body {
    min-width: 700px;
  }
}

// 解决浏览器自动填充账号密码样式错误问题
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-transition-delay: 99999s;
  -webkit-transition: color 99999s ease-out, background-color 99999s ease-out,
    fontSize 99999s ease-out;
}

.node-info-basic-line {
  display: flex;
  justify-content: start;
  align-items: center;
  margin-bottom: 16px;
  background-color: #ffffff;
  height: 20px;
  font-size: 12px;
  .baseic-title {
    width: 68px;
    min-width: 68px;
    margin-right: 4px;
  }
  .baseic-content {
    width: 240px;
    min-width: 240px;
    margin-right: 96px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.table-required {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .table-required-icon {
    color: #e63f3f;
    padding-top: 4px;
    padding-right: 2px;
  }
}

.attr-dialog {
  .el-dialog__body {
    min-height: 400px;
  }
}

// 表单居左对齐，没有必填需要文字对齐
.form-item-pl10 {
  .el-form-item__label {
    padding-left: 8px;
  }
}

// 标识维护-扩展属性-关联标识下拉列表设置最大宽度
.handle-related-select {
  .el-select-dropdown__item {
    max-width: 800px;
    min-height: 34px;
    white-space: normal;
    height: auto;
  }
}
