<template>
  <div :class="classObj" class="app-wrapper">
    <div class="main-container" :class="{ 'is-hideSideBar': hideSideBar }">
      <div class="fixed-header">
        <navbar />
      </div>
      <sidebar v-if="!hideSideBar" class="sidebar-container" />
      <div class="app-main">
        <div class="app-main-body">
          <router-view></router-view>
        </div>

        <div class="footer">
          <span
            >2017-{{
              new Date().getFullYear()
            }}技术支持：中国信息通信研究院(CAICT)</span
          >
        </div>
      </div>
    </div>
  </div>
  <changePsw></changePsw>
</template>

<script setup lang="ts">
import { computed, watchEffect, watch, ref } from "vue";
import { useStore } from "vuex";
import { useWindowSize } from "@vueuse/core";
import router from "@/router";
import Sidebar from "./components/Sidebar/index.vue";
import { Navbar } from "./components";
import changePsw from "./components/password/changePsw.vue";

const store = useStore();
const sidebar = computed(() => store.state.app.sidebar);

const classObj = computed(() => ({
  hideSidebar: !sidebar.value.opened,
  openSidebar: sidebar.value.opened,
  withoutAnimation: sidebar.value.withoutAnimation,
}));

const { width } = useWindowSize();
const WIDTH = 992; // refer to Bootstrap's responsive design
const hideSideBar = ref(false);

watch(
  () => router.currentRoute.value,
  (val: any) => {
    // 数据通道工具不展示左侧菜单
    hideSideBar.value = val.path === "/tool";
  },
  {
    immediate: true,
    deep: true,
  }
);

watchEffect(() => {
  if (width.value - 1 < WIDTH) {
    store.dispatch("app/toggleDevice", "mobile");
    store.dispatch("app/closeSideBar", { withoutAnimation: true });
  } else {
    store.dispatch("app/toggleDevice", "desktop");
  }
});
</script>

<style lang="scss" scoped>
@import "@/assets/styles/mixin.scss";
@import "@/assets/styles/variables.module.scss";

.main-container {
  &.is-hideSideBar {
    margin-left: 0 !important;
  }
}

.app-main {
  position: relative;
  // background-color: #fff;
  margin: 78px 20px 0 20px;
  display: flex;
  flex-direction: column;
  flex: 1;

  .app-main-body {
    flex: 1;
    padding: 24px;
    min-height: 0;
    display: flex;
    flex-direction: column;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    margin-bottom: 10px;
    background-color: #fff;
  }
}

.footer {
  margin: 0 -24px;
  line-height: 36px;
  height: 36px;
  min-height: 36px;
  text-align: center;
  color: #657180;
  font-size: 12px;
  border-top: 1px solid #e7e9f0;
  background-color: #fff;
  a {
    margin-left: 0.15rem;
  }
}

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 1009;
  width: 100%;
  transition: width 0.28s;
  // padding-left: #{$base-sidebar-width};
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
  width: 100%;
}

.sidebarHide .fixed-header {
  width: 100%;
}

.mobile .fixed-header {
  width: 100%;
}
</style>
