<template>
  <footer>
    <el-row>
      <el-col :span="24">
        <span
          >2017-{{
            new Date().getFullYear()
          }}技术支持：中国信息通信研究院(CAICT)</span
        >
        <!-- <span><a href="http://beian.miit.gov.cn">京ICP备:12003601号</a></span> -->
      </el-col>
    </el-row>
  </footer>
</template>

<script lang="ts">
import { mapGetters } from "vuex";

export default {
  name: "MyFooter",
  data() {
    return {
      massage: [],
      icpInfo: [],
    };
  },
  computed: {
    ...mapGetters(["configInfo"]),
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
footer {
  position: absolute;
  display: block;
  width: 100%;
  bottom: 0;
  left: 0;
  height: 0.5rem;
  line-height: 0.5rem;
  font-weight: 400;
  text-align: center;
  z-index: 1000;
  background-color: transparent;
  font-size: 0.12rem;
  color: rgba(0, 0, 0, 0.45);
  span {
    padding: 0 0.1rem;
  }
}
</style>
