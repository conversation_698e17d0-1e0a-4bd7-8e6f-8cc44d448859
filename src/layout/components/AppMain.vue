<template>
  <section class="app-main">
    <router-view v-slot="{ Component, route }">
      <transition name="fade-transform" mode="out-in">
        <keep-alive>
          <component :is="Component" :key="route.path" />
        </keep-alive>
      </transition>
    </router-view>
  </section>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";
import { useStore } from "vuex";

const store = useStore();
const route = useRoute();
</script>

<style lang="scss" scoped>
.app-main {
  /* 50= navbar  50  */
  min-height: calc(100vh - 36px);
  width: 100%;
  margin-top: 20px;
  padding-left: 24px;
  padding-right: 24px;
  padding-top: 0.6rem;
  position: relative;
  overflow: hidden;
  background-color: #f5f7fb;
  display: flex;
  flex-direction: column;
}

.fixed-header + .app-main {
  padding-top: 0.6rem;
}
</style>

<style lang="scss" scoped>
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 17px;
  }
}
</style>
