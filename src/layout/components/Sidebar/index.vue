<template>
  <div
    class="has-logo"
    :style="{
      backgroundColor:
        sideTheme === 'theme-dark'
          ? variables.menuBackground
          : variables.menuLightBackground,
    }"
  >
    <el-scrollbar :class="sideTheme" wrap-class="scrollbar-wrapper">
      <el-menu
        class="layout-sidebar-menu"
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="
          sideTheme === 'theme-dark'
            ? variables.menuBackground
            : variables.menuLightBackground
        "
        :text-color="
          sideTheme === 'theme-dark'
            ? variables.menuColor
            : variables.menuLightColor
        "
        :unique-opened="true"
        :active-text-color="theme"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
    <div
      class="hamburger-container"
      style="padding: 0 15px"
      @click="toggleSideBar"
    >
      <svg
        :class="{ 'is-active': getters.sidebar.opened }"
        class="hamburger"
        viewBox="0 0 1024 1024"
        xmlns="http://www.w3.org/2000/svg"
        width="64"
        height="64"
      >
        <path
          d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 0 0 0-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3a8.9 8.9 0 0 0 14.4 7z"
        />
      </svg>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from "vue-router";
import { useStore } from "vuex";
import { computed } from "vue";
import variables from "@/assets/styles/variables.module.scss";
import SidebarItem from "./SidebarItem.vue";

const route = useRoute();
const store = useStore();

const getters = computed(() => store.getters);
function toggleSideBar() {
  store.dispatch("app/toggleSideBar");
}

const sidebarRouters = computed(() => store.getters.sidebarRouters);
const sideTheme = computed(() => store.state.settings.sideTheme);
const theme = computed(() => store.state.settings.theme);
const isCollapse = computed(() => !store.state.app.sidebar.opened);

const activeMenu = computed(() => {
  const { meta, path } = route;
  // if set path, the sidebar will highlight the path you set
  if (meta.activeMenu) {
    return meta.activeMenu;
  }
  return path;
});
</script>
<style lang="scss" scoped>
.hamburger {
  display: inline-block;
  vertical-align: middle;
  width: 20px;
  height: 20px;
}

.hamburger.is-active {
  transform: rotate(180deg);
}

.hamburger-container {
  line-height: 46px;
  height: 50px;
  float: right;
  cursor: pointer;
  transition: background 0.3s;
  -webkit-tap-highlight-color: transparent;

  &:hover {
    background: rgba(0, 0, 0, 0.025);
  }
}
#app {
  .sidebar-container {
    .layout-sidebar-menu {
      :deep(.el-menu-item) {
        &:hover {
          background-color: rgba(#e8f1ef, 0.6) !important;
        }
        &.is-active {
          background-color: #e8f1ef !important;
          background: rgba(232, 241, 239, 0.6) !important;
          color: #007457;
          border-right: 2px solid #00a57c;
          font-weight: 500;
        }
      }
      :deep(.el-menu) {
        .el-sub-menu {
          .el-menu-item,
          .el-sub-menu__title {
            &:hover {
              background-color: rgba(#e8f1ef, 0.6) !important;
            }
          }
        }
        .el-menu-item {
          &:hover {
            background-color: rgba(#e8f1ef, 0.6) !important;
          }
          &.is-active {
            background-color: #e8f1ef !important;
          }
        }
      }
    }
  }
}
</style>
