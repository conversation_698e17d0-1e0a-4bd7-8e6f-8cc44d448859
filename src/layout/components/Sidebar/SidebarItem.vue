<template>
  <div>
    <template v-if="!item.children || !item.children.length">
      <app-link :to="resolvePath(item.path, item.query)">
        <el-menu-item
          :index="resolvePath(item.path)"
          class="first-level-menu"
          :class="{ 'submenu-title-noDropdown': !isNest }"
        >
          <el-icon v-if="item.icon">
            <component :is="item.icon"> </component>
          </el-icon>
          <template #title>
            <span class="menu-title" :title="hasTitle(item.title)">
              {{ item.title }}
            </span>
          </template>
        </el-menu-item>
      </app-link>
    </template>

    <el-sub-menu
      v-else
      ref="subMenu"
      :index="resolvePath(item.path)"
      popper-append-to-body
      class="second-level-menu"
    >
      <template v-if="item.title" #title>
        <el-icon class="testicon" v-if="item.icon"
          ><component :is="item.icon"></component
        ></el-icon>
        <span class="menu-title" :title="hasTitle(item.title)">{{
          item.title
        }}</span>
      </template>

      <sidebar-item
        v-for="child in item.children"
        :key="child.path"
        :is-nest="true"
        :item="child"
        :base-path="resolvePath(child.path)"
        class="nest-menu"
      />
    </el-sub-menu>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from "vue-router";
import { ref } from "vue";
import { isExternal } from "@/utils/validate";
import AppLink from "./Link.vue";
import { getNormalPath } from "@/utils/ruoyi";

const router = useRouter();

const props = defineProps({
  // route object
  item: {
    type: Object,
    required: true,
  },
  isNest: {
    type: Boolean,
    default: false,
  },
  basePath: {
    type: String,
    default: "",
  },
});

const onlyOneChild = ref({});

function hasOneShowingChild(children: any, parent: any) {
  if (!children) {
    children = [];
  }
  const showingChildren = children.filter((item: any) => {
    if (item.hidden) {
      return false;
    }
    // Temp set(will be used if only has one showing child)
    onlyOneChild.value = item;
    return true;
  });

  // When there is only one child router, the child router is displayed by default
  if (showingChildren.length === 1) {
    return true;
  }

  // Show parent if there are no child router to display
  if (showingChildren.length === 0) {
    onlyOneChild.value = { ...parent, path: "", noShowingChildren: true };
    return true;
  }

  return false;
}

function resolvePath(routePath: any, routeQuery?: any) {
  if (isExternal(routePath)) {
    return routePath;
  }
  if (isExternal(props.basePath)) {
    return props.basePath;
  }
  if (routeQuery) {
    const query = JSON.parse(routeQuery);
    return {
      path: getNormalPath(routePath),
      query,
    };
  }
  return getNormalPath(routePath);
}

function hasTitle(title: any) {
  if (title.length > 5) {
    return title;
  }
  return "";
}
</script>
<style lang="scss" scoped>
.first-level-menu {
  .menu-title {
    color: rgba(0, 0, 0, 0.7);
  }
}
</style>
