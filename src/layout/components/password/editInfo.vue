<template>
  <el-dialog
    append-to-body
    align-center
    v-model="props.editData.dialogFormVisible"
    title="修改资料"
    width="700px"
    @close="$emit('close-dialog')"
    destroy-on-close
  >
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="110px">
      <el-form-item label="所属应用" v-if="isAppRole">
        <el-input disabled v-model.trim="formData.appName" clearable />
      </el-form-item>
      <el-form-item label="登录名" prop="username">
        <el-input
          disabled
          v-model.trim="formData.username"
          placeholder="请输入登录名"
          clearable
        />
      </el-form-item>
      <el-form-item label="邮箱" prop="email">
        <el-input
          v-model.trim="formData.email"
          placeholder="请输入邮箱"
          clearable
        />
      </el-form-item>
      <el-form-item label="手机" prop="phone">
        <el-input
          v-model.trim="formData.phone"
          placeholder="请输入手机号"
          :readonly="modelReadonly"
          :onfocus="modelOnfocus"
          clearable
        />
      </el-form-item>
      <el-form-item label="当前密码" prop="password">
        <el-input
          type="password"
          placeholder="请输入当前密码"
          v-model.trim="formData.password"
          show-password
          clearable
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="props.editData.dialogFormVisible = false"
          >取消</el-button
        >
        <el-button
          type="primary"
          @click="validForm(formRef)"
          v-loading="btnLoading"
        >
          确定
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { reactive, ref, computed } from "vue";
import { ElMessage, FormInstance } from "element-plus";
import { useStore } from "vuex";
import { editInfo } from "@/api/login";
import { getPublicKey, encrypt } from "@/utils/forgeEncrypt";
import { ROLE_TYPE } from "@/utils/constant";

const store = useStore();
const userInfo = computed(() => store.getters.userInfo);
const globalConfig = computed(() => store.getters.globalConfig);
const props = defineProps({
  editData: {
    type: Object,
    default: () => ({}),
  },
});

const formRef = ref<FormInstance>();
const formData = ref({
  username: userInfo.value.username,
  email: userInfo.value.email,
  phone: userInfo.value.phone,
  appName: userInfo.value.appName,
  password: "",
});
const btnLoading = ref(false);
// 防止自动填充
const modelReadonly = ref(true);

// 移入事件，防止自动填充
function modelOnfocus() {
  modelReadonly.value = false;
}
const checkPassWord = (rule: any, value: string, callback: any) => {
  // ⾄8-16位，必须包含大写字母、小写字母 、数字、 特殊字符（四种里至少三种，8-16位）
  const reg =
    /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{8,20}$/;
  if (!reg.test(value)) {
    callback(new Error("8-20位，包含大小写字母、数字、特殊符号至少三种"));
  } else {
    callback();
  }
};

/**
 * 校验邮箱正则
 */
const checkEmail = (rule: any, value: any, callback: any) => {
  const reg = /^$|^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
  if (!reg.test(value)) {
    return callback(new Error("邮箱格式不正确"));
  }
  callback();
};

const rules = reactive({
  dataServiceName: [
    { required: true, message: "请输入登录名", trigger: "blur" },
  ],
  email: [
    { required: true, message: "请输入邮箱", trigger: "blur" },
    { max: 30, message: "邮箱最大长度不超过30", trigger: "blur" },
    { required: true, validator: checkEmail, trigger: "blur" },
  ],
  phone: [
    { required: true, message: "请输入手机号", trigger: "blur" },
    {
      required: true,
      trigger: "blur",
      validator: (rule: any, value: any, cb: any) => {
        if (!value) return cb();
        if (/^(\+\d+)?1[3456789]\d{9}$/.test(value)) {
          cb();
        } else {
          cb(new Error("手机号格式不正确"));
        }
      },
    },
  ],
  password: [
    { required: true, trigger: "blur", message: "请输入您的密码" },
    { required: true, validator: checkPassWord, trigger: "blur" },
  ],
});

// id=3,为应用账号
const isAppRole = computed(() =>
  userInfo.value.roleInfos.some((item: any) => item.roleType === ROLE_TYPE.APP)
);

// 提交按钮
const validForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  await formEl.validate((valid) => {
    if (valid) {
      editPassword();
    }
  });
};

// 修改资料
const editPassword = async () => {
  btnLoading.value = true;
  const publicKey = await getPublicKey();
  const formTemp = {
    username: formData.value.username,
    email: formData.value.email,
    phone: formData.value.phone,
    password: globalConfig.value?.login.passwordEncrypted
      ? encrypt(formData.value.password, publicKey)
      : formData.value.password,
  };
  editInfo(formTemp)
    .then((response: any) => {
      store.dispatch("GetInfo");
      ElMessage({
        message: `修改成功!`,
        type: "success",
      });
      props.editData.dialogFormVisible = false;
    })
    .finally(() => {
      btnLoading.value = false;
    });
};
</script>
<style lang="scss" scoped></style>
