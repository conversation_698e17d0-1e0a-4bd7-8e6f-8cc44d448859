<template>
  <!-- 修改密码组件 -->
  <EditPwd
    v-if="dialogPswVisible"
    :titleCode="titleCode"
    @close-bindHdl-dialog="dialogPswVisible = false"
    @update-table="fetchData"
  />
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import { useStore } from "vuex";
import EditPwd from "@/views/node/editPwd.vue";
import { NEED_UPDATE_PASSWORD } from "@/utils/constant";

const dialogPswVisible = ref(false);
const titleCode = ref(9);
const store = useStore();
const userInfo = computed(() => store.getters.userInfo);

const fetchData = () => {
  dialogPswVisible.value = false;
};

onMounted(() => {
  if (
    userInfo.value.updatePasswordType === NEED_UPDATE_PASSWORD.EXPIRE ||
    userInfo.value.updatePasswordType === NEED_UPDATE_PASSWORD.INIT
  ) {
    dialogPswVisible.value = true;
    titleCode.value = userInfo.value.updatePasswordType;
  }
});
</script>

<style lang="scss" scoped></style>
