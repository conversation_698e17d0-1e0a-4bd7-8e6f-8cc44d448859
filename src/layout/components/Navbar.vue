<template>
  <div class="navbar">
    <div class="navbar-logo">
      <img src="../../assets/logo/logo_middle.png" alt="" class="logo" />
    </div>

    <div class="topmenu-container">
      <el-menu
        mode="horizontal"
        :default-active="activeMenu"
        :ellipsis="false"
        @select="handleSelect"
      >
        <el-menu-item
          v-for="(item, index) in topMenus"
          :key="index"
          :index="item.path"
        >
          {{ item.title }}
        </el-menu-item>
        <el-menu-item :index="queryPath"> 标识查询 </el-menu-item>
        <el-menu-item :index="toolPath" v-if="isAppUser">
          数据通道工具
        </el-menu-item>
      </el-menu>
    </div>
    <div class="right-menu">
      <div class="avatar-container">
        <el-dropdown
          @command="handleCommand"
          class="right-menu-item hover-effect"
          trigger="click"
        >
          <div class="avatar-wrapper">
            <div>{{ getters.name }}</div>
            <el-icon><caret-bottom /></el-icon>
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <!-- <el-dropdown-item divided command="editInfo">
                <span>修改资料</span>
              </el-dropdown-item> -->
              <el-dropdown-item
                v-if="!isSocialUser"
                divided
                command="editPassword"
              >
                <span>修改密码</span>
              </el-dropdown-item>
              <el-dropdown-item divided command="logout">
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
  </div>
  <!-- 修改资料组件 -->
  <editInfo v-if="dialogFormVisible" :editData="editData" />
  <!-- 修改密码组件 -->
  <EditPwd
    v-if="dialogPswVisible"
    @close-bindHdl-dialog="dialogPswVisible = false"
    @update-table="fetchData"
  />
</template>

<script setup lang="ts">
import { ElMessageBox } from "element-plus";
import { computed, reactive, ref, toRefs } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useStore } from "vuex";
import editInfo from "./password/editInfo.vue";
import EditPwd from "@/views/node/editPwd.vue";
import { LOGIN_MODEL } from "@/utils/constant";

const dialogFormVisible = ref(false);
const editData = reactive<any>({
  dialogFormVisible,
});

const dialogPswVisible = ref(false);
const editPswData = reactive<any>({
  dialogPswVisible,
});

const currentIndex = ref(null);
// 隐藏侧边栏路由
const route = useRoute();
const router = useRouter();

const store = useStore();
const getters = computed(() => store.getters);
const { isAppUser, isEntUser, isSocialUser, globalConfig } = toRefs(
  store.getters
);
const topMenus = computed(() => {
  return store.getters.roles;
});

const queryPath = ref("/query");
const toolPath = ref("/tool");
function handleCommand(command: any) {
  switch (command) {
    case "logout":
      logout();
      break;
    case "editInfo":
      edit();
      break;
    case "editPassword":
      editPsw();
      break;
    default:
      break;
  }
}

// 修改资料
function edit() {
  dialogFormVisible.value = true;
}

// 修改密码
function editPsw() {
  dialogPswVisible.value = true;
}

const fetchData = () => {
  dialogPswVisible.value = false;
};

function logout() {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      store.dispatch("LogOut").then(() => {
        if (globalConfig.value?.loginModel === LOGIN_MODEL.SSO) {
          window.location.href = globalConfig.value?.industryPortalUrl;
          return;
        }
        location.href = "/";
        router.push({ path: "/login" });
      });
    })
    .catch();
}

// 默认激活的菜单
const activeMenu = computed(() => {
  const path = route.path;
  let activePath = path;
  if (path !== undefined && path.lastIndexOf("/") > 0) {
    const tmpPath = path.substring(1, path.length);
    activePath = "/" + tmpPath.substring(0, tmpPath.indexOf("/"));
    store.dispatch("app/toggleSideBarHide", {
      status: false,
      route: activePath,
    });
  }
  activeRoutes(activePath);
  return activePath;
});

function handleSelect(key: any, keyPath: any) {
  router.push(key);
  currentIndex.value = key;
}

function handleOut(key: any, keyPath: any) {
  console.log(key, keyPath);
}

function activeRoutes(key: any) {
  const routes = topMenus.value.filter((menu: any) => menu.path === key);
  if (routes.length && routes[0].children?.length > 0) {
    store.commit("SET_SIDEBAR_ROUTERS", routes[0].children);
  }
  return routes;
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables.module.scss";
.navbar {
  height: 58px;
  overflow: hidden;
  position: relative;
  background: #d8e7e3;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;

  .navbar-logo {
    width: 276px;
    min-width: 276px;
    padding-left: 20px;
  }

  .logo {
    // width: 164px;
    height: 40px;
  }

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    flex: 1;
    .el-menu--horizontal .el-menu-item:not(.is-disabled):focus {
      background-color: #d8e7e3;
    }
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    height: 100%;
    display: flex;

    &:focus {
      outline: none;
    }

    .avatar-container {
      background-color: #d8e7e3;
      .right-menu-item {
        line-height: 58px;
        cursor: pointer;
      }
      .avatar-wrapper {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        padding: 0 24px;
        &:hover {
          background-color: #dafff6 !important;
        }
        .el-icon {
          padding-left: 5px;
        }
      }
    }
  }
  .el-menu--horizontal {
    background: #d8e7e3;
    .el-menu-item.is-active {
      // background-color: #00a57c !important;
      // color: #fff !important;
      font-weight: bold;
      border-bottom-color: #00a57c !important;
      &:hover {
        border-bottom-color: #00a57c !important;
      }
    }
    .el-menu-item {
      &:hover {
        background-color: #dafff6;
        color: #303133;
        border-bottom-color: #dafff6 !important;
      }
    }
  }
}
</style>
