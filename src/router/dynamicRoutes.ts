import ParentView from "@/components/ParentView/index.vue";
import Layout from "@/layout/index.vue";
import { SYSTEM_NAME, LEVEL_TYPE } from "@/utils/constant";
import { AUTH_CODE } from "@/utils/authCode";

export const NAV_ROUTES_MAP = {
  [LEVEL_TYPE.PROVINCE]: {
    name: "handle",
    title: SYSTEM_NAME.SYSTEM_NAME_PROVINCE,
    path: "/handle",
    redirect: "/handle/node",
    component: Layout,
    children: [],
  },
  [LEVEL_TYPE.PROXY]: {
    name: "handle",
    title: SYSTEM_NAME.SYSTEM_NAME_PROVINCE_PROXY,
    path: "/handle",
    redirect: "/handle/node",
    component: Layout,
    children: [],
  },
};

export const ALL_ROUTES = [
  {
    name: "provinceNode",
    title: "节点信息",
    path: "/handle/node",
    redirect: "/handle/node/statistics",
    component: ParentView,
    icon: "TrendCharts",
    children: [
      {
        authCode: AUTH_CODE.NODE_STATISTICS,
        name: "provinceNodeStatistics",
        title: "节点统计",
        path: "/handle/node/statistics",
        component: () => import("@/views/node/statistics.vue"),
      },
      {
        authCode: AUTH_CODE.NODE_USER,
        name: "provinceNodeAccount",
        title: "账号信息",
        path: "/handle/node/account",
        component: () => import("@/views/node/account.vue"),
      },
      {
        authCode: AUTH_CODE.NODE_DETAIL,
        name: "provinceNodeInfo",
        title: "节点详情",
        path: "/handle/node/info",
        component: () => import("@/views/node/info.vue"),
      },
    ],
  },

  {
    name: "dataService",
    title: "数据服务管理",
    path: "/handle/dataService",
    redirect: "/handle/dataService/metaDataManage",
    component: ParentView,
    icon: "TrendCharts",
    children: [
      {
        authCode: AUTH_CODE.DATA_SERVICE_ACCESS,
        name: "dataService",
        title: "数据服务接入",
        path: "/handle/loginConfig/dataService",
        component: () => import("@/views/system/dataService/index.vue"),
      },
    ],
  },
  {
    authCode: AUTH_CODE.METADATA_MANAGEMENT,
    name: "metaDataManage",
    title: "元数据对象",
    path: "/handle/dataService/metaDataManage",
    icon: "Grid",
    component: () => import("@/views/dataService/metaDataManage/index.vue"),
  },
  {
    name: "proxyId",
    title: "标识管理",
    path: "/handle/manage",
    redirect: "/handle/manage/register",
    component: ParentView,
    icon: "Management",
    children: [
      {
        authCode: AUTH_CODE.HANDLE_REGISTER,
        name: "proxyIdRegister",
        title: "对象标识注册",
        path: "/handle/manage/register",
        component: () => import("@/views/handleManage/register/index.vue"),
      },
      {
        authCode: AUTH_CODE.HANDLE_WATCH,
        name: "proxyIdView",
        title: "标识查看",
        path: "/handle/manage/view",
        component: () => import("@/views/handleManage/register/index.vue"),
      },
      {
        authCode: AUTH_CODE.HANDLE_MAINTAIN,
        name: "proxyIdManager",
        title: "标识维护",
        path: "/handle/id/manager",
        component: () => import("@/views/handleManage/maintain/index.vue"),
      },
      {
        authCode: AUTH_CODE.HANDLE_AUTO_MAINTAIN,
        name: "proxyIdAutoMaintain",
        title: "自动维护",
        path: "/handle/autoMaintain",
        component: () => import("@/views/handleManage/autoMaintain/index.vue"),
      },
    ],
  },
  {
    name: "provincePrefix",
    title: "节点管理",
    path: "/handle/prefix",
    redirect: "/handle/prefix/manager",
    component: ParentView,
    icon: "HomeFilled",
    children: [
      {
        authCode: AUTH_CODE.PREFIX_APPLY,
        name: "proxyPrefixApply",
        title: "前缀申请",
        path: "/handle/prefix/apply",
        component: () => import("@/views/nodeManage/prefixApply/applyList.vue"),
      },
      {
        authCode: AUTH_CODE.PREFIX_MANAGER,
        name: "provincePrefixManager",
        title: "前缀管理",
        path: "/handle/prefix/manager",
        component: () => import("@/views/nodeManage/prefixManage/index.vue"),
      },
      // 企业系统-前缀管理
      {
        authCode: AUTH_CODE.ENT_PREFIX_MANAGER,
        name: "provincePrefixManager",
        title: "前缀管理",
        path: "/handle/prefix/manager",
        component: () => import("@/views/nodeManage/entPrefixManage/index.vue"),
      },
      {
        authCode: AUTH_CODE.PREFIX_AUDIT,
        name: "provincePrefixType",
        title: "前缀审核",
        path: "/handle/prefix/audit",
        component: () => import("@/views/nodeManage/prefixAudit.vue"),
      },
      {
        authCode: AUTH_CODE.PREFIX_PREPARE,
        name: "provincePrefixPreallot",
        title: "前缀预分配",
        path: "/handle/prefix/preallot",
        component: () => import("@/views/nodeManage/prefixPreallot.vue"),
      },
      {
        authCode: AUTH_CODE.HOSTING_AUDIT,
        name: "provinceHostingAudit",
        title: "托管审核",
        path: "/handle/hosting/audit",
        component: () =>
          import("@/views/nodeManage/hostingAudit/hostingAudit.vue"),
      },
      {
        authCode: AUTH_CODE.HOSTING_APPLY,
        name: "entHostingApply",
        title: "托管申请",
        path: "/handle/hosting/apply",
        component: () =>
          import("@/views/nodeManage/hostingApply/hostingApply.vue"),
      },
      {
        authCode: AUTH_CODE.PREFIX_CONFIRM,
        name: "entPrefixClaim",
        title: "前缀认领",
        path: "/handle/prefix/claim",
        component: () => import("@/views/nodeManage/prefixClaim/index.vue"),
      },
    ],
  },
  {
    name: "provinceLog",
    title: "日志管理",
    path: "/handle/log",
    redirect: "/handle/log/system",
    component: ParentView,
    icon: "Histogram",
    children: [
      {
        authCode: AUTH_CODE.SYSTEM_OPERATION_LOG,
        name: "provinceLogSystem",
        title: "系统操作日志",
        path: "/handle/log/system",
        component: () => import("@/views/logs/sysLogs/index.vue"),
      },
      {
        authCode: AUTH_CODE.DATA_OPERATION_LOG,
        name: "provinceLogData",
        title: "数据操作日志",
        path: "/handle/log/data",
        component: () => import("@/views/logs/dataLogs/index.vue"),
      },
    ],
  },
  {
    name: "provinceSystem",
    title: "系统管理",
    icon: "Platform",
    path: "/handle/system",
    redirect: "/handle/system/manager",
    component: ParentView,
    children: [
      {
        authCode: AUTH_CODE.OPERATION_MANAGER,
        name: "provinceSystemManager",
        title: "运维管理",
        path: "/handle/system/manager",
        component: () => import("@/views/system/operation/index.vue"),
      },
      {
        authCode: AUTH_CODE.USER_MANAGER,
        name: "provinceSystemAccount",
        title: "账号管理",
        path: "/handle/system/account",
        component: () => import("@/views/system/accountManage/index.vue"),
      },
      {
        authCode: AUTH_CODE.ROLE_MANAGER,
        name: "provinceSystemRole",
        title: "角色管理",
        path: "/handle/system/role",
        component: () => import("@/views/system/role/index.vue"),
      },
      {
        authCode: AUTH_CODE.AUTH_MANAGER,
        name: "provinceSystemAuth",
        title: "权限管理",
        path: "/handle/system/auth",
        component: () => import("@/views/system/auth/index.vue"),
      },
      {
        authCode: AUTH_CODE.TENANT_MANAGER,
        name: "provinceSystemSuper",
        title: "省级管理",
        path: "/handle/system/provinceManager",
        component: () => import("@/views/system/provinceManager/index.vue"),
      },
      {
        authCode: AUTH_CODE.REPORT_CONFIG,
        name: "provinceSystemReport",
        title: "上报配置",
        path: "/handle/system/reportConfig",
        component: () => import("@/views/system/reportConfig/index.vue"),
      },
      {
        authCode: AUTH_CODE.HOSTING_CONFIG,
        name: "provinceProxyManager",
        title: "托管配置",
        path: "/handle/manager",
        component: () => import("@/views/proxyManager.vue"),
      },
      {
        authCode: AUTH_CODE.CONFIG_MANAGER,
        name: "entProxyManager",
        title: "配置管理",
        path: "/handle/manager",
        component: () => import("@/views/proxyManager.vue"),
      },
      {
        authCode: AUTH_CODE.APP_MANAGER,
        name: "ApplicationManager",
        title: "应用管理",
        path: "/handle/applicationManager",
        component: () => import("@/views/applicationManage/index.vue"),
      },
      {
        authCode: AUTH_CODE.LOGIN_CONFIG,
        name: "loginConfig",
        title: "登录配置",
        path: "/handle/system/loginConfig",
        component: () => import("@/views/system/loginConfig/index.vue"),
      },
      // {
      //   authCode: AUTH_CODE.DATA_SERVICE,
      //   name: "dataService",
      //   title: "数据服务",
      //   path: "/handle/loginConfig/dataService",
      //   component: () => import("@/views/system/dataService/index.vue"),
      // },
    ],
  },
  {
    authCode: AUTH_CODE.AUTHORIZE_MANAGER,
    name: "GrantManage",
    title: "授权管理",
    icon: "Share",
    path: "/handle/dataAuthManage",
    redirect: "/handle/dataAuthManage/authGroupManage",
    component: ParentView,
    children: [
      {
        authCode: AUTH_CODE.AUTH_GROUP_MANAGER,
        name: "authGroupManage",
        title: "权限组管理",
        path: "/handle/dataAuthManage/authGroupManage",
        component: () =>
          import("@/views/dataAuthManage/authGroupManage/index.vue"),
      },
      {
        authCode: AUTH_CODE.IDENTITY_AUTH_MANAGER,
        name: "authGrantManage",
        title: "身份授权",
        path: "/handle/dataAuthManage/authGrantManage",
        component: () =>
          import("@/views/dataAuthManage/authGrantManage/index.vue"),
      },
      {
        authCode: AUTH_CODE.IDENTITY_MANAGER,
        name: "authIdentifyManage",
        title: "身份管理",
        path: "/handle/dataAuthManage/authIdentifyManage",
        component: () =>
          import("@/views/dataAuthManage/authIdentifyManage/index.vue"),
      },
    ],
  },
];
