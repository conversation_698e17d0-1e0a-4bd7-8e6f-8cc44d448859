export interface IConfig {
  login: {
    captchaEnable: boolean;
    passwordEncrypted: boolean;
  };
  objectHandle: {
    handleAdmin: string;
  };
  systemType: number;
}

export interface ILogin {
  token: string;
  userCenterId?: number;
  binding?: boolean;
}

export interface IBind {
  userCenterId: number | null;
  binding: boolean;
}

export interface IUserInfo {
  levelType: number;
  authTree: any;
  isHosting: boolean;
}
