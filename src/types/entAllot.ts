export interface ISearchParams {
  entPrefix: string;
  entName: string;
  auditState: string;
  startTime: string;
  endTime: string;
  page: number;
  size: number;
}

export interface TableDataItem {
  entPrefix: string;
  entName: string;
  auditState: string;
  updatedTime: string;
}

export interface ISearchData {
  pageSize: number;
  pageNumber: number;
  totalCount: number;
  totalPage: number;
  content: TableDataItem[];
}

export interface SeverTableDataItem {
  id: number;
  hostingServerId: string | null;
  srvName: string;
  ip: string;
  ipType: number;
  tcpPort: number | null;
  udpPort: number | null;
  httpPort: number | null;
}

export interface IServerSearchData {
  pageSize: number;
  pageNumber: number;
  totalCount: number;
  totalPage: number;
  content: SeverTableDataItem[];
}

export interface ApplyTableDataItem {
  entPrefix: string;
  hostingState: number;
  createdTime: string;
  updatedTime: string;
}

export interface IApplySearchData {
  pageSize: number;
  pageNumber: number;
  totalCount: number;
  totalPage: number;
  content: ApplyTableDataItem[];
}

export interface IAllotDetail {
  entPrefix: string;
  srvName: string;
  ipTypeReslove: number;
  tcpPortReslove: number | null;
  udpPortReslove: number | null;
  httpPortReslove: number | null;
  ipReslove: string;
  auditResultMsg: string;
  updatedTime: string;
  updatedBy: string;
}
