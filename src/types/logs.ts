export interface DataLogList {
  handleName: string;
  idisIp: string;
  opCode: number;
  opName: string;
  operatorTime: string;
  prefix: string;
  subPrefix: string;
  type: string;
  userIp: string;
}

export type ISearchData = DataLogList[];

export interface SysLogs {
  description: string;
  host: string;
  logId: number;
  method: string;
  operateTime: string;
  param: string;
  path: string;
  responseCode: string;
  userName: string | null;
}

export interface ISysSearchData {
  pageSize: number;
  pageNumber: number;
  totalCount: number;
  totalPage: number;
  content: SysLogs[];
}
