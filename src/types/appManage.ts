export interface TableData {
  appName: string;
  createdTime: string;
  deployAddress: string;
  handleCode: string;
  id: number;
  prefixName: string | null;
  sysVersion: string;
  updatedTime: string;
  appType: number;
  masterDataScope?: string;
}

export interface Form {
  appName: string;
  appType: number;
  mpDmmAppId?: number | null;
  masterDataScope?: string;
  deployAddress: string;
  entId: number | null;
  prefixId: number | null;
  sysVersion: string;
}

export interface IPrefixData {
  entId: number;
  entPrefix: string;
  id: number;
  state: number;
}

export interface IAppList {
  content: TableData[];
  pageNumber: number;
  pageSize: number;
  totalCount: number;
  totalPage: number;
}
