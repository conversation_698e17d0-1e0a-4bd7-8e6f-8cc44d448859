export interface RoleListItem {
  createdBy: string | number | null;
  createdTime: string;
  entId: number;
  id: number;
  roleCode: string;
  roleName: string;
  sort: number;
  undelete: number;
  updatedBy: string | number;
  updatedTime: string;
  creatorName?: string;
  reviserName?: string;
}

export interface IRoleList {
  content: RoleListItem[];
  pageNumber: number;
  pageSize: number;
  totalCount: number;
  totalPage: number;
}

export interface IRoleListItem {
  createdBy: number;
  createdTime: string;
  entId: number;
  id: number;
  roleCode: string;
  roleName: string;
  sort: number;
  undelete: number;
  updatedBy: number;
  updatedTime: string;
}
