export interface List {
  address: string | null;
  email: string | null;
  entName: string | null;
  handleUser: string | null;
  id: number;
  nickName: string | null;
  phone: string | null;
  remark: string | null;
  roles: string | null;
  username: string | null;
  provinceId?: string | null;
}

export interface FormData {
  entId: number | null;
  username: string;
  nickName: string;
  phone: string;
  email: string;
  address: string;
  remark: string;
  entName: string;
}

export interface EntInfo {
  id: number;
  orgAddrCity: string | null;
  orgAddrProvince: string | null;
  orgAddrProvinceName: string | null;
  orgCode: string;
  orgName: string;
  parentOrgName: string;
}

interface RoleList {
  id: number;
  roleName: string;
}

export interface IAccountList {
  content: List[];
  pageNumber: number;
  pageSize: number;
  totalCount: number;
  totalPage: number;
}

export interface IAdminDetail {
  address: string | null;
  email: string | null;
  entName: string | null;
  handleUser: string | null;
  id: number;
  levelType: number;
  nickName: string | null;
  phone: string;
  remark: string | null;
  roleInfos: RoleList[];
  username: string;
  provinceId: string | null;
}

export type IEntInfo = EntInfo[];

export interface IBindInfo {
  id: number | null;
  username: string;
  password: string;
  account: string;
  orgCode: string;
  orgName: string;
  phone: string;
  email: string;
}
