export interface ITableDataItem {
  prefix: string;
  nodeName: string;
  orgCode: string;
  orgName: string;
  nodeAddr: string;
  updatedTime: string;
}

export interface IProvinceList {
  content: ITableDataItem[];
  pageNumber: number;
  pageSize: number;
  totalCount: number;
  totalPage: number;
}
export interface IFormData {
  prefix: string;
  bizCode: string;
  nodeName: string;
  entName: string;
  nodeAddress: string;
}
export interface IOrgInfo {
  nodeName: string;
  entName: string;
  nodeAddress: string;
}
