export interface INodeDetail {
  entName: string;
  nodeAddress: string;
  nodeName: string;
  nodePrefix: string;
}

export interface IOptBodyDetail {
  id: number;
  manageOrgAddr: string;
  manageOrgCity: string;
  manageOrgCrtCode: string;
  manageOrgDesc: string;
  manageOrgIndustry: string;
  manageOrgName: string;
  manageOrgNature: string;
  manageOrgProvince: string;
  manageWebsite: string;
  manageContactAddr: string;
}

export interface IContact {
  contactName: string;
  dutyType: string;
  email: string;
  id: number;
  phone: string;
  position: string;
}

export interface ITech {
  codeType: string;
  id: number;
  networkBandWidth: string;
  opeLine: string;
  serviceIp: string;
  servicePort: number;
  techSupporter: string;
}

export interface IBuss {
  capitalSource: string;
  id: number;
  inputPeople: number;
  receivedAwards: string;
}
