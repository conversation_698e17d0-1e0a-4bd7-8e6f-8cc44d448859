export interface TableData {
  entPrefix: string;
  httpPort: number | null;
  httpPortReslove: number | null;
  id: number;
  ip: string;
  ipReslove: string;
  ipType: number;
  ipTypeReslove: number;
  srvName: string;
  tcpPort: number;
  tcpPortReslove: number;
  udpPort: number | null;
  udpPortReslove: number | null;
}

export interface ManagerForm {
  srvName: string;
  ip: string;
  ipType: number;
  tcpPort: number | null;
  udpPort: number | null;
  httpPort: number | null;
  ipTypeReslove: number;
  tcpPortReslove: number | null;
  udpPortReslove: number | null;
  httpPortReslove: number | null;
  ipReslove: string;
}

export interface IManagerList {
  content: TableData[];
  empty: boolean;
  first: boolean;
  last: boolean;
  number: number;
  numberOfElements: number;
  pageable: {
    offset: number;
    pageNumber: number;
    pageSize: number;
    paged: boolean;
    sort: {
      empty: boolean;
      sorted: boolean;
      unsorted: boolean;
    };
    unpaged: boolean;
  };
  size: number;
  sort: {
    empty: boolean;
    sorted: boolean;
    unsorted: boolean;
  };
  totalElements: number;
  totalPages: number;
}
