export interface Form {
  fieldIndex: string | number;
  field: string;
  fieldValue: string;
  description: string;
  fieldType: number | null;
  editFlag: boolean;
  remark: string;
  references: {
    propertyList: any;
    handlePropList: any;
    referenceHandle: string;
    referenceHandleProp: {
      field: string;
      fieldIndex: number | null;
      remark: string;
    };
    queryProp: {
      field: string;
      fieldIndex: number | null;
      remark: string;
    };
    paramProp: {
      field: string;
      fieldIndex: number | null;
      remark: string;
    };
    disabled: boolean;
    key: number;
  }[];
}
export interface PropertyList {
  fieldIndex: number;
  field: string;
  fieldType: number;
  remark: string;
}
export interface FieldTypeList {
  value: number;
  name: string;
}
