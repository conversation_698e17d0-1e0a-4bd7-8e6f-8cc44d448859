export interface IHandleRecordItems {
  field: string;
  fieldIndex: number;
  description: string;
  fieldType: number;
  fieldValue: string;
  type: string;
  updateBy: string;
}
export interface IHandleRecord {
  handle: string;
  maintainField: string;
  updateBy: string;
  updateTime: string;
  appName: string;
  maintainState: number;
  items: IHandleRecordItems[];
}
export interface IHandleRecordData {
  pageSize: number;
  pageNumber: number;
  totalCount: number;
  totalPage: number;
  content: IHandleRecord[];
}
export interface IHandleBatchRecord {
  id: number;
  fileName: string;
  fileAddress: string;
  uploadHandleNum: number;
  uploadFailNum: number;
  uploadStatus: number;
  createdTime: string;
  updatedBy: string;
}
