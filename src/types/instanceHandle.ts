export interface ISearchParams {
  instanceHandleName: string;
  instanceHandle: string;
  updateEndTime: string;
  updateStartTime: string;
  pageSize: number;
  currentPage: number;
}

export interface IInstanceHandle {
  creator: string;
  instanceHandle: string;
  instanceHandleName: string;
  id: number;
  createdTime: string;
  updatedTime: string;
  updater: string;
}

export interface ISearchData {
  pageSize: number;
  pageNumber: number;
  totalCount: number;
  totalPage: number;
  content: IInstanceHandle[];
}

export interface IUpdateInstanceHandleParams {
  id?: number | undefined;
  instanceHandleName: string;
  instanceHandle: string;
  items: {
    id?: number | undefined | string;
    itemName: string;
    itemValue: string;
  }[];
}

export interface IInstanceHandleDetail {
  id: number | undefined;
  instanceHandleName: string;
  instanceHandle: string;
  items: {
    id: number | undefined | string;
    itemName: string;
    itemValue: string;
  }[];
}
