export interface IHandleItem {
  adminRead: number;
  adminWrite: number;
  publicRead: number;
  publicWrite: number;
  dataServiceId: number;
  dataServiceName: string;
  dataSourceId: number;
  dataSourceName: string;
  description: string;
  entId: number;
  fieldTResult: string;
  fieldType: string;
  field: string;
  fieldIndex: number;
  itemHandle: string;
  optionalField: null;
  provinceId: number;
  references: any[];
}

export interface IHandleDetail {
  appName: string;
  appId: number;
  entId: number;
  entPrefix: string;
  handle: string;
  entityType: number;
  id: number;
  handleAdmin: null;
  name: string;
  provinceId: number;
  services: number[];
  uploadState: number;
  wildcard: string;
  items: IHandleItem[];
  extendItems: IHandleItem[];
}
// 关联通道拓展属性
export interface IRelateChannelExtendItems {
  field: string;
  description: string;
  fieldType: number;
  remark: string;
  fieldValue: string;
  tableName: string;
  columnName: string;
  dataChannelId: number;
  databaseName: string;
  databaseIp: string;
}
// 关联通道基础属性
export interface IRelateChannelItems {
  entiyObjectFieldCode: string;
  field: string;
  description: string;
  fieldType: number;
  remark: string;
  fieldValue: string;
  tableName: string;
  columnName: string;
  dataChannelId: number;
  databaseName: string;
  databaseIp: string;
}
// 关联通道属性
export interface IRelateChannel {
  name: string;
  handle: string;
  entityType: number;
  handleType: string;
  masterDataName: string;
  dataType: string; // ?
  entiyObjectName: string;
  entiyObjectType: string;
  items: IRelateChannelItems[];
  extendItems: IRelateChannelExtendItems[];
}

// 数据服务列表
export interface IDataServiceItem {
  id: number;
  dataServiceName: string;
  serviceAddress: string;
  serviceToken: string;
  type?: string;
}

export interface DataServiceList {
  disabled?: boolean;
  id: number;
  dataServiceName: string;
  serviceAddress: string;
  serviceToken: string;
}

// 中台
export interface IReferences {
  referenceHandle: string;
  referenceHandleProp: {
    fieldIndex: number;
    field: string;
  };
  queryProp: {
    fieldIndex: number;
    field: string;
  };
  paramProp: {
    fieldIndex: number;
    field: string;
    remark: string;
  };
}
// 中台
export interface IMidHandleItem {
  entiyObjectFieldCode: string;
  field: string;
  fieldType: number;
  fieldValue: string;
  description: string;
  dataServiceId: number;
  dataChannelId: string;
  remark: string;
  references: IReferences[];
}

// 中台
export interface IMidHandleExtItem {
  field: string;
  description: string;
  fieldType: number;
  fieldValue: string;
  remark: string;
  references: IReferences[];
}
// 中台标识详情
export interface IMidHandleDetail {
  id: number;
  appId: number;
  appName: string;
  name: string;
  handle: string;
  entityType: number;
  handleType: number;
  masterDataName: string;
  entityObjectId: string;
  entityObjectTypeId: string;
  items: IMidHandleItem[];
  extendItems: IMidHandleExtItem[];
}
