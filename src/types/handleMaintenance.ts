export interface TableDataItem {
  appId: number;
  orgName: string;
  name: string;
  appName: string;
  handle: string;
  maintainField: string;
  updatedTime: string;
  updatedBy: string;
  maintainState: string;
}

export interface ISearchData {
  pageSize: number;
  pageNumber: number;
  totalCount: number;
  totalPage: number;
  content: TableDataItem[];
}

export interface IReferenceItem {
  referenceHandle: string;
  referenceHandleProp: {
    fieldIndex: number;
    field: string;
    remark: string;
  };
  queryProp: {
    fieldIndex: number;
    field: string;
    remark: string;
  };
  paramProp: {
    fieldIndex: number;
    field: string;
    remark: string;
  };
}
export interface IAttrItem {
  field: string;
  description: string;
  fieldIndex: number;
  fieldType: number;
  fieldValue: string;
  editFlag: string;
  remark: string;
  itemSourceType: number;
  references: IReferenceItem[];
}
export interface IHandleSearchData {
  name: string;
  orgName: string;
  handle: string;
  entityType: string;
  entPrefix: string;
  appName: string;
  appId: number;
  appCode: string;
  version: number;
  basicsItems: IAttrItem[];
  extendItems: IAttrItem[];
}

export interface IUpdateData {
  name: string;
  orgName: string;
  entPrefix: string;
  handle: string;
  appCode: string;
  version: number;
  add: IAttrItem[];
  edit: IAttrItem[];
  del: IAttrItem[];
}
