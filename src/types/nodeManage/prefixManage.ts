export interface TableData {
  createdTime: string;
  entPrefix: string;
  id: number;
  orgAddrCity: string;
  orgAddrCityDesc: string;
  orgAddrDistrict: string;
  orgAddrDistrictDesc: string;
  orgAddrProvince: string;
  orgAddrProvinceDesc: string;
  orgAddress: string;
  orgCode: string;
  orgName: string;
  parentOrgName: string;
  state: number;
  updatedTime: string;
}

export interface Options {
  id: number;
  orgAddrCity: string;
  orgAddrProvince: string;
  orgAddrProvinceName: string | null;
  orgCode: string;
  orgName: string;
  parentOrgName: string;
}

export interface DetailForm {
  createdTime: string;
  entPrefix: string;
  id: 78;
  orgAddrCity: string;
  orgAddrCityDesc: string;
  orgAddrDistrict: string;
  orgAddrDistrictDesc: string;
  orgAddrProvince: string;
  orgAddrProvinceDesc: string;
  orgCode: string;
  orgName: string;
  parentOrgName: string;
  orgAddress: string;
  updatedTime: string;
}
