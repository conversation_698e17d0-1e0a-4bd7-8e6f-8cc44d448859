// 数据库表
export interface IDatabase {
  modelId: string;
  tableName: string;
}

// 数据库表-创建渠道
export interface IChannelDatabase {
  databaseId: string;
  databaseName: string;
  databaseIP: string;
}

// 表字段
export interface IColumn {
  column: string;
  columnAlias: string;
  isSelected: boolean;
}

// 标记TID
export interface ITidMark {
  modelId: string; // 表modelId
  tableName: string; // 表名
  column: string; // 字段
  columnAlias: string; // 字段别名
}

// 关联关系
export interface IAssociation {
  associationTable: string;
  associationTableField: string;
  associationTableFieldAlias: string;
  currentTable: string;
  currentTableField: string;
  currentTableFieldAlias: string;
}

export interface IServiceList {
  id: number;
  dataServiceName: string;
  serviceAddress: string;
  serviceToken: string;
  type: number;
}
