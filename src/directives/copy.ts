import { ElMessage } from "element-plus";
import type { Directive, DirectiveBinding } from "vue";

// export const copy = {
//   mounted(el: any, { value }: any) {
//     el.$value = value;

//     // el控件定义 onclick 事件
//     el.onclick = (e: any) => {
//       // if (!el.$value) {
//       //   // 值为空的时候，给出提示。可根据项目UI仔细设计
//       //   console.log('无复制内容')
//       //   return
//       // }
//       // 动态创建 textarea 标签
//       const textarea: any = document.createElement("textarea");
//       // 将该 textarea 设为 readonly 防止 iOS 下自动唤起键盘，同时将 textarea 移出可视区域
//       textarea.readOnly = "readonly";
//       textarea.style.position = "absolute";
//       textarea.style.left = "-9999px";
//       // 将要 copy 的值赋给 textarea 标签的 value 属性
//       textarea.value = e.target.innerHTML;
//       // 将 textarea 插入到 body 中
//       document.body.appendChild(textarea);
//       // 选中值并复制
//       textarea.select();
//       const result = document.execCommand("Copy");
//       if (result) {
//         ElMessage.success("复制成功");
//       }
//       document.body.removeChild(textarea);
//     };
//     // 绑定点击事件，就是所谓的一键 copy 啦
//     el.addEventListener("click", el.handler);
//   },
//   // 当传进来的值更新的时候触发
//   beforeUpdate(el: any, { value }: any) {
//     el.$value = value;
//   },
//   // 指令与元素解绑的时候，移除事件绑定
//   unmounted(el: any) {
//     el.removeEventListener("click", el.handler);
//   },
// };

interface ElType extends HTMLElement {
  copyData: string | number;
  handleClick: any;
}
export const copy: Directive = {
  mounted(el: ElType, binding: DirectiveBinding) {
    el.copyData = binding.value;
    el.addEventListener("click", handleClick);
  },
  updated(el: ElType, binding: DirectiveBinding) {
    el.copyData = binding.value;
  },
  beforeUnmount(el: ElType) {
    el.removeEventListener("click", el.handleClick);
  },
};

function handleClick(this: any) {
  const input = document.createElement("input");
  input.value = this.copyData.toLocaleString();
  document.body.appendChild(input);
  input.select();
  document.execCommand("Copy");
  document.body.removeChild(input);
  ElMessage.success("复制成功");
}
