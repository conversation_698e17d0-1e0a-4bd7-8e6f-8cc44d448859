// permission.ts

// 引入vue中定义的指令对应的类型定义
import { Directive } from "vue";
import store from "@/store";

export const permission: Directive = {
  mounted(el, binding) {
    // value 获取用户使用自定义指令绑定的内容
    // 获取用户所有的权限
    const AllPermission = store.getters.auths;
    const { value } = binding;
    // 判断用户使用自定义指令，是否使用正确了
    if (binding) {
      let hasPermission = false;
      // 判断传递进来的按钮权限，用户是否拥有
      if (value instanceof Array && value.length > 0) {
        const permissionFunc = value;
        // Array.some(), 数组中有一个结果是true返回true，剩下的元素不会再检测
        hasPermission = AllPermission.some((authCode: any) => {
          return permissionFunc.includes(authCode);
        });
      } else {
        hasPermission = AllPermission.includes(binding.value);
      }

      // 当用户没有这个按钮权限时，设置隐藏这个按钮
      if (!hasPermission) {
        el.style.display = "none";
      }
    } else {
      throw new Error("need roles! Like v-permission='admin'");
    }
  },
};

// 注意，我们这里写的自定义指令，传递内容是一个数组，也就说，按钮权限可能是由
// 多个因素决定的，如果你的业务场景只由一个因素决定，自定义指令也可以不传递一个数组，
// 只传递一个字符串就可以了
