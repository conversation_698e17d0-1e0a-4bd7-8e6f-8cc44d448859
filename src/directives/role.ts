// 引入vue中定义的指令对应的类型定义
import { Directive } from "vue";
import store from "@/store";

export const role: Directive = {
  mounted(el, binding) {
    // value 获取用户使用自定义指令绑定的内容
    const value = binding.value;
    // 判断用户使用自定义指令，是否使用正确了
    if (value) {
      // 当用户没有这个组件权限时，设置隐藏这个按钮
      if (
        !store.getters.userInfo.roleInfos.some(
          (item: any) => item.roleType === value
        )
      ) {
        el.style.display = "none";
      }
    } else {
      throw new Error('need role! Like v-role="ROLE_TYPE.APP"');
    }
  },
};
