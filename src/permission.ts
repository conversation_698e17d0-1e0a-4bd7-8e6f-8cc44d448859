import { ElMessage, tabPaneProps } from "element-plus";
import NProgress from "nprogress";
import router from "./router";
import store from "./store";
import "nprogress/nprogress.css";
import { getToken } from "@/utils/auth";
import { isRelogin } from "@/utils/request";
import { SYSTEM_TYPE } from "@/utils/constant";

NProgress.configure({ showSpinner: false });

const whiteList = ["/login", "/query", "/tool", "/industry-portal"];

let firstPath = ""; // 记录第一个有权限的路由

router.beforeEach((to, from, next) => {
  NProgress.start();
  if (!store.getters.globalConfig.fullFilled) {
    store.dispatch("GetGlobalConfig").finally(() => {
      next({ ...to, replace: true });
    });
    return;
  }
  if (getToken()) {
    if (to.path === "/login") {
      next({ path: "/" });
      NProgress.done();
    } else if (!store.getters.userInfo) {
      console.log("userInfo");
      isRelogin.show = true;
      // 判断当前用户是否已拉取完user_info信息
      store
        .dispatch("GetInfo")
        .then((response) => {
          isRelogin.show = false;
          firstPath = response;
          if (
            from.path === "/login" ||
            (from.path === "/" && to.path === "/")
          ) {
            next({ path: firstPath, replace: true }); // 从登录页面进来，默认跳到第一个路由
            return;
          }
          next({ ...to, replace: true }); // 跳到第一个路由
        })
        .catch((err) => {
          store.dispatch("LogOut").then(() => {
            ElMessage.error(err);
            next({ path: "/login" });
          });
        });
    } else if (to.path === "/401" && store.getters.roles.length) {
      next({ path: firstPath, replace: true }); // 在401页面点击刷新按钮，如果此时有权限，需要跳转到第一个路由
    } else {
      next();
    }
  } else {
    // 没有token
    if (whiteList.indexOf(to.path) !== -1) {
      // 在免登录白名单，直接进入
      next();
    } else {
      next(`/login?redirect=${to.fullPath}`); // 否则全部重定向到登录页
      NProgress.done();
    }
  }
});

router.afterEach(() => {
  NProgress.done();
});
