import forge from "node-forge";
import { ApiGetPublicKey } from "@/api/login";

// base64转换（一般公钥私钥生成都是经过base64转换处理）
const encode64 = (str: string) => forge.util.encode64(str);

/**
 * 用公钥进行加密，生成base64格式
 * @param {*} text 待加密文本 string
 * @param {*} publicKey 公钥 string
 * @returns
 */

export function encrypt(text: string, publicKey?: string) {
  if (!publicKey) return text;
  publicKey = publicKey.replaceAll("\r\n", "");
  // 生成公钥对象（公钥是Pem格式）
  const publicKeyObj = forge.pki.publicKeyFromPem(publicKey);

  // encrypt加密并转换为base64编码格式
  const encryptStr = publicKeyObj.encrypt(text, "RSA-OAEP", {
    md: forge.md.sha256.create(),
    mgf1: {
      md: forge.md.sha1.create(),
    },
  });
  const encryptStrBase64 = encode64(encryptStr);
  // console.log("加密后的base64是encryptStrBase64", encryptStrBase64);
  return encryptStrBase64;
}

// 获取公钥
export async function getPublicKey() {
  const data: any = await ApiGetPublicKey().catch();
  return data.publicKeyPem;
}

export async function encryptPwd(text: any) {
  const publicKey = await getPublicKey();
  return encrypt(text, publicKey);
}
