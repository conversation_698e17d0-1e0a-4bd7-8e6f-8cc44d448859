/* eslint-disable */

import {
  defineComponent as wa,
  ref as er,
  onMounted as io,
  onUnmounted as so,
  computed as ha,
  watch as pa,
  toRefs as uo,
  openBlock as $a,
  createElementBlock as ba,
  createElementVNode as nr,
  normalizeStyle as co,
  withDirectives as lo,
  vShow as fo,
  renderSlot as Fa,
  withModifiers as ar,
} from "vue";
var ka = (o, c, i) => {
    if (!c.has(o)) throw TypeError("Cannot " + i);
  },
  Ta = (o, c, i) => (
    ka(o, c, "read from private field"), i ? i.call(o) : c.get(o)
  ),
  ho = (o, c, i) => {
    if (c.has(o))
      throw TypeError("Cannot add the same private member more than once");
    c instanceof WeakSet ? c.add(o) : c.set(o, i);
  },
  po = (o, c, i, h) => (
    ka(o, c, "write to private field"), h ? h.call(o, i) : c.set(o, i), i
  );
const Sa = [
  "aztec",
  "code_128",
  "code_39",
  "code_93",
  "codabar",
  "data_matrix",
  "ean_13",
  "ean_8",
  "itf",
  "pdf417",
  "qr_code",
  "upc_a",
  "upc_e",
  "unknown",
];
function vo(o) {
  if (Wa(o))
    return {
      width: o.naturalWidth,
      height: o.naturalHeight,
    };
  if (ja(o))
    return {
      width: o.width.baseVal.value,
      height: o.height.baseVal.value,
    };
  if (Ia(o))
    return {
      width: o.videoWidth,
      height: o.videoHeight,
    };
  if (Ha(o))
    return {
      width: o.width,
      height: o.height,
    };
  if (Va(o))
    return {
      width: o.displayWidth,
      height: o.displayHeight,
    };
  if (Ua(o))
    return {
      width: o.width,
      height: o.height,
    };
  if (La(o))
    return {
      width: o.width,
      height: o.height,
    };
  throw new TypeError(
    "The provided value is not of type '(Blob or HTMLCanvasElement or HTMLImageElement or HTMLVideoElement or ImageBitmap or ImageData or OffscreenCanvas or SVGImageElement or VideoFrame)'."
  );
}
function Wa(o) {
  try {
    return o instanceof HTMLImageElement;
  } catch (c) {
    return !1;
  }
}
function ja(o) {
  try {
    return o instanceof SVGImageElement;
  } catch (c) {
    return !1;
  }
}
function Ia(o) {
  try {
    return o instanceof HTMLVideoElement;
  } catch (c) {
    return !1;
  }
}
function Ua(o) {
  try {
    return o instanceof HTMLCanvasElement;
  } catch (c) {
    return !1;
  }
}
function Ha(o) {
  try {
    return o instanceof ImageBitmap;
  } catch (c) {
    return !1;
  }
}
function La(o) {
  try {
    return o instanceof OffscreenCanvas;
  } catch (c) {
    return !1;
  }
}
function Va(o) {
  try {
    return o instanceof VideoFrame;
  } catch (c) {
    return !1;
  }
}
function Ba(o) {
  try {
    return o instanceof Blob;
  } catch (c) {
    return !1;
  }
}
function mo(o) {
  try {
    return o instanceof ImageData;
  } catch (c) {
    return !1;
  }
}
function yo(o, c) {
  try {
    const i = new OffscreenCanvas(o, c);
    if (i.getContext("2d") instanceof OffscreenCanvasRenderingContext2D)
      return i;
    throw void 0;
  } catch (i) {
    const h = document.createElement("canvas");
    return (h.width = o), (h.height = c), h;
  }
}
async function za(o) {
  if (Wa(o) && !(await bo(o)))
    throw new DOMException(
      "Failed to load or decode HTMLImageElement.",
      "InvalidStateError"
    );
  if (ja(o) && !(await Co(o)))
    throw new DOMException(
      "Failed to load or decode SVGImageElement.",
      "InvalidStateError"
    );
  if (Va(o) && _o(o))
    throw new DOMException("VideoFrame is closed.", "InvalidStateError");
  if (Ia(o) && (o.readyState === 0 || o.readyState === 1))
    throw new DOMException("Invalid element or state.", "InvalidStateError");
  if (Ha(o) && To(o))
    throw new DOMException(
      "The image source is detached.",
      "InvalidStateError"
    );
  const { width: c, height: i } = vo(o);
  if (c === 0 || i === 0) return null;
  const h = yo(c, i).getContext("2d");
  h.drawImage(o, 0, 0);
  try {
    return h.getImageData(0, 0, c, i);
  } catch (m) {
    throw new DOMException("Source would taint origin.", "SecurityError");
  }
}
async function go(o) {
  let c;
  try {
    if (createImageBitmap) c = await createImageBitmap(o);
    else if (Image) {
      c = new Image();
      let i = "";
      try {
        (i = URL.createObjectURL(o)), (c.src = i), await c.decode();
      } finally {
        URL.revokeObjectURL(i);
      }
    } else return o;
  } catch (i) {
    throw new DOMException(
      "Failed to load or decode Blob.",
      "InvalidStateError"
    );
  }
  return await za(c);
}
function wo(o) {
  const { width: c, height: i } = o;
  if (c === 0 || i === 0) return null;
  const h = o.getContext("2d");
  try {
    return h.getImageData(0, 0, c, i);
  } catch (m) {
    throw new DOMException("Source would taint origin.", "SecurityError");
  }
}
async function $o(o) {
  if (Ba(o)) return await go(o);
  if (mo(o)) {
    if (Po(o))
      throw new DOMException(
        "The image data has been detached.",
        "InvalidStateError"
      );
    return o;
  }
  return Ua(o) || La(o) ? wo(o) : await za(o);
}
async function bo(o) {
  try {
    return await o.decode(), !0;
  } catch (c) {
    return !1;
  }
}
async function Co(o) {
  var c;
  try {
    return await ((c = o.decode) == null ? void 0 : c.call(o)), !0;
  } catch (i) {
    return !1;
  }
}
function _o(o) {
  return o.format === null;
}
function Po(o) {
  return o.data.buffer.byteLength === 0;
}
function To(o) {
  return o.width === 0 && o.height === 0;
}
function Aa(o, c) {
  return o instanceof DOMException
    ? new DOMException(`${c}: ${o.message}`, o.name)
    : o instanceof Error
    ? new o.constructor(`${c}: ${o.message}`)
    : new Error(`${c}: ${o}`);
}
const Ea = [
  "Aztec",
  "Codabar",
  "Code128",
  "Code39",
  "Code93",
  "DataBar",
  "DataBarExpanded",
  "DataMatrix",
  "EAN-13",
  "EAN-8",
  "ITF",
  "Linear-Codes",
  "Matrix-Codes",
  "MaxiCode",
  "MicroQRCode",
  "None",
  "PDF417",
  "QRCode",
  "UPC-A",
  "UPC-E",
];
function So(o) {
  return o.join("|");
}
function Ao(o) {
  const c = Oa(o);
  let i = 0,
    h = Ea.length - 1;
  for (; i <= h; ) {
    const m = Math.floor((i + h) / 2),
      y = Ea[m],
      b = Oa(y);
    if (b === c) return y;
    b < c ? (i = m + 1) : (h = m - 1);
  }
  return "None";
}
function Oa(o) {
  return o.toLowerCase().replace(/_-\[\]/g, "");
}
function Eo(o, c) {
  return o.Binarizer[c];
}
function Oo(o, c) {
  return o.CharacterSet[c];
}
const Do = ["Text", "Binary", "Mixed", "GS1", "ISO15434", "UnknownECI"];
function Mo(o) {
  return Do[o.value];
}
function xo(o, c) {
  return o.EanAddOnSymbol[c];
}
function Ro(o, c) {
  return o.TextMode[c];
}
const ir = {
  formats: [],
  tryHarder: !0,
  tryRotate: !0,
  tryInvert: !0,
  tryDownscale: !0,
  binarizer: "LocalAverage",
  isPure: !1,
  downscaleFactor: 3,
  downscaleThreshold: 500,
  minLineCount: 2,
  maxNumberOfSymbols: 255,
  tryCode39ExtendedMode: !1,
  validateCode39CheckSum: !1,
  validateITFCheckSum: !1,
  returnCodabarStartEnd: !1,
  returnErrors: !1,
  eanAddOnSymbol: "Read",
  textMode: "Plain",
  characterSet: "Unknown",
};
function qa(o, c) {
  return {
    ...c,
    formats: So(c.formats),
    binarizer: Eo(o, c.binarizer),
    eanAddOnSymbol: xo(o, c.eanAddOnSymbol),
    textMode: Ro(o, c.textMode),
    characterSet: Oo(o, c.characterSet),
  };
}
function Ya(o) {
  return {
    ...o,
    format: Ao(o.format),
    eccLevel: o.eccLevel,
    contentType: Mo(o.contentType),
  };
}

const Fo = {
  locateFile: (o, c) => {
    const i = o.match(/_(.+?)\.wasm$/);
    // return i
    //   ? `https://fastly.jsdelivr.net/npm/zxing-wasm@1.0.0-rc.3/dist/${i[1]}/${o}`
    //   : c + o;
    return i ? "/zxing_reader.wasm" : c + o;
  },
};
let ma = /* @__PURE__ */ new WeakMap();
function Ca(o, c) {
  var i;
  const h = ma.get(o);
  if (
    h != null &&
    h.modulePromise &&
    (c === void 0 || Object.is(c, h.moduleOverrides))
  )
    return h.modulePromise;
  const m =
      (i = c != null ? c : h == null ? void 0 : h.moduleOverrides) != null
        ? i
        : Fo,
    y = o({ ...m });
  return (
    ma.set(o, {
      moduleOverrides: m,
      modulePromise: y,
    }),
    y
  );
}
function ko(o, c) {
  ma.set(o, {
    moduleOverrides: c,
  });
}
async function Wo(o, c, i = ir) {
  const h = {
      ...ir,
      ...i,
    },
    m = await Ca(o),
    { size: y } = c,
    b = new Uint8Array(await c.arrayBuffer()),
    C = m._malloc(y);
  m.HEAPU8.set(b, C);
  const P = m.readBarcodesFromImage(C, y, qa(m, h));
  m._free(C);
  const g = [];
  for (let E = 0; E < P.size(); ++E) g.push(Ya(P.get(E)));
  return g;
}
async function jo(o, c, i = ir) {
  const h = {
      ...ir,
      ...i,
    },
    m = await Ca(o),
    {
      data: y,
      width: b,
      height: C,
      data: { byteLength: P },
    } = c,
    g = m._malloc(P);
  m.HEAPU8.set(y, g);
  const E = m.readBarcodesFromPixmap(g, b, C, qa(m, h));
  m._free(g);
  const O = [];
  for (let S = 0; S < E.size(); ++S) O.push(Ya(E.get(S)));
  return O;
}
var _a = (() => {
  var o =
    typeof document < "u" && document.currentScript
      ? document.currentScript.src
      : void 0;
  return function (c = {}) {
    var i = c,
      h,
      m;
    i.ready = new Promise((n, e) => {
      (h = n), (m = e);
    });
    var y = Object.assign({}, i),
      b = "./this.program",
      C = typeof window == "object",
      P = typeof importScripts == "function";
    typeof process == "object" &&
      typeof process.versions == "object" &&
      process.versions.node;
    var g = "";
    function E(n) {
      return i.locateFile ? i.locateFile(n, g) : g + n;
    }
    var O;
    (C || P) &&
      (P
        ? (g = self.location.href)
        : typeof document < "u" &&
          document.currentScript &&
          (g = document.currentScript.src),
      o && (g = o),
      g.indexOf("blob:") !== 0
        ? (g = g.substr(0, g.replace(/[?#].*/, "").lastIndexOf("/") + 1))
        : (g = ""),
      P &&
        (O = (n) => {
          var e = new XMLHttpRequest();
          return (
            e.open("GET", n, !1),
            (e.responseType = "arraybuffer"),
            e.send(null),
            new Uint8Array(e.response)
          );
        })),
      i.print || console.log.bind(console);
    var S = i.printErr || console.error.bind(console);
    Object.assign(i, y),
      (y = null),
      i.arguments && i.arguments,
      i.thisProgram && (b = i.thisProgram),
      i.quit && i.quit;
    var G;
    i.wasmBinary && (G = i.wasmBinary),
      typeof WebAssembly != "object" && de("no native wasm support detected");
    var N,
      J = !1;
    function K(n, e) {
      n || de(e);
    }
    var z, R, Y, Z, V, x, Pe, Te;
    function Ie() {
      var n = N.buffer;
      (i.HEAP8 = z = new Int8Array(n)),
        (i.HEAP16 = Y = new Int16Array(n)),
        (i.HEAPU8 = R = new Uint8Array(n)),
        (i.HEAPU16 = Z = new Uint16Array(n)),
        (i.HEAP32 = V = new Int32Array(n)),
        (i.HEAPU32 = x = new Uint32Array(n)),
        (i.HEAPF32 = Pe = new Float32Array(n)),
        (i.HEAPF64 = Te = new Float64Array(n));
    }
    var Ue = [],
      He = [],
      ze = [];
    function mt() {
      if (i.preRun)
        for (
          typeof i.preRun == "function" && (i.preRun = [i.preRun]);
          i.preRun.length;

        )
          qe(i.preRun.shift());
      Ne(Ue);
    }
    function yt() {
      Ne(He);
    }
    function gt() {
      if (i.postRun)
        for (
          typeof i.postRun == "function" && (i.postRun = [i.postRun]);
          i.postRun.length;

        )
          wt(i.postRun.shift());
      Ne(ze);
    }
    function qe(n) {
      Ue.unshift(n);
    }
    function dt(n) {
      He.unshift(n);
    }
    function wt(n) {
      ze.unshift(n);
    }
    var ie = 0,
      oe = null;
    function ve(n) {
      ie++, i.monitorRunDependencies && i.monitorRunDependencies(ie);
    }
    function $t(n) {
      if (
        (ie--,
        i.monitorRunDependencies && i.monitorRunDependencies(ie),
        ie == 0 && oe)
      ) {
        var e = oe;
        (oe = null), e();
      }
    }
    function de(n) {
      i.onAbort && i.onAbort(n),
        (n = "Aborted(" + n + ")"),
        S(n),
        (J = !0),
        (n += ". Build with -sASSERTIONS for more info.");
      var e = new WebAssembly.RuntimeError(n);
      throw (m(e), e);
    }
    var bt = "data:application/octet-stream;base64,",
      Ye = (n) => n.startsWith(bt),
      ue;
    (ue = "zxing_reader.wasm"), Ye(ue) || (ue = E(ue));
    function Ge(n) {
      if (n == ue && G) return new Uint8Array(G);
      if (O) return O(n);
      throw "both async and sync fetching of the wasm failed";
    }
    function cr(n) {
      return !G && (C || P) && typeof fetch == "function"
        ? fetch(n, { credentials: "same-origin" })
            .then((e) => {
              if (!e.ok) throw "failed to load wasm binary file at '" + n + "'";
              return e.arrayBuffer();
            })
            .catch(() => Ge(n))
        : Promise.resolve().then(() => Ge(n));
    }
    function Ct(n, e, t) {
      return cr(n)
        .then((r) => WebAssembly.instantiate(r, e))
        .then((r) => r)
        .then(t, (r) => {
          S(`failed to asynchronously prepare wasm: ${r}`), de(r);
        });
    }
    function lr(n, e, t, r) {
      return !n &&
        typeof WebAssembly.instantiateStreaming == "function" &&
        !Ye(e) &&
        typeof fetch == "function"
        ? fetch(e, { credentials: "same-origin" }).then((a) => {
            var s = WebAssembly.instantiateStreaming(a, t);
            return s.then(r, function (u) {
              return (
                S(`wasm streaming compile failed: ${u}`),
                S("falling back to ArrayBuffer instantiation"),
                Ct(e, t, r)
              );
            });
          })
        : Ct(e, t, r);
    }
    function fr() {
      var n = { a: Yn };
      function e(r, a) {
        return (
          (k = r.exports), (N = k.fa), Ie(), (Ft = k.ja), dt(k.ga), $t(), k
        );
      }
      ve();
      function t(r) {
        e(r.instance);
      }
      if (i.instantiateWasm)
        try {
          return i.instantiateWasm(n, e);
        } catch (r) {
          S(`Module.instantiateWasm callback failed with error: ${r}`), m(r);
        }
      return lr(G, ue, n, t).catch(m), {};
    }
    var Ne = (n) => {
      for (; n.length > 0; ) n.shift()(i);
    };
    i.noExitRuntime;
    var Se = [],
      Ae = 0,
      dr = (n) => {
        var e = new Je(n);
        return (
          e.get_caught() || (e.set_caught(!0), Ae--),
          e.set_rethrown(!1),
          Se.push(e),
          Qt(e.excPtr),
          e.get_exception_ptr()
        );
      },
      ne = 0,
      hr = () => {
        F(0, 0);
        var n = Se.pop();
        Jt(n.excPtr), (ne = 0);
      };
    function Je(n) {
      (this.excPtr = n),
        (this.ptr = n - 24),
        (this.set_type = function (e) {
          x[(this.ptr + 4) >> 2] = e;
        }),
        (this.get_type = function () {
          return x[(this.ptr + 4) >> 2];
        }),
        (this.set_destructor = function (e) {
          x[(this.ptr + 8) >> 2] = e;
        }),
        (this.get_destructor = function () {
          return x[(this.ptr + 8) >> 2];
        }),
        (this.set_caught = function (e) {
          (e = e ? 1 : 0), (z[(this.ptr + 12) >> 0] = e);
        }),
        (this.get_caught = function () {
          return z[(this.ptr + 12) >> 0] != 0;
        }),
        (this.set_rethrown = function (e) {
          (e = e ? 1 : 0), (z[(this.ptr + 13) >> 0] = e);
        }),
        (this.get_rethrown = function () {
          return z[(this.ptr + 13) >> 0] != 0;
        }),
        (this.init = function (e, t) {
          this.set_adjusted_ptr(0), this.set_type(e), this.set_destructor(t);
        }),
        (this.set_adjusted_ptr = function (e) {
          x[(this.ptr + 16) >> 2] = e;
        }),
        (this.get_adjusted_ptr = function () {
          return x[(this.ptr + 16) >> 2];
        }),
        (this.get_exception_ptr = function () {
          var e = Xt(this.get_type());
          if (e) return x[this.excPtr >> 2];
          var t = this.get_adjusted_ptr();
          return t !== 0 ? t : this.excPtr;
        });
    }
    var pr = (n) => {
        throw (ne || (ne = n), ne);
      },
      Qe = (n) => {
        var e = ne;
        if (!e) return Ce(0), 0;
        var t = new Je(e);
        t.set_adjusted_ptr(e);
        var r = t.get_type();
        if (!r) return Ce(0), e;
        for (var a in n) {
          var s = n[a];
          if (s === 0 || s === r) break;
          var u = t.ptr + 16;
          if (Zt(s, r, u)) return Ce(s), e;
        }
        return Ce(r), e;
      },
      vr = () => Qe([]),
      mr = (n) => Qe([n]),
      yr = (n, e) => Qe([n, e]),
      gr = () => {
        var n = Se.pop();
        n || de("no exception to throw");
        var e = n.excPtr;
        throw (
          (n.get_rethrown() ||
            (Se.push(n), n.set_rethrown(!0), n.set_caught(!1), Ae++),
          (ne = e),
          ne)
        );
      },
      wr = (n, e, t) => {
        var r = new Je(n);
        throw (r.init(e, t), (ne = n), Ae++, ne);
      },
      $r = () => Ae,
      Ee = {},
      _t = (n) => {
        for (; n.length; ) {
          var e = n.pop(),
            t = n.pop();
          t(e);
        }
      };
    function Ze(n) {
      return this.fromWireType(V[n >> 2]);
    }
    var he = {},
      ce = {},
      Oe = {},
      Pt,
      De = (n) => {
        throw new Pt(n);
      },
      le = (n, e, t) => {
        n.forEach(function (l) {
          Oe[l] = e;
        });
        function r(l) {
          var f = t(l);
          f.length !== n.length && De("Mismatched type converter count");
          for (var d = 0; d < n.length; ++d) te(n[d], f[d]);
        }
        var a = new Array(e.length),
          s = [],
          u = 0;
        e.forEach((l, f) => {
          ce.hasOwnProperty(l)
            ? (a[f] = ce[l])
            : (s.push(l),
              he.hasOwnProperty(l) || (he[l] = []),
              he[l].push(() => {
                (a[f] = ce[l]), ++u, u === s.length && r(a);
              }));
        }),
          s.length === 0 && r(a);
      },
      br = (n) => {
        var e = Ee[n];
        delete Ee[n];
        var t = e.rawConstructor,
          r = e.rawDestructor,
          a = e.fields,
          s = a
            .map((u) => u.getterReturnType)
            .concat(a.map((u) => u.setterArgumentType));
        le([n], s, (u) => {
          var l = {};
          return (
            a.forEach((f, d) => {
              var v = f.fieldName,
                $ = u[d],
                _ = f.getter,
                A = f.getterContext,
                M = u[d + a.length],
                U = f.setter,
                H = f.setterContext;
              l[v] = {
                read: (L) => $.fromWireType(_(A, L)),
                write: (L, w) => {
                  var p = [];
                  U(H, L, M.toWireType(p, w)), _t(p);
                },
              };
            }),
            [
              {
                name: e.name,
                fromWireType: (f) => {
                  var d = {};
                  for (var v in l) d[v] = l[v].read(f);
                  return r(f), d;
                },
                toWireType: (f, d) => {
                  for (var v in l)
                    if (!(v in d)) throw new TypeError(`Missing field: "${v}"`);
                  var $ = t();
                  for (v in l) l[v].write($, d[v]);
                  return f !== null && f.push(r, $), $;
                },
                argPackAdvance: re,
                readValueFromPointer: Ze,
                destructorFunction: r,
              },
            ]
          );
        });
      },
      Cr = (n, e, t, r, a) => {},
      _r = () => {
        for (var n = new Array(256), e = 0; e < 256; ++e)
          n[e] = String.fromCharCode(e);
        Tt = n;
      },
      Tt,
      q = (n) => {
        for (var e = "", t = n; R[t]; ) e += Tt[R[t++]];
        return e;
      },
      pe,
      D = (n) => {
        throw new pe(n);
      };
    function Pr(n, e, t = {}) {
      var r = e.name;
      if (
        (n || D(`type "${r}" must have a positive integer typeid pointer`),
        ce.hasOwnProperty(n))
      ) {
        if (t.ignoreDuplicateRegistrations) return;
        D(`Cannot register type '${r}' twice`);
      }
      if (((ce[n] = e), delete Oe[n], he.hasOwnProperty(n))) {
        var a = he[n];
        delete he[n], a.forEach((s) => s());
      }
    }
    function te(n, e, t = {}) {
      if (!("argPackAdvance" in e))
        throw new TypeError(
          "registerType registeredInstance requires argPackAdvance"
        );
      return Pr(n, e, t);
    }
    var re = 8,
      Tr = (n, e, t, r) => {
        (e = q(e)),
          te(n, {
            name: e,
            fromWireType: function (a) {
              return !!a;
            },
            toWireType: function (a, s) {
              return s ? t : r;
            },
            argPackAdvance: re,
            readValueFromPointer: function (a) {
              return this.fromWireType(R[a]);
            },
            destructorFunction: null,
          });
      },
      Sr = (n) => ({
        count: n.count,
        deleteScheduled: n.deleteScheduled,
        preservePointerOnDelete: n.preservePointerOnDelete,
        ptr: n.ptr,
        ptrType: n.ptrType,
        smartPtr: n.smartPtr,
        smartPtrType: n.smartPtrType,
      }),
      Xe = (n) => {
        function e(t) {
          return t.$$.ptrType.registeredClass.name;
        }
        D(e(n) + " instance already deleted");
      },
      Ke = !1,
      St = (n) => {},
      Ar = (n) => {
        n.smartPtr
          ? n.smartPtrType.rawDestructor(n.smartPtr)
          : n.ptrType.registeredClass.rawDestructor(n.ptr);
      },
      At = (n) => {
        n.count.value -= 1;
        var e = n.count.value === 0;
        e && Ar(n);
      },
      Et = (n, e, t) => {
        if (e === t) return n;
        if (t.baseClass === void 0) return null;
        var r = Et(n, e, t.baseClass);
        return r === null ? null : t.downcast(r);
      },
      Ot = {},
      Er = () => Object.keys(we).length,
      Or = () => {
        var n = [];
        for (var e in we) we.hasOwnProperty(e) && n.push(we[e]);
        return n;
      },
      ye = [],
      et = () => {
        for (; ye.length; ) {
          var n = ye.pop();
          (n.$$.deleteScheduled = !1), n.delete();
        }
      },
      ge,
      Dr = (n) => {
        (ge = n), ye.length && ge && ge(et);
      },
      Mr = () => {
        (i.getInheritedInstanceCount = Er),
          (i.getLiveInheritedInstances = Or),
          (i.flushPendingDeletes = et),
          (i.setDelayFunction = Dr);
      },
      we = {},
      xr = (n, e) => {
        for (e === void 0 && D("ptr should not be undefined"); n.baseClass; )
          (e = n.upcast(e)), (n = n.baseClass);
        return e;
      },
      Rr = (n, e) => ((e = xr(n, e)), we[e]),
      Me = (n, e) => {
        (!e.ptrType || !e.ptr) &&
          De("makeClassHandle requires ptr and ptrType");
        var t = !!e.smartPtrType,
          r = !!e.smartPtr;
        return (
          t !== r && De("Both smartPtrType and smartPtr must be specified"),
          (e.count = { value: 1 }),
          $e(Object.create(n, { $$: { value: e } }))
        );
      };
    function Fr(n) {
      var e = this.getPointee(n);
      if (!e) return this.destructor(n), null;
      var t = Rr(this.registeredClass, e);
      if (t !== void 0) {
        if (t.$$.count.value === 0)
          return (t.$$.ptr = e), (t.$$.smartPtr = n), t.clone();
        var r = t.clone();
        return this.destructor(n), r;
      }
      function a() {
        return this.isSmartPointer
          ? Me(this.registeredClass.instancePrototype, {
              ptrType: this.pointeeType,
              ptr: e,
              smartPtrType: this,
              smartPtr: n,
            })
          : Me(this.registeredClass.instancePrototype, {
              ptrType: this,
              ptr: n,
            });
      }
      var s = this.registeredClass.getActualType(e),
        u = Ot[s];
      if (!u) return a.call(this);
      var l;
      this.isConst ? (l = u.constPointerType) : (l = u.pointerType);
      var f = Et(e, this.registeredClass, l.registeredClass);
      return f === null
        ? a.call(this)
        : this.isSmartPointer
        ? Me(l.registeredClass.instancePrototype, {
            ptrType: l,
            ptr: f,
            smartPtrType: this,
            smartPtr: n,
          })
        : Me(l.registeredClass.instancePrototype, { ptrType: l, ptr: f });
    }
    var $e = (n) =>
        typeof FinalizationRegistry > "u"
          ? (($e = (e) => e), n)
          : ((Ke = new FinalizationRegistry((e) => {
              At(e.$$);
            })),
            ($e = (e) => {
              var t = e.$$,
                r = !!t.smartPtr;
              if (r) {
                var a = { $$: t };
                Ke.register(e, a, e);
              }
              return e;
            }),
            (St = (e) => Ke.unregister(e)),
            $e(n)),
      kr = () => {
        Object.assign(xe.prototype, {
          isAliasOf(n) {
            if (!(this instanceof xe) || !(n instanceof xe)) return !1;
            var e = this.$$.ptrType.registeredClass,
              t = this.$$.ptr;
            n.$$ = n.$$;
            for (
              var r = n.$$.ptrType.registeredClass, a = n.$$.ptr;
              e.baseClass;

            )
              (t = e.upcast(t)), (e = e.baseClass);
            for (; r.baseClass; ) (a = r.upcast(a)), (r = r.baseClass);
            return e === r && t === a;
          },
          clone() {
            if ((this.$$.ptr || Xe(this), this.$$.preservePointerOnDelete))
              return (this.$$.count.value += 1), this;
            var n = $e(
              Object.create(Object.getPrototypeOf(this), {
                $$: { value: Sr(this.$$) },
              })
            );
            return (n.$$.count.value += 1), (n.$$.deleteScheduled = !1), n;
          },
          delete() {
            this.$$.ptr || Xe(this),
              this.$$.deleteScheduled &&
                !this.$$.preservePointerOnDelete &&
                D("Object already scheduled for deletion"),
              St(this),
              At(this.$$),
              this.$$.preservePointerOnDelete ||
                ((this.$$.smartPtr = void 0), (this.$$.ptr = void 0));
          },
          isDeleted() {
            return !this.$$.ptr;
          },
          deleteLater() {
            return (
              this.$$.ptr || Xe(this),
              this.$$.deleteScheduled &&
                !this.$$.preservePointerOnDelete &&
                D("Object already scheduled for deletion"),
              ye.push(this),
              ye.length === 1 && ge && ge(et),
              (this.$$.deleteScheduled = !0),
              this
            );
          },
        });
      };
    function xe() {}
    var Wr = 48,
      jr = 57,
      Dt = (n) => {
        if (n === void 0) return "_unknown";
        n = n.replace(/[^a-zA-Z0-9_]/g, "$");
        var e = n.charCodeAt(0);
        return e >= Wr && e <= jr ? `_${n}` : n;
      };
    function tt(n, e) {
      return (
        (n = Dt(n)),
        {
          [n]: function () {
            return e.apply(this, arguments);
          },
        }[n]
      );
    }
    var Mt = (n, e, t) => {
        if (n[e].overloadTable === void 0) {
          var r = n[e];
          (n[e] = function () {
            return (
              n[e].overloadTable.hasOwnProperty(arguments.length) ||
                D(
                  `Function '${t}' called with an invalid number of arguments (${arguments.length}) - expects one of (${n[e].overloadTable})!`
                ),
              n[e].overloadTable[arguments.length].apply(this, arguments)
            );
          }),
            (n[e].overloadTable = []),
            (n[e].overloadTable[r.argCount] = r);
        }
      },
      rt = (n, e, t) => {
        i.hasOwnProperty(n)
          ? ((t === void 0 ||
              (i[n].overloadTable !== void 0 &&
                i[n].overloadTable[t] !== void 0)) &&
              D(`Cannot register public name '${n}' twice`),
            Mt(i, n, n),
            i.hasOwnProperty(t) &&
              D(
                `Cannot register multiple overloads of a function with the same number of arguments (${t})!`
              ),
            (i[n].overloadTable[t] = e))
          : ((i[n] = e), t !== void 0 && (i[n].numArguments = t));
      };
    function Ir(n, e, t, r, a, s, u, l) {
      (this.name = n),
        (this.constructor = e),
        (this.instancePrototype = t),
        (this.rawDestructor = r),
        (this.baseClass = a),
        (this.getActualType = s),
        (this.upcast = u),
        (this.downcast = l),
        (this.pureVirtualFunctions = []);
    }
    var nt = (n, e, t) => {
      for (; e !== t; )
        e.upcast ||
          D(
            `Expected null or instance of ${t.name}, got an instance of ${e.name}`
          ),
          (n = e.upcast(n)),
          (e = e.baseClass);
      return n;
    };
    function Ur(n, e) {
      if (e === null)
        return this.isReference && D(`null is not a valid ${this.name}`), 0;
      e.$$ || D(`Cannot pass "${st(e)}" as a ${this.name}`),
        e.$$.ptr ||
          D(`Cannot pass deleted object as a pointer of type ${this.name}`);
      var t = e.$$.ptrType.registeredClass,
        r = nt(e.$$.ptr, t, this.registeredClass);
      return r;
    }
    function Hr(n, e) {
      var t;
      if (e === null)
        return (
          this.isReference && D(`null is not a valid ${this.name}`),
          this.isSmartPointer
            ? ((t = this.rawConstructor()),
              n !== null && n.push(this.rawDestructor, t),
              t)
            : 0
        );
      e.$$ || D(`Cannot pass "${st(e)}" as a ${this.name}`),
        e.$$.ptr ||
          D(`Cannot pass deleted object as a pointer of type ${this.name}`),
        !this.isConst &&
          e.$$.ptrType.isConst &&
          D(
            `Cannot convert argument of type ${
              e.$$.smartPtrType ? e.$$.smartPtrType.name : e.$$.ptrType.name
            } to parameter type ${this.name}`
          );
      var r = e.$$.ptrType.registeredClass;
      if (((t = nt(e.$$.ptr, r, this.registeredClass)), this.isSmartPointer))
        switch (
          (e.$$.smartPtr === void 0 &&
            D("Passing raw pointer to smart pointer is illegal"),
          this.sharingPolicy)
        ) {
          case 0:
            e.$$.smartPtrType === this
              ? (t = e.$$.smartPtr)
              : D(
                  `Cannot convert argument of type ${
                    e.$$.smartPtrType
                      ? e.$$.smartPtrType.name
                      : e.$$.ptrType.name
                  } to parameter type ${this.name}`
                );
            break;
          case 1:
            t = e.$$.smartPtr;
            break;
          case 2:
            if (e.$$.smartPtrType === this) t = e.$$.smartPtr;
            else {
              var a = e.clone();
              (t = this.rawShare(
                t,
                se.toHandle(() => a.delete())
              )),
                n !== null && n.push(this.rawDestructor, t);
            }
            break;
          default:
            D("Unsupporting sharing policy");
        }
      return t;
    }
    function Lr(n, e) {
      if (e === null)
        return this.isReference && D(`null is not a valid ${this.name}`), 0;
      e.$$ || D(`Cannot pass "${st(e)}" as a ${this.name}`),
        e.$$.ptr ||
          D(`Cannot pass deleted object as a pointer of type ${this.name}`),
        e.$$.ptrType.isConst &&
          D(
            `Cannot convert argument of type ${e.$$.ptrType.name} to parameter type ${this.name}`
          );
      var t = e.$$.ptrType.registeredClass,
        r = nt(e.$$.ptr, t, this.registeredClass);
      return r;
    }
    function xt(n) {
      return this.fromWireType(x[n >> 2]);
    }
    var Vr = () => {
      Object.assign(Re.prototype, {
        getPointee(n) {
          return this.rawGetPointee && (n = this.rawGetPointee(n)), n;
        },
        destructor(n) {
          this.rawDestructor && this.rawDestructor(n);
        },
        argPackAdvance: re,
        readValueFromPointer: xt,
        deleteObject(n) {
          n !== null && n.delete();
        },
        fromWireType: Fr,
      });
    };
    function Re(n, e, t, r, a, s, u, l, f, d, v) {
      (this.name = n),
        (this.registeredClass = e),
        (this.isReference = t),
        (this.isConst = r),
        (this.isSmartPointer = a),
        (this.pointeeType = s),
        (this.sharingPolicy = u),
        (this.rawGetPointee = l),
        (this.rawConstructor = f),
        (this.rawShare = d),
        (this.rawDestructor = v),
        !a && e.baseClass === void 0
          ? r
            ? ((this.toWireType = Ur), (this.destructorFunction = null))
            : ((this.toWireType = Lr), (this.destructorFunction = null))
          : (this.toWireType = Hr);
    }
    var Rt = (n, e, t) => {
        i.hasOwnProperty(n) || De("Replacing nonexistant public symbol"),
          i[n].overloadTable !== void 0 && t !== void 0
            ? (i[n].overloadTable[t] = e)
            : ((i[n] = e), (i[n].argCount = t));
      },
      Br = (n, e, t) => {
        var r = i["dynCall_" + n];
        return t && t.length ? r.apply(null, [e].concat(t)) : r.call(null, e);
      },
      Fe = [],
      Ft,
      W = (n) => {
        var e = Fe[n];
        return (
          e || (n >= Fe.length && (Fe.length = n + 1), (Fe[n] = e = Ft.get(n))),
          e
        );
      },
      zr = (n, e, t) => {
        if (n.includes("j")) return Br(n, e, t);
        var r = W(e).apply(null, t);
        return r;
      },
      qr = (n, e) => {
        var t = [];
        return function () {
          return (t.length = 0), Object.assign(t, arguments), zr(n, e, t);
        };
      },
      ee = (n, e) => {
        n = q(n);
        function t() {
          return n.includes("j") ? qr(n, e) : W(e);
        }
        var r = t();
        return (
          typeof r != "function" &&
            D(`unknown function pointer with signature ${n}: ${e}`),
          r
        );
      },
      Yr = (n, e) => {
        var t = tt(e, function (r) {
          (this.name = e), (this.message = r);
          var a = new Error(r).stack;
          a !== void 0 &&
            (this.stack =
              this.toString() +
              `
` +
              a.replace(/^Error(:[^\n]*)?\n/, ""));
        });
        return (
          (t.prototype = Object.create(n.prototype)),
          (t.prototype.constructor = t),
          (t.prototype.toString = function () {
            return this.message === void 0
              ? this.name
              : `${this.name}: ${this.message}`;
          }),
          t
        );
      },
      kt,
      Wt = (n) => {
        var e = Nt(n),
          t = q(e);
        return ae(e), t;
      },
      ke = (n, e) => {
        var t = [],
          r = {};
        function a(s) {
          if (!r[s] && !ce[s]) {
            if (Oe[s]) {
              Oe[s].forEach(a);
              return;
            }
            t.push(s), (r[s] = !0);
          }
        }
        throw (e.forEach(a), new kt(`${n}: ` + t.map(Wt).join([", "])));
      },
      Gr = (n, e, t, r, a, s, u, l, f, d, v, $, _) => {
        (v = q(v)),
          (s = ee(a, s)),
          l && (l = ee(u, l)),
          d && (d = ee(f, d)),
          (_ = ee($, _));
        var A = Dt(v);
        rt(A, function () {
          ke(`Cannot construct ${v} due to unbound types`, [r]);
        }),
          le([n, e, t], r ? [r] : [], function (M) {
            M = M[0];
            var U, H;
            r
              ? ((U = M.registeredClass), (H = U.instancePrototype))
              : (H = xe.prototype);
            var L = tt(A, function () {
                if (Object.getPrototypeOf(this) !== w)
                  throw new pe("Use 'new' to construct " + v);
                if (p.constructor_body === void 0)
                  throw new pe(v + " has no accessible constructor");
                var me = p.constructor_body[arguments.length];
                if (me === void 0)
                  throw new pe(
                    `Tried to invoke ctor of ${v} with invalid number of parameters (${
                      arguments.length
                    }) - expected (${Object.keys(
                      p.constructor_body
                    ).toString()}) parameters instead!`
                  );
                return me.apply(this, arguments);
              }),
              w = Object.create(H, { constructor: { value: L } });
            L.prototype = w;
            var p = new Ir(v, L, w, _, U, s, l, d);
            p.baseClass &&
              (p.baseClass.__derivedClasses === void 0 &&
                (p.baseClass.__derivedClasses = []),
              p.baseClass.__derivedClasses.push(p));
            var T = new Re(v, p, !0, !1, !1),
              B = new Re(v + "*", p, !1, !1, !1),
              Q = new Re(v + " const*", p, !1, !0, !1);
            return (
              (Ot[n] = { pointerType: B, constPointerType: Q }),
              Rt(A, L),
              [T, B, Q]
            );
          });
      },
      at = (n, e) => {
        for (var t = [], r = 0; r < n; r++) t.push(x[(e + r * 4) >> 2]);
        return t;
      };
    function ot(n, e, t, r, a, s) {
      var u = e.length;
      u < 2 &&
        D(
          "argTypes array size mismatch! Must at least get return value and 'this' types!"
        );
      for (
        var l = e[1] !== null && t !== null, f = !1, d = 1;
        d < e.length;
        ++d
      )
        if (e[d] !== null && e[d].destructorFunction === void 0) {
          f = !0;
          break;
        }
      var v = e[0].name !== "void",
        $ = u - 2,
        _ = new Array($),
        A = [],
        M = [];
      return function () {
        arguments.length !== $ &&
          D(
            `function ${n} called with ${arguments.length} arguments, expected ${$}`
          ),
          (M.length = 0);
        var U;
        (A.length = l ? 2 : 1),
          (A[0] = a),
          l && ((U = e[1].toWireType(M, this)), (A[1] = U));
        for (var H = 0; H < $; ++H)
          (_[H] = e[H + 2].toWireType(M, arguments[H])), A.push(_[H]);
        var L = r.apply(null, A);
        function w(p) {
          if (f) _t(M);
          else
            for (var T = l ? 1 : 2; T < e.length; T++) {
              var B = T === 1 ? U : _[T - 2];
              e[T].destructorFunction !== null && e[T].destructorFunction(B);
            }
          if (v) return e[0].fromWireType(p);
        }
        return w(L);
      };
    }
    var Nr = (n, e, t, r, a, s) => {
        var u = at(e, t);
        (a = ee(r, a)),
          le([], [n], function (l) {
            l = l[0];
            var f = `constructor ${l.name}`;
            if (
              (l.registeredClass.constructor_body === void 0 &&
                (l.registeredClass.constructor_body = []),
              l.registeredClass.constructor_body[e - 1] !== void 0)
            )
              throw new pe(
                `Cannot register multiple constructors with identical number of parameters (${
                  e - 1
                }) for class '${
                  l.name
                }'! Overload resolution is currently only performed using the parameter count, not actual type info!`
              );
            return (
              (l.registeredClass.constructor_body[e - 1] = () => {
                ke(`Cannot construct ${l.name} due to unbound types`, u);
              }),
              le(
                [],
                u,
                (d) => (
                  d.splice(1, 0, null),
                  (l.registeredClass.constructor_body[e - 1] = ot(
                    f,
                    d,
                    null,
                    a,
                    s
                  )),
                  []
                )
              ),
              []
            );
          });
      },
      jt = (n) => {
        n = n.trim();
        const e = n.indexOf("(");
        return e !== -1
          ? (K(
              n[n.length - 1] == ")",
              "Parentheses for argument names should match."
            ),
            n.substr(0, e))
          : n;
      },
      Jr = (n, e, t, r, a, s, u, l, f) => {
        var d = at(t, r);
        (e = q(e)),
          (e = jt(e)),
          (s = ee(a, s)),
          le([], [n], function (v) {
            v = v[0];
            var $ = `${v.name}.${e}`;
            e.startsWith("@@") && (e = Symbol[e.substring(2)]),
              l && v.registeredClass.pureVirtualFunctions.push(e);
            function _() {
              ke(`Cannot call ${$} due to unbound types`, d);
            }
            var A = v.registeredClass.instancePrototype,
              M = A[e];
            return (
              M === void 0 ||
              (M.overloadTable === void 0 &&
                M.className !== v.name &&
                M.argCount === t - 2)
                ? ((_.argCount = t - 2), (_.className = v.name), (A[e] = _))
                : (Mt(A, e, $), (A[e].overloadTable[t - 2] = _)),
              le([], d, function (U) {
                var H = ot($, U, v, s, u);
                return (
                  A[e].overloadTable === void 0
                    ? ((H.argCount = t - 2), (A[e] = H))
                    : (A[e].overloadTable[t - 2] = H),
                  []
                );
              }),
              []
            );
          });
      };
    function Qr() {
      Object.assign(It.prototype, {
        get(n) {
          return this.allocated[n];
        },
        has(n) {
          return this.allocated[n] !== void 0;
        },
        allocate(n) {
          var e = this.freelist.pop() || this.allocated.length;
          return (this.allocated[e] = n), e;
        },
        free(n) {
          (this.allocated[n] = void 0), this.freelist.push(n);
        },
      });
    }
    function It() {
      (this.allocated = [void 0]), (this.freelist = []);
    }
    var X = new It(),
      Ut = (n) => {
        n >= X.reserved && --X.get(n).refcount === 0 && X.free(n);
      },
      Zr = () => {
        for (var n = 0, e = X.reserved; e < X.allocated.length; ++e)
          X.allocated[e] !== void 0 && ++n;
        return n;
      },
      Xr = () => {
        X.allocated.push(
          { value: void 0 },
          { value: null },
          { value: !0 },
          { value: !1 }
        ),
          (X.reserved = X.allocated.length),
          (i.count_emval_handles = Zr);
      },
      se = {
        toValue: (n) => (
          n || D("Cannot use deleted val. handle = " + n), X.get(n).value
        ),
        toHandle: (n) => {
          switch (n) {
            case void 0:
              return 1;
            case null:
              return 2;
            case !0:
              return 3;
            case !1:
              return 4;
            default:
              return X.allocate({ refcount: 1, value: n });
          }
        },
      },
      Kr = (n, e) => {
        (e = q(e)),
          te(n, {
            name: e,
            fromWireType: (t) => {
              var r = se.toValue(t);
              return Ut(t), r;
            },
            toWireType: (t, r) => se.toHandle(r),
            argPackAdvance: re,
            readValueFromPointer: Ze,
            destructorFunction: null,
          });
      },
      en = (n, e, t) => {
        switch (e) {
          case 1:
            return t
              ? function (r) {
                  return this.fromWireType(z[r >> 0]);
                }
              : function (r) {
                  return this.fromWireType(R[r >> 0]);
                };
          case 2:
            return t
              ? function (r) {
                  return this.fromWireType(Y[r >> 1]);
                }
              : function (r) {
                  return this.fromWireType(Z[r >> 1]);
                };
          case 4:
            return t
              ? function (r) {
                  return this.fromWireType(V[r >> 2]);
                }
              : function (r) {
                  return this.fromWireType(x[r >> 2]);
                };
          default:
            throw new TypeError(`invalid integer width (${e}): ${n}`);
        }
      },
      tn = (n, e, t, r) => {
        e = q(e);
        function a() {}
        (a.values = {}),
          te(n, {
            name: e,
            constructor: a,
            fromWireType: function (s) {
              return this.constructor.values[s];
            },
            toWireType: (s, u) => u.value,
            argPackAdvance: re,
            readValueFromPointer: en(e, t, r),
            destructorFunction: null,
          }),
          rt(e, a);
      },
      it = (n, e) => {
        var t = ce[n];
        return t === void 0 && D(e + " has unknown type " + Wt(n)), t;
      },
      rn = (n, e, t) => {
        var r = it(n, "enum");
        e = q(e);
        var a = r.constructor,
          s = Object.create(r.constructor.prototype, {
            value: { value: t },
            constructor: { value: tt(`${r.name}_${e}`, function () {}) },
          });
        (a.values[t] = s), (a[e] = s);
      },
      st = (n) => {
        if (n === null) return "null";
        var e = typeof n;
        return e === "object" || e === "array" || e === "function"
          ? n.toString()
          : "" + n;
      },
      nn = (n, e) => {
        switch (e) {
          case 4:
            return function (t) {
              return this.fromWireType(Pe[t >> 2]);
            };
          case 8:
            return function (t) {
              return this.fromWireType(Te[t >> 3]);
            };
          default:
            throw new TypeError(`invalid float width (${e}): ${n}`);
        }
      },
      an = (n, e, t) => {
        (e = q(e)),
          te(n, {
            name: e,
            fromWireType: (r) => r,
            toWireType: (r, a) => a,
            argPackAdvance: re,
            readValueFromPointer: nn(e, t),
            destructorFunction: null,
          });
      },
      on = (n, e, t, r, a, s, u) => {
        var l = at(e, t);
        (n = q(n)),
          (n = jt(n)),
          (a = ee(r, a)),
          rt(
            n,
            function () {
              ke(`Cannot call ${n} due to unbound types`, l);
            },
            e - 1
          ),
          le([], l, function (f) {
            var d = [f[0], null].concat(f.slice(1));
            return Rt(n, ot(n, d, null, a, s), e - 1), [];
          });
      },
      sn = (n, e, t) => {
        switch (e) {
          case 1:
            return t ? (r) => z[r >> 0] : (r) => R[r >> 0];
          case 2:
            return t ? (r) => Y[r >> 1] : (r) => Z[r >> 1];
          case 4:
            return t ? (r) => V[r >> 2] : (r) => x[r >> 2];
          default:
            throw new TypeError(`invalid integer width (${e}): ${n}`);
        }
      },
      un = (n, e, t, r, a) => {
        e = q(e);
        var s = (v) => v;
        if (r === 0) {
          var u = 32 - 8 * t;
          s = (v) => (v << u) >>> u;
        }
        var l = e.includes("unsigned"),
          f = (v, $) => {},
          d;
        l
          ? (d = function (v, $) {
              return f($, this.name), $ >>> 0;
            })
          : (d = function (v, $) {
              return f($, this.name), $;
            }),
          te(n, {
            name: e,
            fromWireType: s,
            toWireType: d,
            argPackAdvance: re,
            readValueFromPointer: sn(e, t, r !== 0),
            destructorFunction: null,
          });
      },
      cn = (n, e, t) => {
        var r = [
            Int8Array,
            Uint8Array,
            Int16Array,
            Uint16Array,
            Int32Array,
            Uint32Array,
            Float32Array,
            Float64Array,
          ],
          a = r[e];
        function s(u) {
          var l = x[u >> 2],
            f = x[(u + 4) >> 2];
          return new a(z.buffer, f, l);
        }
        (t = q(t)),
          te(
            n,
            {
              name: t,
              fromWireType: s,
              argPackAdvance: re,
              readValueFromPointer: s,
            },
            { ignoreDuplicateRegistrations: !0 }
          );
      },
      Ht = (n, e, t, r) => {
        if (!(r > 0)) return 0;
        for (var a = t, s = t + r - 1, u = 0; u < n.length; ++u) {
          var l = n.charCodeAt(u);
          if (l >= 55296 && l <= 57343) {
            var f = n.charCodeAt(++u);
            l = (65536 + ((l & 1023) << 10)) | (f & 1023);
          }
          if (l <= 127) {
            if (t >= s) break;
            e[t++] = l;
          } else if (l <= 2047) {
            if (t + 1 >= s) break;
            (e[t++] = 192 | (l >> 6)), (e[t++] = 128 | (l & 63));
          } else if (l <= 65535) {
            if (t + 2 >= s) break;
            (e[t++] = 224 | (l >> 12)),
              (e[t++] = 128 | ((l >> 6) & 63)),
              (e[t++] = 128 | (l & 63));
          } else {
            if (t + 3 >= s) break;
            (e[t++] = 240 | (l >> 18)),
              (e[t++] = 128 | ((l >> 12) & 63)),
              (e[t++] = 128 | ((l >> 6) & 63)),
              (e[t++] = 128 | (l & 63));
          }
        }
        return (e[t] = 0), t - a;
      },
      ln = (n, e, t) => Ht(n, R, e, t),
      Lt = (n) => {
        for (var e = 0, t = 0; t < n.length; ++t) {
          var r = n.charCodeAt(t);
          r <= 127
            ? e++
            : r <= 2047
            ? (e += 2)
            : r >= 55296 && r <= 57343
            ? ((e += 4), ++t)
            : (e += 3);
        }
        return e;
      },
      Vt = typeof TextDecoder < "u" ? new TextDecoder("utf8") : void 0,
      fn = (n, e, t) => {
        for (var r = e + t, a = e; n[a] && !(a >= r); ) ++a;
        if (a - e > 16 && n.buffer && Vt) return Vt.decode(n.subarray(e, a));
        for (var s = ""; e < a; ) {
          var u = n[e++];
          if (!(u & 128)) {
            s += String.fromCharCode(u);
            continue;
          }
          var l = n[e++] & 63;
          if ((u & 224) == 192) {
            s += String.fromCharCode(((u & 31) << 6) | l);
            continue;
          }
          var f = n[e++] & 63;
          if (
            ((u & 240) == 224
              ? (u = ((u & 15) << 12) | (l << 6) | f)
              : (u = ((u & 7) << 18) | (l << 12) | (f << 6) | (n[e++] & 63)),
            u < 65536)
          )
            s += String.fromCharCode(u);
          else {
            var d = u - 65536;
            s += String.fromCharCode(55296 | (d >> 10), 56320 | (d & 1023));
          }
        }
        return s;
      },
      ut = (n, e) => (n ? fn(R, n, e) : ""),
      dn = (n, e) => {
        e = q(e);
        var t = e === "std::string";
        te(n, {
          name: e,
          fromWireType(r) {
            var a = x[r >> 2],
              s = r + 4,
              u;
            if (t)
              for (var l = s, f = 0; f <= a; ++f) {
                var d = s + f;
                if (f == a || R[d] == 0) {
                  var v = d - l,
                    $ = ut(l, v);
                  u === void 0
                    ? (u = $)
                    : ((u += String.fromCharCode(0)), (u += $)),
                    (l = d + 1);
                }
              }
            else {
              for (var _ = new Array(a), f = 0; f < a; ++f)
                _[f] = String.fromCharCode(R[s + f]);
              u = _.join("");
            }
            return ae(r), u;
          },
          toWireType(r, a) {
            a instanceof ArrayBuffer && (a = new Uint8Array(a));
            var s,
              u = typeof a == "string";
            u ||
              a instanceof Uint8Array ||
              a instanceof Uint8ClampedArray ||
              a instanceof Int8Array ||
              D("Cannot pass non-string to std::string"),
              t && u ? (s = Lt(a)) : (s = a.length);
            var l = lt(4 + s + 1),
              f = l + 4;
            if (((x[l >> 2] = s), t && u)) ln(a, f, s + 1);
            else if (u)
              for (var d = 0; d < s; ++d) {
                var v = a.charCodeAt(d);
                v > 255 &&
                  (ae(f),
                  D("String has UTF-16 code units that do not fit in 8 bits")),
                  (R[f + d] = v);
              }
            else for (var d = 0; d < s; ++d) R[f + d] = a[d];
            return r !== null && r.push(ae, l), l;
          },
          argPackAdvance: re,
          readValueFromPointer: xt,
          destructorFunction(r) {
            ae(r);
          },
        });
      },
      Bt = typeof TextDecoder < "u" ? new TextDecoder("utf-16le") : void 0,
      hn = (n, e) => {
        for (var t = n, r = t >> 1, a = r + e / 2; !(r >= a) && Z[r]; ) ++r;
        if (((t = r << 1), t - n > 32 && Bt))
          return Bt.decode(R.subarray(n, t));
        for (var s = "", u = 0; !(u >= e / 2); ++u) {
          var l = Y[(n + u * 2) >> 1];
          if (l == 0) break;
          s += String.fromCharCode(l);
        }
        return s;
      },
      pn = (n, e, t) => {
        if ((t === void 0 && (t = 2147483647), t < 2)) return 0;
        t -= 2;
        for (
          var r = e, a = t < n.length * 2 ? t / 2 : n.length, s = 0;
          s < a;
          ++s
        ) {
          var u = n.charCodeAt(s);
          (Y[e >> 1] = u), (e += 2);
        }
        return (Y[e >> 1] = 0), e - r;
      },
      vn = (n) => n.length * 2,
      mn = (n, e) => {
        for (var t = 0, r = ""; !(t >= e / 4); ) {
          var a = V[(n + t * 4) >> 2];
          if (a == 0) break;
          if ((++t, a >= 65536)) {
            var s = a - 65536;
            r += String.fromCharCode(55296 | (s >> 10), 56320 | (s & 1023));
          } else r += String.fromCharCode(a);
        }
        return r;
      },
      yn = (n, e, t) => {
        if ((t === void 0 && (t = 2147483647), t < 4)) return 0;
        for (var r = e, a = r + t - 4, s = 0; s < n.length; ++s) {
          var u = n.charCodeAt(s);
          if (u >= 55296 && u <= 57343) {
            var l = n.charCodeAt(++s);
            u = (65536 + ((u & 1023) << 10)) | (l & 1023);
          }
          if (((V[e >> 2] = u), (e += 4), e + 4 > a)) break;
        }
        return (V[e >> 2] = 0), e - r;
      },
      gn = (n) => {
        for (var e = 0, t = 0; t < n.length; ++t) {
          var r = n.charCodeAt(t);
          r >= 55296 && r <= 57343 && ++t, (e += 4);
        }
        return e;
      },
      wn = (n, e, t) => {
        t = q(t);
        var r, a, s, u, l;
        e === 2
          ? ((r = hn), (a = pn), (u = vn), (s = () => Z), (l = 1))
          : e === 4 && ((r = mn), (a = yn), (u = gn), (s = () => x), (l = 2)),
          te(n, {
            name: t,
            fromWireType: (f) => {
              for (
                var d = x[f >> 2], v = s(), $, _ = f + 4, A = 0;
                A <= d;
                ++A
              ) {
                var M = f + 4 + A * e;
                if (A == d || v[M >> l] == 0) {
                  var U = M - _,
                    H = r(_, U);
                  $ === void 0
                    ? ($ = H)
                    : (($ += String.fromCharCode(0)), ($ += H)),
                    (_ = M + e);
                }
              }
              return ae(f), $;
            },
            toWireType: (f, d) => {
              typeof d != "string" &&
                D(`Cannot pass non-string to C++ string type ${t}`);
              var v = u(d),
                $ = lt(4 + v + e);
              return (
                (x[$ >> 2] = v >> l),
                a(d, $ + 4, v + e),
                f !== null && f.push(ae, $),
                $
              );
            },
            argPackAdvance: re,
            readValueFromPointer: Ze,
            destructorFunction(f) {
              ae(f);
            },
          });
      },
      $n = (n, e, t, r, a, s) => {
        Ee[n] = {
          name: q(e),
          rawConstructor: ee(t, r),
          rawDestructor: ee(a, s),
          fields: [],
        };
      },
      bn = (n, e, t, r, a, s, u, l, f, d) => {
        Ee[n].fields.push({
          fieldName: q(e),
          getterReturnType: t,
          getter: ee(r, a),
          getterContext: s,
          setterArgumentType: u,
          setter: ee(l, f),
          setterContext: d,
        });
      },
      Cn = (n, e) => {
        (e = q(e)),
          te(n, {
            isVoid: !0,
            name: e,
            argPackAdvance: 0,
            fromWireType: () => {},
            toWireType: (t, r) => {},
          });
      },
      _n = {},
      Pn = (n) => {
        var e = _n[n];
        return e === void 0 ? q(n) : e;
      },
      zt = () => {
        if (typeof globalThis == "object") return globalThis;
        function n(e) {
          e.$$$embind_global$$$ = e;
          var t =
            typeof $$$embind_global$$$ == "object" &&
            e.$$$embind_global$$$ == e;
          return t || delete e.$$$embind_global$$$, t;
        }
        if (
          typeof $$$embind_global$$$ == "object" ||
          (typeof global == "object" && n(global)
            ? ($$$embind_global$$$ = global)
            : typeof self == "object" &&
              n(self) &&
              ($$$embind_global$$$ = self),
          typeof $$$embind_global$$$ == "object")
        )
          return $$$embind_global$$$;
        throw Error("unable to get global object.");
      },
      Tn = (n) =>
        n === 0 ? se.toHandle(zt()) : ((n = Pn(n)), se.toHandle(zt()[n])),
      Sn = (n) => {
        n > 4 && (X.get(n).refcount += 1);
      },
      An = (n) => {
        var e = new Array(n + 1);
        return function (t, r, a) {
          e[0] = t;
          for (var s = 0; s < n; ++s) {
            var u = it(x[(r + s * 4) >> 2], "parameter " + s);
            (e[s + 1] = u.readValueFromPointer(a)), (a += u.argPackAdvance);
          }
          var l = new (t.bind.apply(t, e))();
          return se.toHandle(l);
        };
      },
      qt = {},
      En = (n, e, t, r) => {
        n = se.toValue(n);
        var a = qt[e];
        return a || ((a = An(e)), (qt[e] = a)), a(n, t, r);
      },
      On = (n, e) => {
        n = it(n, "_emval_take_value");
        var t = n.readValueFromPointer(e);
        return se.toHandle(t);
      },
      Dn = () => {
        de("");
      },
      Mn = (n, e, t) => R.copyWithin(n, e, e + t),
      xn = () => 2147483648,
      Rn = (n) => {
        var e = N.buffer,
          t = (n - e.byteLength + 65535) / 65536;
        try {
          return N.grow(t), Ie(), 1;
        } catch (r) {}
      },
      Fn = (n) => {
        var e = R.length;
        n >>>= 0;
        var t = xn();
        if (n > t) return !1;
        for (var r = (f, d) => f + ((d - (f % d)) % d), a = 1; a <= 4; a *= 2) {
          var s = e * (1 + 0.2 / a);
          s = Math.min(s, n + 100663296);
          var u = Math.min(t, r(Math.max(n, s), 65536)),
            l = Rn(u);
          if (l) return !0;
        }
        return !1;
      },
      ct = {},
      kn = () => b || "./this.program",
      be = () => {
        if (!be.strings) {
          var n =
              (
                (typeof navigator == "object" &&
                  navigator.languages &&
                  navigator.languages[0]) ||
                "C"
              ).replace("-", "_") + ".UTF-8",
            e = {
              USER: "web_user",
              LOGNAME: "web_user",
              PATH: "/",
              PWD: "/",
              HOME: "/home/<USER>",
              LANG: n,
              _: kn(),
            };
          for (var t in ct) ct[t] === void 0 ? delete e[t] : (e[t] = ct[t]);
          var r = [];
          for (var t in e) r.push(`${t}=${e[t]}`);
          be.strings = r;
        }
        return be.strings;
      },
      Wn = (n, e) => {
        for (var t = 0; t < n.length; ++t) z[e++ >> 0] = n.charCodeAt(t);
        z[e >> 0] = 0;
      },
      jn = (n, e) => {
        var t = 0;
        return (
          be().forEach((r, a) => {
            var s = e + t;
            (x[(n + a * 4) >> 2] = s), Wn(r, s), (t += r.length + 1);
          }),
          0
        );
      },
      In = (n, e) => {
        var t = be();
        x[n >> 2] = t.length;
        var r = 0;
        return t.forEach((a) => (r += a.length + 1)), (x[e >> 2] = r), 0;
      },
      Un = (n) => n,
      We = (n) => n % 4 === 0 && (n % 100 !== 0 || n % 400 === 0),
      Hn = (n, e) => {
        for (var t = 0, r = 0; r <= e; t += n[r++]);
        return t;
      },
      Yt = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],
      Gt = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],
      Ln = (n, e) => {
        for (var t = new Date(n.getTime()); e > 0; ) {
          var r = We(t.getFullYear()),
            a = t.getMonth(),
            s = (r ? Yt : Gt)[a];
          if (e > s - t.getDate())
            (e -= s - t.getDate() + 1),
              t.setDate(1),
              a < 11
                ? t.setMonth(a + 1)
                : (t.setMonth(0), t.setFullYear(t.getFullYear() + 1));
          else return t.setDate(t.getDate() + e), t;
        }
        return t;
      };
    function Vn(n, e, t) {
      var r = t > 0 ? t : Lt(n) + 1,
        a = new Array(r),
        s = Ht(n, a, 0, a.length);
      return e && (a.length = s), a;
    }
    var Bn = (n, e) => {
        z.set(n, e);
      },
      zn = (n, e, t, r) => {
        var a = x[(r + 40) >> 2],
          s = {
            tm_sec: V[r >> 2],
            tm_min: V[(r + 4) >> 2],
            tm_hour: V[(r + 8) >> 2],
            tm_mday: V[(r + 12) >> 2],
            tm_mon: V[(r + 16) >> 2],
            tm_year: V[(r + 20) >> 2],
            tm_wday: V[(r + 24) >> 2],
            tm_yday: V[(r + 28) >> 2],
            tm_isdst: V[(r + 32) >> 2],
            tm_gmtoff: V[(r + 36) >> 2],
            tm_zone: a ? ut(a) : "",
          },
          u = ut(t),
          l = {
            "%c": "%a %b %d %H:%M:%S %Y",
            "%D": "%m/%d/%y",
            "%F": "%Y-%m-%d",
            "%h": "%b",
            "%r": "%I:%M:%S %p",
            "%R": "%H:%M",
            "%T": "%H:%M:%S",
            "%x": "%m/%d/%y",
            "%X": "%H:%M:%S",
            "%Ec": "%c",
            "%EC": "%C",
            "%Ex": "%m/%d/%y",
            "%EX": "%H:%M:%S",
            "%Ey": "%y",
            "%EY": "%Y",
            "%Od": "%d",
            "%Oe": "%e",
            "%OH": "%H",
            "%OI": "%I",
            "%Om": "%m",
            "%OM": "%M",
            "%OS": "%S",
            "%Ou": "%u",
            "%OU": "%U",
            "%OV": "%V",
            "%Ow": "%w",
            "%OW": "%W",
            "%Oy": "%y",
          };
        for (var f in l) u = u.replace(new RegExp(f, "g"), l[f]);
        var d = [
            "Sunday",
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday",
          ],
          v = [
            "January",
            "February",
            "March",
            "April",
            "May",
            "June",
            "July",
            "August",
            "September",
            "October",
            "November",
            "December",
          ];
        function $(w, p, T) {
          for (
            var B = typeof w == "number" ? w.toString() : w || "";
            B.length < p;

          )
            B = T[0] + B;
          return B;
        }
        function _(w, p) {
          return $(w, p, "0");
        }
        function A(w, p) {
          function T(Q) {
            return Q < 0 ? -1 : Q > 0 ? 1 : 0;
          }
          var B;
          return (
            (B = T(w.getFullYear() - p.getFullYear())) === 0 &&
              (B = T(w.getMonth() - p.getMonth())) === 0 &&
              (B = T(w.getDate() - p.getDate())),
            B
          );
        }
        function M(w) {
          switch (w.getDay()) {
            case 0:
              return new Date(w.getFullYear() - 1, 11, 29);
            case 1:
              return w;
            case 2:
              return new Date(w.getFullYear(), 0, 3);
            case 3:
              return new Date(w.getFullYear(), 0, 2);
            case 4:
              return new Date(w.getFullYear(), 0, 1);
            case 5:
              return new Date(w.getFullYear() - 1, 11, 31);
            case 6:
              return new Date(w.getFullYear() - 1, 11, 30);
          }
        }
        function U(w) {
          var p = Ln(new Date(w.tm_year + 1900, 0, 1), w.tm_yday),
            T = new Date(p.getFullYear(), 0, 4),
            B = new Date(p.getFullYear() + 1, 0, 4),
            Q = M(T),
            me = M(B);
          return A(Q, p) <= 0
            ? A(me, p) <= 0
              ? p.getFullYear() + 1
              : p.getFullYear()
            : p.getFullYear() - 1;
        }
        var H = {
          "%a": (w) => d[w.tm_wday].substring(0, 3),
          "%A": (w) => d[w.tm_wday],
          "%b": (w) => v[w.tm_mon].substring(0, 3),
          "%B": (w) => v[w.tm_mon],
          "%C": (w) => {
            var p = w.tm_year + 1900;
            return _((p / 100) | 0, 2);
          },
          "%d": (w) => _(w.tm_mday, 2),
          "%e": (w) => $(w.tm_mday, 2, " "),
          "%g": (w) => U(w).toString().substring(2),
          "%G": (w) => U(w),
          "%H": (w) => _(w.tm_hour, 2),
          "%I": (w) => {
            var p = w.tm_hour;
            return p == 0 ? (p = 12) : p > 12 && (p -= 12), _(p, 2);
          },
          "%j": (w) =>
            _(w.tm_mday + Hn(We(w.tm_year + 1900) ? Yt : Gt, w.tm_mon - 1), 3),
          "%m": (w) => _(w.tm_mon + 1, 2),
          "%M": (w) => _(w.tm_min, 2),
          "%n": () => `
`,
          "%p": (w) => (w.tm_hour >= 0 && w.tm_hour < 12 ? "AM" : "PM"),
          "%S": (w) => _(w.tm_sec, 2),
          "%t": () => "	",
          "%u": (w) => w.tm_wday || 7,
          "%U": (w) => {
            var p = w.tm_yday + 7 - w.tm_wday;
            return _(Math.floor(p / 7), 2);
          },
          "%V": (w) => {
            var p = Math.floor((w.tm_yday + 7 - ((w.tm_wday + 6) % 7)) / 7);
            if (((w.tm_wday + 371 - w.tm_yday - 2) % 7 <= 2 && p++, p)) {
              if (p == 53) {
                var T = (w.tm_wday + 371 - w.tm_yday) % 7;
                T != 4 && (T != 3 || !We(w.tm_year)) && (p = 1);
              }
            } else {
              p = 52;
              var B = (w.tm_wday + 7 - w.tm_yday - 1) % 7;
              (B == 4 || (B == 5 && We((w.tm_year % 400) - 1))) && p++;
            }
            return _(p, 2);
          },
          "%w": (w) => w.tm_wday,
          "%W": (w) => {
            var p = w.tm_yday + 7 - ((w.tm_wday + 6) % 7);
            return _(Math.floor(p / 7), 2);
          },
          "%y": (w) => (w.tm_year + 1900).toString().substring(2),
          "%Y": (w) => w.tm_year + 1900,
          "%z": (w) => {
            var p = w.tm_gmtoff,
              T = p >= 0;
            return (
              (p = Math.abs(p) / 60),
              (p = (p / 60) * 100 + (p % 60)),
              (T ? "+" : "-") + ("0000" + p).slice(-4)
            );
          },
          "%Z": (w) => w.tm_zone,
          "%%": () => "%",
        };
        u = u.replace(/%%/g, "\0\0");
        for (var f in H)
          u.includes(f) && (u = u.replace(new RegExp(f, "g"), H[f](s)));
        u = u.replace(/\0\0/g, "%");
        var L = Vn(u, !1);
        return L.length > e ? 0 : (Bn(L, n), L.length - 1);
      },
      qn = (n, e, t, r, a) => zn(n, e, t, r);
    (Pt = i.InternalError =
      class extends Error {
        constructor(n) {
          super(n), (this.name = "InternalError");
        }
      }),
      _r(),
      (pe = i.BindingError =
        class extends Error {
          constructor(n) {
            super(n), (this.name = "BindingError");
          }
        }),
      kr(),
      Mr(),
      Vr(),
      (kt = i.UnboundTypeError = Yr(Error, "UnboundTypeError")),
      Qr(),
      Xr();
    var Yn = {
        s: dr,
        v: hr,
        b: vr,
        g: mr,
        q: yr,
        I: gr,
        f: wr,
        T: $r,
        d: pr,
        ba: br,
        P: Cr,
        Y: Tr,
        aa: Gr,
        $: Nr,
        w: Jr,
        X: Kr,
        x: tn,
        h: rn,
        K: an,
        L: on,
        t: un,
        n: cn,
        J: dn,
        C: wn,
        A: $n,
        ca: bn,
        Z: Cn,
        ea: Ut,
        da: Tn,
        M: Sn,
        V: En,
        _: On,
        B: Dn,
        W: Mn,
        U: Fn,
        R: jn,
        S: In,
        D: sa,
        E: ia,
        m: ua,
        a: Gn,
        e: Zn,
        o: ea,
        k: Qn,
        G: aa,
        u: ra,
        F: oa,
        z: la,
        O: da,
        l: Xn,
        j: Kn,
        c: Jn,
        p: Nn,
        H: na,
        r: ca,
        i: ta,
        y: fa,
        N: Un,
        Q: qn,
      },
      k = fr(),
      ae = (i._free = (n) => (ae = i._free = k.ha)(n)),
      lt = (i._malloc = (n) => (lt = i._malloc = k.ia)(n)),
      Nt = (n) => (Nt = k.ka)(n);
    i.__embind_initialize_bindings = () =>
      (i.__embind_initialize_bindings = k.la)();
    var F = (n, e) => (F = k.ma)(n, e),
      Ce = (n) => (Ce = k.na)(n),
      j = () => (j = k.oa)(),
      I = (n) => (I = k.pa)(n),
      Jt = (n) => (Jt = k.qa)(n),
      Qt = (n) => (Qt = k.ra)(n),
      Zt = (n, e, t) => (Zt = k.sa)(n, e, t),
      Xt = (n) => (Xt = k.ta)(n);
    i.dynCall_viijii = (n, e, t, r, a, s, u) =>
      (i.dynCall_viijii = k.ua)(n, e, t, r, a, s, u);
    var Kt = (i.dynCall_jiiii = (n, e, t, r, a) =>
      (Kt = i.dynCall_jiiii = k.va)(n, e, t, r, a));
    (i.dynCall_iiiiij = (n, e, t, r, a, s, u) =>
      (i.dynCall_iiiiij = k.wa)(n, e, t, r, a, s, u)),
      (i.dynCall_iiiiijj = (n, e, t, r, a, s, u, l, f) =>
        (i.dynCall_iiiiijj = k.xa)(n, e, t, r, a, s, u, l, f)),
      (i.dynCall_iiiiiijj = (n, e, t, r, a, s, u, l, f, d) =>
        (i.dynCall_iiiiiijj = k.ya)(n, e, t, r, a, s, u, l, f, d));
    function Gn(n, e) {
      var t = j();
      try {
        return W(n)(e);
      } catch (r) {
        if ((I(t), r !== r + 0)) throw r;
        F(1, 0);
      }
    }
    function Nn(n, e, t, r) {
      var a = j();
      try {
        W(n)(e, t, r);
      } catch (s) {
        if ((I(a), s !== s + 0)) throw s;
        F(1, 0);
      }
    }
    function Jn(n, e, t) {
      var r = j();
      try {
        W(n)(e, t);
      } catch (a) {
        if ((I(r), a !== a + 0)) throw a;
        F(1, 0);
      }
    }
    function Qn(n, e, t, r, a) {
      var s = j();
      try {
        return W(n)(e, t, r, a);
      } catch (u) {
        if ((I(s), u !== u + 0)) throw u;
        F(1, 0);
      }
    }
    function Zn(n, e, t) {
      var r = j();
      try {
        return W(n)(e, t);
      } catch (a) {
        if ((I(r), a !== a + 0)) throw a;
        F(1, 0);
      }
    }
    function Xn(n) {
      var e = j();
      try {
        W(n)();
      } catch (t) {
        if ((I(e), t !== t + 0)) throw t;
        F(1, 0);
      }
    }
    function Kn(n, e) {
      var t = j();
      try {
        W(n)(e);
      } catch (r) {
        if ((I(t), r !== r + 0)) throw r;
        F(1, 0);
      }
    }
    function ea(n, e, t, r) {
      var a = j();
      try {
        return W(n)(e, t, r);
      } catch (s) {
        if ((I(a), s !== s + 0)) throw s;
        F(1, 0);
      }
    }
    function ta(n, e, t, r, a, s, u, l, f, d, v) {
      var $ = j();
      try {
        W(n)(e, t, r, a, s, u, l, f, d, v);
      } catch (_) {
        if ((I($), _ !== _ + 0)) throw _;
        F(1, 0);
      }
    }
    function ra(n, e, t, r, a, s, u) {
      var l = j();
      try {
        return W(n)(e, t, r, a, s, u);
      } catch (f) {
        if ((I(l), f !== f + 0)) throw f;
        F(1, 0);
      }
    }
    function na(n, e, t, r, a) {
      var s = j();
      try {
        W(n)(e, t, r, a);
      } catch (u) {
        if ((I(s), u !== u + 0)) throw u;
        F(1, 0);
      }
    }
    function aa(n, e, t, r, a, s) {
      var u = j();
      try {
        return W(n)(e, t, r, a, s);
      } catch (l) {
        if ((I(u), l !== l + 0)) throw l;
        F(1, 0);
      }
    }
    function oa(n, e, t, r, a, s, u, l) {
      var f = j();
      try {
        return W(n)(e, t, r, a, s, u, l);
      } catch (d) {
        if ((I(f), d !== d + 0)) throw d;
        F(1, 0);
      }
    }
    function ia(n, e, t, r) {
      var a = j();
      try {
        return W(n)(e, t, r);
      } catch (s) {
        if ((I(a), s !== s + 0)) throw s;
        F(1, 0);
      }
    }
    function sa(n, e, t, r) {
      var a = j();
      try {
        return W(n)(e, t, r);
      } catch (s) {
        if ((I(a), s !== s + 0)) throw s;
        F(1, 0);
      }
    }
    function ua(n) {
      var e = j();
      try {
        return W(n)();
      } catch (t) {
        if ((I(e), t !== t + 0)) throw t;
        F(1, 0);
      }
    }
    function ca(n, e, t, r, a, s, u, l) {
      var f = j();
      try {
        W(n)(e, t, r, a, s, u, l);
      } catch (d) {
        if ((I(f), d !== d + 0)) throw d;
        F(1, 0);
      }
    }
    function la(n, e, t, r, a, s, u, l, f, d, v, $) {
      var _ = j();
      try {
        return W(n)(e, t, r, a, s, u, l, f, d, v, $);
      } catch (A) {
        if ((I(_), A !== A + 0)) throw A;
        F(1, 0);
      }
    }
    function fa(n, e, t, r, a, s, u, l, f, d, v, $, _, A, M, U) {
      var H = j();
      try {
        W(n)(e, t, r, a, s, u, l, f, d, v, $, _, A, M, U);
      } catch (L) {
        if ((I(H), L !== L + 0)) throw L;
        F(1, 0);
      }
    }
    function da(n, e, t, r, a) {
      var s = j();
      try {
        return Kt(n, e, t, r, a);
      } catch (u) {
        if ((I(s), u !== u + 0)) throw u;
        F(1, 0);
      }
    }
    var ht;
    oe = function n() {
      ht || ft(), ht || (oe = n);
    };
    function ft() {
      if (ie > 0 || (mt(), ie > 0)) return;
      function n() {
        ht ||
          ((ht = !0),
          (i.calledRun = !0),
          !J &&
            (yt(),
            h(i),
            i.onRuntimeInitialized && i.onRuntimeInitialized(),
            gt()));
      }
      i.setStatus
        ? (i.setStatus("Running..."),
          setTimeout(function () {
            setTimeout(function () {
              i.setStatus("");
            }, 1),
              n();
          }, 1))
        : n();
    }
    if (i.preInit)
      for (
        typeof i.preInit == "function" && (i.preInit = [i.preInit]);
        i.preInit.length > 0;

      )
        i.preInit.pop()();
    return ft(), c.ready;
  };
})();
function Io(o) {
  return Ca(_a, o);
}
async function Uo(o, c) {
  return Wo(_a, o, c);
}
async function Ho(o, c) {
  return jo(_a, o, c);
}
var Lo = (() => {
  var o =
    typeof document < "u" && document.currentScript
      ? document.currentScript.src
      : void 0;
  return function (c = {}) {
    var i = c,
      h,
      m;
    i.ready = new Promise((e, t) => {
      (h = e), (m = t);
    });
    var y = Object.assign({}, i),
      b = "./this.program",
      C = typeof window == "object",
      P = typeof importScripts == "function";
    typeof process == "object" &&
      typeof process.versions == "object" &&
      process.versions.node;
    var g = "";
    function E(e) {
      return i.locateFile ? i.locateFile(e, g) : g + e;
    }
    var O;
    (C || P) &&
      (P
        ? (g = self.location.href)
        : typeof document < "u" &&
          document.currentScript &&
          (g = document.currentScript.src),
      o && (g = o),
      g.indexOf("blob:") !== 0
        ? (g = g.substr(0, g.replace(/[?#].*/, "").lastIndexOf("/") + 1))
        : (g = ""),
      P &&
        (O = (e) => {
          var t = new XMLHttpRequest();
          return (
            t.open("GET", e, !1),
            (t.responseType = "arraybuffer"),
            t.send(null),
            new Uint8Array(t.response)
          );
        })),
      i.print || console.log.bind(console);
    var S = i.printErr || console.error.bind(console);
    Object.assign(i, y),
      (y = null),
      i.arguments && i.arguments,
      i.thisProgram && (b = i.thisProgram),
      i.quit && i.quit;
    var G;
    i.wasmBinary && (G = i.wasmBinary),
      typeof WebAssembly != "object" && de("no native wasm support detected");
    var N,
      J = !1;
    function K(e, t) {
      e || de(t);
    }
    var z, R, Y, Z, V, x, Pe, Te;
    function Ie() {
      var e = N.buffer;
      (i.HEAP8 = z = new Int8Array(e)),
        (i.HEAP16 = Y = new Int16Array(e)),
        (i.HEAPU8 = R = new Uint8Array(e)),
        (i.HEAPU16 = Z = new Uint16Array(e)),
        (i.HEAP32 = V = new Int32Array(e)),
        (i.HEAPU32 = x = new Uint32Array(e)),
        (i.HEAPF32 = Pe = new Float32Array(e)),
        (i.HEAPF64 = Te = new Float64Array(e));
    }
    var Ue = [],
      He = [],
      ze = [];
    function mt() {
      if (i.preRun)
        for (
          typeof i.preRun == "function" && (i.preRun = [i.preRun]);
          i.preRun.length;

        )
          qe(i.preRun.shift());
      Ne(Ue);
    }
    function yt() {
      Ne(He);
    }
    function gt() {
      if (i.postRun)
        for (
          typeof i.postRun == "function" && (i.postRun = [i.postRun]);
          i.postRun.length;

        )
          wt(i.postRun.shift());
      Ne(ze);
    }
    function qe(e) {
      Ue.unshift(e);
    }
    function dt(e) {
      He.unshift(e);
    }
    function wt(e) {
      ze.unshift(e);
    }
    var ie = 0,
      oe = null;
    function ve(e) {
      ie++, i.monitorRunDependencies && i.monitorRunDependencies(ie);
    }
    function $t(e) {
      if (
        (ie--,
        i.monitorRunDependencies && i.monitorRunDependencies(ie),
        ie == 0 && oe)
      ) {
        var t = oe;
        (oe = null), t();
      }
    }
    function de(e) {
      i.onAbort && i.onAbort(e),
        (e = "Aborted(" + e + ")"),
        S(e),
        (J = !0),
        (e += ". Build with -sASSERTIONS for more info.");
      var t = new WebAssembly.RuntimeError(e);
      throw (m(t), t);
    }
    var bt = "data:application/octet-stream;base64,",
      Ye = (e) => e.startsWith(bt),
      ue;
    (ue = "zxing_full.wasm"), Ye(ue) || (ue = E(ue));
    function Ge(e) {
      if (e == ue && G) return new Uint8Array(G);
      if (O) return O(e);
      throw "both async and sync fetching of the wasm failed";
    }
    function cr(e) {
      return !G && (C || P) && typeof fetch == "function"
        ? fetch(e, { credentials: "same-origin" })
            .then((t) => {
              if (!t.ok) throw "failed to load wasm binary file at '" + e + "'";
              return t.arrayBuffer();
            })
            .catch(() => Ge(e))
        : Promise.resolve().then(() => Ge(e));
    }
    function Ct(e, t, r) {
      return cr(e)
        .then((a) => WebAssembly.instantiate(a, t))
        .then((a) => a)
        .then(r, (a) => {
          S(`failed to asynchronously prepare wasm: ${a}`), de(a);
        });
    }
    function lr(e, t, r, a) {
      return !e &&
        typeof WebAssembly.instantiateStreaming == "function" &&
        !Ye(t) &&
        typeof fetch == "function"
        ? fetch(t, { credentials: "same-origin" }).then((s) => {
            var u = WebAssembly.instantiateStreaming(s, r);
            return u.then(a, function (l) {
              return (
                S(`wasm streaming compile failed: ${l}`),
                S("falling back to ArrayBuffer instantiation"),
                Ct(t, r, a)
              );
            });
          })
        : Ct(t, r, a);
    }
    function fr() {
      var e = { a: Yn };
      function t(a, s) {
        return (
          (k = a.exports), (N = k.ga), Ie(), (Ft = k.ka), dt(k.ha), $t(), k
        );
      }
      ve();
      function r(a) {
        t(a.instance);
      }
      if (i.instantiateWasm)
        try {
          return i.instantiateWasm(e, t);
        } catch (a) {
          S(`Module.instantiateWasm callback failed with error: ${a}`), m(a);
        }
      return lr(G, ue, e, r).catch(m), {};
    }
    var Ne = (e) => {
      for (; e.length > 0; ) e.shift()(i);
    };
    i.noExitRuntime;
    var Se = [],
      Ae = 0,
      dr = (e) => {
        var t = new Je(e);
        return (
          t.get_caught() || (t.set_caught(!0), Ae--),
          t.set_rethrown(!1),
          Se.push(t),
          Qt(t.excPtr),
          t.get_exception_ptr()
        );
      },
      ne = 0,
      hr = () => {
        F(0, 0);
        var e = Se.pop();
        Jt(e.excPtr), (ne = 0);
      };
    function Je(e) {
      (this.excPtr = e),
        (this.ptr = e - 24),
        (this.set_type = function (t) {
          x[(this.ptr + 4) >> 2] = t;
        }),
        (this.get_type = function () {
          return x[(this.ptr + 4) >> 2];
        }),
        (this.set_destructor = function (t) {
          x[(this.ptr + 8) >> 2] = t;
        }),
        (this.get_destructor = function () {
          return x[(this.ptr + 8) >> 2];
        }),
        (this.set_caught = function (t) {
          (t = t ? 1 : 0), (z[(this.ptr + 12) >> 0] = t);
        }),
        (this.get_caught = function () {
          return z[(this.ptr + 12) >> 0] != 0;
        }),
        (this.set_rethrown = function (t) {
          (t = t ? 1 : 0), (z[(this.ptr + 13) >> 0] = t);
        }),
        (this.get_rethrown = function () {
          return z[(this.ptr + 13) >> 0] != 0;
        }),
        (this.init = function (t, r) {
          this.set_adjusted_ptr(0), this.set_type(t), this.set_destructor(r);
        }),
        (this.set_adjusted_ptr = function (t) {
          x[(this.ptr + 16) >> 2] = t;
        }),
        (this.get_adjusted_ptr = function () {
          return x[(this.ptr + 16) >> 2];
        }),
        (this.get_exception_ptr = function () {
          var t = Xt(this.get_type());
          if (t) return x[this.excPtr >> 2];
          var r = this.get_adjusted_ptr();
          return r !== 0 ? r : this.excPtr;
        });
    }
    var pr = (e) => {
        throw (ne || (ne = e), ne);
      },
      Qe = (e) => {
        var t = ne;
        if (!t) return Ce(0), 0;
        var r = new Je(t);
        r.set_adjusted_ptr(t);
        var a = r.get_type();
        if (!a) return Ce(0), t;
        for (var s in e) {
          var u = e[s];
          if (u === 0 || u === a) break;
          var l = r.ptr + 16;
          if (Zt(u, a, l)) return Ce(u), t;
        }
        return Ce(a), t;
      },
      vr = () => Qe([]),
      mr = (e) => Qe([e]),
      yr = (e, t) => Qe([e, t]),
      gr = () => {
        var e = Se.pop();
        e || de("no exception to throw");
        var t = e.excPtr;
        throw (
          (e.get_rethrown() ||
            (Se.push(e), e.set_rethrown(!0), e.set_caught(!1), Ae++),
          (ne = t),
          ne)
        );
      },
      wr = (e, t, r) => {
        var a = new Je(e);
        throw (a.init(t, r), (ne = e), Ae++, ne);
      },
      $r = () => Ae,
      Ee = {},
      _t = (e) => {
        for (; e.length; ) {
          var t = e.pop(),
            r = e.pop();
          r(t);
        }
      };
    function Ze(e) {
      return this.fromWireType(V[e >> 2]);
    }
    var he = {},
      ce = {},
      Oe = {},
      Pt,
      De = (e) => {
        throw new Pt(e);
      },
      le = (e, t, r) => {
        e.forEach(function (f) {
          Oe[f] = t;
        });
        function a(f) {
          var d = r(f);
          d.length !== e.length && De("Mismatched type converter count");
          for (var v = 0; v < e.length; ++v) te(e[v], d[v]);
        }
        var s = new Array(t.length),
          u = [],
          l = 0;
        t.forEach((f, d) => {
          ce.hasOwnProperty(f)
            ? (s[d] = ce[f])
            : (u.push(f),
              he.hasOwnProperty(f) || (he[f] = []),
              he[f].push(() => {
                (s[d] = ce[f]), ++l, l === u.length && a(s);
              }));
        }),
          u.length === 0 && a(s);
      },
      br = (e) => {
        var t = Ee[e];
        delete Ee[e];
        var r = t.rawConstructor,
          a = t.rawDestructor,
          s = t.fields,
          u = s
            .map((l) => l.getterReturnType)
            .concat(s.map((l) => l.setterArgumentType));
        le([e], u, (l) => {
          var f = {};
          return (
            s.forEach((d, v) => {
              var $ = d.fieldName,
                _ = l[v],
                A = d.getter,
                M = d.getterContext,
                U = l[v + s.length],
                H = d.setter,
                L = d.setterContext;
              f[$] = {
                read: (w) => _.fromWireType(A(M, w)),
                write: (w, p) => {
                  var T = [];
                  H(L, w, U.toWireType(T, p)), _t(T);
                },
              };
            }),
            [
              {
                name: t.name,
                fromWireType: (d) => {
                  var v = {};
                  for (var $ in f) v[$] = f[$].read(d);
                  return a(d), v;
                },
                toWireType: (d, v) => {
                  for (var $ in f)
                    if (!($ in v)) throw new TypeError(`Missing field: "${$}"`);
                  var _ = r();
                  for ($ in f) f[$].write(_, v[$]);
                  return d !== null && d.push(a, _), _;
                },
                argPackAdvance: re,
                readValueFromPointer: Ze,
                destructorFunction: a,
              },
            ]
          );
        });
      },
      Cr = (e, t, r, a, s) => {},
      _r = () => {
        for (var e = new Array(256), t = 0; t < 256; ++t)
          e[t] = String.fromCharCode(t);
        Tt = e;
      },
      Tt,
      q = (e) => {
        for (var t = "", r = e; R[r]; ) t += Tt[R[r++]];
        return t;
      },
      pe,
      D = (e) => {
        throw new pe(e);
      };
    function Pr(e, t, r = {}) {
      var a = t.name;
      if (
        (e || D(`type "${a}" must have a positive integer typeid pointer`),
        ce.hasOwnProperty(e))
      ) {
        if (r.ignoreDuplicateRegistrations) return;
        D(`Cannot register type '${a}' twice`);
      }
      if (((ce[e] = t), delete Oe[e], he.hasOwnProperty(e))) {
        var s = he[e];
        delete he[e], s.forEach((u) => u());
      }
    }
    function te(e, t, r = {}) {
      if (!("argPackAdvance" in t))
        throw new TypeError(
          "registerType registeredInstance requires argPackAdvance"
        );
      return Pr(e, t, r);
    }
    var re = 8,
      Tr = (e, t, r, a) => {
        (t = q(t)),
          te(e, {
            name: t,
            fromWireType: function (s) {
              return !!s;
            },
            toWireType: function (s, u) {
              return u ? r : a;
            },
            argPackAdvance: re,
            readValueFromPointer: function (s) {
              return this.fromWireType(R[s]);
            },
            destructorFunction: null,
          });
      },
      Sr = (e) => ({
        count: e.count,
        deleteScheduled: e.deleteScheduled,
        preservePointerOnDelete: e.preservePointerOnDelete,
        ptr: e.ptr,
        ptrType: e.ptrType,
        smartPtr: e.smartPtr,
        smartPtrType: e.smartPtrType,
      }),
      Xe = (e) => {
        function t(r) {
          return r.$$.ptrType.registeredClass.name;
        }
        D(t(e) + " instance already deleted");
      },
      Ke = !1,
      St = (e) => {},
      Ar = (e) => {
        e.smartPtr
          ? e.smartPtrType.rawDestructor(e.smartPtr)
          : e.ptrType.registeredClass.rawDestructor(e.ptr);
      },
      At = (e) => {
        e.count.value -= 1;
        var t = e.count.value === 0;
        t && Ar(e);
      },
      Et = (e, t, r) => {
        if (t === r) return e;
        if (r.baseClass === void 0) return null;
        var a = Et(e, t, r.baseClass);
        return a === null ? null : r.downcast(a);
      },
      Ot = {},
      Er = () => Object.keys(we).length,
      Or = () => {
        var e = [];
        for (var t in we) we.hasOwnProperty(t) && e.push(we[t]);
        return e;
      },
      ye = [],
      et = () => {
        for (; ye.length; ) {
          var e = ye.pop();
          (e.$$.deleteScheduled = !1), e.delete();
        }
      },
      ge,
      Dr = (e) => {
        (ge = e), ye.length && ge && ge(et);
      },
      Mr = () => {
        (i.getInheritedInstanceCount = Er),
          (i.getLiveInheritedInstances = Or),
          (i.flushPendingDeletes = et),
          (i.setDelayFunction = Dr);
      },
      we = {},
      xr = (e, t) => {
        for (t === void 0 && D("ptr should not be undefined"); e.baseClass; )
          (t = e.upcast(t)), (e = e.baseClass);
        return t;
      },
      Rr = (e, t) => ((t = xr(e, t)), we[t]),
      Me = (e, t) => {
        (!t.ptrType || !t.ptr) &&
          De("makeClassHandle requires ptr and ptrType");
        var r = !!t.smartPtrType,
          a = !!t.smartPtr;
        return (
          r !== a && De("Both smartPtrType and smartPtr must be specified"),
          (t.count = { value: 1 }),
          $e(Object.create(e, { $$: { value: t } }))
        );
      };
    function Fr(e) {
      var t = this.getPointee(e);
      if (!t) return this.destructor(e), null;
      var r = Rr(this.registeredClass, t);
      if (r !== void 0) {
        if (r.$$.count.value === 0)
          return (r.$$.ptr = t), (r.$$.smartPtr = e), r.clone();
        var a = r.clone();
        return this.destructor(e), a;
      }
      function s() {
        return this.isSmartPointer
          ? Me(this.registeredClass.instancePrototype, {
              ptrType: this.pointeeType,
              ptr: t,
              smartPtrType: this,
              smartPtr: e,
            })
          : Me(this.registeredClass.instancePrototype, {
              ptrType: this,
              ptr: e,
            });
      }
      var u = this.registeredClass.getActualType(t),
        l = Ot[u];
      if (!l) return s.call(this);
      var f;
      this.isConst ? (f = l.constPointerType) : (f = l.pointerType);
      var d = Et(t, this.registeredClass, f.registeredClass);
      return d === null
        ? s.call(this)
        : this.isSmartPointer
        ? Me(f.registeredClass.instancePrototype, {
            ptrType: f,
            ptr: d,
            smartPtrType: this,
            smartPtr: e,
          })
        : Me(f.registeredClass.instancePrototype, { ptrType: f, ptr: d });
    }
    var $e = (e) =>
        typeof FinalizationRegistry > "u"
          ? (($e = (t) => t), e)
          : ((Ke = new FinalizationRegistry((t) => {
              At(t.$$);
            })),
            ($e = (t) => {
              var r = t.$$,
                a = !!r.smartPtr;
              if (a) {
                var s = { $$: r };
                Ke.register(t, s, t);
              }
              return t;
            }),
            (St = (t) => Ke.unregister(t)),
            $e(e)),
      kr = () => {
        Object.assign(xe.prototype, {
          isAliasOf(e) {
            if (!(this instanceof xe) || !(e instanceof xe)) return !1;
            var t = this.$$.ptrType.registeredClass,
              r = this.$$.ptr;
            e.$$ = e.$$;
            for (
              var a = e.$$.ptrType.registeredClass, s = e.$$.ptr;
              t.baseClass;

            )
              (r = t.upcast(r)), (t = t.baseClass);
            for (; a.baseClass; ) (s = a.upcast(s)), (a = a.baseClass);
            return t === a && r === s;
          },
          clone() {
            if ((this.$$.ptr || Xe(this), this.$$.preservePointerOnDelete))
              return (this.$$.count.value += 1), this;
            var e = $e(
              Object.create(Object.getPrototypeOf(this), {
                $$: { value: Sr(this.$$) },
              })
            );
            return (e.$$.count.value += 1), (e.$$.deleteScheduled = !1), e;
          },
          delete() {
            this.$$.ptr || Xe(this),
              this.$$.deleteScheduled &&
                !this.$$.preservePointerOnDelete &&
                D("Object already scheduled for deletion"),
              St(this),
              At(this.$$),
              this.$$.preservePointerOnDelete ||
                ((this.$$.smartPtr = void 0), (this.$$.ptr = void 0));
          },
          isDeleted() {
            return !this.$$.ptr;
          },
          deleteLater() {
            return (
              this.$$.ptr || Xe(this),
              this.$$.deleteScheduled &&
                !this.$$.preservePointerOnDelete &&
                D("Object already scheduled for deletion"),
              ye.push(this),
              ye.length === 1 && ge && ge(et),
              (this.$$.deleteScheduled = !0),
              this
            );
          },
        });
      };
    function xe() {}
    var Wr = 48,
      jr = 57,
      Dt = (e) => {
        if (e === void 0) return "_unknown";
        e = e.replace(/[^a-zA-Z0-9_]/g, "$");
        var t = e.charCodeAt(0);
        return t >= Wr && t <= jr ? `_${e}` : e;
      };
    function tt(e, t) {
      return (
        (e = Dt(e)),
        {
          [e]: function () {
            return t.apply(this, arguments);
          },
        }[e]
      );
    }
    var Mt = (e, t, r) => {
        if (e[t].overloadTable === void 0) {
          var a = e[t];
          (e[t] = function () {
            return (
              e[t].overloadTable.hasOwnProperty(arguments.length) ||
                D(
                  `Function '${r}' called with an invalid number of arguments (${arguments.length}) - expects one of (${e[t].overloadTable})!`
                ),
              e[t].overloadTable[arguments.length].apply(this, arguments)
            );
          }),
            (e[t].overloadTable = []),
            (e[t].overloadTable[a.argCount] = a);
        }
      },
      rt = (e, t, r) => {
        i.hasOwnProperty(e)
          ? ((r === void 0 ||
              (i[e].overloadTable !== void 0 &&
                i[e].overloadTable[r] !== void 0)) &&
              D(`Cannot register public name '${e}' twice`),
            Mt(i, e, e),
            i.hasOwnProperty(r) &&
              D(
                `Cannot register multiple overloads of a function with the same number of arguments (${r})!`
              ),
            (i[e].overloadTable[r] = t))
          : ((i[e] = t), r !== void 0 && (i[e].numArguments = r));
      };
    function Ir(e, t, r, a, s, u, l, f) {
      (this.name = e),
        (this.constructor = t),
        (this.instancePrototype = r),
        (this.rawDestructor = a),
        (this.baseClass = s),
        (this.getActualType = u),
        (this.upcast = l),
        (this.downcast = f),
        (this.pureVirtualFunctions = []);
    }
    var nt = (e, t, r) => {
      for (; t !== r; )
        t.upcast ||
          D(
            `Expected null or instance of ${r.name}, got an instance of ${t.name}`
          ),
          (e = t.upcast(e)),
          (t = t.baseClass);
      return e;
    };
    function Ur(e, t) {
      if (t === null)
        return this.isReference && D(`null is not a valid ${this.name}`), 0;
      t.$$ || D(`Cannot pass "${st(t)}" as a ${this.name}`),
        t.$$.ptr ||
          D(`Cannot pass deleted object as a pointer of type ${this.name}`);
      var r = t.$$.ptrType.registeredClass,
        a = nt(t.$$.ptr, r, this.registeredClass);
      return a;
    }
    function Hr(e, t) {
      var r;
      if (t === null)
        return (
          this.isReference && D(`null is not a valid ${this.name}`),
          this.isSmartPointer
            ? ((r = this.rawConstructor()),
              e !== null && e.push(this.rawDestructor, r),
              r)
            : 0
        );
      t.$$ || D(`Cannot pass "${st(t)}" as a ${this.name}`),
        t.$$.ptr ||
          D(`Cannot pass deleted object as a pointer of type ${this.name}`),
        !this.isConst &&
          t.$$.ptrType.isConst &&
          D(
            `Cannot convert argument of type ${
              t.$$.smartPtrType ? t.$$.smartPtrType.name : t.$$.ptrType.name
            } to parameter type ${this.name}`
          );
      var a = t.$$.ptrType.registeredClass;
      if (((r = nt(t.$$.ptr, a, this.registeredClass)), this.isSmartPointer))
        switch (
          (t.$$.smartPtr === void 0 &&
            D("Passing raw pointer to smart pointer is illegal"),
          this.sharingPolicy)
        ) {
          case 0:
            t.$$.smartPtrType === this
              ? (r = t.$$.smartPtr)
              : D(
                  `Cannot convert argument of type ${
                    t.$$.smartPtrType
                      ? t.$$.smartPtrType.name
                      : t.$$.ptrType.name
                  } to parameter type ${this.name}`
                );
            break;
          case 1:
            r = t.$$.smartPtr;
            break;
          case 2:
            if (t.$$.smartPtrType === this) r = t.$$.smartPtr;
            else {
              var s = t.clone();
              (r = this.rawShare(
                r,
                se.toHandle(() => s.delete())
              )),
                e !== null && e.push(this.rawDestructor, r);
            }
            break;
          default:
            D("Unsupporting sharing policy");
        }
      return r;
    }
    function Lr(e, t) {
      if (t === null)
        return this.isReference && D(`null is not a valid ${this.name}`), 0;
      t.$$ || D(`Cannot pass "${st(t)}" as a ${this.name}`),
        t.$$.ptr ||
          D(`Cannot pass deleted object as a pointer of type ${this.name}`),
        t.$$.ptrType.isConst &&
          D(
            `Cannot convert argument of type ${t.$$.ptrType.name} to parameter type ${this.name}`
          );
      var r = t.$$.ptrType.registeredClass,
        a = nt(t.$$.ptr, r, this.registeredClass);
      return a;
    }
    function xt(e) {
      return this.fromWireType(x[e >> 2]);
    }
    var Vr = () => {
      Object.assign(Re.prototype, {
        getPointee(e) {
          return this.rawGetPointee && (e = this.rawGetPointee(e)), e;
        },
        destructor(e) {
          this.rawDestructor && this.rawDestructor(e);
        },
        argPackAdvance: re,
        readValueFromPointer: xt,
        deleteObject(e) {
          e !== null && e.delete();
        },
        fromWireType: Fr,
      });
    };
    function Re(e, t, r, a, s, u, l, f, d, v, $) {
      (this.name = e),
        (this.registeredClass = t),
        (this.isReference = r),
        (this.isConst = a),
        (this.isSmartPointer = s),
        (this.pointeeType = u),
        (this.sharingPolicy = l),
        (this.rawGetPointee = f),
        (this.rawConstructor = d),
        (this.rawShare = v),
        (this.rawDestructor = $),
        !s && t.baseClass === void 0
          ? a
            ? ((this.toWireType = Ur), (this.destructorFunction = null))
            : ((this.toWireType = Lr), (this.destructorFunction = null))
          : (this.toWireType = Hr);
    }
    var Rt = (e, t, r) => {
        i.hasOwnProperty(e) || De("Replacing nonexistant public symbol"),
          i[e].overloadTable !== void 0 && r !== void 0
            ? (i[e].overloadTable[r] = t)
            : ((i[e] = t), (i[e].argCount = r));
      },
      Br = (e, t, r) => {
        var a = i["dynCall_" + e];
        return r && r.length ? a.apply(null, [t].concat(r)) : a.call(null, t);
      },
      Fe = [],
      Ft,
      W = (e) => {
        var t = Fe[e];
        return (
          t || (e >= Fe.length && (Fe.length = e + 1), (Fe[e] = t = Ft.get(e))),
          t
        );
      },
      zr = (e, t, r) => {
        if (e.includes("j")) return Br(e, t, r);
        var a = W(t).apply(null, r);
        return a;
      },
      qr = (e, t) => {
        var r = [];
        return function () {
          return (r.length = 0), Object.assign(r, arguments), zr(e, t, r);
        };
      },
      ee = (e, t) => {
        e = q(e);
        function r() {
          return e.includes("j") ? qr(e, t) : W(t);
        }
        var a = r();
        return (
          typeof a != "function" &&
            D(`unknown function pointer with signature ${e}: ${t}`),
          a
        );
      },
      Yr = (e, t) => {
        var r = tt(t, function (a) {
          (this.name = t), (this.message = a);
          var s = new Error(a).stack;
          s !== void 0 &&
            (this.stack =
              this.toString() +
              `
` +
              s.replace(/^Error(:[^\n]*)?\n/, ""));
        });
        return (
          (r.prototype = Object.create(e.prototype)),
          (r.prototype.constructor = r),
          (r.prototype.toString = function () {
            return this.message === void 0
              ? this.name
              : `${this.name}: ${this.message}`;
          }),
          r
        );
      },
      kt,
      Wt = (e) => {
        var t = Nt(e),
          r = q(t);
        return ae(t), r;
      },
      ke = (e, t) => {
        var r = [],
          a = {};
        function s(u) {
          if (!a[u] && !ce[u]) {
            if (Oe[u]) {
              Oe[u].forEach(s);
              return;
            }
            r.push(u), (a[u] = !0);
          }
        }
        throw (t.forEach(s), new kt(`${e}: ` + r.map(Wt).join([", "])));
      },
      Gr = (e, t, r, a, s, u, l, f, d, v, $, _, A) => {
        ($ = q($)),
          (u = ee(s, u)),
          f && (f = ee(l, f)),
          v && (v = ee(d, v)),
          (A = ee(_, A));
        var M = Dt($);
        rt(M, function () {
          ke(`Cannot construct ${$} due to unbound types`, [a]);
        }),
          le([e, t, r], a ? [a] : [], function (U) {
            U = U[0];
            var H, L;
            a
              ? ((H = U.registeredClass), (L = H.instancePrototype))
              : (L = xe.prototype);
            var w = tt(M, function () {
                if (Object.getPrototypeOf(this) !== p)
                  throw new pe("Use 'new' to construct " + $);
                if (T.constructor_body === void 0)
                  throw new pe($ + " has no accessible constructor");
                var rr = T.constructor_body[arguments.length];
                if (rr === void 0)
                  throw new pe(
                    `Tried to invoke ctor of ${$} with invalid number of parameters (${
                      arguments.length
                    }) - expected (${Object.keys(
                      T.constructor_body
                    ).toString()}) parameters instead!`
                  );
                return rr.apply(this, arguments);
              }),
              p = Object.create(L, { constructor: { value: w } });
            w.prototype = p;
            var T = new Ir($, w, p, A, H, u, f, v);
            T.baseClass &&
              (T.baseClass.__derivedClasses === void 0 &&
                (T.baseClass.__derivedClasses = []),
              T.baseClass.__derivedClasses.push(T));
            var B = new Re($, T, !0, !1, !1),
              Q = new Re($ + "*", T, !1, !1, !1),
              me = new Re($ + " const*", T, !1, !0, !1);
            return (
              (Ot[e] = { pointerType: Q, constPointerType: me }),
              Rt(M, w),
              [B, Q, me]
            );
          });
      },
      at = (e, t) => {
        for (var r = [], a = 0; a < e; a++) r.push(x[(t + a * 4) >> 2]);
        return r;
      };
    function ot(e, t, r, a, s, u) {
      var l = t.length;
      l < 2 &&
        D(
          "argTypes array size mismatch! Must at least get return value and 'this' types!"
        );
      for (
        var f = t[1] !== null && r !== null, d = !1, v = 1;
        v < t.length;
        ++v
      )
        if (t[v] !== null && t[v].destructorFunction === void 0) {
          d = !0;
          break;
        }
      var $ = t[0].name !== "void",
        _ = l - 2,
        A = new Array(_),
        M = [],
        U = [];
      return function () {
        arguments.length !== _ &&
          D(
            `function ${e} called with ${arguments.length} arguments, expected ${_}`
          ),
          (U.length = 0);
        var H;
        (M.length = f ? 2 : 1),
          (M[0] = s),
          f && ((H = t[1].toWireType(U, this)), (M[1] = H));
        for (var L = 0; L < _; ++L)
          (A[L] = t[L + 2].toWireType(U, arguments[L])), M.push(A[L]);
        var w = a.apply(null, M);
        function p(T) {
          if (d) _t(U);
          else
            for (var B = f ? 1 : 2; B < t.length; B++) {
              var Q = B === 1 ? H : A[B - 2];
              t[B].destructorFunction !== null && t[B].destructorFunction(Q);
            }
          if ($) return t[0].fromWireType(T);
        }
        return p(w);
      };
    }
    var Nr = (e, t, r, a, s, u) => {
        var l = at(t, r);
        (s = ee(a, s)),
          le([], [e], function (f) {
            f = f[0];
            var d = `constructor ${f.name}`;
            if (
              (f.registeredClass.constructor_body === void 0 &&
                (f.registeredClass.constructor_body = []),
              f.registeredClass.constructor_body[t - 1] !== void 0)
            )
              throw new pe(
                `Cannot register multiple constructors with identical number of parameters (${
                  t - 1
                }) for class '${
                  f.name
                }'! Overload resolution is currently only performed using the parameter count, not actual type info!`
              );
            return (
              (f.registeredClass.constructor_body[t - 1] = () => {
                ke(`Cannot construct ${f.name} due to unbound types`, l);
              }),
              le(
                [],
                l,
                (v) => (
                  v.splice(1, 0, null),
                  (f.registeredClass.constructor_body[t - 1] = ot(
                    d,
                    v,
                    null,
                    s,
                    u
                  )),
                  []
                )
              ),
              []
            );
          });
      },
      jt = (e) => {
        e = e.trim();
        const t = e.indexOf("(");
        return t !== -1
          ? (K(
              e[e.length - 1] == ")",
              "Parentheses for argument names should match."
            ),
            e.substr(0, t))
          : e;
      },
      Jr = (e, t, r, a, s, u, l, f, d) => {
        var v = at(r, a);
        (t = q(t)),
          (t = jt(t)),
          (u = ee(s, u)),
          le([], [e], function ($) {
            $ = $[0];
            var _ = `${$.name}.${t}`;
            t.startsWith("@@") && (t = Symbol[t.substring(2)]),
              f && $.registeredClass.pureVirtualFunctions.push(t);
            function A() {
              ke(`Cannot call ${_} due to unbound types`, v);
            }
            var M = $.registeredClass.instancePrototype,
              U = M[t];
            return (
              U === void 0 ||
              (U.overloadTable === void 0 &&
                U.className !== $.name &&
                U.argCount === r - 2)
                ? ((A.argCount = r - 2), (A.className = $.name), (M[t] = A))
                : (Mt(M, t, _), (M[t].overloadTable[r - 2] = A)),
              le([], v, function (H) {
                var L = ot(_, H, $, u, l);
                return (
                  M[t].overloadTable === void 0
                    ? ((L.argCount = r - 2), (M[t] = L))
                    : (M[t].overloadTable[r - 2] = L),
                  []
                );
              }),
              []
            );
          });
      };
    function Qr() {
      Object.assign(It.prototype, {
        get(e) {
          return this.allocated[e];
        },
        has(e) {
          return this.allocated[e] !== void 0;
        },
        allocate(e) {
          var t = this.freelist.pop() || this.allocated.length;
          return (this.allocated[t] = e), t;
        },
        free(e) {
          (this.allocated[e] = void 0), this.freelist.push(e);
        },
      });
    }
    function It() {
      (this.allocated = [void 0]), (this.freelist = []);
    }
    var X = new It(),
      Ut = (e) => {
        e >= X.reserved && --X.get(e).refcount === 0 && X.free(e);
      },
      Zr = () => {
        for (var e = 0, t = X.reserved; t < X.allocated.length; ++t)
          X.allocated[t] !== void 0 && ++e;
        return e;
      },
      Xr = () => {
        X.allocated.push(
          { value: void 0 },
          { value: null },
          { value: !0 },
          { value: !1 }
        ),
          (X.reserved = X.allocated.length),
          (i.count_emval_handles = Zr);
      },
      se = {
        toValue: (e) => (
          e || D("Cannot use deleted val. handle = " + e), X.get(e).value
        ),
        toHandle: (e) => {
          switch (e) {
            case void 0:
              return 1;
            case null:
              return 2;
            case !0:
              return 3;
            case !1:
              return 4;
            default:
              return X.allocate({ refcount: 1, value: e });
          }
        },
      },
      Kr = (e, t) => {
        (t = q(t)),
          te(e, {
            name: t,
            fromWireType: (r) => {
              var a = se.toValue(r);
              return Ut(r), a;
            },
            toWireType: (r, a) => se.toHandle(a),
            argPackAdvance: re,
            readValueFromPointer: Ze,
            destructorFunction: null,
          });
      },
      en = (e, t, r) => {
        switch (t) {
          case 1:
            return r
              ? function (a) {
                  return this.fromWireType(z[a >> 0]);
                }
              : function (a) {
                  return this.fromWireType(R[a >> 0]);
                };
          case 2:
            return r
              ? function (a) {
                  return this.fromWireType(Y[a >> 1]);
                }
              : function (a) {
                  return this.fromWireType(Z[a >> 1]);
                };
          case 4:
            return r
              ? function (a) {
                  return this.fromWireType(V[a >> 2]);
                }
              : function (a) {
                  return this.fromWireType(x[a >> 2]);
                };
          default:
            throw new TypeError(`invalid integer width (${t}): ${e}`);
        }
      },
      tn = (e, t, r, a) => {
        t = q(t);
        function s() {}
        (s.values = {}),
          te(e, {
            name: t,
            constructor: s,
            fromWireType: function (u) {
              return this.constructor.values[u];
            },
            toWireType: (u, l) => l.value,
            argPackAdvance: re,
            readValueFromPointer: en(t, r, a),
            destructorFunction: null,
          }),
          rt(t, s);
      },
      it = (e, t) => {
        var r = ce[e];
        return r === void 0 && D(t + " has unknown type " + Wt(e)), r;
      },
      rn = (e, t, r) => {
        var a = it(e, "enum");
        t = q(t);
        var s = a.constructor,
          u = Object.create(a.constructor.prototype, {
            value: { value: r },
            constructor: { value: tt(`${a.name}_${t}`, function () {}) },
          });
        (s.values[r] = u), (s[t] = u);
      },
      st = (e) => {
        if (e === null) return "null";
        var t = typeof e;
        return t === "object" || t === "array" || t === "function"
          ? e.toString()
          : "" + e;
      },
      nn = (e, t) => {
        switch (t) {
          case 4:
            return function (r) {
              return this.fromWireType(Pe[r >> 2]);
            };
          case 8:
            return function (r) {
              return this.fromWireType(Te[r >> 3]);
            };
          default:
            throw new TypeError(`invalid float width (${t}): ${e}`);
        }
      },
      an = (e, t, r) => {
        (t = q(t)),
          te(e, {
            name: t,
            fromWireType: (a) => a,
            toWireType: (a, s) => s,
            argPackAdvance: re,
            readValueFromPointer: nn(t, r),
            destructorFunction: null,
          });
      },
      on = (e, t, r, a, s, u, l) => {
        var f = at(t, r);
        (e = q(e)),
          (e = jt(e)),
          (s = ee(a, s)),
          rt(
            e,
            function () {
              ke(`Cannot call ${e} due to unbound types`, f);
            },
            t - 1
          ),
          le([], f, function (d) {
            var v = [d[0], null].concat(d.slice(1));
            return Rt(e, ot(e, v, null, s, u), t - 1), [];
          });
      },
      sn = (e, t, r) => {
        switch (t) {
          case 1:
            return r ? (a) => z[a >> 0] : (a) => R[a >> 0];
          case 2:
            return r ? (a) => Y[a >> 1] : (a) => Z[a >> 1];
          case 4:
            return r ? (a) => V[a >> 2] : (a) => x[a >> 2];
          default:
            throw new TypeError(`invalid integer width (${t}): ${e}`);
        }
      },
      un = (e, t, r, a, s) => {
        t = q(t);
        var u = ($) => $;
        if (a === 0) {
          var l = 32 - 8 * r;
          u = ($) => ($ << l) >>> l;
        }
        var f = t.includes("unsigned"),
          d = ($, _) => {},
          v;
        f
          ? (v = function ($, _) {
              return d(_, this.name), _ >>> 0;
            })
          : (v = function ($, _) {
              return d(_, this.name), _;
            }),
          te(e, {
            name: t,
            fromWireType: u,
            toWireType: v,
            argPackAdvance: re,
            readValueFromPointer: sn(t, r, a !== 0),
            destructorFunction: null,
          });
      },
      cn = (e, t, r) => {
        var a = [
            Int8Array,
            Uint8Array,
            Int16Array,
            Uint16Array,
            Int32Array,
            Uint32Array,
            Float32Array,
            Float64Array,
          ],
          s = a[t];
        function u(l) {
          var f = x[l >> 2],
            d = x[(l + 4) >> 2];
          return new s(z.buffer, d, f);
        }
        (r = q(r)),
          te(
            e,
            {
              name: r,
              fromWireType: u,
              argPackAdvance: re,
              readValueFromPointer: u,
            },
            { ignoreDuplicateRegistrations: !0 }
          );
      },
      Ht = (e, t, r, a) => {
        if (!(a > 0)) return 0;
        for (var s = r, u = r + a - 1, l = 0; l < e.length; ++l) {
          var f = e.charCodeAt(l);
          if (f >= 55296 && f <= 57343) {
            var d = e.charCodeAt(++l);
            f = (65536 + ((f & 1023) << 10)) | (d & 1023);
          }
          if (f <= 127) {
            if (r >= u) break;
            t[r++] = f;
          } else if (f <= 2047) {
            if (r + 1 >= u) break;
            (t[r++] = 192 | (f >> 6)), (t[r++] = 128 | (f & 63));
          } else if (f <= 65535) {
            if (r + 2 >= u) break;
            (t[r++] = 224 | (f >> 12)),
              (t[r++] = 128 | ((f >> 6) & 63)),
              (t[r++] = 128 | (f & 63));
          } else {
            if (r + 3 >= u) break;
            (t[r++] = 240 | (f >> 18)),
              (t[r++] = 128 | ((f >> 12) & 63)),
              (t[r++] = 128 | ((f >> 6) & 63)),
              (t[r++] = 128 | (f & 63));
          }
        }
        return (t[r] = 0), r - s;
      },
      ln = (e, t, r) => Ht(e, R, t, r),
      Lt = (e) => {
        for (var t = 0, r = 0; r < e.length; ++r) {
          var a = e.charCodeAt(r);
          a <= 127
            ? t++
            : a <= 2047
            ? (t += 2)
            : a >= 55296 && a <= 57343
            ? ((t += 4), ++r)
            : (t += 3);
        }
        return t;
      },
      Vt = typeof TextDecoder < "u" ? new TextDecoder("utf8") : void 0,
      fn = (e, t, r) => {
        for (var a = t + r, s = t; e[s] && !(s >= a); ) ++s;
        if (s - t > 16 && e.buffer && Vt) return Vt.decode(e.subarray(t, s));
        for (var u = ""; t < s; ) {
          var l = e[t++];
          if (!(l & 128)) {
            u += String.fromCharCode(l);
            continue;
          }
          var f = e[t++] & 63;
          if ((l & 224) == 192) {
            u += String.fromCharCode(((l & 31) << 6) | f);
            continue;
          }
          var d = e[t++] & 63;
          if (
            ((l & 240) == 224
              ? (l = ((l & 15) << 12) | (f << 6) | d)
              : (l = ((l & 7) << 18) | (f << 12) | (d << 6) | (e[t++] & 63)),
            l < 65536)
          )
            u += String.fromCharCode(l);
          else {
            var v = l - 65536;
            u += String.fromCharCode(55296 | (v >> 10), 56320 | (v & 1023));
          }
        }
        return u;
      },
      ut = (e, t) => (e ? fn(R, e, t) : ""),
      dn = (e, t) => {
        t = q(t);
        var r = t === "std::string";
        te(e, {
          name: t,
          fromWireType(a) {
            var s = x[a >> 2],
              u = a + 4,
              l;
            if (r)
              for (var f = u, d = 0; d <= s; ++d) {
                var v = u + d;
                if (d == s || R[v] == 0) {
                  var $ = v - f,
                    _ = ut(f, $);
                  l === void 0
                    ? (l = _)
                    : ((l += String.fromCharCode(0)), (l += _)),
                    (f = v + 1);
                }
              }
            else {
              for (var A = new Array(s), d = 0; d < s; ++d)
                A[d] = String.fromCharCode(R[u + d]);
              l = A.join("");
            }
            return ae(a), l;
          },
          toWireType(a, s) {
            s instanceof ArrayBuffer && (s = new Uint8Array(s));
            var u,
              l = typeof s == "string";
            l ||
              s instanceof Uint8Array ||
              s instanceof Uint8ClampedArray ||
              s instanceof Int8Array ||
              D("Cannot pass non-string to std::string"),
              r && l ? (u = Lt(s)) : (u = s.length);
            var f = lt(4 + u + 1),
              d = f + 4;
            if (((x[f >> 2] = u), r && l)) ln(s, d, u + 1);
            else if (l)
              for (var v = 0; v < u; ++v) {
                var $ = s.charCodeAt(v);
                $ > 255 &&
                  (ae(d),
                  D("String has UTF-16 code units that do not fit in 8 bits")),
                  (R[d + v] = $);
              }
            else for (var v = 0; v < u; ++v) R[d + v] = s[v];
            return a !== null && a.push(ae, f), f;
          },
          argPackAdvance: re,
          readValueFromPointer: xt,
          destructorFunction(a) {
            ae(a);
          },
        });
      },
      Bt = typeof TextDecoder < "u" ? new TextDecoder("utf-16le") : void 0,
      hn = (e, t) => {
        for (var r = e, a = r >> 1, s = a + t / 2; !(a >= s) && Z[a]; ) ++a;
        if (((r = a << 1), r - e > 32 && Bt))
          return Bt.decode(R.subarray(e, r));
        for (var u = "", l = 0; !(l >= t / 2); ++l) {
          var f = Y[(e + l * 2) >> 1];
          if (f == 0) break;
          u += String.fromCharCode(f);
        }
        return u;
      },
      pn = (e, t, r) => {
        if ((r === void 0 && (r = 2147483647), r < 2)) return 0;
        r -= 2;
        for (
          var a = t, s = r < e.length * 2 ? r / 2 : e.length, u = 0;
          u < s;
          ++u
        ) {
          var l = e.charCodeAt(u);
          (Y[t >> 1] = l), (t += 2);
        }
        return (Y[t >> 1] = 0), t - a;
      },
      vn = (e) => e.length * 2,
      mn = (e, t) => {
        for (var r = 0, a = ""; !(r >= t / 4); ) {
          var s = V[(e + r * 4) >> 2];
          if (s == 0) break;
          if ((++r, s >= 65536)) {
            var u = s - 65536;
            a += String.fromCharCode(55296 | (u >> 10), 56320 | (u & 1023));
          } else a += String.fromCharCode(s);
        }
        return a;
      },
      yn = (e, t, r) => {
        if ((r === void 0 && (r = 2147483647), r < 4)) return 0;
        for (var a = t, s = a + r - 4, u = 0; u < e.length; ++u) {
          var l = e.charCodeAt(u);
          if (l >= 55296 && l <= 57343) {
            var f = e.charCodeAt(++u);
            l = (65536 + ((l & 1023) << 10)) | (f & 1023);
          }
          if (((V[t >> 2] = l), (t += 4), t + 4 > s)) break;
        }
        return (V[t >> 2] = 0), t - a;
      },
      gn = (e) => {
        for (var t = 0, r = 0; r < e.length; ++r) {
          var a = e.charCodeAt(r);
          a >= 55296 && a <= 57343 && ++r, (t += 4);
        }
        return t;
      },
      wn = (e, t, r) => {
        r = q(r);
        var a, s, u, l, f;
        t === 2
          ? ((a = hn), (s = pn), (l = vn), (u = () => Z), (f = 1))
          : t === 4 && ((a = mn), (s = yn), (l = gn), (u = () => x), (f = 2)),
          te(e, {
            name: r,
            fromWireType: (d) => {
              for (
                var v = x[d >> 2], $ = u(), _, A = d + 4, M = 0;
                M <= v;
                ++M
              ) {
                var U = d + 4 + M * t;
                if (M == v || $[U >> f] == 0) {
                  var H = U - A,
                    L = a(A, H);
                  _ === void 0
                    ? (_ = L)
                    : ((_ += String.fromCharCode(0)), (_ += L)),
                    (A = U + t);
                }
              }
              return ae(d), _;
            },
            toWireType: (d, v) => {
              typeof v != "string" &&
                D(`Cannot pass non-string to C++ string type ${r}`);
              var $ = l(v),
                _ = lt(4 + $ + t);
              return (
                (x[_ >> 2] = $ >> f),
                s(v, _ + 4, $ + t),
                d !== null && d.push(ae, _),
                _
              );
            },
            argPackAdvance: re,
            readValueFromPointer: Ze,
            destructorFunction(d) {
              ae(d);
            },
          });
      },
      $n = (e, t, r, a, s, u) => {
        Ee[e] = {
          name: q(t),
          rawConstructor: ee(r, a),
          rawDestructor: ee(s, u),
          fields: [],
        };
      },
      bn = (e, t, r, a, s, u, l, f, d, v) => {
        Ee[e].fields.push({
          fieldName: q(t),
          getterReturnType: r,
          getter: ee(a, s),
          getterContext: u,
          setterArgumentType: l,
          setter: ee(f, d),
          setterContext: v,
        });
      },
      Cn = (e, t) => {
        (t = q(t)),
          te(e, {
            isVoid: !0,
            name: t,
            argPackAdvance: 0,
            fromWireType: () => {},
            toWireType: (r, a) => {},
          });
      },
      _n = {},
      Pn = (e) => {
        var t = _n[e];
        return t === void 0 ? q(e) : t;
      },
      zt = () => {
        if (typeof globalThis == "object") return globalThis;
        function e(t) {
          t.$$$embind_global$$$ = t;
          var r =
            typeof $$$embind_global$$$ == "object" &&
            t.$$$embind_global$$$ == t;
          return r || delete t.$$$embind_global$$$, r;
        }
        if (
          typeof $$$embind_global$$$ == "object" ||
          (typeof global == "object" && e(global)
            ? ($$$embind_global$$$ = global)
            : typeof self == "object" &&
              e(self) &&
              ($$$embind_global$$$ = self),
          typeof $$$embind_global$$$ == "object")
        )
          return $$$embind_global$$$;
        throw Error("unable to get global object.");
      },
      Tn = (e) =>
        e === 0 ? se.toHandle(zt()) : ((e = Pn(e)), se.toHandle(zt()[e])),
      Sn = (e) => {
        e > 4 && (X.get(e).refcount += 1);
      },
      An = (e) => {
        var t = new Array(e + 1);
        return function (r, a, s) {
          t[0] = r;
          for (var u = 0; u < e; ++u) {
            var l = it(x[(a + u * 4) >> 2], "parameter " + u);
            (t[u + 1] = l.readValueFromPointer(s)), (s += l.argPackAdvance);
          }
          var f = new (r.bind.apply(r, t))();
          return se.toHandle(f);
        };
      },
      qt = {},
      En = (e, t, r, a) => {
        e = se.toValue(e);
        var s = qt[t];
        return s || ((s = An(t)), (qt[t] = s)), s(e, r, a);
      },
      On = (e, t) => {
        e = it(e, "_emval_take_value");
        var r = e.readValueFromPointer(t);
        return se.toHandle(r);
      },
      Dn = () => {
        de("");
      },
      Mn = (e, t, r) => R.copyWithin(e, t, t + r),
      xn = () => 2147483648,
      Rn = (e) => {
        var t = N.buffer,
          r = (e - t.byteLength + 65535) / 65536;
        try {
          return N.grow(r), Ie(), 1;
        } catch (a) {}
      },
      Fn = (e) => {
        var t = R.length;
        e >>>= 0;
        var r = xn();
        if (e > r) return !1;
        for (var a = (d, v) => d + ((v - (d % v)) % v), s = 1; s <= 4; s *= 2) {
          var u = t * (1 + 0.2 / s);
          u = Math.min(u, e + 100663296);
          var l = Math.min(r, a(Math.max(e, u), 65536)),
            f = Rn(l);
          if (f) return !0;
        }
        return !1;
      },
      ct = {},
      kn = () => b || "./this.program",
      be = () => {
        if (!be.strings) {
          var e =
              (
                (typeof navigator == "object" &&
                  navigator.languages &&
                  navigator.languages[0]) ||
                "C"
              ).replace("-", "_") + ".UTF-8",
            t = {
              USER: "web_user",
              LOGNAME: "web_user",
              PATH: "/",
              PWD: "/",
              HOME: "/home/<USER>",
              LANG: e,
              _: kn(),
            };
          for (var r in ct) ct[r] === void 0 ? delete t[r] : (t[r] = ct[r]);
          var a = [];
          for (var r in t) a.push(`${r}=${t[r]}`);
          be.strings = a;
        }
        return be.strings;
      },
      Wn = (e, t) => {
        for (var r = 0; r < e.length; ++r) z[t++ >> 0] = e.charCodeAt(r);
        z[t >> 0] = 0;
      },
      jn = (e, t) => {
        var r = 0;
        return (
          be().forEach((a, s) => {
            var u = t + r;
            (x[(e + s * 4) >> 2] = u), Wn(a, u), (r += a.length + 1);
          }),
          0
        );
      },
      In = (e, t) => {
        var r = be();
        x[e >> 2] = r.length;
        var a = 0;
        return r.forEach((s) => (a += s.length + 1)), (x[t >> 2] = a), 0;
      },
      Un = (e) => e,
      We = (e) => e % 4 === 0 && (e % 100 !== 0 || e % 400 === 0),
      Hn = (e, t) => {
        for (var r = 0, a = 0; a <= t; r += e[a++]);
        return r;
      },
      Yt = [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],
      Gt = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],
      Ln = (e, t) => {
        for (var r = new Date(e.getTime()); t > 0; ) {
          var a = We(r.getFullYear()),
            s = r.getMonth(),
            u = (a ? Yt : Gt)[s];
          if (t > u - r.getDate())
            (t -= u - r.getDate() + 1),
              r.setDate(1),
              s < 11
                ? r.setMonth(s + 1)
                : (r.setMonth(0), r.setFullYear(r.getFullYear() + 1));
          else return r.setDate(r.getDate() + t), r;
        }
        return r;
      };
    function Vn(e, t, r) {
      var a = r > 0 ? r : Lt(e) + 1,
        s = new Array(a),
        u = Ht(e, s, 0, s.length);
      return t && (s.length = u), s;
    }
    var Bn = (e, t) => {
        z.set(e, t);
      },
      zn = (e, t, r, a) => {
        var s = x[(a + 40) >> 2],
          u = {
            tm_sec: V[a >> 2],
            tm_min: V[(a + 4) >> 2],
            tm_hour: V[(a + 8) >> 2],
            tm_mday: V[(a + 12) >> 2],
            tm_mon: V[(a + 16) >> 2],
            tm_year: V[(a + 20) >> 2],
            tm_wday: V[(a + 24) >> 2],
            tm_yday: V[(a + 28) >> 2],
            tm_isdst: V[(a + 32) >> 2],
            tm_gmtoff: V[(a + 36) >> 2],
            tm_zone: s ? ut(s) : "",
          },
          l = ut(r),
          f = {
            "%c": "%a %b %d %H:%M:%S %Y",
            "%D": "%m/%d/%y",
            "%F": "%Y-%m-%d",
            "%h": "%b",
            "%r": "%I:%M:%S %p",
            "%R": "%H:%M",
            "%T": "%H:%M:%S",
            "%x": "%m/%d/%y",
            "%X": "%H:%M:%S",
            "%Ec": "%c",
            "%EC": "%C",
            "%Ex": "%m/%d/%y",
            "%EX": "%H:%M:%S",
            "%Ey": "%y",
            "%EY": "%Y",
            "%Od": "%d",
            "%Oe": "%e",
            "%OH": "%H",
            "%OI": "%I",
            "%Om": "%m",
            "%OM": "%M",
            "%OS": "%S",
            "%Ou": "%u",
            "%OU": "%U",
            "%OV": "%V",
            "%Ow": "%w",
            "%OW": "%W",
            "%Oy": "%y",
          };
        for (var d in f) l = l.replace(new RegExp(d, "g"), f[d]);
        var v = [
            "Sunday",
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday",
          ],
          $ = [
            "January",
            "February",
            "March",
            "April",
            "May",
            "June",
            "July",
            "August",
            "September",
            "October",
            "November",
            "December",
          ];
        function _(p, T, B) {
          for (
            var Q = typeof p == "number" ? p.toString() : p || "";
            Q.length < T;

          )
            Q = B[0] + Q;
          return Q;
        }
        function A(p, T) {
          return _(p, T, "0");
        }
        function M(p, T) {
          function B(me) {
            return me < 0 ? -1 : me > 0 ? 1 : 0;
          }
          var Q;
          return (
            (Q = B(p.getFullYear() - T.getFullYear())) === 0 &&
              (Q = B(p.getMonth() - T.getMonth())) === 0 &&
              (Q = B(p.getDate() - T.getDate())),
            Q
          );
        }
        function U(p) {
          switch (p.getDay()) {
            case 0:
              return new Date(p.getFullYear() - 1, 11, 29);
            case 1:
              return p;
            case 2:
              return new Date(p.getFullYear(), 0, 3);
            case 3:
              return new Date(p.getFullYear(), 0, 2);
            case 4:
              return new Date(p.getFullYear(), 0, 1);
            case 5:
              return new Date(p.getFullYear() - 1, 11, 31);
            case 6:
              return new Date(p.getFullYear() - 1, 11, 30);
          }
        }
        function H(p) {
          var T = Ln(new Date(p.tm_year + 1900, 0, 1), p.tm_yday),
            B = new Date(T.getFullYear(), 0, 4),
            Q = new Date(T.getFullYear() + 1, 0, 4),
            me = U(B),
            rr = U(Q);
          return M(me, T) <= 0
            ? M(rr, T) <= 0
              ? T.getFullYear() + 1
              : T.getFullYear()
            : T.getFullYear() - 1;
        }
        var L = {
          "%a": (p) => v[p.tm_wday].substring(0, 3),
          "%A": (p) => v[p.tm_wday],
          "%b": (p) => $[p.tm_mon].substring(0, 3),
          "%B": (p) => $[p.tm_mon],
          "%C": (p) => {
            var T = p.tm_year + 1900;
            return A((T / 100) | 0, 2);
          },
          "%d": (p) => A(p.tm_mday, 2),
          "%e": (p) => _(p.tm_mday, 2, " "),
          "%g": (p) => H(p).toString().substring(2),
          "%G": (p) => H(p),
          "%H": (p) => A(p.tm_hour, 2),
          "%I": (p) => {
            var T = p.tm_hour;
            return T == 0 ? (T = 12) : T > 12 && (T -= 12), A(T, 2);
          },
          "%j": (p) =>
            A(p.tm_mday + Hn(We(p.tm_year + 1900) ? Yt : Gt, p.tm_mon - 1), 3),
          "%m": (p) => A(p.tm_mon + 1, 2),
          "%M": (p) => A(p.tm_min, 2),
          "%n": () => `
`,
          "%p": (p) => (p.tm_hour >= 0 && p.tm_hour < 12 ? "AM" : "PM"),
          "%S": (p) => A(p.tm_sec, 2),
          "%t": () => "	",
          "%u": (p) => p.tm_wday || 7,
          "%U": (p) => {
            var T = p.tm_yday + 7 - p.tm_wday;
            return A(Math.floor(T / 7), 2);
          },
          "%V": (p) => {
            var T = Math.floor((p.tm_yday + 7 - ((p.tm_wday + 6) % 7)) / 7);
            if (((p.tm_wday + 371 - p.tm_yday - 2) % 7 <= 2 && T++, T)) {
              if (T == 53) {
                var B = (p.tm_wday + 371 - p.tm_yday) % 7;
                B != 4 && (B != 3 || !We(p.tm_year)) && (T = 1);
              }
            } else {
              T = 52;
              var Q = (p.tm_wday + 7 - p.tm_yday - 1) % 7;
              (Q == 4 || (Q == 5 && We((p.tm_year % 400) - 1))) && T++;
            }
            return A(T, 2);
          },
          "%w": (p) => p.tm_wday,
          "%W": (p) => {
            var T = p.tm_yday + 7 - ((p.tm_wday + 6) % 7);
            return A(Math.floor(T / 7), 2);
          },
          "%y": (p) => (p.tm_year + 1900).toString().substring(2),
          "%Y": (p) => p.tm_year + 1900,
          "%z": (p) => {
            var T = p.tm_gmtoff,
              B = T >= 0;
            return (
              (T = Math.abs(T) / 60),
              (T = (T / 60) * 100 + (T % 60)),
              (B ? "+" : "-") + ("0000" + T).slice(-4)
            );
          },
          "%Z": (p) => p.tm_zone,
          "%%": () => "%",
        };
        l = l.replace(/%%/g, "\0\0");
        for (var d in L)
          l.includes(d) && (l = l.replace(new RegExp(d, "g"), L[d](u)));
        l = l.replace(/\0\0/g, "%");
        var w = Vn(l, !1);
        return w.length > t ? 0 : (Bn(w, e), w.length - 1);
      },
      qn = (e, t, r, a, s) => zn(e, t, r, a);
    (Pt = i.InternalError =
      class extends Error {
        constructor(e) {
          super(e), (this.name = "InternalError");
        }
      }),
      _r(),
      (pe = i.BindingError =
        class extends Error {
          constructor(e) {
            super(e), (this.name = "BindingError");
          }
        }),
      kr(),
      Mr(),
      Vr(),
      (kt = i.UnboundTypeError = Yr(Error, "UnboundTypeError")),
      Qr(),
      Xr();
    var Yn = {
        s: dr,
        v: hr,
        b: vr,
        g: mr,
        m: yr,
        K: gr,
        e: wr,
        T: $r,
        d: pr,
        ba: br,
        P: Cr,
        Y: Tr,
        aa: Gr,
        $: Nr,
        x: Jr,
        X: Kr,
        y: tn,
        j: rn,
        M: an,
        D: on,
        t: un,
        p: cn,
        L: dn,
        C: wn,
        w: $n,
        ca: bn,
        Z: Cn,
        fa: Ut,
        da: Tn,
        N: Sn,
        W: En,
        _: On,
        B: Dn,
        V: Mn,
        U: Fn,
        R: jn,
        S: In,
        G: ua,
        H: sa,
        n: ca,
        a: Gn,
        f: Zn,
        q: ea,
        k: Qn,
        J: oa,
        u: aa,
        I: ia,
        A: fa,
        O: ht,
        l: Xn,
        i: Kn,
        c: Jn,
        o: Nn,
        E: ra,
        ea: ta,
        r: la,
        h: na,
        z: da,
        F: Un,
        Q: qn,
      },
      k = fr(),
      ae = (i._free = (e) => (ae = i._free = k.ia)(e)),
      lt = (i._malloc = (e) => (lt = i._malloc = k.ja)(e)),
      Nt = (e) => (Nt = k.la)(e);
    i.__embind_initialize_bindings = () =>
      (i.__embind_initialize_bindings = k.ma)();
    var F = (e, t) => (F = k.na)(e, t),
      Ce = (e) => (Ce = k.oa)(e),
      j = () => (j = k.pa)(),
      I = (e) => (I = k.qa)(e),
      Jt = (e) => (Jt = k.ra)(e),
      Qt = (e) => (Qt = k.sa)(e),
      Zt = (e, t, r) => (Zt = k.ta)(e, t, r),
      Xt = (e) => (Xt = k.ua)(e);
    i.dynCall_viijii = (e, t, r, a, s, u, l) =>
      (i.dynCall_viijii = k.va)(e, t, r, a, s, u, l);
    var Kt = (i.dynCall_jiiii = (e, t, r, a, s) =>
      (Kt = i.dynCall_jiiii = k.wa)(e, t, r, a, s));
    (i.dynCall_iiiiij = (e, t, r, a, s, u, l) =>
      (i.dynCall_iiiiij = k.xa)(e, t, r, a, s, u, l)),
      (i.dynCall_iiiiijj = (e, t, r, a, s, u, l, f, d) =>
        (i.dynCall_iiiiijj = k.ya)(e, t, r, a, s, u, l, f, d)),
      (i.dynCall_iiiiiijj = (e, t, r, a, s, u, l, f, d, v) =>
        (i.dynCall_iiiiiijj = k.za)(e, t, r, a, s, u, l, f, d, v));
    function Gn(e, t) {
      var r = j();
      try {
        return W(e)(t);
      } catch (a) {
        if ((I(r), a !== a + 0)) throw a;
        F(1, 0);
      }
    }
    function Nn(e, t, r, a) {
      var s = j();
      try {
        W(e)(t, r, a);
      } catch (u) {
        if ((I(s), u !== u + 0)) throw u;
        F(1, 0);
      }
    }
    function Jn(e, t, r) {
      var a = j();
      try {
        W(e)(t, r);
      } catch (s) {
        if ((I(a), s !== s + 0)) throw s;
        F(1, 0);
      }
    }
    function Qn(e, t, r, a, s) {
      var u = j();
      try {
        return W(e)(t, r, a, s);
      } catch (l) {
        if ((I(u), l !== l + 0)) throw l;
        F(1, 0);
      }
    }
    function Zn(e, t, r) {
      var a = j();
      try {
        return W(e)(t, r);
      } catch (s) {
        if ((I(a), s !== s + 0)) throw s;
        F(1, 0);
      }
    }
    function Xn(e) {
      var t = j();
      try {
        W(e)();
      } catch (r) {
        if ((I(t), r !== r + 0)) throw r;
        F(1, 0);
      }
    }
    function Kn(e, t) {
      var r = j();
      try {
        W(e)(t);
      } catch (a) {
        if ((I(r), a !== a + 0)) throw a;
        F(1, 0);
      }
    }
    function ea(e, t, r, a) {
      var s = j();
      try {
        return W(e)(t, r, a);
      } catch (u) {
        if ((I(s), u !== u + 0)) throw u;
        F(1, 0);
      }
    }
    function ta(e, t, r, a, s, u) {
      var l = j();
      try {
        W(e)(t, r, a, s, u);
      } catch (f) {
        if ((I(l), f !== f + 0)) throw f;
        F(1, 0);
      }
    }
    function ra(e, t, r, a, s) {
      var u = j();
      try {
        W(e)(t, r, a, s);
      } catch (l) {
        if ((I(u), l !== l + 0)) throw l;
        F(1, 0);
      }
    }
    function na(e, t, r, a, s, u, l, f, d, v, $) {
      var _ = j();
      try {
        W(e)(t, r, a, s, u, l, f, d, v, $);
      } catch (A) {
        if ((I(_), A !== A + 0)) throw A;
        F(1, 0);
      }
    }
    function aa(e, t, r, a, s, u, l) {
      var f = j();
      try {
        return W(e)(t, r, a, s, u, l);
      } catch (d) {
        if ((I(f), d !== d + 0)) throw d;
        F(1, 0);
      }
    }
    function oa(e, t, r, a, s, u) {
      var l = j();
      try {
        return W(e)(t, r, a, s, u);
      } catch (f) {
        if ((I(l), f !== f + 0)) throw f;
        F(1, 0);
      }
    }
    function ia(e, t, r, a, s, u, l, f) {
      var d = j();
      try {
        return W(e)(t, r, a, s, u, l, f);
      } catch (v) {
        if ((I(d), v !== v + 0)) throw v;
        F(1, 0);
      }
    }
    function sa(e, t, r, a) {
      var s = j();
      try {
        return W(e)(t, r, a);
      } catch (u) {
        if ((I(s), u !== u + 0)) throw u;
        F(1, 0);
      }
    }
    function ua(e, t, r, a) {
      var s = j();
      try {
        return W(e)(t, r, a);
      } catch (u) {
        if ((I(s), u !== u + 0)) throw u;
        F(1, 0);
      }
    }
    function ca(e) {
      var t = j();
      try {
        return W(e)();
      } catch (r) {
        if ((I(t), r !== r + 0)) throw r;
        F(1, 0);
      }
    }
    function la(e, t, r, a, s, u, l, f) {
      var d = j();
      try {
        W(e)(t, r, a, s, u, l, f);
      } catch (v) {
        if ((I(d), v !== v + 0)) throw v;
        F(1, 0);
      }
    }
    function fa(e, t, r, a, s, u, l, f, d, v, $, _) {
      var A = j();
      try {
        return W(e)(t, r, a, s, u, l, f, d, v, $, _);
      } catch (M) {
        if ((I(A), M !== M + 0)) throw M;
        F(1, 0);
      }
    }
    function da(e, t, r, a, s, u, l, f, d, v, $, _, A, M, U, H) {
      var L = j();
      try {
        W(e)(t, r, a, s, u, l, f, d, v, $, _, A, M, U, H);
      } catch (w) {
        if ((I(L), w !== w + 0)) throw w;
        F(1, 0);
      }
    }
    function ht(e, t, r, a, s) {
      var u = j();
      try {
        return Kt(e, t, r, a, s);
      } catch (l) {
        if ((I(u), l !== l + 0)) throw l;
        F(1, 0);
      }
    }
    var ft;
    oe = function e() {
      ft || n(), ft || (oe = e);
    };
    function n() {
      if (ie > 0 || (mt(), ie > 0)) return;
      function e() {
        ft ||
          ((ft = !0),
          (i.calledRun = !0),
          !J &&
            (yt(),
            h(i),
            i.onRuntimeInitialized && i.onRuntimeInitialized(),
            gt()));
      }
      i.setStatus
        ? (i.setStatus("Running..."),
          setTimeout(function () {
            setTimeout(function () {
              i.setStatus("");
            }, 1),
              e();
          }, 1))
        : e();
    }
    if (i.preInit)
      for (
        typeof i.preInit == "function" && (i.preInit = [i.preInit]);
        i.preInit.length > 0;

      )
        i.preInit.pop()();
    return n(), c.ready;
  };
})();
function Fi(o) {
  return ko(Lo, o);
}
const ya = /* @__PURE__ */ new Map([
  ["aztec", "Aztec"],
  ["code_128", "Code128"],
  ["code_39", "Code39"],
  ["code_93", "Code93"],
  ["codabar", "Codabar"],
  ["data_matrix", "DataMatrix"],
  ["ean_13", "EAN-13"],
  ["ean_8", "EAN-8"],
  ["itf", "ITF"],
  ["pdf417", "PDF417"],
  ["qr_code", "QRCode"],
  ["upc_a", "UPC-A"],
  ["upc_e", "UPC-E"],
]);
function Vo(o) {
  for (const [c, i] of ya) if (o === i) return c;
  return "unknown";
}
var tr;
class ur extends EventTarget {
  constructor(c = {}) {
    var i;
    super(), ho(this, tr, void 0);
    try {
      const h =
        (i = c == null ? void 0 : c.formats) == null
          ? void 0
          : i.filter((m) => m !== "unknown");
      if ((h == null ? void 0 : h.length) === 0)
        throw new TypeError("Hint option provided, but is empty.");
      h == null ||
        h.forEach((m) => {
          if (!Sa.includes(m))
            throw new TypeError(
              `Failed to read the 'formats' property from 'BarcodeDetectorOptions': The provided value '${m}' is not a valid enum value of type BarcodeFormat.`
            );
        }),
        po(this, tr, h != null ? h : []),
        Io()
          .then((m) => {
            this.dispatchEvent(
              new CustomEvent("load", {
                detail: m,
              })
            );
          })
          .catch((m) => {
            this.dispatchEvent(new CustomEvent("error", { detail: m }));
          });
    } catch (h) {
      throw Aa(h, "Failed to construct 'BarcodeDetector'");
    }
  }
  static async getSupportedFormats() {
    return Sa.filter((c) => c !== "unknown");
  }
  async detect(c) {
    try {
      const i = await $o(c);
      if (i === null) return [];
      let h;
      try {
        Ba(i)
          ? (h = await Uo(i, {
              tryHarder: !0,
              formats: Ta(this, tr).map((m) => ya.get(m)),
            }))
          : (h = await Ho(i, {
              tryHarder: !0,
              formats: Ta(this, tr).map((m) => ya.get(m)),
            }));
      } catch (m) {
        throw (
          (console.error(m),
          new DOMException(
            "Barcode detection service unavailable.",
            "NotSupportedError"
          ))
        );
      }
      return h.map((m) => {
        const {
            topLeft: { x: y, y: b },
            topRight: { x: C, y: P },
            bottomLeft: { x: g, y: E },
            bottomRight: { x: O, y: S },
          } = m.position,
          G = Math.min(y, C, g, O),
          N = Math.min(b, P, E, S),
          J = Math.max(y, C, g, O),
          K = Math.max(b, P, E, S);
        return {
          boundingBox: new DOMRectReadOnly(G, N, J - G, K - N),
          rawValue: m.text,
          format: Vo(m.format),
          cornerPoints: [
            {
              x: y,
              y: b,
            },
            {
              x: C,
              y: P,
            },
            {
              x: O,
              y: S,
            },
            {
              x: g,
              y: E,
            },
          ],
        };
      });
    } catch (i) {
      throw Aa(i, "Failed to execute 'detect' on 'BarcodeDetector'");
    }
  }
}
tr = /* @__PURE__ */ new WeakMap();
const Pa = (o, c, i = "error") => {
    let h, m;
    const y = new Promise((b, C) => {
      (h = b), (m = C), o.addEventListener(c, h), o.addEventListener(i, m);
    });
    return (
      y.finally(() => {
        o.removeEventListener(c, h), o.removeEventListener(i, m);
      }),
      y
    );
  },
  Da = (o) => new Promise((c) => setTimeout(c, o));
class Bo extends Error {
  constructor() {
    super("can't process cross-origin image"),
      (this.name = "DropImageFetchError");
  }
}
class Ga extends Error {
  constructor() {
    super("this browser has no Stream API support"),
      (this.name = "StreamApiNotSupportedError");
  }
}
class zo extends Error {
  constructor() {
    super(
      "camera access is only permitted in secure context. Use HTTPS or localhost rather than HTTP."
    ),
      (this.name = "InsecureContextError");
  }
}
class qo extends Error {
  constructor() {
    super(
      "Loading camera stream timed out after 3 seconds. If you are on iOS in PWA mode, this is a known issue (see https://github.com/gruhn/vue-qrcode-reader/issues/298)"
    ),
      (this.name = "StreamLoadTimeoutError");
  }
}
let ga;
const Yo = (o) => {
    ga = new ur({ formats: o });
  },
  Go = async (
    o,
    { detectHandler: c, locateHandler: i, minDelay: h, formats: m }
  ) => {
    ga = new ur({ formats: m });
    const y = (b) => async (C) => {
      if (o.readyState > 1) {
        const { lastScanned: P, contentBefore: g, lastScanHadContent: E } = b;
        if (C - P < h) window.requestAnimationFrame(y(b));
        else {
          const O = await ga.detect(o),
            S = O.some((J) => !g.includes(J.rawValue));
          S && c(O);
          const G = O.length > 0;
          G && i(O), !G && E && i(O);
          const N = {
            lastScanned: C,
            lastScanHadContent: G,
            // It can happen that a QR code is constantly in view of the camera but
            // maybe a scanned frame is a bit blurry and we detect nothing but in the
            // next frame we detect the code again. We also want to avoid emitting
            // a `detect` event in such a case. So we don't reset `contentBefore`,
            // if we detect nothing, only if we detect something new.
            contentBefore: S ? O.map((J) => J.rawValue) : g,
          };
          window.requestAnimationFrame(y(N));
        }
      }
    };
    y({
      lastScanned: performance.now(),
      contentBefore: [],
      lastScanHadContent: !1,
    })(performance.now());
  },
  No = async (o) => {
    if (o.startsWith("http") && o.includes(location.host) === !1)
      throw new Bo();
    const c = document.createElement("img");
    return (c.src = o), await Pa(c, "load"), c;
  },
  Na = async (o, c = ["qr_code"]) =>
    await new ur({
      formats: c,
    }).detect(o),
  Jo = async (o, c = ["qr_code"]) => {
    const i = new ur({
        formats: c,
      }),
      h = await No(o);
    return await i.detect(h);
  };
var Ja = {},
  fe = {};
Object.defineProperty(fe, "__esModule", {
  value: !0,
});
fe.compactObject = Xa;
fe.deprecated = ai;
var Qo = (fe.detectBrowser = oi);
fe.disableLog = ti;
fe.disableWarnings = ri;
fe.extractVersion = or;
fe.filterStats = ii;
fe.log = ni;
fe.walkStats = sr;
fe.wrapPeerConnectionEvent = ei;
function Zo(o, c, i) {
  return (
    (c = Xo(c)),
    c in o
      ? Object.defineProperty(o, c, {
          value: i,
          enumerable: !0,
          configurable: !0,
          writable: !0,
        })
      : (o[c] = i),
    o
  );
}
function Xo(o) {
  var c = Ko(o, "string");
  return Ve(c) === "symbol" ? c : String(c);
}
function Ko(o, c) {
  if (Ve(o) !== "object" || o === null) return o;
  var i = o[Symbol.toPrimitive];
  if (i !== void 0) {
    var h = i.call(o, c || "default");
    if (Ve(h) !== "object") return h;
    throw new TypeError("@@toPrimitive must return a primitive value.");
  }
  return (c === "string" ? String : Number)(o);
}
function Ve(o) {
  "@babel/helpers - typeof";
  return (
    (Ve =
      typeof Symbol == "function" && typeof Symbol.iterator == "symbol"
        ? function (c) {
            return typeof c;
          }
        : function (c) {
            return c &&
              typeof Symbol == "function" &&
              c.constructor === Symbol &&
              c !== Symbol.prototype
              ? "symbol"
              : typeof c;
          }),
    Ve(o)
  );
}
var Qa = !0,
  Za = !0;
function or(o, c, i) {
  var h = o.match(c);
  return h && h.length >= i && parseInt(h[i], 10);
}
function ei(o, c, i) {
  if (o.RTCPeerConnection) {
    var h = o.RTCPeerConnection.prototype,
      m = h.addEventListener;
    h.addEventListener = function (b, C) {
      if (b !== c) return m.apply(this, arguments);
      var P = function (E) {
        var O = i(E);
        O && (C.handleEvent ? C.handleEvent(O) : C(O));
      };
      return (
        (this._eventMap = this._eventMap || {}),
        this._eventMap[c] || (this._eventMap[c] = /* @__PURE__ */ new Map()),
        this._eventMap[c].set(C, P),
        m.apply(this, [b, P])
      );
    };
    var y = h.removeEventListener;
    (h.removeEventListener = function (b, C) {
      if (b !== c || !this._eventMap || !this._eventMap[c])
        return y.apply(this, arguments);
      if (!this._eventMap[c].has(C)) return y.apply(this, arguments);
      var P = this._eventMap[c].get(C);
      return (
        this._eventMap[c].delete(C),
        this._eventMap[c].size === 0 && delete this._eventMap[c],
        Object.keys(this._eventMap).length === 0 && delete this._eventMap,
        y.apply(this, [b, P])
      );
    }),
      Object.defineProperty(h, "on" + c, {
        get: function () {
          return this["_on" + c];
        },
        set: function (C) {
          this["_on" + c] &&
            (this.removeEventListener(c, this["_on" + c]),
            delete this["_on" + c]),
            C && this.addEventListener(c, (this["_on" + c] = C));
        },
        enumerable: !0,
        configurable: !0,
      });
  }
}
function ti(o) {
  return typeof o != "boolean"
    ? new Error("Argument type: " + Ve(o) + ". Please use a boolean.")
    : ((Qa = o),
      o ? "adapter.js logging disabled" : "adapter.js logging enabled");
}
function ri(o) {
  return typeof o != "boolean"
    ? new Error("Argument type: " + Ve(o) + ". Please use a boolean.")
    : ((Za = !o),
      "adapter.js deprecation warnings " + (o ? "disabled" : "enabled"));
}
function ni() {
  if ((typeof window > "u" ? "undefined" : Ve(window)) === "object") {
    if (Qa) return;
    typeof console < "u" &&
      typeof console.log == "function" &&
      console.log.apply(console, arguments);
  }
}
function ai(o, c) {
  Za && console.warn(o + " is deprecated, please use " + c + " instead.");
}
function oi(o) {
  var c = {
    browser: null,
    version: null,
  };
  if (typeof o > "u" || !o.navigator || !o.navigator.userAgent)
    return (c.browser = "Not a browser."), c;
  var i = o.navigator;
  if (i.mozGetUserMedia)
    (c.browser = "firefox"),
      (c.version = or(i.userAgent, /Firefox\/(\d+)\./, 1));
  else if (
    i.webkitGetUserMedia ||
    (o.isSecureContext === !1 && o.webkitRTCPeerConnection)
  )
    (c.browser = "chrome"),
      (c.version = or(i.userAgent, /Chrom(e|ium)\/(\d+)\./, 2));
  else if (o.RTCPeerConnection && i.userAgent.match(/AppleWebKit\/(\d+)\./))
    (c.browser = "safari"),
      (c.version = or(i.userAgent, /AppleWebKit\/(\d+)\./, 1)),
      (c.supportsUnifiedPlan =
        o.RTCRtpTransceiver &&
        "currentDirection" in o.RTCRtpTransceiver.prototype);
  else return (c.browser = "Not a supported browser."), c;
  return c;
}
function Ma(o) {
  return Object.prototype.toString.call(o) === "[object Object]";
}
function Xa(o) {
  return Ma(o)
    ? Object.keys(o).reduce(function (c, i) {
        var h = Ma(o[i]),
          m = h ? Xa(o[i]) : o[i],
          y = h && !Object.keys(m).length;
        return m === void 0 || y ? c : Object.assign(c, Zo({}, i, m));
      }, {})
    : o;
}
function sr(o, c, i) {
  !c ||
    i.has(c.id) ||
    (i.set(c.id, c),
    Object.keys(c).forEach(function (h) {
      h.endsWith("Id")
        ? sr(o, o.get(c[h]), i)
        : h.endsWith("Ids") &&
          c[h].forEach(function (m) {
            sr(o, o.get(m), i);
          });
    }));
}
function ii(o, c, i) {
  var h = i ? "outbound-rtp" : "inbound-rtp",
    m = /* @__PURE__ */ new Map();
  if (c === null) return m;
  var y = [];
  return (
    o.forEach(function (b) {
      b.type === "track" && b.trackIdentifier === c.id && y.push(b);
    }),
    y.forEach(function (b) {
      o.forEach(function (C) {
        C.type === h && C.trackId === b.id && sr(o, C, m);
      });
    }),
    m
  );
}
Object.defineProperty(Ja, "__esModule", {
  value: !0,
});
var si = (Ja.shimGetUserMedia = li),
  ui = ci(fe);
function Ka(o) {
  if (typeof WeakMap != "function") return null;
  var c = /* @__PURE__ */ new WeakMap(),
    i = /* @__PURE__ */ new WeakMap();
  return (Ka = function (m) {
    return m ? i : c;
  })(o);
}
function ci(o, c) {
  if (!c && o && o.__esModule) return o;
  if (o === null || (Le(o) !== "object" && typeof o != "function"))
    return { default: o };
  var i = Ka(c);
  if (i && i.has(o)) return i.get(o);
  var h = {},
    m = Object.defineProperty && Object.getOwnPropertyDescriptor;
  for (var y in o)
    if (y !== "default" && Object.prototype.hasOwnProperty.call(o, y)) {
      var b = m ? Object.getOwnPropertyDescriptor(o, y) : null;
      b && (b.get || b.set) ? Object.defineProperty(h, y, b) : (h[y] = o[y]);
    }
  return (h.default = o), i && i.set(o, h), h;
}
function Le(o) {
  "@babel/helpers - typeof";
  return (
    (Le =
      typeof Symbol == "function" && typeof Symbol.iterator == "symbol"
        ? function (c) {
            return typeof c;
          }
        : function (c) {
            return c &&
              typeof Symbol == "function" &&
              c.constructor === Symbol &&
              c !== Symbol.prototype
              ? "symbol"
              : typeof c;
          }),
    Le(o)
  );
}
var xa = ui.log;
function li(o, c) {
  var i = o && o.navigator;
  if (i.mediaDevices) {
    var h = function (g) {
        if (Le(g) !== "object" || g.mandatory || g.optional) return g;
        var E = {};
        return (
          Object.keys(g).forEach(function (O) {
            if (!(O === "require" || O === "advanced" || O === "mediaSource")) {
              var S =
                Le(g[O]) === "object"
                  ? g[O]
                  : {
                      ideal: g[O],
                    };
              S.exact !== void 0 &&
                typeof S.exact == "number" &&
                (S.min = S.max = S.exact);
              var G = function (K, z) {
                return K
                  ? K + z.charAt(0).toUpperCase() + z.slice(1)
                  : z === "deviceId"
                  ? "sourceId"
                  : z;
              };
              if (S.ideal !== void 0) {
                E.optional = E.optional || [];
                var N = {};
                typeof S.ideal == "number"
                  ? ((N[G("min", O)] = S.ideal),
                    E.optional.push(N),
                    (N = {}),
                    (N[G("max", O)] = S.ideal),
                    E.optional.push(N))
                  : ((N[G("", O)] = S.ideal), E.optional.push(N));
              }
              S.exact !== void 0 && typeof S.exact != "number"
                ? ((E.mandatory = E.mandatory || {}),
                  (E.mandatory[G("", O)] = S.exact))
                : ["min", "max"].forEach(function (J) {
                    S[J] !== void 0 &&
                      ((E.mandatory = E.mandatory || {}),
                      (E.mandatory[G(J, O)] = S[J]));
                  });
            }
          }),
          g.advanced && (E.optional = (E.optional || []).concat(g.advanced)),
          E
        );
      },
      m = function (g, E) {
        if (c.version >= 61) return E(g);
        if (
          ((g = JSON.parse(JSON.stringify(g))), g && Le(g.audio) === "object")
        ) {
          var O = function (K, z, R) {
            z in K && !(R in K) && ((K[R] = K[z]), delete K[z]);
          };
          (g = JSON.parse(JSON.stringify(g))),
            O(g.audio, "autoGainControl", "googAutoGainControl"),
            O(g.audio, "noiseSuppression", "googNoiseSuppression"),
            (g.audio = h(g.audio));
        }
        if (g && Le(g.video) === "object") {
          var S = g.video.facingMode;
          S =
            S &&
            (Le(S) === "object"
              ? S
              : {
                  ideal: S,
                });
          var G = c.version < 66;
          if (
            S &&
            (S.exact === "user" ||
              S.exact === "environment" ||
              S.ideal === "user" ||
              S.ideal === "environment") &&
            !(
              i.mediaDevices.getSupportedConstraints &&
              i.mediaDevices.getSupportedConstraints().facingMode &&
              !G
            )
          ) {
            delete g.video.facingMode;
            var N;
            if (
              (S.exact === "environment" || S.ideal === "environment"
                ? (N = ["back", "rear"])
                : (S.exact === "user" || S.ideal === "user") && (N = ["front"]),
              N)
            )
              return i.mediaDevices.enumerateDevices().then(function (J) {
                J = J.filter(function (z) {
                  return z.kind === "videoinput";
                });
                var K = J.find(function (z) {
                  return N.some(function (R) {
                    return z.label.toLowerCase().includes(R);
                  });
                });
                return (
                  !K && J.length && N.includes("back") && (K = J[J.length - 1]),
                  K &&
                    (g.video.deviceId = S.exact
                      ? {
                          exact: K.deviceId,
                        }
                      : {
                          ideal: K.deviceId,
                        }),
                  (g.video = h(g.video)),
                  xa("chrome: " + JSON.stringify(g)),
                  E(g)
                );
              });
          }
          g.video = h(g.video);
        }
        return xa("chrome: " + JSON.stringify(g)), E(g);
      },
      y = function (g) {
        return c.version >= 64
          ? g
          : {
              name:
                {
                  PermissionDeniedError: "NotAllowedError",
                  PermissionDismissedError: "NotAllowedError",
                  InvalidStateError: "NotAllowedError",
                  DevicesNotFoundError: "NotFoundError",
                  ConstraintNotSatisfiedError: "OverconstrainedError",
                  TrackStartError: "NotReadableError",
                  MediaDeviceFailedDueToShutdown: "NotAllowedError",
                  MediaDeviceKillSwitchOn: "NotAllowedError",
                  TabCaptureError: "AbortError",
                  ScreenCaptureError: "AbortError",
                  DeviceCaptureError: "AbortError",
                }[g.name] || g.name,
              message: g.message,
              constraint: g.constraint || g.constraintName,
              toString: function () {
                return this.name + (this.message && ": ") + this.message;
              },
            };
      },
      b = function (g, E, O) {
        m(g, function (S) {
          i.webkitGetUserMedia(S, E, function (G) {
            O && O(y(G));
          });
        });
      };
    if (((i.getUserMedia = b.bind(i)), i.mediaDevices.getUserMedia)) {
      var C = i.mediaDevices.getUserMedia.bind(i.mediaDevices);
      i.mediaDevices.getUserMedia = function (P) {
        return m(P, function (g) {
          return C(g).then(
            function (E) {
              if (
                (g.audio && !E.getAudioTracks().length) ||
                (g.video && !E.getVideoTracks().length)
              )
                throw (
                  (E.getTracks().forEach(function (O) {
                    O.stop();
                  }),
                  new DOMException("", "NotFoundError"))
                );
              return E;
            },
            function (E) {
              return Promise.reject(y(E));
            }
          );
        });
      };
    }
  }
}
var eo = {};
Object.defineProperty(eo, "__esModule", {
  value: !0,
});
var fi = (eo.shimGetUserMedia = pi),
  di = hi(fe);
function to(o) {
  if (typeof WeakMap != "function") return null;
  var c = /* @__PURE__ */ new WeakMap(),
    i = /* @__PURE__ */ new WeakMap();
  return (to = function (m) {
    return m ? i : c;
  })(o);
}
function hi(o, c) {
  if (!c && o && o.__esModule) return o;
  if (o === null || (pt(o) !== "object" && typeof o != "function"))
    return { default: o };
  var i = to(c);
  if (i && i.has(o)) return i.get(o);
  var h = {},
    m = Object.defineProperty && Object.getOwnPropertyDescriptor;
  for (var y in o)
    if (y !== "default" && Object.prototype.hasOwnProperty.call(o, y)) {
      var b = m ? Object.getOwnPropertyDescriptor(o, y) : null;
      b && (b.get || b.set) ? Object.defineProperty(h, y, b) : (h[y] = o[y]);
    }
  return (h.default = o), i && i.set(o, h), h;
}
function pt(o) {
  "@babel/helpers - typeof";
  return (
    (pt =
      typeof Symbol == "function" && typeof Symbol.iterator == "symbol"
        ? function (c) {
            return typeof c;
          }
        : function (c) {
            return c &&
              typeof Symbol == "function" &&
              c.constructor === Symbol &&
              c !== Symbol.prototype
              ? "symbol"
              : typeof c;
          }),
    pt(o)
  );
}
function pi(o, c) {
  var i = o && o.navigator,
    h = o && o.MediaStreamTrack;
  if (
    ((i.getUserMedia = function (P, g, E) {
      di.deprecated(
        "navigator.getUserMedia",
        "navigator.mediaDevices.getUserMedia"
      ),
        i.mediaDevices.getUserMedia(P).then(g, E);
    }),
    !(
      c.version > 55 &&
      "autoGainControl" in i.mediaDevices.getSupportedConstraints()
    ))
  ) {
    var m = function (g, E, O) {
        E in g && !(O in g) && ((g[O] = g[E]), delete g[E]);
      },
      y = i.mediaDevices.getUserMedia.bind(i.mediaDevices);
    if (
      ((i.mediaDevices.getUserMedia = function (P) {
        return (
          pt(P) === "object" &&
            pt(P.audio) === "object" &&
            ((P = JSON.parse(JSON.stringify(P))),
            m(P.audio, "autoGainControl", "mozAutoGainControl"),
            m(P.audio, "noiseSuppression", "mozNoiseSuppression")),
          y(P)
        );
      }),
      h && h.prototype.getSettings)
    ) {
      var b = h.prototype.getSettings;
      h.prototype.getSettings = function () {
        var P = b.apply(this, arguments);
        return (
          m(P, "mozAutoGainControl", "autoGainControl"),
          m(P, "mozNoiseSuppression", "noiseSuppression"),
          P
        );
      };
    }
    if (h && h.prototype.applyConstraints) {
      var C = h.prototype.applyConstraints;
      h.prototype.applyConstraints = function (P) {
        return (
          this.kind === "audio" &&
            pt(P) === "object" &&
            ((P = JSON.parse(JSON.stringify(P))),
            m(P, "autoGainControl", "mozAutoGainControl"),
            m(P, "noiseSuppression", "mozNoiseSuppression")),
          C.apply(this, [P])
        );
      };
    }
  }
}
var _e = {};
Object.defineProperty(_e, "__esModule", {
  value: !0,
});
_e.shimAudioContext = Pi;
_e.shimCallbacksAPI = wi;
_e.shimConstraints = ao;
_e.shimCreateOfferLegacy = _i;
var vi = (_e.shimGetUserMedia = $i);
_e.shimLocalStreamsAPI = yi;
_e.shimRTCIceServerUrls = bi;
_e.shimRemoteStreamsAPI = gi;
_e.shimTrackEventTransceiver = Ci;
var ro = mi(fe);
function no(o) {
  if (typeof WeakMap != "function") return null;
  var c = /* @__PURE__ */ new WeakMap(),
    i = /* @__PURE__ */ new WeakMap();
  return (no = function (m) {
    return m ? i : c;
  })(o);
}
function mi(o, c) {
  if (!c && o && o.__esModule) return o;
  if (o === null || (Be(o) !== "object" && typeof o != "function"))
    return { default: o };
  var i = no(c);
  if (i && i.has(o)) return i.get(o);
  var h = {},
    m = Object.defineProperty && Object.getOwnPropertyDescriptor;
  for (var y in o)
    if (y !== "default" && Object.prototype.hasOwnProperty.call(o, y)) {
      var b = m ? Object.getOwnPropertyDescriptor(o, y) : null;
      b && (b.get || b.set) ? Object.defineProperty(h, y, b) : (h[y] = o[y]);
    }
  return (h.default = o), i && i.set(o, h), h;
}
function Be(o) {
  "@babel/helpers - typeof";
  return (
    (Be =
      typeof Symbol == "function" && typeof Symbol.iterator == "symbol"
        ? function (c) {
            return typeof c;
          }
        : function (c) {
            return c &&
              typeof Symbol == "function" &&
              c.constructor === Symbol &&
              c !== Symbol.prototype
              ? "symbol"
              : typeof c;
          }),
    Be(o)
  );
}
function yi(o) {
  if (!(Be(o) !== "object" || !o.RTCPeerConnection)) {
    if (
      ("getLocalStreams" in o.RTCPeerConnection.prototype ||
        (o.RTCPeerConnection.prototype.getLocalStreams = function () {
          return (
            this._localStreams || (this._localStreams = []), this._localStreams
          );
        }),
      !("addStream" in o.RTCPeerConnection.prototype))
    ) {
      var c = o.RTCPeerConnection.prototype.addTrack;
      (o.RTCPeerConnection.prototype.addStream = function (h) {
        var m = this;
        this._localStreams || (this._localStreams = []),
          this._localStreams.includes(h) || this._localStreams.push(h),
          h.getAudioTracks().forEach(function (y) {
            return c.call(m, y, h);
          }),
          h.getVideoTracks().forEach(function (y) {
            return c.call(m, y, h);
          });
      }),
        (o.RTCPeerConnection.prototype.addTrack = function (h) {
          for (
            var m = this,
              y = arguments.length,
              b = new Array(y > 1 ? y - 1 : 0),
              C = 1;
            C < y;
            C++
          )
            b[C - 1] = arguments[C];
          return (
            b &&
              b.forEach(function (P) {
                m._localStreams
                  ? m._localStreams.includes(P) || m._localStreams.push(P)
                  : (m._localStreams = [P]);
              }),
            c.apply(this, arguments)
          );
        });
    }
    "removeStream" in o.RTCPeerConnection.prototype ||
      (o.RTCPeerConnection.prototype.removeStream = function (h) {
        var m = this;
        this._localStreams || (this._localStreams = []);
        var y = this._localStreams.indexOf(h);
        if (y !== -1) {
          this._localStreams.splice(y, 1);
          var b = h.getTracks();
          this.getSenders().forEach(function (C) {
            b.includes(C.track) && m.removeTrack(C);
          });
        }
      });
  }
}
function gi(o) {
  if (
    !(Be(o) !== "object" || !o.RTCPeerConnection) &&
    ("getRemoteStreams" in o.RTCPeerConnection.prototype ||
      (o.RTCPeerConnection.prototype.getRemoteStreams = function () {
        return this._remoteStreams ? this._remoteStreams : [];
      }),
    !("onaddstream" in o.RTCPeerConnection.prototype))
  ) {
    Object.defineProperty(o.RTCPeerConnection.prototype, "onaddstream", {
      get: function () {
        return this._onaddstream;
      },
      set: function (h) {
        var m = this;
        this._onaddstream &&
          (this.removeEventListener("addstream", this._onaddstream),
          this.removeEventListener("track", this._onaddstreampoly)),
          this.addEventListener("addstream", (this._onaddstream = h)),
          this.addEventListener(
            "track",
            (this._onaddstreampoly = function (y) {
              y.streams.forEach(function (b) {
                if (
                  (m._remoteStreams || (m._remoteStreams = []),
                  !m._remoteStreams.includes(b))
                ) {
                  m._remoteStreams.push(b);
                  var C = new Event("addstream");
                  (C.stream = b), m.dispatchEvent(C);
                }
              });
            })
          );
      },
    });
    var c = o.RTCPeerConnection.prototype.setRemoteDescription;
    o.RTCPeerConnection.prototype.setRemoteDescription = function () {
      var h = this;
      return (
        this._onaddstreampoly ||
          this.addEventListener(
            "track",
            (this._onaddstreampoly = function (m) {
              m.streams.forEach(function (y) {
                if (
                  (h._remoteStreams || (h._remoteStreams = []),
                  !(h._remoteStreams.indexOf(y) >= 0))
                ) {
                  h._remoteStreams.push(y);
                  var b = new Event("addstream");
                  (b.stream = y), h.dispatchEvent(b);
                }
              });
            })
          ),
        c.apply(h, arguments)
      );
    };
  }
}
function wi(o) {
  if (!(Be(o) !== "object" || !o.RTCPeerConnection)) {
    var c = o.RTCPeerConnection.prototype,
      i = c.createOffer,
      h = c.createAnswer,
      m = c.setLocalDescription,
      y = c.setRemoteDescription,
      b = c.addIceCandidate;
    (c.createOffer = function (g, E) {
      var O = arguments.length >= 2 ? arguments[2] : arguments[0],
        S = i.apply(this, [O]);
      return E ? (S.then(g, E), Promise.resolve()) : S;
    }),
      (c.createAnswer = function (g, E) {
        var O = arguments.length >= 2 ? arguments[2] : arguments[0],
          S = h.apply(this, [O]);
        return E ? (S.then(g, E), Promise.resolve()) : S;
      });
    var C = function (g, E, O) {
      var S = m.apply(this, [g]);
      return O ? (S.then(E, O), Promise.resolve()) : S;
    };
    (c.setLocalDescription = C),
      (C = function (g, E, O) {
        var S = y.apply(this, [g]);
        return O ? (S.then(E, O), Promise.resolve()) : S;
      }),
      (c.setRemoteDescription = C),
      (C = function (g, E, O) {
        var S = b.apply(this, [g]);
        return O ? (S.then(E, O), Promise.resolve()) : S;
      }),
      (c.addIceCandidate = C);
  }
}
function $i(o) {
  var c = o && o.navigator;
  if (c.mediaDevices && c.mediaDevices.getUserMedia) {
    var i = c.mediaDevices,
      h = i.getUserMedia.bind(i);
    c.mediaDevices.getUserMedia = function (m) {
      return h(ao(m));
    };
  }
  !c.getUserMedia &&
    c.mediaDevices &&
    c.mediaDevices.getUserMedia &&
    (c.getUserMedia = function (y, b, C) {
      c.mediaDevices.getUserMedia(y).then(b, C);
    }.bind(c));
}
function ao(o) {
  return o && o.video !== void 0
    ? Object.assign({}, o, {
        video: ro.compactObject(o.video),
      })
    : o;
}
function bi(o) {
  if (o.RTCPeerConnection) {
    var c = o.RTCPeerConnection;
    (o.RTCPeerConnection = function (h, m) {
      if (h && h.iceServers) {
        for (var y = [], b = 0; b < h.iceServers.length; b++) {
          var C = h.iceServers[b];
          C.urls === void 0 && C.url
            ? (ro.deprecated("RTCIceServer.url", "RTCIceServer.urls"),
              (C = JSON.parse(JSON.stringify(C))),
              (C.urls = C.url),
              delete C.url,
              y.push(C))
            : y.push(h.iceServers[b]);
        }
        h.iceServers = y;
      }
      return new c(h, m);
    }),
      (o.RTCPeerConnection.prototype = c.prototype),
      "generateCertificate" in c &&
        Object.defineProperty(o.RTCPeerConnection, "generateCertificate", {
          get: function () {
            return c.generateCertificate;
          },
        });
  }
}
function Ci(o) {
  Be(o) === "object" &&
    o.RTCTrackEvent &&
    "receiver" in o.RTCTrackEvent.prototype &&
    !("transceiver" in o.RTCTrackEvent.prototype) &&
    Object.defineProperty(o.RTCTrackEvent.prototype, "transceiver", {
      get: function () {
        return {
          receiver: this.receiver,
        };
      },
    });
}
function _i(o) {
  var c = o.RTCPeerConnection.prototype.createOffer;
  o.RTCPeerConnection.prototype.createOffer = function (h) {
    if (h) {
      typeof h.offerToReceiveAudio < "u" &&
        (h.offerToReceiveAudio = !!h.offerToReceiveAudio);
      var m = this.getTransceivers().find(function (b) {
        return b.receiver.track.kind === "audio";
      });
      h.offerToReceiveAudio === !1 && m
        ? m.direction === "sendrecv"
          ? m.setDirection
            ? m.setDirection("sendonly")
            : (m.direction = "sendonly")
          : m.direction === "recvonly" &&
            (m.setDirection
              ? m.setDirection("inactive")
              : (m.direction = "inactive"))
        : h.offerToReceiveAudio === !0 &&
          !m &&
          this.addTransceiver("audio", {
            direction: "recvonly",
          }),
        typeof h.offerToReceiveVideo < "u" &&
          (h.offerToReceiveVideo = !!h.offerToReceiveVideo);
      var y = this.getTransceivers().find(function (b) {
        return b.receiver.track.kind === "video";
      });
      h.offerToReceiveVideo === !1 && y
        ? y.direction === "sendrecv"
          ? y.setDirection
            ? y.setDirection("sendonly")
            : (y.direction = "sendonly")
          : y.direction === "recvonly" &&
            (y.setDirection
              ? y.setDirection("inactive")
              : (y.direction = "inactive"))
        : h.offerToReceiveVideo === !0 &&
          !y &&
          this.addTransceiver("video", {
            direction: "recvonly",
          });
    }
    return c.apply(this, arguments);
  };
}
function Pi(o) {
  Be(o) !== "object" ||
    o.AudioContext ||
    (o.AudioContext = o.webkitAudioContext);
}
const Ti = (o) => {
  let c = !1,
    i;
  return (...h) => (c || ((i = o(h)), (c = !0)), i);
};
function je(o, c) {
  if (o === !1) throw new Error(c != null ? c : "assertion failure");
}
const Si = Ti(() => {
  const o = Qo(window);
  switch (o.browser) {
    case "chrome":
      si(window, o);
      break;
    case "firefox":
      fi(window, o);
      break;
    case "safari":
      vi(window, o);
      break;
    default:
      throw new Ga();
  }
});
let vt = Promise.resolve({ type: "stop", data: {} });
async function Ra(o, c, i) {
  var C, P, g;
  if (window.isSecureContext !== !0) throw new zo();
  if (
    ((C = navigator == null ? void 0 : navigator.mediaDevices) == null
      ? void 0
      : C.getUserMedia) === void 0
  )
    throw new Ga();
  Si();
  const h = await navigator.mediaDevices.getUserMedia({
    audio: !1,
    video: c,
  });
  o.srcObject !== void 0
    ? (o.srcObject = h)
    : o.mozSrcObject !== void 0
    ? (o.mozSrcObject = h)
    : window.URL.createObjectURL
    ? (o.src = window.URL.createObjectURL(h))
    : window.webkitURL
    ? (o.src = window.webkitURL.createObjectURL(h))
    : (o.src = h.id),
    o.play(),
    await Promise.race([
      Pa(o, "loadeddata"),
      // On iOS devices in PWA mode, QrcodeStream works initially, but after
      // killing and restarting the PWA, all video elements fail to load camera
      // streams and never emit the `loadeddata` event. Looks like this is
      // related to a WebKit issue (see #298). No workarounds at the moment.
      // To at least detect this situation, we throw an error if the event
      // has not been emitted after a 3 second timeout.
      Da(3e3).then(() => {
        throw new qo();
      }),
    ]),
    await Da(500);
  const [m] = h.getVideoTracks(),
    y =
      (g =
        (P = m == null ? void 0 : m.getCapabilities) == null
          ? void 0
          : P.call(m)) != null
        ? g
        : {};
  let b = !1;
  return (
    i &&
      y.torch &&
      (await m.applyConstraints({ advanced: [{ torch: !0 }] }), (b = !0)),
    {
      type: "start",
      data: {
        videoEl: o,
        stream: h,
        capabilities: y,
        constraints: c,
        isTorchOn: b,
      },
    }
  );
}
async function Ai(o, { constraints: c, torch: i, restart: h = !1 }) {
  vt = vt.then((y) => {
    if (y.type === "start") {
      const {
        data: { videoEl: b, stream: C, constraints: P, isTorchOn: g },
      } = y;
      return !h && o === b && c === P && i === g
        ? y
        : oo(b, C, g).then(() => Ra(o, c, i));
    }
    return Ra(o, c, i);
  });
  const m = await vt;
  if (m.type === "stop")
    throw new Error(
      "Something went wrong with the camera task queue (start task)."
    );
  return m.data.capabilities;
}
async function oo(o, c, i) {
  (o.src = ""), (o.srcObject = null), o.load(), await Pa(o, "error");
  for (const h of c.getTracks())
    i != null || (await h.applyConstraints({ advanced: [{ torch: !1 }] })),
      c.removeTrack(h),
      h.stop();
  return {
    type: "stop",
    data: {},
  };
}
async function va() {
  if (
    ((vt = vt.then((c) => {
      if (c.type === "stop") return c;
      const {
        data: { videoEl: i, stream: h, isTorchOn: m },
      } = c;
      return oo(i, h, m);
    })),
    (await vt).type === "start")
  )
    throw new Error(
      "Something went wrong with the camera task queue (stop task)."
    );
}
const Ei = /* @__PURE__ */ wa({
    __name: "QrcodeStream",
    props: {
      constraints: {
        type: Object,
        default() {
          return { facingMode: "environment" };
        },
      },
      formats: {
        type: Array,
        default: () => ["qr_code"],
      },
      paused: {
        type: Boolean,
        default: !1,
      },
      torch: {
        type: Boolean,
        default: !1,
      },
      track: {
        type: Function,
      },
    },
    emits: ["detect", "camera-on", "camera-off", "error"],
    setup(o, { emit: c }) {
      const i = o,
        h = c,
        m = er(),
        y = er(),
        b = er(),
        C = er(!1),
        P = er(!1);
      io(() => {
        P.value = !0;
      }),
        so(() => {
          va();
        });
      const g = ha(() => ({
        torch: i.torch,
        constraints: i.constraints,
        shouldStream: P.value && !i.paused,
      }));
      pa(
        g,
        async (R) => {
          const Y = b.value;
          je(
            Y !== void 0,
            "cameraSettings watcher should never be triggered when component is not mounted. Thus video element should always be defined."
          );
          const Z = m.value;
          je(
            Z !== void 0,
            "cameraSettings watcher should never be triggered when component is not mounted. Thus canvas should always be defined."
          );
          const V = Z.getContext("2d");
          if (
            (je(
              V !== null,
              "if cavnas is defined, canvas 2d context should also be non-null"
            ),
            R.shouldStream)
          )
            try {
              const x = await Ai(Y, R);
              P.value ? ((C.value = !0), h("camera-on", x)) : await va();
            } catch (x) {
              h("error", x);
            }
          else
            (Z.width = Y.videoWidth),
              (Z.height = Y.videoHeight),
              V.drawImage(Y, 0, 0, Y.videoWidth, Y.videoHeight),
              va(),
              (C.value = !1),
              h("camera-off");
        },
        { deep: !0 }
      );
      const { formats: E } = uo(i);
      pa(E, (R) => {
        P.value && Yo(R);
      });
      const O = ha(() => g.value.shouldStream && C.value);
      pa(O, (R) => {
        if (R) {
          je(
            m.value !== void 0,
            "shouldScan watcher should only be triggered when component is mounted. Thus pause frame canvas is defined"
          ),
            S(m.value),
            je(
              y.value !== void 0,
              "shouldScan watcher should only be triggered when component is mounted. Thus tracking canvas is defined"
            ),
            S(y.value);
          const Y = () => (i.track === void 0 ? 500 : 40);
          je(
            b.value !== void 0,
            "shouldScan watcher should only be triggered when component is mounted. Thus video element is defined"
          ),
            Go(b.value, {
              detectHandler: (Z) => h("detect", Z),
              formats: i.formats,
              locateHandler: G,
              minDelay: Y(),
            });
        }
      });
      const S = (R) => {
          const Y = R.getContext("2d");
          je(Y !== null, "canvas 2d context should always be non-null"),
            Y.clearRect(0, 0, R.width, R.height);
        },
        G = (R) => {
          const Y = y.value;
          je(
            Y !== void 0,
            "onLocate handler should only be called when component is mounted. Thus tracking canvas is always defined."
          );
          const Z = b.value;
          if (
            (je(
              Z !== void 0,
              "onLocate handler should only be called when component is mounted. Thus video element is always defined."
            ),
            R.length === 0 || i.track === void 0)
          )
            S(Y);
          else {
            const V = Z.offsetWidth,
              x = Z.offsetHeight,
              Pe = Z.videoWidth,
              Te = Z.videoHeight,
              Ie = Math.max(V / Pe, x / Te),
              Ue = Pe * Ie,
              He = Te * Ie,
              ze = Ue / Pe,
              mt = He / Te,
              yt = (V - Ue) / 2,
              gt = (x - He) / 2,
              qe = ({ x: oe, y: ve }) => ({
                x: Math.floor(oe * ze),
                y: Math.floor(ve * mt),
              }),
              dt = ({ x: oe, y: ve }) => ({
                x: Math.floor(oe + yt),
                y: Math.floor(ve + gt),
              }),
              wt = R.map((oe) => {
                const { boundingBox: ve, cornerPoints: $t } = oe,
                  { x: de, y: bt } = dt(
                    qe({
                      x: ve.x,
                      y: ve.y,
                    })
                  ),
                  { x: Ye, y: ue } = qe({
                    x: ve.width,
                    y: ve.height,
                  });
                return {
                  ...oe,
                  cornerPoints: $t.map((Ge) => dt(qe(Ge))),
                  boundingBox: DOMRectReadOnly.fromRect({
                    x: de,
                    y: bt,
                    width: Ye,
                    height: ue,
                  }),
                };
              });
            (Y.width = Z.offsetWidth), (Y.height = Z.offsetHeight);
            const ie = Y.getContext("2d");
            i.track(wt, ie);
          }
        },
        N = {
          width: "100%",
          height: "100%",
          position: "relative",
          // notice that we use z-index only once for the wrapper div.
          // If z-index is not defined, elements are stacked in the order they appear in the DOM.
          // The first element is at the very bottom and subsequent elements are added on top.
          "z-index": "0",
        },
        J = {
          width: "100%",
          height: "100%",
          position: "absolute",
          top: "0",
          left: "0",
        },
        K = {
          width: "100%",
          height: "100%",
          "object-fit": "cover",
        },
        z = ha(() =>
          O.value
            ? K
            : {
                ...K,
                visibility: "hidden",
                position: "absolute",
              }
        );
      return (R, Y) => (
        $a(),
        ba("div", { style: N }, [
          nr(
            "video",
            {
              ref_key: "videoRef",
              ref: b,
              style: co(z.value),
              autoplay: "",
              muted: "",
              playsinline: "",
            },
            null,
            4
          ),
          lo(
            nr(
              "canvas",
              {
                id: "qrcode-stream-pause-frame",
                ref_key: "pauseFrameRef",
                ref: m,
                style: K,
              },
              null,
              512
            ),
            [[fo, !O.value]]
          ),
          nr(
            "canvas",
            {
              id: "qrcode-stream-tracking-layer",
              ref_key: "trackingLayerRef",
              ref: y,
              style: J,
            },
            null,
            512
          ),
          nr("div", { style: J }, [Fa(R.$slots, "default")]),
        ])
      );
    },
  }),
  Oi = /* @__PURE__ */ wa({
    __name: "QrcodeCapture",
    props: {
      formats: {
        type: Array,
        default: () => ["qr_code"],
      },
    },
    emits: ["detect"],
    setup(o, { emit: c }) {
      const i = o,
        h = c,
        m = (y) => {
          if (!(!(y.target instanceof HTMLInputElement) || !y.target.files))
            for (const b of Array.from(y.target.files))
              Na(b, i.formats).then((C) => {
                h("detect", C);
              });
        };
      return (y, b) => (
        $a(),
        ba(
          "input",
          {
            onChange: m,
            type: "file",
            name: "image",
            accept: "image/*",
            capture: "environment",
            multiple: "",
          },
          null,
          32
        )
      );
    },
  }),
  Di = ["onDrop"],
  Mi = /* @__PURE__ */ wa({
    __name: "QrcodeDropZone",
    props: {
      formats: {
        type: Array,
        default: () => ["qr_code"],
      },
    },
    emits: ["detect", "dragover", "error"],
    setup(o, { emit: c }) {
      const i = o,
        h = c,
        m = async (C) => {
          try {
            const P = await C;
            h("detect", P);
          } catch (P) {
            h("error", P);
          }
        },
        y = (C) => {
          h("dragover", C);
        },
        b = ({ dataTransfer: C }) => {
          if (!C) return;
          y(!1);
          const P = [...Array.from(C.files)],
            g = C.getData("text/uri-list");
          P.forEach((E) => {
            m(Na(E));
          }),
            g !== "" && m(Jo(g, i.formats));
        };
      return (C, P) => (
        $a(),
        ba(
          "div",
          {
            onDrop: ar(b, ["prevent", "stop"]),
            onDragenter: P[0] || (P[0] = ar((g) => y(!0), ["prevent", "stop"])),
            onDragleave: P[1] || (P[1] = ar((g) => y(!1), ["prevent", "stop"])),
            onDragover: P[2] || (P[2] = ar(() => {}, ["prevent", "stop"])),
          },
          [Fa(C.$slots, "default")],
          40,
          Di
        )
      );
    },
  });
function xi(o) {
  o.component("qrcode-stream", Ei),
    o.component("qrcode-capture", Oi),
    o.component("qrcode-drop-zone", Mi);
}
const ki = { install: xi };
export {
  Oi as QrcodeCapture,
  Mi as QrcodeDropZone,
  Ei as QrcodeStream,
  ki as VueQrcodeReader,
  xi as install,
  Fi as setZXingModuleOverrides,
};
