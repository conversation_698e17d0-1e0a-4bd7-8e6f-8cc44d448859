// 登录模型
export const LOGIN_MODEL = {
  INNER: 1, // 内部账号
  SOCIAL: 2, // 中台账号
  SSO: 3, // 单点登录
};

// 用户类型
export const LEVEL_TYPE = {
  SUPER: 0, // 国家烟草专卖局（用于多租户模式）
  PROVINCE: 1, // 省级
  PROXY: 2, // 企业
};

// 角色id枚举，系统管理员、企业管理员是内置的，不允许删除
export const ROLE_TYPE = {
  SUPER_ADMIN: 0, // 超级管理员（管理多租户）
  SYSTEM_ADMIN: 1, // 系统管理员
  ENT_SYSTEM_ADMIN: 2, // 企业管理员
  APP: 3, // 应用账号
};

// 角色id枚举，系统管理员、企业管理员是内置的，不允许删除
export const ROLE_TYPE_CODE = {
  sys_admin: 1, // 省级管理员
  sys_ent_admin: 2, // 企业管理员
  sys_app_user: 3, // 企业应用角色
};

export const SYSTEM_TYPE = {
  SYSTEM_PROVINCE: 1, // 省级节点系统
  SYSTEM_PROXY: 2, // 企业节点系统
};

export const SYSTEM_NAME = {
  SYSTEM_NAME_PROVINCE: "省级节点",
  SYSTEM_NAME_PROVINCE_PROXY: "托管企业节点",
  SYSTEM_NAME_PROXY: "企业节点",
};

export const HANDLE_FIELD_TYPE = {
  // FIXED: -1, // 固定写死的值
  // TEXT: 1,
  // RELATE: 2, // 对象关联对象标识
  // LINK: 3,
  // INSTANCE: 5,
  Title: 9999,
  TEXT: -1, // 固定写死的值
  FIXED: 1, // 固定值
  DOURCE: 2, // 标识解析数据源
  HANDLE_VALUE: 3, // 标识值
  HANDLE_WITH_ATTR: 4, // 标识-属性
};

export const GRAPH_TYPE = {
  COMPLEX: "complex",
  SIMPLE: "simple",
};

export const HANDLE_TYPE = {
  // 1：实例标识 2：对象标识 3：应用标识 4：其他标识 5:实例标识新
  // PROJECT: 1,
  // TEMPLATE: 2,
  // APP: 3,
  // OTHER: 4,
  // INSTANCE: 5,

  // 1：标识 2：其他
  HANDLE: 1,
  OBJECT: 2,
};

// 对象标识-数据源类型
export const OBJECT_DATA_SOURCE_TYPE = {
  // 1:本地数据源 2:接口数据源 3：数据库服务数据
  LOCAL: 1,
  INTERFACE: 2,
  SQL_SERVER: 3,
};

export const OBJECT_DATA_SOURCE_TYPE_NAME = {
  [OBJECT_DATA_SOURCE_TYPE.LOCAL]: "本地数据源",
  [OBJECT_DATA_SOURCE_TYPE.INTERFACE]: "接口数据源",
  [OBJECT_DATA_SOURCE_TYPE.SQL_SERVER]: "数据库服务数据",
};

// 对象标识-实体类型
export const OBJECT_ENTITY_TYPE = {
  // 1:业务实体 2:资源实体
  BUSINESS: 1,
  RESOURCE: 2,
};

export const OBJECT_ENTITY_TYPE_NAME = {
  [OBJECT_ENTITY_TYPE.BUSINESS]: "业务实体",
  [OBJECT_ENTITY_TYPE.RESOURCE]: "资源实体",
};

// 标识注册操作按钮权限码
export const HANDLE_CREATE_AUTHCODE = {
  detail: "HANDLE_CREATE_DETAIL",
  edit: "HANDLE_CREATE_EDIT",
  delete: "HANDLE_CREATE_DELTELE",
  add: "HANDLE_CREATE_ADD",
};

export const DIALOG_TYPE_MAP = {
  add: 1,
  editor: 2,
  addNext: 3,
};

export const DIALOG_TYPE_NAME_MAP = {
  [DIALOG_TYPE_MAP.add]: "新增",
  [DIALOG_TYPE_MAP.editor]: "编辑",
  [DIALOG_TYPE_MAP.addNext]: "新增下一个",
};

export const FIELD_TYPE_MAP = {
  fixed: 1,
  source: 2,
  handleValue: 3,
  handleWithAttr: 4,
};

export const FIELD_TYPE_NAME_MAP = {
  [FIELD_TYPE_MAP.fixed]: "固定值",
  [FIELD_TYPE_MAP.source]: "标识解析数据源",
  [FIELD_TYPE_MAP.handleValue]: "关联标识",
  [FIELD_TYPE_MAP.handleWithAttr]: "关联属性",
};

export const FIELD_TYPE_LIST = [
  { value: 1, name: "固定值" },
  { value: 2, name: "标识解析数据源" },
  { value: 3, name: "关联标识" },
  { value: 4, name: "关联属性" },
];

export const FIELD_TYPE_LIST_M = [
  // { value: 1, name: "固定值" },
  { value: 3, name: "关联标识" },
  // { value: 4, name: "关联属性" },
];

// 是否相等
export const IF_EQUAL = {
  EQUAL: true, // 相等
  NOT: false, // 不相等
};

// 维护状态
export const STATUS_LIST = [
  { value: 1, label: "成功" },
  { value: 2, label: "失败" },
  { value: 0, label: "维护中" },
];
export const STATUS_MAP = {
  success: 1,
  failed: 2,
  maintain: 3,
};
export const STATUS_NAME_MAP = {
  [STATUS_MAP.success]: "成功",
  [STATUS_MAP.failed]: "失败",
  [STATUS_MAP.maintain]: "维护中",
};

// 完成状态
export const STATUS_FINISH = [
  { value: 0, label: "上传中" },
  { value: 1, label: "完成" },
];
export const STATUS_FINISH_MAP = {
  doing: 0,
  done: 1,
};
export const STATUS_FINISH_NAME_MAP = {
  [STATUS_FINISH_MAP.doing]: "上传中",
  [STATUS_FINISH_MAP.done]: "完成",
};

export const CURRENT_PAGE = {
  LOGIN: 0, // 登录画面
  FORGET_PASSWORD: 1, // 忘记密码
  BUSINESS_MIDDLE: 2, // 业务中台
  BIND_ACCOUNT: 3, // 绑定账号
};

export const PREALLOT_TITLE_CODE = {
  ADD: 1,
  EDIT: 2,
  DETAIL: 3,
};

export const PREALLOT_TITLE_MAP = {
  [PREALLOT_TITLE_CODE.ADD]: "新增",
  [PREALLOT_TITLE_CODE.EDIT]: "编辑",
  [PREALLOT_TITLE_CODE.DETAIL]: "详情",
};

export const PREALLOT_AUDIT_CODE = {
  PASS: 1,
  REJECT: 2,
};

export const PREALLOT_AUDIT_MAP = {
  [PREALLOT_AUDIT_CODE.PASS]: "分配",
  [PREALLOT_AUDIT_CODE.REJECT]: "驳回",
};

export const PREALLOT_AUDIT_STATUS_CODE = {
  UNCLAIMED: 0,
  CLAIMED: 1,
  REJECTED: 2,
};

export const PREALLOT_AUDIT_STATUS_MAP = {
  [PREALLOT_AUDIT_STATUS_CODE.UNCLAIMED]: "未认领",
  [PREALLOT_AUDIT_STATUS_CODE.CLAIMED]: "已认领",
  [PREALLOT_AUDIT_STATUS_CODE.REJECTED]: "已驳回",
};

export const IS_TENANT = {
  TENANT_N: 0, // 不是多租户
  TENANT_Y: 1, // 是多租户
};

export const ADMIN = "admin";

export const NEED_UPDATE_PASSWORD = {
  INIT: 1, // 初始化
  EFFECT: 0, // 有效期内
  EXPIRE: 2, // 过期
};

export const HANDLE_SOURCE = {
  REGISTER: "register", // 标识注册
  MAINTAIN: "maintain", // 标识维护
  AUTO: "auto", // 自动维护
};
