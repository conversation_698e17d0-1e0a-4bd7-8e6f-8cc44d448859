// 用户类型
export const AUTH_CODE = {
  NODE_INFO: "NODE_INFO",
  SYS_MANAGER: "SYS_MANAGER",
  NODE_MANAGER: "NODE_MANAGER",
  HANDLE_MANAGER: "HANDLE_MANAGER",
  LOG_MANAGER: "LOG_MANAGER",
  NODE_STATISTICS: "NODE_STATISTICS",
  NODE_STATISTICS_DETAIL: "NODE_STATISTICS_DETAIL",
  NODE_USER: "NODE_USER",
  NODE_USER_DETAIL: "NODE_USER_DETAIL",
  NODE_USER_EDIT: "NODE_USER_EDIT",
  NODE_USER_PWDEDIT: "NODE_USER_PWDEDIT",
  NODE_DETAIL: "NODE_DETAIL",
  NODE_DETAIL_DETAIL: "NODE_DETAIL_DETAIL",
  NODE_DETAIL_EDIT: "NODE_DETAIL_EDIT",
  USER_MANAGER: "USER_MANAGER",
  USER_MANAGER_PROVINCE_PAGE: "USER_MANAGER_PROVINCE_PAGE",
  USER_MANAGER_ENT_PAGE: "USER_MANAGER_ENT_PAGE",
  USER_MANAGER_APP_PAGE: "USER_MANAGER_APP_PAGE",
  USER_MANAGER_ADD: "USER_MANAGER_ADD",
  USER_MANAGER_EDIT: "USER_MANAGER_EDIT",
  USER_MANAGER_BINDING: "USER_MANAGER_BINDING",
  USER_MANAGER_UNBINDING: "USER_MANAGER_UNBINDING",
  USER_MANAGER_PWDEDIT: "USER_MANAGER_PWDEDIT",
  USER_MANAGER_DELETE: "USER_MANAGER_DELETE",
  ROLE_MANAGER: "ROLE_MANAGER",
  ROLE_MANAGER_PAGE: "ROLE_MANAGER_PAGE",
  ROLE_MANAGER_ADD: "ROLE_MANAGER_ADD",
  ROLE_MANAGER_EDIT: "ROLE_MANAGER_EDIT",
  ROLE_MANAGER_DETAIL: "ROLE_MANAGER_DETAIL",
  ROLE_MANAGER_DELETE: "ROLE_MANAGER_DELETE",
  AUTH_MANAGER: "AUTH_MANAGER",
  AUTH_MANAGER_TREELIST: "AUTH_MANAGER_TREELIST",
  AUTH_MANAGER_TREEEDIT: "AUTH_MANAGER_TREEEDIT",
  OPERATION_MANAGER: "OPERATION_MANAGER",
  OPERATION_MANAGER_DETAIL: "OPERATION_MANAGER_DETAIL",
  OPERATION_MANAGER_MAINEDIT: "OPERATION_MANAGER_MAINEDIT",
  OPERATION_MANAGER_CONTACEEDIT: "OPERATION_MANAGER_CONTACEE",
  OPERATION_MANAGER_TECHEDIT: "OPERATION_MANAGER_TECHEDIT",
  OPERATION_MANAGER_BUSSEDIT: "OPERATION_MANAGER_BUSSEDIT",
  TENANT_MANAGER: "TENANT_MANAGER",
  TENANT_MANAGER_PAGE: "TENANT_MANAGER_PAGE",
  TENANT_MANAGER_ADD: "TENANT_MANAGER_ADD",
  TENANT_MANAGER_DELETE: "TENANT_MANAGER_DELETE",
  REPORT_CONFIG: "REPORT_CONFIG",
  REPORT_CONFIG_EPPDETAIL: "REPORT_CONFIG_EPPDETAIL",
  REPORT_CONFIG_EPPCONFIG: "REPORT_CONFIG_EPPCONFIG",
  APP_MANAGER: "APP_MANAGER",
  APP_MANAGER_PAGE: "APP_MANAGER_PAGE",
  APP_MANAGER_ADD: "APP_MANAGER_ADD",
  APP_MANAGER_EDIT: "APP_MANAGER_EDIT",
  APP_MANAGER_DETAIL: "APP_MANAGER_DETAIL",
  APP_MANAGER_DELETE: "APP_MANAGER_DELETE",
  HOSTING_CONFIG: "HOSTING_CONFIG",
  HOSTING_CONFIG_PAGE: "HOSTING_CONFIG_PAGE",
  HOSTING_CONFIG_ADD: "HOSTING_CONFIG_ADD",
  HOSTING_CONFIG_EDIT: "HOSTING_CONFIG_EDIT",
  HOSTING_CONFIG_DELETE: "HOSTING_CONFIG_DELETE",
  PREFIX_MANAGER: "PREFIX_MANAGER",
  PREFIX_MANAGER_PAGE: "PREFIX_MANAGER_PAGE",
  PREFIX_MANAGER_ALLOT: "PREFIX_MANAGER_ALLOT",
  PREFIX_MANAGER_DETAIL: "PREFIX_MANAGER_DETAIL",
  PREFIX_MANAGER_CHANGESTATE: "PREFIX_MANAGER_CHANGESTATE",
  PREFIX_MANAGER_CONFIG: "PREFIX_MANAGER_CONFIG",
  PREFIX_MANAGER_DELETE: "PREFIX_MANAGER_DELETE",
  PREFIX_AUDIT: "PREFIX_AUDIT",
  PREFIX_AUDIT_PAGE: "PREFIX_AUDIT_PAGE",
  PREFIX_AUDIT_DETAIL: "PREFIX_AUDIT_DETAIL",
  PREFIX_AUDIT_AUDIT: "PREFIX_AUDIT_AUDIT",
  PREFIX_APPLY: "PREFIX_APPLY",
  PREFIX_APPLY_PAGE: "PREFIX_APPLY_PAGE",
  PREFIX_APPLY_ADD: "PREFIX_APPLY_ADD",
  PREFIX_APPLY_DETAIL: "PREFIX_APPLY_DETAIL",
  HOSTING_APPLY: "HOSTING_APPLY",
  HOSTING_APPLY_PAGE: "HOSTING_APPLY_PAGE",
  HOSTING_APPLY_APPLY: "HOSTING_APPLY_APPLY",
  HOSTING_AUDIT: "HOSTING_AUDIT",
  HOSTING_AUDIT_PAGE: "HOSTING_AUDIT_PAGE",
  HOSTING_AUDIT_DISTRBUTION: "HOSTING_AUDIT_DISTRBUTION",
  HOSTING_AUDIT_REJECT: "HOSTING_AUDIT_REJECT",
  HOSTING_AUDIT_DETAIL: "HOSTING_AUDIT_DETAIL",
  HOSTING_AUDIT_CANCEL: "HOSTING_AUDIT_CANCEL",
  PREFIX_PREPARE: "PREFIX_PREPARE",
  PREFIX_PREPARE_PAGE: "PREFIX_PREPARE_PAGE",
  PREFIX_PREPARE_ADD: "PREFIX_PREPARE_ADD",
  PREFIX_PREPARE_BATCH_ADD: "PREFIX_PREPARE_BATCH_ADD",
  PREFIX_PREPARE_EDIT: "PREFIX_PREPARE_EDIT",
  PREFIX_PREPARE_AUDIT: "PREFIX_PREPARE_AUDIT",
  PREFIX_PREPARE_DELETE: "PREFIX_PREPARE_DELETE",
  PREFIX_CONFIRM: "PREFIX_CONFIRM",
  PREFIX_CONFIRM_PAGE: "PREFIX_CONFIRM_PAGE",
  PREFIX_CONFIRM_CLAIM: "PREFIX_CONFIRM_CLAIM",
  PREFIX_CONFIRM_CANCEL: "PREFIX_CONFIRM_CANCEL",
  HANDLE_REGISTER: "HANDLE_REGISTER",
  HANDLE_REGISTER_PAGE: "HANDLE_REGISTER_PAGE",
  HANDLE_REGISTER_DETAIL: "HANDLE_REGISTER_DETAIL",
  HANDLE_REGISTER_ADD: "HANDLE_REGISTER_ADD",
  HANDLE_REGISTER_EDIT: "HANDLE_REGISTER_EDIT",
  HANDLE_REGISTER_DELETE: "HANDLE_REGISTER_DELETE",
  HANDLE_MAINTAIN: "HANDLE_MAINTAIN",
  HANDLE_AUTO_MAINTAIN: "HANDLE_AUTO_MAINTAIN",
  HANDLE_MAINTAIN_PAGE: "HANDLE_MAINTAIN_PAGE",
  HANDLE_MAINTAIN_MAINTAIN: "HANDLE_MAINTAIN_MAINTAIN",
  HANDLE_MAINTAIN_DETAIL: "HANDLE_MAINTAIN_DETAIL",
  HANDLE_MAINTAIN_RECORD: "HANDLE_MAINTAIN_RECORD",
  HANDLE_WATCH: "HANDLE_WATCH",
  HANDLE_WATCH_PAGE: "HANDLE_WATCH_PAGE",
  HANDLE_WATCH_DETAIL: "HANDLE_WATCH_DETAIL",
  SYSTEM_OPERATION_LOG: "SYSTEM_OPERATION_LOG",
  SYSTEM_OPERATION_LOG_PAGE: "SYSTEM_OPERATION_LOG_PAGE",
  DATA_OPERATION_LOG: "DATA_OPERATION_LOG",
  DATA_OPERATION_LOG_PAGE: "DATA_OPERATION_LOG_PAGE",
  LOGIN_CONFIG: "LOGIN_CONFIG",
  LOGIN_CONFIG_DETAIL: "LOGIN_CONFIG_DETAIL",
  LOGIN_CONFIG_EDIT: "LOGIN_CONFIG_EDIT",
  DATA_SERVICE: "DATA_SERVICE",
  DATA_SERVICE_PAGE: "DATA_SERVICE_PAGE",
  DATA_SERVICE_ADD: "DATA_SERVICE_ADD",
  DATA_SERVICE_EDIT: "DATA_SERVICE_EDIT",
  DATA_SERVICE_DETAIL: "DATA_SERVICE_DETAIL",
  DATA_SERVICE_DELETE: "DATA_SERVICE_DELETE",
  // 企业业务系统
  CONFIG_MANAGER: "CONFIG_MANAGER",
  CONFIG_MANAGER_PAGE: "CONFIG_MANAGER_PAGE",
  CONFIG_MANAGER_ADD: "CONFIG_MANAGER_ADD",
  CONFIG_MANAGER_EDIT: "CONFIG_MANAGER_EDIT",
  CONFIG_MANAGER_DELETE: "CONFIG_MANAGER_DELETE",
  ENT_PREFIX_MANAGER: "ENT_PREFIX_MANAGER",
  ENT_PREFIX_MANAGER_ADD: "ENT_PREFIX_MANAGER_ADD",
  ENT_PREFIX_MANAGER_PAGE: "ENT_PREFIX_MANAGER_PAGE",
  ENT_PREFIX_MANAGER_DETAIL: "ENT_PREFIX_MANAGER_DETAIL",
  ENT_PREFIX_MANAGER_CONFIG: "ENT_PREFIX_MANAGER_CONFIG",
  ENT_PREFIX_MANAGER_DELETE: "PENT_REFIX_MANAGER_DELETE",
  HANDLE_REGISTER_BATCH_UPLOAD: "HANDLE_REGISTER_BATCH_UPLOAD",
  HANDLE_REGISTER_BATCH_UPLOAD_RECORD: "HANDLE_REGISTER_BATCH_UPLOAD_RECORD",
  DATA_SERVICE_ACCESS: "DATA_SERVICE_ACCESS", // 数据服务接入
  METADATA_MANAGEMENT: "METADATA_MANAGEMENT", // 元数据对象

  AUTHORIZE_MANAGER: "AUTHORIZE_MANAGER", // 授权管理
  AUTH_GROUP_MANAGER: "AUTH_GROUP_MANAGER", // 权限组管理(企业用户)
  IDENTITY_AUTH_MANAGER: "IDENTITY_AUTH_MANAGER", // 身份授权(企业用户)
  IDENTITY_MANAGER: "IDENTITY_MANAGER", // 身份管理(企业管理员)
};
