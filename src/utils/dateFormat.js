/**
 * 根据日期字符串获取日期详细数据
 * @param fmt 想要的日期格式（如：YYYY/mm/dd HH:MM:SS 或者 YYYY-mm-dd HH:MM 或 YYYY-mm-dd）
 * @param date 日期数据
 * @param isNeedWeek 是否需要星期几
 * @returns {String}
 */
export function dateFormat(fmt, date, isNeedWeek = false) {
  const opt = {
    "Y+": date.getFullYear().toString(), // 年
    "m+": (date.getMonth() + 1).toString(), // 月
    "d+": date.getDate().toString(), // 日
    "H+": date.getHours().toString(), // 时
    "M+": date.getMinutes().toString(), // 分
    "S+": date.getSeconds().toString(), // 秒
    // 有其他格式化字符需求可以继续添加，必须转化成字符串
  };
  let ret;
  for (const k in opt) {
    ret = new RegExp("(" + k + ")").exec(fmt);
    if (ret) {
      fmt = fmt.replace(
        ret[1],
        ret[1].length === 1 ? opt[k] : opt[k].padStart(ret[1].length, "0")
      );
    }
  }
  // 如果需要星期几的话会返回
  return isNeedWeek ? `${fmt} ${getWeek(fmt.split(" ")[0])}` : fmt;
}

/**
 * 根据日期字符串获取星期几
 * @param date 日期字符串（如：2020/05/02）
 * @param word 前置文字 (如：星期、周 等)
 * @returns {String}
 */
export function getWeek(dateString, word = "星期") {
  const dateArray = dateString.split("/");
  const date = new Date(dateArray[0], parseInt(dateArray[1] - 1), dateArray[2]);
  return word + "日一二三四五六".charAt(date.getDay());
}
