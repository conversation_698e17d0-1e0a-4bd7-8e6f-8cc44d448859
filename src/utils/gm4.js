import CryptoSM from "sm-crypto";
import CryptoJS from "crypto-js";
// -----------------------  国密SM4算法 加密、解密 -----------------------
const SM4_KEY = "064cb5b0dd44c6de9160866202c18e58";

const SM4 = {
  encryptData(data) {
    // 第一步：SM4 加密
    const encryptData = CryptoSM.sm4.encrypt(data, SM4_KEY);
    // 第二步： Base64 编码
    return CryptoJS.enc.Base64.stringify(CryptoJS.enc.Utf8.parse(encryptData));
  },

  decryptData(data) {
    // 第一步：Base64 解码
    const words = CryptoJS.enc.Base64.parse(data);
    const decode64Str = CryptoJS.enc.Utf8.stringify(words);
    // 第二步：SM4 解密
    return CryptoSM.sm4.decrypt(decode64Str, SM4_KEY);
  },
};

// -----------------------  对外暴露： 加密、解密 -----------------------

// 默认使用SM4算法
const EncryptObject = SM4;

/**
 * 加密
 */
export const encryptData = function (data) {
  return !data ? null : EncryptObject.encryptData(data);
};

/**
 * 解密
 */
export const decryptData = function (data) {
  return !data ? null : EncryptObject.decryptData(data);
};
