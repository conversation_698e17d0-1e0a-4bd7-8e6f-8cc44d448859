// 数据通道-下发状态
export const DATA_CHANNEL_SEND_STATUS_LIST = [
  {
    id: 1,
    name: "下发成功",
  },
  {
    id: 2,
    name: "下发失败",
  },
];

export const DATA_CHANNEL_SEND_STATUS_MAP = {
  1: "下发成功",
  2: "下发失败",
};

// 对象标识类型
export const OBJECT_HANDLE_TYPE_LIST = [
  {
    id: 1,
    name: "主数据",
  },
  {
    id: 2,
    name: "非主数据",
  },
];

// 属性类型
export const ATTR_TYPE_MAP = {
  FIXED: 1, // 固定值
  SOURCE: 2, // 标识解析数据源
  RELATE_HANDLE: 3, // 关联标识
};

export const ATTR_TYPE_NAME_MAP = {
  "1": "固定值", // 固定值
  "2": "标识解析数据源", // 标识解析数据源
  "3": "关联标识", // 关联标识
};

export const BASIC_ATTR_TYPE_LIST = [
  {
    id: 1,
    name: "固定值",
  },
  {
    id: 2,
    name: "标识解析数据源",
  },
];

export const EXTEND_ATTR_TYPE_LIST = [
  {
    id: 2,
    name: "标识解析数据源",
  },
  {
    id: 3,
    name: "关联标识",
  },
];

// 弹窗类型
export const DIALOG_TYPE = {
  ADD: "add",
  EDIT: "edit",
};

// 属性分类
export const ATTR_CATEGORY = {
  BASIC: "basic", // 基础属性
  EXTEND: "extend", // 扩展属性
};

// 标识类型-map
export const HANDLE_TYPE_MAP = {
  MASTER: 1, // 主数据标识
  NORMAL: 2, // 非主数据标识
};

// 标识类型-list
export const HANDLE_TYPE_LIST = [
  {
    id: 1,
    name: "主数据标识",
  },
  {
    id: 2,
    name: "非主数据标识",
  },
];

// 实例数据类型1-基础，2-数组
export const DATA_TYPE_LIST = [
  { id: 1, name: "基础" },
  { id: 2, name: "数组" },
];

export const DATA_TYPE_NAME_MAP = {
  "1": "基础",
  "2": "数组",
};

export const APP_TYPE: Record<string, number> = {
  DMM: 1, // 中台应用
  NORMAL: 2, // 非中台应用
};

export const APP_TYPE_DESC: Record<string, string> = {
  [APP_TYPE.DMM]: "中台应用",
  [APP_TYPE.NORMAL]: "非中台应用",
};

export const MASTER_DATA_ENUM: { key: number; value: string }[] = [
  {
    key: 1,
    value: "盒烟",
  },
  {
    key: 2,
    value: "条烟",
  },
  {
    key: 3,
    value: "件烟",
  },
  {
    key: 4,
    value: "原烟",
  },
  {
    key: 5,
    value: "成品烟",
  },
  {
    key: 6,
    value: "丝束",
  },
  {
    key: 7,
    value: "滤棒",
  },
];
