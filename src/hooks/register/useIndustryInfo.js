import { reactive, toRefs } from "vue";
import { industryInfoTree } from "../../api/register/index";

const useIndustryInfo = () => {
  const response = reactive({
    bigIndustry: [],
    smallIndustry: {},
  });
  industryInfoTree().then(
    (res) => {
      const { data } = res;
      const { bigIndustry, smallIndustry } = handleData(data);
      response.bigIndustry = bigIndustry;
      response.smallIndustry = smallIndustry;
    },
    (err) => {}
  );
  return { ...toRefs(response) };
};

function handleData(data) {
  const response = {
    bigIndustry: [],
    smallIndustry: {},
  };
  for (const key in data) {
    if (key === "all") {
      const allList = [];
      for (const key in data.all) {
        const value = data.all[key];
        allList.push({ key, value });
      }
      response.bigIndustry = allList;
    } else {
      const arr = [];
      for (const tag in data[key]) {
        const val = data[key][tag];
        arr.push({
          key: tag,
          value: val,
        });
      }
      response.smallIndustry[key] = arr;
    }
  }
  return response;
}

export default useIndustryInfo;
