export function handleIndustry(data) {
  const options = [];
  for (const key in data.all) {
    options.push({
      value: key,
      label: data.all[key],
    });
  }
  for (const tag in data) {
    if (tag !== "all") {
      const li = data[tag];
      for (let i = 0, length = options.length; i < length; i++) {
        const item = options[i];
        if (item.value === tag) {
          options[i].children = [];
          for (const j in li) {
            options[i].children.push({
              value: j,
              label: li[j],
            });
          }
        }
      }
    }
  }
  return options;
}

export default handleIndustry;
