{"name": "chinatobacco", "version": "3.5.0", "description": "中国烟草工业互联网业务管理系统", "author": "中国烟草", "license": "MIT", "scripts": {"dev:province": "vite --mode development-province", "dev:ent": "vite --mode development-ent", "build": "vite build", "preview": "vite preview", "lint": "eslint src/**/*.{js,jsx,vue,ts,tsx} --fix", "prepare": "husky install", "commit": "git-cz"}, "repository": {"type": "git", "url": ""}, "lint-staged": {"src/**/*.{js,jsx,vue,ts,tsx}": ["eslint --fix"], "*.js": "eslint --cache --fix"}, "dependencies": {"@antv/layout": "^0.3.5", "@antv/x6": "^1.34.2", "@antv/x6-vue-shape": "^1.5.3", "@element-plus/icons-vue": "1.1.4", "@vue/cli-plugin-typescript": "^5.0.8", "@vueuse/core": "^8.2.6", "axios": "0.26.1", "crypto-js": "^4.1.1", "echarts": "5.3.2", "el-table-infinite-scroll": "^3.0.3", "element-china-area-data": "^5.0.1", "element-plus": "2.3.8", "file-saver": "2.0.5", "fuse.js": "6.5.3", "js-cookie": "3.0.1", "jsencrypt": "3.2.1", "jsqr": "^1.4.0", "lodash": "^4.17.21", "node-forge": "^1.3.1", "nprogress": "0.2.0", "sm-crypto": "^0.3.13", "typescript": "^4.9.3", "vue": "3.2.31", "vue-clipboard3": "^2.0.0", "vue-cropper": "1.0.3", "vue-qrcode-reader": "^5.4.0", "vue-router": "4.0.14", "vuex": "4.0.2"}, "devDependencies": {"@babel/core": "^7.20.2", "@babel/eslint-parser": "^7.19.1", "@types/file-saver": "^2.0.5", "@types/node-forge": "^1.3.1", "@types/nprogress": "^0.2.0", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "@vitejs/plugin-vue": "2.3.1", "@vue/compiler-sfc": "3.2.31", "@vue/eslint-config-typescript": "^11.0.2", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.27.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.7.0", "husky": "^8.0.2", "lint-staged": "^13.0.3", "prettier": "^2.7.1", "sass": "1.50.0", "unplugin-auto-import": "0.6.9", "vite": "^2.9.15", "vite-plugin-compression": "0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-svg-icons": "1.0.5", "vite-plugin-vue-setup-extend": "0.4.0"}, "main": "vite.config.js", "keywords": [], "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}