import vue from "@vitejs/plugin-vue";
// import { viteMockServe } from "vite-plugin-mock";

import eslintPlugin from "vite-plugin-eslint";

import createAutoImport from "./auto-import";
import createSvgIcon from "./svg-icon";
import createCompression from "./compression";
import createSetupExtend from "./setup-extend";

export default function createVitePlugins(viteEnv, command) {
  const isBuild = command === "build";
  const prodMock = true;
  const vitePlugins = [
    vue(),
    eslintPlugin({
      include: ["src/**/*.js", "src/**/*.vue", "src/*.js", "src/*.vue"],
      emitError: false,
      failOnError: false,
    }),
  ];
  vitePlugins.push(createAutoImport());
  vitePlugins.push(createSetupExtend());
  vitePlugins.push(createSvgIcon(isBuild));
  // vitePlugins.push(
  //   viteMockServe({
  //     supportTs: false,
  //     logger: false,
  //     mockPath: "mock",
  //     localEnabled: command === "serve", // 设置是否启用本地 xxx.ts 文件，不要在生产环境中打开它.设置为 false 将禁用 mock 功能
  //     prodEnabled: command !== "serve" && prodMock, // 设置打包是否启用 mock 功能
  //     injectCode: `
  //         import { setupProdMockServer } from './mockProdServer';
  //         setupProdMockServer();
  //       `,
  //   })
  // );
  if (isBuild) {
    vitePlugins.push(...createCompression(viteEnv));
  }
  return vitePlugins;
}
