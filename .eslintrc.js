module.exports = {
  env: {
    browser: true,
    es2021: true,
  },
  extends: [
    "plugin:vue/vue3-essential",
    "eslint:recommended",
    "@vue/typescript/recommended",
    "plugin:vue/essential",
    "airbnb-base",
    "plugin:prettier/recommended",
  ],
  parserOptions: {
    ecmaVersion: "latest",
    sourceType: "module",
  },
  // plugins: ["vue"],
  plugins: ["vue", "@typescript-eslint"],
  rules: {
    "import/no-extraneous-dependencies": "off",
    "import/no-unresolved": "off",
    "import/extensions": "off",
    "import/no-absolute-path": "off",
    "import/prefer-default-export": 0,
    "prettier/prettier": ["error", { endOfLine: "auto" }],
    "vue/multi-word-component-names": "off", // 关闭组件命名规则
    "no-lonely-if": "off",
    "no-use-before-define": "off",
    radix: "off",
    "no-param-reassign": "off",
    "prefer-regex-literals": "off",
    "operator-assignment": "off",
    camelcase: "off",
    "prefer-template": "off",
    "prefer-destructuring": "off",
    "array-callback-return": "off",
    "consistent-return": "off",
    "no-restricted-syntax": "off",
    "guard-for-in": "off",
    "prefer-rest-params": "off",
    "no-plusplus": "off",
    "no-shadow": "off",
    "vue/valid-define-emits": "off",
    "no-undef": "off",
    "vue/no-mutating-props": "off",
    "vue/no-v-model-argument": "off",
    "prefer-promise-reject-errors": "off",
    "no-restricted-globals": "off",
    "no-unused-expressions": "off",
    "vars-on-top": "off",
    "no-var": "off",
    "no-prototype-builtins": "off",
    "no-unused-vars": "off",
    "no-implied-eval": "off",
    "no-unreachable": "off",
    "no-continue": "off",
    "no-multi-assign": "off",
    "no-useless-escape": "off",
    "vue/no-multiple-template-root": "off",
    "@typescript-eslint/no-unused-vars": "off",
    // 允许非空断言
    "@typescript-eslint/no-non-null-assertion": "off",
    // 允许自定义模块和命名空间
    "@typescript-eslint/no-namespace": "off",
    // 允许对this设置alias
    "@typescript-eslint/no-this-alias": "off",
    // 允许使用any类型
    "@typescript-eslint/no-explicit-any": ["off"],
    "no-console": "off",
    "no-case-declarations": "off",
  },
};
